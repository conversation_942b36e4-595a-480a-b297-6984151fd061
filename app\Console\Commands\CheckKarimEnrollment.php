<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Enrollment;
use App\Models\ExamResult;

class CheckKarimEnrollment extends Command
{
    protected $signature = 'check:karim-enrollment';
    protected $description = 'Check Karim enrollment status';

    public function handle()
    {
        $student = User::find(26); // <EMAIL>
        
        $this->info("Student: {$student->name} ({$student->email})");
        
        $enrollment = Enrollment::where('user_id', 26)->with('trainingSession')->first();
        
        if ($enrollment) {
            $this->line("Enrollment Status: {$enrollment->status}");
            $this->line("Training Session: {$enrollment->trainingSession->title}");
            $this->line("Session Active: " . ($enrollment->trainingSession->active ? 'Yes' : 'No'));
            $this->line("Enrollment ID: {$enrollment->id}");
            
            if ($enrollment->status !== 'approved') {
                $this->error("❌ ISSUE: Enrollment is not approved!");
                $this->line("💡 SOLUTION: Approve the enrollment to fix the exam display issue");
                
                if ($this->confirm('Do you want to approve this enrollment now?')) {
                    $enrollment->status = 'approved';
                    $enrollment->save();
                    $this->info("✅ Enrollment approved successfully!");
                }
            } else {
                $this->info("✅ Enrollment is already approved");
            }
        } else {
            $this->error("No enrollment found for this student");
        }
        
        // Check exam result
        $examResult = ExamResult::where('user_id', 26)->with('exam')->first();
        if ($examResult) {
            $this->line("Exam Result: {$examResult->exam->title} - {$examResult->score}% (" . ($examResult->passed ? 'PASSED' : 'FAILED') . ")");
        }
        
        return Command::SUCCESS;
    }
}
