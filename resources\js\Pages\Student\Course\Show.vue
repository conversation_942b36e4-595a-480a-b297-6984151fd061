<template>
  <Head :title="course.title" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          {{ course.title }}
        </h2>
      </div>
    </template>

    <div class="pt-4 pb-0">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="flex justify-end">
          <Link :href="route('student.course-progress.index')" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
            Retour à mes cours
          </Link>
        </div>
      </div>
    </div>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Informations sur le cours -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <div class="mb-4">
              <h2 class="text-xl font-semibold mb-2">{{ course.title }}</h2>
              <div class="text-sm text-gray-600">
                Session: <span class="font-medium">{{ course.training_session.title }}</span> |
                Domaine: <span class="font-medium">{{ course.training_session.training_domain.name }}</span>
              </div>
            </div>

            <div class="mb-4">
              <p class="text-gray-700">{{ course.description || 'cours pour la secourisme' }}</p>
            </div>

            <div class="mb-2">
              <div class="flex justify-between items-center">
                <div class="text-sm text-gray-600">
                  {{ completedCount }} sur {{ totalCount }} matériels complétés
                </div>
                <div class="text-sm font-medium" :class="progressPercentage === 100 ? 'text-green-600' : 'text-blue-600'">
                  {{ progressPercentage }}%
                </div>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5 mt-1">
                <div
                  class="h-2.5 rounded-full"
                  :class="progressPercentage === 100 ? 'bg-green-600' : 'bg-blue-600'"
                  :style="{ width: `${progressPercentage}%` }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Contenu du cours -->
          <div class="md:col-span-2">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
              <div class="p-6">
                <h3 class="text-lg font-semibold mb-4">Contenu du cours</h3>

                <div class="text-center mb-6">
                  <p class="text-gray-600 mb-4">Cliquez sur le bouton ci-dessous pour accéder aux matériels du cours.</p>
                  <Link
                    :href="route('student.courses.materials', course.id)"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Accéder aux matériels du cours
                  </Link>
                </div>

                <!-- Examens associés -->
                <div v-if="exams.length > 0" class="mt-8">
                  <h3 class="text-lg font-semibold mb-4">Examens</h3>

                  <div class="space-y-4">
                    <div v-for="exam in exams" :key="exam.id" class="border rounded-lg p-4 hover:bg-gray-50">
                      <div class="flex items-center justify-between">
                        <div>
                          <h4 class="font-semibold">{{ exam.title }}</h4>
                          <p class="text-sm text-gray-600">
                            Type: {{ formatExamType(exam.type) }} | Durée: {{ exam.duration_minutes }} minutes
                          </p>
                          <p class="text-sm text-gray-600">
                            Score minimum: {{ exam.passing_score }}%
                          </p>
                        </div>
                        <div>
                          <div v-if="hasPassedExam(exam.id)" class="text-green-600 mb-2 text-sm">
                            Examen réussi
                          </div>
                          <div v-else-if="hasAttemptedExam(exam.id)" class="text-red-600 mb-2 text-sm">
                            Tentatives: {{ getAttemptCount(exam.id) }}
                          </div>
                          <button
                            @click="takeExam(exam)"
                            class="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                            :disabled="!canTakeExam(exam)"
                            :class="{ 'opacity-50 cursor-not-allowed': !canTakeExam(exam) }"
                          >
                            {{ hasPassedExam(exam.id) ? 'Voir résultats' : 'Passer l\'examen' }}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div v-else class="mt-8 text-gray-500 italic">
                  Aucun examen disponible pour ce cours.
                </div>
              </div>
            </div>
          </div>

          <!-- Informations et actions -->
          <div>
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
              <div class="p-6">
                <h3 class="text-lg font-semibold mb-4">Informations sur le cours</h3>

                <div class="mb-4">
                  <h4 class="text-sm font-medium text-gray-700">Description</h4>
                  <p class="text-sm text-gray-600">{{ course.description || 'cours pour la secourisme' }}</p>
                </div>

                <div class="mb-4">
                  <h4 class="text-sm font-medium text-gray-700">Session de formation</h4>
                  <p class="text-sm text-gray-600">{{ course.training_session.title || 'Non spécifié' }}</p>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
              <div class="p-6">
                <h3 class="text-lg font-semibold mb-4">Actions</h3>

                <div class="space-y-2">
                  <Link
                    :href="route('student.course-progress.index')"
                    class="block w-full px-4 py-2 bg-gray-200 text-gray-700 text-center rounded-md hover:bg-gray-300"
                  >
                    Retour à mes cours
                  </Link>

                  <Link
                    :href="route('student.courses.materials', course.id)"
                    class="block w-full px-4 py-2 bg-blue-600 text-white text-center rounded-md hover:bg-blue-700"
                  >
                    Accéder aux matériels du cours
                  </Link>

                  <Link
                    v-if="exams.length > 0"
                    :href="route('student.exams.index')"
                    class="block w-full px-4 py-2 bg-indigo-600 text-white text-center rounded-md hover:bg-indigo-700"
                  >
                    Voir mes examens
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal pour afficher le matériel de cours -->
    <div v-if="showMaterialModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">{{ selectedMaterial.title }}</h3>
          <button @click="closeMaterialModal" class="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="mb-4">
          <p class="text-sm text-gray-600 mb-2">{{ selectedMaterial.description }}</p>
        </div>

        <!-- Contenu du matériel selon le type -->
        <div v-if="selectedMaterial.type === 'text'" class="prose max-w-none">
          <div v-html="selectedMaterial.content"></div>
        </div>

        <div v-else-if="selectedMaterial.type === 'pdf'" class="h-[60vh]">
          <iframe :src="getFileUrl(selectedMaterial)" class="w-full h-full border"></iframe>
        </div>

        <div v-else-if="selectedMaterial.type === 'video'" class="aspect-w-16 aspect-h-9">
          <video controls class="w-full">
            <source :src="getFileUrl(selectedMaterial)" type="video/mp4">
            Votre navigateur ne supporte pas la lecture de vidéos.
          </video>
        </div>

        <div v-else-if="selectedMaterial.type === 'audio'" class="my-4">
          <audio controls class="w-full">
            <source :src="getFileUrl(selectedMaterial)" type="audio/mpeg">
            Votre navigateur ne supporte pas la lecture audio.
          </audio>
        </div>

        <div v-else-if="selectedMaterial.type === 'image'" class="my-4 text-center">
          <img :src="getFileUrl(selectedMaterial)" class="max-w-full max-h-[60vh] mx-auto" alt="Image">
        </div>

        <div v-else-if="selectedMaterial.type === 'archive'" class="my-4">
          <p class="text-gray-700">Ce fichier est une archive. Veuillez le télécharger pour consulter son contenu.</p>
          <div class="mt-2">
            <a :href="route('student.course-materials.download', selectedMaterial.id)" download class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Télécharger l'archive
            </a>
          </div>
        </div>

        <div v-else-if="selectedMaterial.type === 'gallery'" class="my-4">
          <p class="text-gray-700 mb-4">Galerie d'images</p>
          <!-- Implémentation du slider de galerie avec Swiper -->
          <div class="relative">
            <swiper
              :modules="[Navigation, Pagination, Autoplay]"
              :slides-per-view="1"
              :space-between="30"
              :navigation="true"
              :pagination="{ clickable: true }"
              :autoplay="{ delay: 5000, disableOnInteraction: false }"
              class="gallery-swiper"
            >
              <swiper-slide v-for="(image, index) in selectedMaterial.gallery_images || []" :key="index">
                <div class="swiper-image-container">
                  <img
                    :src="route('student.course-materials.view', { id: selectedMaterial.id, image_index: index })"
                    class="w-full h-auto max-h-[60vh] object-contain mx-auto"
                    alt="Image de galerie"
                  >
                  <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2 text-center">
                    Image {{ index + 1 }} / {{ selectedMaterial.gallery_images.length }}
                  </div>
                </div>
              </swiper-slide>
            </swiper>
          </div>

          <!-- Miniatures des images -->
          <div class="grid grid-cols-4 md:grid-cols-6 gap-2 mt-4">
            <div
              v-for="(image, index) in selectedMaterial.gallery_images || []"
              :key="index"
              class="border p-1 cursor-pointer hover:border-blue-500"
              @click="currentSlideIndex = index"
            >
              <img
                :src="route('student.course-materials.view', { id: selectedMaterial.id, image_index: index })"
                class="w-full h-16 object-cover"
                alt="Miniature"
              >
            </div>
          </div>
        </div>

        <div v-else-if="selectedMaterial.type === 'embed_video'" class="aspect-w-16 aspect-h-9">
          <div v-html="selectedMaterial.embed_code || '<p>Vidéo non disponible</p>'"></div>
        </div>

        <div v-else-if="selectedMaterial.type === 'quiz'" class="space-y-4">
          <!-- Implémentation du quiz ici -->
          <p class="text-gray-700">Quiz non disponible pour le moment.</p>
        </div>

        <div v-else class="my-4">
          <p class="text-gray-700">Ce type de contenu ({{ selectedMaterial.type }}) n'est pas pris en charge pour la visualisation en ligne.</p>
          <div v-if="selectedMaterial.file_path" class="mt-2">
            <a :href="route('student.course-materials.download', selectedMaterial.id)" download class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Télécharger le fichier
            </a>
          </div>
        </div>

        <div class="mt-6 flex justify-end space-x-2">
          <button
            @click="closeMaterialModal"
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            Fermer
          </button>

          <button
            @click="markAsCompleted(selectedMaterial)"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            :disabled="isCompleted(selectedMaterial.id)"
            :class="{ 'opacity-50 cursor-not-allowed': isCompleted(selectedMaterial.id) }"
          >
            {{ isCompleted(selectedMaterial.id) ? 'Déjà complété' : 'Marquer comme complété' }}
          </button>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
// Import Swiper
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

// Props
const props = defineProps({
  course: Object,
  modules: Array,
  generalMaterials: Array,
  progress: Array,
  exams: Array,
  examResults: Array,
  progressPercentage: Number,
  completedCount: Number,
  totalCount: Number
});

// Filtrer les matériels actifs
const filteredGeneralMaterials = computed(() => {
  return props.generalMaterials.filter(material => material.active);
});

// Filtrer les modules et leurs matériels actifs
const filteredModules = computed(() => {
  return props.modules.map(module => {
    return {
      ...module,
      materials: module.materials.filter(material => material.active)
    };
  });
});

// État
const showMaterialModal = ref(false);
const selectedMaterial = ref({});
const currentSlideIndex = ref(0);

// Méthodes
const formatMaterialType = (type) => {
  const types = {
    'text': 'Texte',
    'pdf': 'Document PDF',
    'video': 'Vidéo',
    'quiz': 'Quiz',
    'image': 'Image',
    'audio': 'Audio',
    'archive': 'Archive',
    'gallery': 'Galerie',
    'embed_video': 'Vidéo externe'
  };
  return types[type] || type;
};

const viewMaterial = (material) => {
  selectedMaterial.value = material;
  showMaterialModal.value = true;
};

const closeMaterialModal = () => {
  showMaterialModal.value = false;
};

const isCompleted = (materialId) => {
  return props.progress.some(p => p.course_material_id === materialId && p.completed);
};

const markAsCompleted = (material) => {
  router.post(route('student.course-progress.store'), {
    course_id: props.course.id,
    course_material_id: material.id,
    completed: true
  }, {
    preserveScroll: true,
    onSuccess: () => {
      // Le composant sera automatiquement mis à jour avec les nouvelles données
    }
  });
};

const getFileUrl = (material) => {
  if (!material || !material.id) return '';

  // Utiliser les routes dédiées pour la visualisation des fichiers
  return route('student.course-materials.view', material.id);
};

const downloadMaterial = (material) => {
  if (material.allow_download && material.file_path) {
    window.open(route('student.course-materials.download', material.id), '_blank');
  }
};

const formatExamType = (type) => {
  const types = {
    'mcq': 'QCM',
    'text': 'Rédaction',
    'file': 'Soumission de fichier'
  };
  return types[type] || type;
};

const hasPassedExam = (examId) => {
  return props.examResults.some(r => r.exam_id === examId && r.passed);
};

const hasAttemptedExam = (examId) => {
  return props.examResults.some(r => r.exam_id === examId);
};

const getAttemptCount = (examId) => {
  return props.examResults.filter(r => r.exam_id === examId).length;
};

const canTakeExam = (exam) => {
  if (hasPassedExam(exam.id)) return true; // Peut toujours voir les résultats

  const attemptCount = getAttemptCount(exam.id);
  return attemptCount < exam.attempts_allowed;
};

const takeExam = (exam) => {
  router.get(route('student.exams.show', exam.id));
};
</script>

<style>
/* Styles pour le slider de galerie */
.gallery-swiper {
  width: 100%;
  height: auto;
  margin-bottom: 20px;
}

.swiper-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 30px;
}

:root {
  --swiper-theme-color: #4f46e5;
  --swiper-navigation-size: 30px;
}

.swiper-button-next,
.swiper-button-prev {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.swiper-pagination-bullet-active {
  background-color: #4f46e5;
}
</style>
