<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\TrainingDomain;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class TrainerController extends Controller
{
    /**
     * Affiche la liste des formateurs
     */
    public function index(Request $request)
    {
        // Initialiser la requête
        $query = User::where('role', 'trainer');

        // Appliquer la recherche si elle existe
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                  ->orWhere('email', 'like', $searchTerm);
            });
        }

        // Filtrer par statut
        if ($request->has('active') && $request->active !== '') {
            $query->where('active', $request->active === 'true');
        }

        // Filtrer par nombre de sessions
        if ($request->has('has_sessions') && $request->has_sessions !== '') {
            if ($request->has_sessions === 'true') {
                $query->has('trainingSessions');
            } else {
                $query->doesntHave('trainingSessions');
            }
        }

        // Filtrer par domaine de formation
        if ($request->has('domain_id') && !empty($request->domain_id)) {
            $query->whereHas('trainingSessions', function($q) use ($request) {
                $q->where('training_domain_id', $request->domain_id);
            });
        }

        // Récupérer les formateurs avec pagination
        $trainers = $query->orderBy('name')
            ->paginate(10)
            ->withQueryString();

        // Récupérer tous les domaines de formation pour le filtre
        $domains = TrainingDomain::orderBy('name')->get();

        // Retourner la vue avec les formateurs et les filtres
        return Inertia::render('Admin/Trainers/Index', [
            'trainers' => $trainers,
            'domains' => $domains,
            'filters' => [
                'search' => $request->search ?? '',
                'active' => $request->active ?? '',
                'has_sessions' => $request->has_sessions ?? '',
                'domain_id' => $request->domain_id ?? '',
            ]
        ]);
    }

    /**
     * Affiche le formulaire de création d'un formateur
     */
    public function create()
    {
        return Inertia::render('Admin/Trainers/Create');
    }

    /**
     * Enregistre un nouveau formateur
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'bio' => 'nullable|string',
        ]);

        $validated['role'] = 'trainer';
        $validated['password'] = Hash::make($validated['password']);
        $validated['active'] = true;

        User::create($validated);

        return redirect()->route('admin.trainers.index')
            ->with('success', 'Formateur créé avec succès.');
    }

    /**
     * Affiche les détails d'un formateur
     */
    public function show(User $trainer)
    {
        if ($trainer->role !== 'trainer') {
            abort(404);
        }

        // Récupérer les sessions de formation associées à ce formateur
        $trainingSessions = $trainer->trainingSessions()
            ->with('trainingDomain')
            ->orderBy('start_date', 'desc')
            ->get();

        return Inertia::render('Admin/Trainers/Show', [
            'trainer' => $trainer,
            'trainingSessions' => $trainingSessions
        ]);
    }

    /**
     * Affiche le formulaire de modification d'un formateur
     */
    public function edit(User $trainer)
    {
        if ($trainer->role !== 'trainer') {
            abort(404);
        }

        return Inertia::render('Admin/Trainers/Edit', [
            'trainer' => $trainer
        ]);
    }

    /**
     * Met à jour les informations d'un formateur
     */
    public function update(Request $request, User $trainer)
    {
        if ($trainer->role !== 'trainer') {
            abort(404);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($trainer->id)],
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'bio' => 'nullable|string',
            'active' => 'required|boolean',
        ]);

        // Mise à jour du mot de passe uniquement s'il est fourni
        if ($request->filled('password')) {
            $request->validate([
                'password' => 'string|min:8',
            ]);
            $validated['password'] = Hash::make($request->password);
        }

        $trainer->update($validated);

        return redirect()->route('admin.trainers.index')
            ->with('success', 'Formateur mis à jour avec succès.');
    }

    /**
     * Supprime un formateur
     */
    public function destroy(User $trainer)
    {
        if ($trainer->role !== 'trainer') {
            abort(404);
        }

        // Vérifier si le formateur a des sessions de formation associées
        if ($trainer->trainingSessions()->count() > 0) {
            return redirect()->route('admin.trainers.index')
                ->with('error', 'Impossible de supprimer ce formateur car il a des sessions de formation associées.');
        }

        $trainer->delete();

        return redirect()->route('admin.trainers.index')
            ->with('success', 'Formateur supprimé avec succès.');
    }
}
