<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ne rien faire si la table n'existe pas encore
        if (!Schema::hasTable('exam_questions')) {
            return;
        }

        Schema::table('exam_questions', function (Blueprint $table) {
            // Renommer la colonne correct_answer en correct_options si elle existe
            if (Schema::hasColumn('exam_questions', 'correct_answer')) {
                $table->renameColumn('correct_answer', 'correct_options');
            } else if (!Schema::hasColumn('exam_questions', 'correct_options')) {
                $table->json('correct_options')->nullable()->after('options');
            }

            // Supprimer la colonne multiple_answers_allowed si elle existe
            if (Schema::hasColumn('exam_questions', 'multiple_answers_allowed')) {
                $table->dropColumn('multiple_answers_allowed');
            }

            // Ajouter la colonne explanation si elle n'existe pas
            if (!Schema::hasColumn('exam_questions', 'explanation')) {
                $table->text('explanation')->nullable()->after('correct_options');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exam_questions', function (Blueprint $table) {
            // Renommer la colonne correct_options en correct_answer
            if (Schema::hasColumn('exam_questions', 'correct_options')) {
                $table->renameColumn('correct_options', 'correct_answer');
            }

            // Ajouter la colonne multiple_answers_allowed
            if (!Schema::hasColumn('exam_questions', 'multiple_answers_allowed')) {
                $table->boolean('multiple_answers_allowed')->default(false)->after('correct_answer');
            }

            // Supprimer la colonne explanation
            if (Schema::hasColumn('exam_questions', 'explanation')) {
                $table->dropColumn('explanation');
            }
        });
    }
};
