<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\CourseProgress;
use App\Models\CourseMaterial;
use App\Models\Enrollment;
use App\Models\Exam;
use App\Models\ExamResult;
use App\Models\Module;
use App\Models\TrainingSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class CourseProgressController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->user();

        // Récupérer les inscriptions de l'apprenant
        $enrollments = Enrollment::where('user_id', $user->id)
            ->where('status', 'approved')
            ->pluck('training_session_id')
            ->toArray();

        // Récupérer les cours disponibles pour l'apprenant
        $courses = Course::whereIn('training_session_id', $enrollments)
            ->where('active', true)
            ->with(['trainingSession', 'trainingSession.trainingDomain'])
            ->get();

        // Récupérer la progression pour chaque cours
        $progressData = [];
        foreach ($courses as $course) {
            $materialsCount = CourseMaterial::where('course_id', $course->id)
                ->count();

            $completedCount = CourseProgress::where('user_id', $user->id)
                ->where('course_id', $course->id)
                ->where('completed', true)
                ->count();

            $progressPercentage = $materialsCount > 0 ? round(($completedCount / $materialsCount) * 100) : 0;

            $progressData[] = [
                'id' => $course->id,
                'title' => $course->title,
                'session' => $course->trainingSession->title,
                'domain' => $course->trainingSession->trainingDomain->name,
                'progress' => $progressPercentage,
                'completed' => $completedCount,
                'total' => $materialsCount
            ];
        }

        return Inertia::render('Student/CourseProgress/Index', [
            'courses' => $progressData
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'course_id' => 'required|exists:courses,id',
            'course_material_id' => 'required|exists:course_materials,id',
            'completed' => 'required|boolean',
        ]);

        $user = Auth::user();

        // Vérifier si l'utilisateur est inscrit au cours
        $course = Course::findOrFail($validated['course_id']);
        $enrollment = Enrollment::where('user_id', $user->id)
            ->where('training_session_id', $course->training_session_id)
            ->where('status', 'approved')
            ->first();

        if (!$enrollment) {
            return response()->json(['message' => 'Vous n\'êtes pas inscrit à ce cours'], 403);
        }

        // Créer ou mettre à jour la progression
        $progress = CourseProgress::updateOrCreate(
            [
                'user_id' => $user->id,
                'course_id' => $validated['course_id'],
                'course_material_id' => $validated['course_material_id'],
            ],
            [
                'completed' => $validated['completed'],
                'completed_at' => now(),
                'last_accessed_at' => now(),
            ]
        );

        return redirect()->back();
    }

    /**
     * Display the specified resource.
     */
    public function show(Course $course)
    {
        $user = Auth::user();

        // Vérifier si l'utilisateur est inscrit au cours
        $enrollment = Enrollment::where('user_id', $user->id)
            ->where('training_session_id', $course->training_session_id)
            ->where('status', 'approved')
            ->first();

        if (!$enrollment) {
            return redirect()->route('student.dashboard')
                ->with('error', 'Vous n\'êtes pas inscrit à ce cours');
        }

        // Récupérer les modules du cours
        $modules = Module::where('course_id', $course->id)
            ->where('is_published', true)
            ->orderBy('order')
            ->with(['materials' => function ($query) {
                $query->orderBy('order');
            }])
            ->get();

        // Récupérer les matériels généraux du cours (sans module)
        $generalMaterials = CourseMaterial::where('course_id', $course->id)
            ->whereNull('module_id')
            ->orderBy('order')
            ->get();

        // Traiter les matériels pour les galeries d'images et les chemins de fichiers
        foreach ($generalMaterials as $material) {
            // Traiter les galeries d'images
            if ($material->type === 'gallery' && $material->gallery_data) {
                $material->gallery_images = json_decode($material->gallery_data, true);
            }

            // S'assurer que les chemins de fichiers sont corrects
            if ($material->file_path && !filter_var($material->file_path, FILTER_VALIDATE_URL)) {
                // Vérifier si le fichier existe dans le stockage
                $storage_path = 'public/' . $material->file_path;
                if (!Storage::exists($storage_path)) {
                    // Essayer avec le préfixe course-materials
                    $alt_path = 'public/course-materials/' . $material->file_path;
                    if (Storage::exists($alt_path)) {
                        $material->file_path = 'course-materials/' . $material->file_path;
                    }
                }
            }
        }

        // Traiter les matériels des modules
        foreach ($modules as $module) {
            foreach ($module->materials as $material) {
                // Traiter les galeries d'images
                if ($material->type === 'gallery' && $material->gallery_data) {
                    $material->gallery_images = json_decode($material->gallery_data, true);
                }

                // S'assurer que les chemins de fichiers sont corrects
                if ($material->file_path && !filter_var($material->file_path, FILTER_VALIDATE_URL)) {
                    // Vérifier si le fichier existe dans le stockage
                    $storage_path = 'public/' . $material->file_path;
                    if (!Storage::exists($storage_path)) {
                        // Essayer avec le préfixe course-materials
                        $alt_path = 'public/course-materials/' . $material->file_path;
                        if (Storage::exists($alt_path)) {
                            $material->file_path = 'course-materials/' . $material->file_path;
                        }
                    }
                }
            }
        }

        // Récupérer la progression de l'utilisateur pour ce cours
        $progress = CourseProgress::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->get();

        // Récupérer les examens associés au cours
        $exams = Exam::where('training_session_id', $course->training_session_id)
            ->where('active', true)
            ->get();

        // Récupérer les résultats d'examens de l'utilisateur
        $examResults = ExamResult::where('user_id', $user->id)
            ->whereIn('exam_id', $exams->pluck('id'))
            ->get();

        // Calculer la progression globale
        $allMaterials = CourseMaterial::where('course_id', $course->id)
            ->get();

        $completedCount = $progress->where('completed', true)->count();
        $totalCount = $allMaterials->count();
        $progressPercentage = $totalCount > 0 ? round(($completedCount / $totalCount) * 100) : 0;

        // Journaliser les informations pour le débogage
        \Log::info('Matériels récupérés pour l\'apprenant (show)', [
            'user_id' => $user->id,
            'course_id' => $course->id,
            'general_materials_count' => $generalMaterials->count(),
            'modules_count' => $modules->count(),
            'modules_materials_count' => $modules->sum(function($module) {
                return $module->materials->count();
            })
        ]);

        return Inertia::render('Student/Course/Show', [
            'course' => $course->load('trainingSession.trainer', 'trainingSession.trainingDomain'),
            'modules' => $modules,
            'generalMaterials' => $generalMaterials,
            'progress' => $progress,
            'exams' => $exams,
            'examResults' => $examResults,
            'progressPercentage' => $progressPercentage,
            'completedCount' => $completedCount,
            'totalCount' => $totalCount
        ]);
    }

    /**
     * Display the materials of the specified course.
     */
    public function materials(Course $course)
    {
        $user = Auth::user();

        // Vérifier si l'utilisateur est inscrit au cours
        $enrollment = Enrollment::where('user_id', $user->id)
            ->where('training_session_id', $course->training_session_id)
            ->where('status', 'approved')
            ->first();

        if (!$enrollment) {
            return redirect()->route('student.dashboard')
                ->with('error', 'Vous n\'êtes pas inscrit à ce cours');
        }

        // Récupérer les modules du cours
        $modules = Module::where('course_id', $course->id)
            ->where('is_published', true)
            ->orderBy('order')
            ->with(['materials' => function ($query) {
                $query->orderBy('order');
            }])
            ->get();

        // Récupérer les matériels généraux du cours (sans module)
        $generalMaterials = CourseMaterial::where('course_id', $course->id)
            ->whereNull('module_id')
            ->orderBy('order')
            ->get();

        // Traiter les matériels pour les galeries d'images et les chemins de fichiers
        foreach ($generalMaterials as $material) {
            // Traiter les galeries d'images
            if ($material->type === 'gallery') {
                $galleryImages = [];

                // Essayer d'abord avec metadata (format JSON)
                $metadata = null;
                if ($material->metadata) {
                    $metadata = is_array($material->metadata) ? $material->metadata : json_decode($material->metadata, true);
                }

                if (is_array($metadata)) {
                    if (isset($metadata['images']) && is_array($metadata['images'])) {
                        // Format utilisé dans l'interface admin
                        $galleryImages = $metadata['images'];
                    } elseif (isset($metadata['gallery_paths']) && is_array($metadata['gallery_paths'])) {
                        // Format utilisé dans l'interface formateur
                        foreach ($metadata['gallery_paths'] as $index => $path) {
                            $galleryImages[] = [
                                'path' => $path,
                                'caption' => 'Image ' . ($index + 1)
                            ];
                        }
                    }
                }

                // Si aucune image n'a été trouvée dans les métadonnées, essayer avec gallery_data
                if (empty($galleryImages) && $material->gallery_data) {
                    $galleryData = json_decode($material->gallery_data, true);
                    if (isset($galleryData['images']) && is_array($galleryData['images'])) {
                        $galleryImages = $galleryData['images'];
                    }
                }

                $material->gallery_images = $galleryImages;
            }

            // S'assurer que les chemins de fichiers sont corrects
            if ($material->file_path && !filter_var($material->file_path, FILTER_VALIDATE_URL)) {
                // Vérifier si le fichier existe dans le stockage
                $storage_path = 'public/' . $material->file_path;
                if (!Storage::exists($storage_path)) {
                    // Essayer avec le préfixe course-materials
                    $alt_path = 'public/course-materials/' . $material->file_path;
                    if (Storage::exists($alt_path)) {
                        $material->file_path = 'course-materials/' . $material->file_path;
                    }
                }
            }
        }

        // Traiter les matériels des modules
        foreach ($modules as $module) {
            foreach ($module->materials as $material) {
                // Traiter les galeries d'images
                if ($material->type === 'gallery') {
                    $galleryImages = [];

                    // Essayer d'abord avec metadata (format JSON)
                    $metadata = null;
                    if ($material->metadata) {
                        $metadata = is_array($material->metadata) ? $material->metadata : json_decode($material->metadata, true);
                    }

                    if (is_array($metadata)) {
                        if (isset($metadata['images']) && is_array($metadata['images'])) {
                            // Format utilisé dans l'interface admin
                            $galleryImages = $metadata['images'];
                        } elseif (isset($metadata['gallery_paths']) && is_array($metadata['gallery_paths'])) {
                            // Format utilisé dans l'interface formateur
                            foreach ($metadata['gallery_paths'] as $index => $path) {
                                $galleryImages[] = [
                                    'path' => $path,
                                    'caption' => 'Image ' . ($index + 1)
                                ];
                            }
                        }
                    }

                    // Si aucune image n'a été trouvée dans les métadonnées, essayer avec gallery_data
                    if (empty($galleryImages) && $material->gallery_data) {
                        $galleryData = json_decode($material->gallery_data, true);
                        if (isset($galleryData['images']) && is_array($galleryData['images'])) {
                            $galleryImages = $galleryData['images'];
                        }
                    }

                    $material->gallery_images = $galleryImages;
                }

                // S'assurer que les chemins de fichiers sont corrects
                if ($material->file_path && !filter_var($material->file_path, FILTER_VALIDATE_URL)) {
                    // Vérifier si le fichier existe dans le stockage
                    $storage_path = 'public/' . $material->file_path;
                    if (!Storage::exists($storage_path)) {
                        // Essayer avec le préfixe course-materials
                        $alt_path = 'public/course-materials/' . $material->file_path;
                        if (Storage::exists($alt_path)) {
                            $material->file_path = 'course-materials/' . $material->file_path;
                        }
                    }
                }
            }
        }

        // Récupérer la progression de l'utilisateur pour ce cours
        $progress = CourseProgress::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->get();

        // Journaliser les informations pour le débogage
        \Log::info('Matériels récupérés pour l\'apprenant', [
            'user_id' => $user->id,
            'course_id' => $course->id,
            'general_materials_count' => $generalMaterials->count(),
            'modules_count' => $modules->count(),
            'modules_materials_count' => $modules->sum(function($module) {
                return $module->materials->count();
            })
        ]);

        // Calculer la progression globale
        $allMaterials = CourseMaterial::where('course_id', $course->id)
            ->get();

        $completedCount = $progress->where('completed', true)->count();
        $totalCount = $allMaterials->count();
        $progressPercentage = $totalCount > 0 ? round(($completedCount / $totalCount) * 100) : 0;

        return Inertia::render('Student/Course/Materials', [
            'course' => $course->load('trainingSession.trainer', 'trainingSession.trainingDomain'),
            'modules' => $modules,
            'generalMaterials' => $generalMaterials,
            'progress' => $progress,
            'progressPercentage' => $progressPercentage,
            'completedCount' => $completedCount,
            'totalCount' => $totalCount
        ]);
    }

    /**
     * Télécharger un fichier associé à un matériel de cours
     */
    public function downloadMaterial(string $id)
    {
        $user = Auth::user();
        $material = CourseMaterial::with('course.trainingSession')->findOrFail($id);

        // Vérifier si l'utilisateur est inscrit au cours
        $enrollment = Enrollment::where('user_id', $user->id)
            ->where('training_session_id', $material->course->training_session_id)
            ->where('status', 'approved')
            ->first();

        if (!$enrollment) {
            return redirect()->route('student.dashboard')
                ->with('error', 'Vous n\'êtes pas inscrit à ce cours');
        }

        // Vérifier si le téléchargement est autorisé
        if (!$material->allow_download) {
            return redirect()->back()->with('error', 'Le téléchargement de ce matériel n\'est pas autorisé.');
        }

        // Vérifier si un fichier est associé
        if (!$material->file_path) {
            return redirect()->back()->with('error', 'Aucun fichier n\'est associé à ce matériel.');
        }

        // Obtenir le chemin complet du fichier
        $filePath = Storage::disk('public')->path($material->file_path);

        // Vérifier si le fichier existe
        if (!file_exists($filePath)) {
            return redirect()->back()->with('error', 'Le fichier n\'existe pas sur le serveur.');
        }

        // Déterminer l'extension du fichier
        $extension = pathinfo($material->file_path, PATHINFO_EXTENSION);

        // Créer un nom de fichier pour le téléchargement
        $downloadName = $material->title . '.' . $extension;

        // Journaliser les informations pour le débogage
        \Log::info('Téléchargement de fichier par étudiant', [
            'user_id' => $user->id,
            'material_id' => $material->id,
            'file_path' => $material->file_path,
            'full_path' => $filePath,
            'download_name' => $downloadName,
            'exists' => file_exists($filePath)
        ]);

        // Télécharger le fichier avec le bon type MIME
        return response()->download($filePath, $downloadName, [
            'Content-Type' => $material->mime_type ?? 'application/octet-stream',
        ]);
    }

    /**
     * Afficher le fichier associé à un matériel de cours
     */
    public function viewMaterial(Request $request, string $id)
    {
        $user = Auth::user();
        $material = CourseMaterial::with('course.trainingSession')->findOrFail($id);

        // Vérifier si l'utilisateur est inscrit au cours
        $enrollment = Enrollment::where('user_id', $user->id)
            ->where('training_session_id', $material->course->training_session_id)
            ->where('status', 'approved')
            ->first();

        if (!$enrollment) {
            return redirect()->route('student.dashboard')
                ->with('error', 'Vous n\'êtes pas inscrit à ce cours');
        }

        // Vérifier si la visualisation en ligne est autorisée
        if (!$material->allow_online_viewing) {
            return redirect()->back()->with('error', 'La visualisation en ligne de ce matériel n\'est pas autorisée.');
        }

        // Vérifier si un fichier est associé
        if (!$material->file_path && $material->type !== 'gallery') {
            return redirect()->back()->with('error', 'Aucun fichier n\'est associé à ce matériel.');
        }

        // Traitement spécial pour les galeries d'images
        if ($material->type === 'gallery') {
            // Si un index d'image est spécifié, afficher cette image
            if ($request->has('image_index')) {
                $imageIndex = $request->input('image_index');

                // Essayer d'abord avec metadata (format JSON)
                $metadata = null;
                if ($material->metadata) {
                    $metadata = is_array($material->metadata) ? $material->metadata : json_decode($material->metadata, true);
                }

                // Extraire les chemins d'images en fonction de la structure des métadonnées
                $galleryPaths = [];

                if (is_array($metadata)) {
                    if (isset($metadata['gallery_paths']) && is_array($metadata['gallery_paths'])) {
                        // Format utilisé dans l'interface formateur
                        $galleryPaths = array_values($metadata['gallery_paths']);
                    } elseif (isset($metadata['images']) && is_array($metadata['images'])) {
                        // Format utilisé dans l'interface admin
                        $galleryPaths = array_map(function($img) {
                            return is_array($img) ? ($img['path'] ?? '') : $img;
                        }, $metadata['images']);
                    } elseif (is_array($metadata) && !isset($metadata['gallery_paths']) && !isset($metadata['images'])) {
                        // Si les métadonnées sont directement un tableau de chemins
                        $galleryPaths = array_values($metadata);
                    }
                }

                // Si aucun chemin n'a été trouvé dans les métadonnées, essayer avec gallery_data
                if (empty($galleryPaths) && $material->gallery_data) {
                    $galleryData = json_decode($material->gallery_data, true);
                    if (isset($galleryData['images']) && is_array($galleryData['images'])) {
                        $galleryPaths = array_map(function($img) {
                            return is_array($img) ? ($img['path'] ?? '') : $img;
                        }, $galleryData['images']);
                    }
                }

                // Vérifier si l'index demandé existe
                if (isset($galleryPaths[$imageIndex])) {
                    $imagePath = $galleryPaths[$imageIndex];
                    $fullPath = Storage::disk('public')->path($imagePath);

                    if (file_exists($fullPath)) {
                        // Déterminer le type MIME
                        $mimeType = mime_content_type($fullPath) ?: 'image/jpeg';

                        // Journaliser les informations pour le débogage
                        \Log::info('Visualisation d\'image de galerie par étudiant', [
                            'user_id' => $user->id,
                            'material_id' => $material->id,
                            'image_index' => $imageIndex,
                            'image_path' => $imagePath,
                            'full_path' => $fullPath,
                            'mime_type' => $mimeType
                        ]);

                        return response()->file($fullPath, [
                            'Content-Type' => $mimeType,
                            'Content-Disposition' => 'inline; filename="' . basename($imagePath) . '"',
                        ]);
                    }
                }

                \Log::error('Image de galerie non trouvée pour étudiant', [
                    'user_id' => $user->id,
                    'material_id' => $material->id,
                    'image_index' => $imageIndex,
                    'metadata' => $metadata,
                    'gallery_data' => $material->gallery_data
                ]);

                return response()->json(['error' => 'Image non trouvée'], 404);
            }

            // Sinon, rediriger vers la page du matériel
            return redirect()->route('student.courses.materials', $material->course_id);
        }

        // Obtenir le chemin complet du fichier
        $filePath = Storage::disk('public')->path($material->file_path);

        // Vérifier si le fichier existe
        if (!file_exists($filePath)) {
            return redirect()->back()->with('error', 'Le fichier n\'existe pas sur le serveur.');
        }

        // Déterminer le type MIME si non spécifié
        $mimeType = $material->mime_type;
        if (!$mimeType) {
            $extension = pathinfo($filePath, PATHINFO_EXTENSION);
            $mimeType = $this->getMimeTypeFromExtension($extension);
        }

        // Journaliser les informations pour le débogage
        \Log::info('Visualisation de fichier par étudiant', [
            'user_id' => $user->id,
            'material_id' => $material->id,
            'file_path' => $material->file_path,
            'full_path' => $filePath,
            'mime_type' => $mimeType,
            'exists' => file_exists($filePath)
        ]);

        // Afficher le fichier avec le bon type MIME
        return response()->file($filePath, [
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'inline; filename="' . basename($material->file_path) . '"',
        ]);
    }

    /**
     * Déterminer le type MIME à partir de l'extension du fichier
     */
    private function getMimeTypeFromExtension($extension)
    {
        $mimeTypes = [
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt' => 'application/vnd.ms-powerpoint',
            'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'mp3' => 'audio/mpeg',
            'mp4' => 'video/mp4',
            'avi' => 'video/x-msvideo',
            'mov' => 'video/quicktime',
            'zip' => 'application/zip',
            'rar' => 'application/x-rar-compressed',
            'txt' => 'text/plain',
            'html' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
        ];

        return $mimeTypes[strtolower($extension)] ?? 'application/octet-stream';
    }
}
