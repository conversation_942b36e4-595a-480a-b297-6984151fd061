<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Module;
use App\Models\TrainingSession;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ModuleController extends Controller
{
    /**
     * Affiche la liste des modules pour un cours
     */
    public function index(Request $request)
    {
        $courseId = $request->query('course_id');

        if (!$courseId) {
            return redirect()->route('admin.courses.index')
                ->with('error', 'Veuillez sélectionner un cours pour voir ses modules.');
        }

        // Récupérer le cours avec ses modules
        $course = Course::with(['trainingSession', 'modules.materials'])
            ->findOrFail($courseId);

        return Inertia::render('Admin/Modules/Index', [
            'course' => $course,
            'modules' => $course->modules()->with('materials')->orderBy('order')->get(),
        ]);
    }

    /**
     * Affiche le formulaire de création d'un module
     */
    public function create(Request $request)
    {
        $courseId = $request->query('course_id');

        if (!$courseId) {
            return redirect()->route('admin.courses.index')
                ->with('error', 'Veuillez sélectionner un cours pour ajouter un module.');
        }

        // Récupérer le cours
        $course = Course::with('trainingSession')->findOrFail($courseId);

        return Inertia::render('Admin/Modules/Create', [
            'course' => $course,
        ]);
    }

    /**
     * Enregistre un nouveau module
     */
    public function store(Request $request)
    {
        // Valider les données
        $validated = $request->validate([
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'order' => 'required|integer|min:1',
            'is_published' => 'boolean',
            'publish_date' => 'nullable|date',
        ]);

        // Créer le module
        $module = Module::create($validated);

        return redirect()->route('admin.modules.index', ['course_id' => $validated['course_id']])
            ->with('success', 'Module créé avec succès.');
    }

    /**
     * Affiche un module
     */
    public function show(string $id)
    {
        $module = Module::with(['course.trainingSession', 'materials'])->findOrFail($id);

        return Inertia::render('Admin/Modules/Show', [
            'module' => $module,
            'course' => $module->course,
            'materials' => $module->materials()->orderBy('order')->get(),
        ]);
    }

    /**
     * Affiche le formulaire de modification d'un module
     */
    public function edit(string $id)
    {
        $module = Module::with('course.trainingSession')->findOrFail($id);

        return Inertia::render('Admin/Modules/Edit', [
            'module' => $module,
            'course' => $module->course,
        ]);
    }

    /**
     * Met à jour un module
     */
    public function update(Request $request, string $id)
    {
        $module = Module::with('course')->findOrFail($id);

        // Valider les données
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'order' => 'required|integer|min:1',
            'is_published' => 'boolean',
            'publish_date' => 'nullable|date',
        ]);

        // Mettre à jour le module
        $module->update($validated);

        return redirect()->route('admin.modules.index', ['course_id' => $module->course_id])
            ->with('success', 'Module mis à jour avec succès.');
    }

    /**
     * Supprime un module
     */
    public function destroy(string $id)
    {
        $module = Module::with('course')->findOrFail($id);
        $courseId = $module->course_id;

        // Supprimer le module
        $module->delete();

        return redirect()->route('admin.modules.index', ['course_id' => $courseId])
            ->with('success', 'Module supprimé avec succès.');
    }
}
