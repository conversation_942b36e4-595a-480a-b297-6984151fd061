<?php

namespace App\Mail;

use App\Models\Enrollment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class InvoiceMail extends Mailable
{
    use Queueable, SerializesModels;

    public $enrollment;
    public $invoiceData;

    /**
     * Create a new message instance.
     */
    public function __construct(Enrollment $enrollment, array $invoiceData)
    {
        $this->enrollment = $enrollment;
        $this->invoiceData = $invoiceData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Facture de formation - ' . $this->invoiceData['invoice_number'],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.invoice',
            with: [
                'enrollment' => $this->enrollment,
                'student' => $this->enrollment->user,
                'trainingSession' => $this->enrollment->trainingSession,
                'trainingDomain' => $this->enrollment->trainingSession->trainingDomain,
                'invoiceData' => $this->invoiceData,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
