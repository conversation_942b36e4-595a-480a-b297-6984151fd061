<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;

// Configuration de la base de données
$capsule = new Capsule;

$capsule->addConnection([
    'driver' => 'mysql',
    'host' => env('DB_HOST', '127.0.0.1'),
    'port' => env('DB_PORT', '3306'),
    'database' => env('DB_DATABASE', 'formation_pcmet'),
    'username' => env('DB_USERNAME', 'root'),
    'password' => env('DB_PASSWORD', ''),
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    // Vérifier si la table announcements existe
    $tables = Capsule::select('SHOW TABLES');
    $tableNames = array_map(function($table) {
        return array_values((array)$table)[0];
    }, $tables);
    
    echo "Tables existantes:\n";
    foreach ($tableNames as $table) {
        echo "- $table\n";
    }
    
    // Si la table announcements existe, marquer la migration comme exécutée
    if (in_array('announcements', $tableNames)) {
        echo "\nLa table 'announcements' existe déjà.\n";
        
        // Vérifier si l'entrée existe dans migrations
        $migrationExists = Capsule::table('migrations')
            ->where('migration', '2023_06_15_000001_create_announcements_table')
            ->exists();
            
        if (!$migrationExists) {
            echo "Ajout de l'entrée dans la table migrations...\n";
            Capsule::table('migrations')->insert([
                'migration' => '2023_06_15_000001_create_announcements_table',
                'batch' => 1
            ]);
            echo "Migration marquée comme exécutée.\n";
        } else {
            echo "La migration est déjà marquée comme exécutée.\n";
        }
    }
    
    // Vérifier si training_sessions existe
    if (in_array('training_sessions', $tableNames)) {
        echo "\nLa table 'training_sessions' existe déjà.\n";
    } else {
        echo "\nLa table 'training_sessions' n'existe pas. Elle sera créée par les migrations.\n";
    }
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
