<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the status enum to include 'draft' and 'active'
        DB::statement("ALTER TABLE certificates MODIFY COLUMN status ENUM('draft', 'issued', 'active', 'revoked', 'expired') DEFAULT 'draft'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original enum values
        DB::statement("ALTER TABLE certificates MODIFY COLUMN status ENUM('issued', 'revoked', 'expired') DEFAULT 'issued'");
    }
};
