<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HomepageContent;
use App\Services\MediaOptimizationService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Storage;

class HomepageContentController extends Controller
{
    protected $mediaService;

    public function __construct(MediaOptimizationService $mediaService)
    {
        $this->mediaService = $mediaService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $contents = HomepageContent::orderBy('section')
            ->orderBy('sort_order')
            ->get()
            ->groupBy('section');

        return Inertia::render('Admin/HomepageContent/Index', [
            'contents' => $contents
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/HomepageContent/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'section' => 'required|string|max:255',
            'key' => 'required|string|max:255',
            'value' => 'nullable|string',
            'type' => 'required|in:text,image,video,json',
            'metadata' => 'nullable|array',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        // Handle file uploads
        if ($request->hasFile('file')) {
            $file = $request->file('file');

            // Validate file based on type
            if ($request->type === 'image') {
                $errors = $this->mediaService->validateImage($file);
                if (!empty($errors)) {
                    return back()->withErrors(['file' => implode(' ', $errors)]);
                }
                $path = $this->mediaService->optimizeImage($file, $request->section);
            } elseif ($request->type === 'video') {
                $errors = $this->mediaService->validateVideo($file);
                if (!empty($errors)) {
                    return back()->withErrors(['file' => implode(' ', $errors)]);
                }
                $path = $this->mediaService->optimizeVideo($file, $request->section);
            } else {
                return back()->withErrors(['file' => 'Type de fichier non supporté.']);
            }

            $request->merge(['value' => $path]);

            // Add metadata
            $metadata = $this->mediaService->getMediaMetadata($path);
            $request->merge(['metadata' => array_merge($request->metadata ?? [], $metadata)]);
        }

        HomepageContent::create($request->all());

        return redirect()->route('admin.homepage-content.index')
            ->with('success', 'Contenu ajouté avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(HomepageContent $homepageContent)
    {
        return Inertia::render('Admin/HomepageContent/Show', [
            'content' => $homepageContent
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(HomepageContent $homepageContent)
    {
        return Inertia::render('Admin/HomepageContent/Edit', [
            'content' => $homepageContent
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, HomepageContent $homepageContent)
    {
        $request->validate([
            'section' => 'required|string|max:255',
            'key' => 'required|string|max:255',
            'value' => 'nullable|string',
            'type' => 'required|in:text,image,video,json',
            'metadata' => 'nullable|array',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        // Handle file uploads
        if ($request->hasFile('file')) {
            $file = $request->file('file');

            // Validate file based on type
            if ($request->type === 'image') {
                $errors = $this->mediaService->validateImage($file);
                if (!empty($errors)) {
                    return back()->withErrors(['file' => implode(' ', $errors)]);
                }
            } elseif ($request->type === 'video') {
                $errors = $this->mediaService->validateVideo($file);
                if (!empty($errors)) {
                    return back()->withErrors(['file' => implode(' ', $errors)]);
                }
            }

            // Delete old file if exists
            if ($homepageContent->type === 'image' || $homepageContent->type === 'video') {
                $this->mediaService->deleteMedia($homepageContent->value);
            }

            // Upload and optimize new file
            if ($request->type === 'image') {
                $path = $this->mediaService->optimizeImage($file, $request->section);
            } else {
                $path = $this->mediaService->optimizeVideo($file, $request->section);
            }

            $request->merge(['value' => $path]);

            // Add metadata
            $metadata = $this->mediaService->getMediaMetadata($path);
            $request->merge(['metadata' => array_merge($request->metadata ?? [], $metadata)]);
        }

        $homepageContent->update($request->all());

        return redirect()->route('admin.homepage-content.index')
            ->with('success', 'Contenu mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(HomepageContent $homepageContent)
    {
        // Delete associated file if exists
        if (($homepageContent->type === 'image' || $homepageContent->type === 'video') && $homepageContent->value) {
            $this->mediaService->deleteMedia($homepageContent->value);
        }

        $homepageContent->delete();

        return redirect()->route('admin.homepage-content.index')
            ->with('success', 'Contenu supprimé avec succès.');
    }

    /**
     * Show media management page
     */
    public function media()
    {
        $contents = HomepageContent::where('type', 'image')
            ->orWhere('type', 'video')
            ->orderBy('section')
            ->orderBy('sort_order')
            ->get()
            ->groupBy('section');

        return Inertia::render('Admin/HomepageContent/Media', [
            'contents' => $contents
        ]);
    }

    /**
     * Upload file for homepage content
     */
    public function uploadFile(Request $request)
    {
        $request->validate([
            'file' => 'required|file|max:10240', // 10MB max
            'section' => 'required|string'
        ]);

        $file = $request->file('file');
        $path = $file->store('homepage/' . $request->section, 'public');

        return response()->json([
            'path' => $path,
            'url' => Storage::url($path),
            'name' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'type' => $file->getMimeType()
        ]);
    }
}
