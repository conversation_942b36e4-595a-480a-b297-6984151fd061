<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HomepageContent;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Storage;

class HomepageContentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $contents = HomepageContent::orderBy('section')
            ->orderBy('sort_order')
            ->get()
            ->groupBy('section');

        return Inertia::render('Admin/HomepageContent/Index', [
            'contents' => $contents
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/HomepageContent/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'section' => 'required|string|max:255',
            'key' => 'required|string|max:255',
            'value' => 'nullable|string',
            'type' => 'required|in:text,image,video,json',
            'metadata' => 'nullable|array',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        // Handle file uploads
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $path = $file->store('homepage/' . $request->section, 'public');
            $request->merge(['value' => $path]);
        }

        HomepageContent::create($request->all());

        return redirect()->route('admin.homepage-content.index')
            ->with('success', 'Contenu ajouté avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(HomepageContent $homepageContent)
    {
        return Inertia::render('Admin/HomepageContent/Show', [
            'content' => $homepageContent
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(HomepageContent $homepageContent)
    {
        return Inertia::render('Admin/HomepageContent/Edit', [
            'content' => $homepageContent
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, HomepageContent $homepageContent)
    {
        $request->validate([
            'section' => 'required|string|max:255',
            'key' => 'required|string|max:255',
            'value' => 'nullable|string',
            'type' => 'required|in:text,image,video,json',
            'metadata' => 'nullable|array',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        // Handle file uploads
        if ($request->hasFile('file')) {
            // Delete old file if exists
            if ($homepageContent->type === 'image' || $homepageContent->type === 'video') {
                Storage::disk('public')->delete($homepageContent->value);
            }

            $file = $request->file('file');
            $path = $file->store('homepage/' . $request->section, 'public');
            $request->merge(['value' => $path]);
        }

        $homepageContent->update($request->all());

        return redirect()->route('admin.homepage-content.index')
            ->with('success', 'Contenu mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(HomepageContent $homepageContent)
    {
        // Delete associated file if exists
        if (($homepageContent->type === 'image' || $homepageContent->type === 'video') && $homepageContent->value) {
            Storage::disk('public')->delete($homepageContent->value);
        }

        $homepageContent->delete();

        return redirect()->route('admin.homepage-content.index')
            ->with('success', 'Contenu supprimé avec succès.');
    }

    /**
     * Upload file for homepage content
     */
    public function uploadFile(Request $request)
    {
        $request->validate([
            'file' => 'required|file|max:10240', // 10MB max
            'section' => 'required|string'
        ]);

        $file = $request->file('file');
        $path = $file->store('homepage/' . $request->section, 'public');

        return response()->json([
            'path' => $path,
            'url' => Storage::url($path),
            'name' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'type' => $file->getMimeType()
        ]);
    }
}
