<template>
  <Head :title="module.title" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Module: {{ module.title }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Liens de navigation -->
            <div class="mb-6">
              <Link :href="route('admin.modules.index', { course_id: course.id })" class="text-indigo-600 hover:text-indigo-900">
                &larr; Retour aux modules
              </Link>
            </div>

            <!-- Informations sur le module -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 class="text-lg font-semibold mb-2">Informations sur le module</h3>
                  <p><span class="font-medium">Cours:</span> {{ course.title }}</p>
                  <p><span class="font-medium">Description:</span> {{ module.description || 'Aucune description' }}</p>
                  <p><span class="font-medium">Ordre:</span> {{ module.order }}</p>
                  <p>
                    <span class="font-medium">Statut:</span>
                    <span :class="module.is_published ? 'text-green-600' : 'text-red-600'">
                      {{ module.is_published ? 'Publié' : 'Non publié' }}
                    </span>
                  </p>
                  <p v-if="module.publish_date">
                    <span class="font-medium">Date de publication:</span> {{ formatDate(module.publish_date) }}
                  </p>
                </div>
                <div class="flex justify-end items-start">
                  <div class="space-x-2">
                    <Link :href="route('admin.modules.edit', module.id)" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                      Modifier
                    </Link>
                    <button @click="confirmDelete" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                      Supprimer
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Matériels du module -->
            <div class="mb-6">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Matériels pédagogiques</h3>
                <Link :href="route('admin.course-materials.create', { module_id: module.id })" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                  Ajouter un matériel
                </Link>
              </div>
              <div v-if="materials.length > 0">
                <div class="overflow-x-auto">
                  <table class="min-w-full bg-white border border-gray-200">
                    <thead>
                      <tr>
                        <th class="py-2 px-4 border-b text-left">Titre</th>
                        <th class="py-2 px-4 border-b text-left">Type</th>
                        <th class="py-2 px-4 border-b text-left">Ordre</th>
                        <th class="py-2 px-4 border-b text-left">Statut</th>
                        <th class="py-2 px-4 border-b text-center">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="material in materials" :key="material.id" class="hover:bg-gray-50">
                        <td class="py-2 px-4 border-b">{{ material.title }}</td>
                        <td class="py-2 px-4 border-b">{{ formatMaterialType(material.type) }}</td>
                        <td class="py-2 px-4 border-b">{{ material.order }}</td>
                        <td class="py-2 px-4 border-b">
                          <span :class="material.active ? 'text-green-600' : 'text-red-600'">
                            {{ material.active ? 'Actif' : 'Inactif' }}
                          </span>
                        </td>
                        <td class="py-2 px-4 border-b text-center">
                          <div class="flex justify-center space-x-2">
                            <Link :href="route('admin.course-materials.show', material.id)" class="text-blue-600 hover:text-blue-900">
                              Voir
                            </Link>
                            <Link :href="route('admin.course-materials.edit', material.id)" class="text-indigo-600 hover:text-indigo-900">
                              Modifier
                            </Link>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <div v-else class="text-center py-8">
                <p class="text-gray-500">Aucun matériel n'a été ajouté à ce module.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de suppression -->
    <Modal :show="showDeleteModal" @close="closeDeleteModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">
          Confirmer la suppression
        </h2>
        <p class="mt-1 text-sm text-gray-600">
          Êtes-vous sûr de vouloir supprimer ce module ? Cette action est irréversible et supprimera également tous les matériels associés.
        </p>
        <div class="mt-6 flex justify-end">
          <SecondaryButton @click="closeDeleteModal" class="mr-2">
            Annuler
          </SecondaryButton>
          <DangerButton @click="deleteModule" :class="{ 'opacity-25': deleteForm.processing }" :disabled="deleteForm.processing">
            Supprimer
          </DangerButton>
        </div>
      </div>
    </Modal>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';

// Props
const props = defineProps({
  module: Object,
  course: Object,
  materials: Array,
});

// État pour la modal de suppression
const showDeleteModal = ref(false);

// Formulaire pour la suppression
const deleteForm = useForm({});

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return '';
  const options = { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const formatMaterialType = (type) => {
  const types = {
    'text': 'Texte',
    'pdf': 'Document PDF',
    'video': 'Vidéo',
    'audio': 'Audio (podcast)',
    'image': 'Image',
    'archive': 'Archive',
    'gallery': 'Galerie d\'images',
    'embed_video': 'Vidéo externe'
  };
  return types[type] || type;
};

const confirmDelete = () => {
  showDeleteModal.value = true;
};

const closeDeleteModal = () => {
  showDeleteModal.value = false;
};

const deleteModule = () => {
  deleteForm.delete(route('admin.modules.destroy', props.module.id), {
    onSuccess: () => {
      closeDeleteModal();
    },
  });
};
</script>
