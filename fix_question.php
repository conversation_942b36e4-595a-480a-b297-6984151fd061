<?php

require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\ExamQuestion;

// Récupérer la question 24 (question de type single_choice)
$question = ExamQuestion::find(24);

if ($question) {
    echo "Question ID: " . $question->id . "\n";
    echo "Question Text: " . $question->question_text . "\n";
    echo "Question Type: " . $question->question_type . "\n";
    echo "Options: " . json_encode($question->options) . "\n";
    echo "Correct Options: " . json_encode($question->correct_options) . "\n";

    // Convertir les options en format objet avec clés A, B, C, D
    $options = $question->options;
    $newOptions = [];

    if (is_array($options)) {
        foreach ($options as $index => $option) {
            $key = chr(65 + $index); // A, B, C, D, ...
            $newOptions[$key] = $option;
        }
    }

    // Mettre à jour les options
    $question->options = $newOptions;

    // Mettre à jour les options correctes
    // Si correct_options est [0], le convertir en ["A"]
    $correctOptions = $question->correct_options;
    $newCorrectOptions = [];

    if (is_array($correctOptions)) {
        foreach ($correctOptions as $index) {
            if (is_numeric($index)) {
                $newCorrectOptions[] = chr(65 + $index); // A, B, C, D, ...
            } else {
                $newCorrectOptions[] = $index;
            }
        }
    }

    $question->correct_options = $newCorrectOptions;
    $question->save();

    echo "Question mise à jour avec options = " . json_encode($newOptions) . "\n";
    echo "Question mise à jour avec correct_options = " . json_encode($newCorrectOptions) . "\n";

    // Vérifier la mise à jour
    $question = ExamQuestion::find(24);
    echo "Après mise à jour - Options: " . json_encode($question->options) . "\n";
    echo "Après mise à jour - Correct Options: " . json_encode($question->correct_options) . "\n";
} else {
    echo "Question non trouvée\n";
}

// Récupérer toutes les questions de l'examen 16
$questions = ExamQuestion::where('exam_id', 16)->get();

echo "\nToutes les questions de l'examen 16:\n";
foreach ($questions as $q) {
    echo "Question ID: " . $q->id . "\n";
    echo "Question Text: " . $q->question_text . "\n";
    echo "Question Type: " . $q->question_type . "\n";
    echo "Options: " . json_encode($q->options) . "\n";
    echo "Correct Options: " . json_encode($q->correct_options) . "\n";
    echo "-------------------\n";
}
