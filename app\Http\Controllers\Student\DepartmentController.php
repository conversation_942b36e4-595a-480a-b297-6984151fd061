<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Models\TrainingSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DepartmentController extends Controller
{
    /**
     * Affiche les sessions d'un département spécifique
     */
    public function show(string $department)
    {
        // Valider le département
        $validDepartments = TrainingSession::DEPARTMENTS;
        if (!in_array($department, $validDepartments)) {
            abort(404, 'Département non trouvé');
        }

        // Récupérer l'utilisateur connecté (apprenant)
        $student = Auth::user();

        // Récupérer les niveaux complétés par département
        $completedLevels = $student->getCompletedLevelsByDepartment();

        // Récupérer les sessions pour ce département organisées par niveau
        $sessionsByLevel = $this->getSessionsByLevel($student, $department, $completedLevels);

        // Calculer les statistiques du département
        $stats = $this->getDepartmentStats($student, $department, $sessionsByLevel);

        return Inertia::render('Student/Departments/Show', [
            'department' => $department,
            'sessionsByLevel' => $sessionsByLevel,
            'completedLevels' => $completedLevels[$department] ?? [],
            'stats' => $stats,
            'departmentInfo' => $this->getDepartmentInfo($department),
        ]);
    }

    /**
     * Récupère les sessions d'un département organisées par niveau
     */
    private function getSessionsByLevel($student, $department, $completedLevels)
    {
        $levels = ['Niveau 1', 'Niveau 2', 'Niveau 3', 'Niveau 4', 'Niveau 5', 'Niveau 6', 'Niveau 7'];
        $sessionsByLevel = [];

        foreach ($levels as $level) {
            // Récupérer les sessions pour ce niveau et département
            $sessions = TrainingSession::where('department', $department)
                ->where('level', $level)
                ->where('active', true)
                ->withCount('enrollments')
                ->orderBy('start_date')
                ->get();

            if ($sessions->isNotEmpty()) {
                $sessionsByLevel[$level] = [];
                
                foreach ($sessions as $session) {
                    $sessionData = $this->evaluateSessionState($student, $session, $department, $level, $completedLevels);
                    $sessionsByLevel[$level][] = $sessionData;
                }
            }
        }

        return $sessionsByLevel;
    }

    /**
     * Évalue l'état d'une session pour un étudiant
     */
    private function evaluateSessionState($student, $session, $department, $level, $completedLevels)
    {
        // Vérifier si l'apprenant est déjà inscrit
        $enrollment = $student->enrollments()
            ->where('training_session_id', $session->id)
            ->first();

        if ($enrollment) {
            switch ($enrollment->status) {
                case 'completed':
                    return [
                        'session' => $session,
                        'state' => 'completed',
                        'reason' => 'Formation terminée avec succès',
                        'canEnroll' => false
                    ];
                case 'approved':
                    return [
                        'session' => $session,
                        'state' => 'enrolled',
                        'reason' => 'Inscription approuvée',
                        'canEnroll' => false
                    ];
                case 'pending':
                    return [
                        'session' => $session,
                        'state' => 'pending',
                        'reason' => 'Inscription en attente d\'approbation',
                        'canEnroll' => false
                    ];
                case 'rejected':
                    return [
                        'session' => $session,
                        'state' => 'rejected',
                        'reason' => 'Inscription refusée',
                        'canEnroll' => false
                    ];
                default:
                    // Continuer l'évaluation pour les autres statuts
                    break;
            }
        }

        // Vérifier les prérequis de niveau
        if (!$student->canEnrollInLevel($department, $level)) {
            $levelNumber = (int) str_replace('Niveau ', '', $level);
            $previousLevel = "Niveau " . ($levelNumber - 1);

            return [
                'session' => $session,
                'state' => 'locked',
                'reason' => "Vous devez d'abord terminer le $previousLevel en $department",
                'canEnroll' => false
            ];
        }

        // Vérifier si la session est complète
        if ($session->max_participants && $session->enrollments_count >= $session->max_participants) {
            return [
                'session' => $session,
                'state' => 'full',
                'reason' => 'Session complète',
                'canEnroll' => false
            ];
        }

        // Vérifier si la session a déjà commencé
        if (now() > $session->start_date) {
            return [
                'session' => $session,
                'state' => 'started',
                'reason' => 'Session déjà commencée',
                'canEnroll' => false
            ];
        }

        // Session disponible pour inscription
        return [
            'session' => $session,
            'state' => 'available',
            'reason' => 'Inscription possible',
            'canEnroll' => true
        ];
    }

    /**
     * Calcule les statistiques d'un département
     */
    private function getDepartmentStats($student, $department, $sessionsByLevel)
    {
        $totalSessions = 0;
        $availableSessions = 0;
        $enrolledSessions = 0;
        $completedSessions = 0;

        foreach ($sessionsByLevel as $level => $sessions) {
            foreach ($sessions as $sessionData) {
                $totalSessions++;
                
                switch ($sessionData['state']) {
                    case 'available':
                        $availableSessions++;
                        break;
                    case 'enrolled':
                    case 'pending':
                        $enrolledSessions++;
                        break;
                    case 'completed':
                        $completedSessions++;
                        break;
                }
            }
        }

        return [
            'total_sessions' => $totalSessions,
            'available_sessions' => $availableSessions,
            'enrolled_sessions' => $enrolledSessions,
            'completed_sessions' => $completedSessions,
        ];
    }

    /**
     * Retourne les informations d'un département
     */
    private function getDepartmentInfo($department)
    {
        $departmentInfo = [
            'Secourisme' => [
                'title' => 'Secourisme',
                'description' => 'Formations aux gestes de premiers secours et techniques de sauvetage',
                'icon' => 'heart',
                'color' => 'red',
                'gradient' => 'from-red-500 to-red-700'
            ],
            'Langue' => [
                'title' => 'Langue',
                'description' => 'Formation linguistique et communication internationale',
                'icon' => 'language',
                'color' => 'blue',
                'gradient' => 'from-blue-500 to-blue-700'
            ],
            'Formation à la carte' => [
                'title' => 'Formation à la carte',
                'description' => 'Formations personnalisées selon vos besoins spécifiques',
                'icon' => 'academic-cap',
                'color' => 'green',
                'gradient' => 'from-green-500 to-green-700'
            ]
        ];

        return $departmentInfo[$department] ?? null;
    }
}
