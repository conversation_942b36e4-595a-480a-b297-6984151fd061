<template>
  <Head :title="course.title" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Détails du cours
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link :href="route('admin.courses.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à la liste
              </Link>
            </div>

            <!-- Informations sur le cours -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 class="text-lg font-semibold mb-2">Informations sur le cours</h3>
                  <p><span class="font-medium">Titre:</span> {{ course.title }}</p>
                  <p><span class="font-medium">Session de formation:</span> {{ course.training_session.title }}</p>
                  <p><span class="font-medium">Domaine:</span> {{ course.training_session.training_domain.name }}</p>
                  <p><span class="font-medium">Formateur:</span> {{ course.training_session.trainer.name }}</p>
                  <p><span class="font-medium">Ordre d'affichage:</span> {{ course.order }}</p>
                  <p>
                    <span class="font-medium">Statut:</span>
                    <span
                      :class="[
                        'px-2 inline-flex text-xs leading-5 font-semibold rounded-full ml-2',
                        course.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      ]"
                    >
                      {{ course.active ? 'Actif' : 'Inactif' }}
                    </span>
                  </p>
                </div>
                <div>
                  <h3 class="text-lg font-semibold mb-2">Description</h3>
                  <p class="text-gray-700">{{ course.description || 'Aucune description' }}</p>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">Gestion du cours</h3>
              <div class="flex space-x-2">
                <Link
                  :href="route('admin.modules.index', { course_id: course.id })"
                  class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                >
                  Gérer les modules
                </Link>
                <Link
                  :href="route('admin.course-materials.create', { course_id: course.id })"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Ajouter un matériel
                </Link>
                <Link
                  :href="route('admin.courses.edit', course.id)"
                  class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                >
                  Modifier le cours
                </Link>
              </div>
            </div>

            <!-- Matériels pédagogiques -->
            <div class="mb-4">
              <h3 class="text-lg font-semibold">Matériels pédagogiques</h3>
            </div>

            <!-- Liste des matériels pédagogiques -->
            <div class="overflow-x-auto">
              <table class="min-w-full bg-white border border-gray-200">
                <thead>
                  <tr>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                      Titre
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="material in course.materials" :key="material.id">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">{{ material.title }}</div>
                      <div class="text-sm text-gray-500">Ordre: {{ material.order }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        :class="[
                          'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                          getTypeClass(material.type)
                        ]"
                      >
                        {{ formatMaterialType(material.type) }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        :class="[
                          'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                          material.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        ]"
                      >
                        {{ material.active ? 'Actif' : 'Inactif' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div class="flex space-x-2">
                        <Link :href="route('admin.course-materials.show', material.id)" class="text-blue-600 hover:text-blue-900">
                          Voir
                        </Link>
                        <Link :href="route('admin.course-materials.edit', material.id)" class="text-indigo-600 hover:text-indigo-900">
                          Modifier
                        </Link>
                        <a v-if="material.file_path && material.allow_download" :href="`/admin/course-materials/${material.id}/download`" class="text-green-600 hover:text-green-900">
                          Télécharger
                        </a>
                      </div>
                    </td>
                  </tr>
                  <tr v-if="course.materials.length === 0">
                    <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                      Aucun matériel pédagogique trouvé
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  course: Object,
});

// Méthodes
const formatMaterialType = (type) => {
  const types = {
    'text': 'Texte',
    'pdf': 'Document PDF',
    'video': 'Vidéo',
    'audio': 'Audio',
    'image': 'Image',
    'archive': 'Archive (ZIP, RAR, etc.)'
  };
  return types[type] || type;
};

const getTypeClass = (type) => {
  const classes = {
    'text': 'bg-gray-100 text-gray-800',
    'pdf': 'bg-red-100 text-red-800',
    'video': 'bg-blue-100 text-blue-800',
    'audio': 'bg-purple-100 text-purple-800',
    'image': 'bg-yellow-100 text-yellow-800',
    'archive': 'bg-green-100 text-green-800'
  };
  return classes[type] || 'bg-gray-100 text-gray-800';
};
</script>
