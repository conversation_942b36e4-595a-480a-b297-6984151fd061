<?php

require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\ExamQuestion;

// Récupérer toutes les questions de type single_choice
$questions = ExamQuestion::where('question_type', 'single_choice')->get();

echo "Nombre de questions de type single_choice trouvées : " . count($questions) . "\n";

foreach ($questions as $question) {
    echo "Question ID: " . $question->id . "\n";
    echo "Question Text: " . $question->question_text . "\n";
    echo "Options avant: " . json_encode($question->options) . "\n";
    echo "Correct Options avant: " . json_encode($question->correct_options) . "\n";

    // Convertir les options en format objet avec clés A, B, C, D
    $options = $question->options;
    $newOptions = [];

    if (is_array($options)) {
        foreach ($options as $index => $option) {
            if (is_numeric($index)) {
                $key = chr(65 + (int)$index); // A, B, C, D, ...
                $newOptions[$key] = $option;
            } else {
                $newOptions[$index] = $option;
            }
        }
    } else if (is_string($options)) {
        try {
            $parsedOptions = json_decode($options, true);
            if (is_array($parsedOptions)) {
                foreach ($parsedOptions as $index => $option) {
                    if (is_numeric($index)) {
                        $key = chr(65 + (int)$index); // A, B, C, D, ...
                        $newOptions[$key] = $option;
                    } else {
                        $newOptions[$index] = $option;
                    }
                }
            }
        } catch (\Exception $e) {
            echo "Erreur lors du parsing des options: " . $e->getMessage() . "\n";
        }
    }

    // Mettre à jour les options correctes
    $correctOptions = $question->correct_options;
    $newCorrectOptions = [];

    if (is_array($correctOptions)) {
        foreach ($correctOptions as $index) {
            if (is_numeric($index)) {
                $newCorrectOptions[] = chr(65 + (int)$index); // A, B, C, D, ...
            } else {
                $newCorrectOptions[] = $index;
            }
        }
    } else if (is_string($correctOptions)) {
        try {
            $parsedCorrectOptions = json_decode($correctOptions, true);
            if (is_array($parsedCorrectOptions)) {
                foreach ($parsedCorrectOptions as $index) {
                    if (is_numeric($index)) {
                        $newCorrectOptions[] = chr(65 + (int)$index); // A, B, C, D, ...
                    } else {
                        $newCorrectOptions[] = $index;
                    }
                }
            }
        } catch (\Exception $e) {
            echo "Erreur lors du parsing des options correctes: " . $e->getMessage() . "\n";
        }
    }

    // Si aucune option correcte n'est définie, définir la première option comme correcte
    if (empty($newCorrectOptions) && !empty($newOptions)) {
        $newCorrectOptions[] = 'A';
    }

    // Mettre à jour la question
    $question->options = $newOptions;
    $question->correct_options = $newCorrectOptions;
    $question->save();

    echo "Options après: " . json_encode($question->options) . "\n";
    echo "Correct Options après: " . json_encode($question->correct_options) . "\n";
    echo "-------------------\n";
}

echo "Mise à jour terminée.\n";
