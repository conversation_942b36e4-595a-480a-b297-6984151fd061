<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Enrollment;
use App\Models\TrainingSession;
use App\Models\TrainingDomain;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class EnrollmentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Initialiser la requête
        $query = Enrollment::with(['user', 'trainingSession', 'trainingSession.trainingDomain']);

        // Appliquer la recherche si elle existe
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->whereHas('user', function($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                  ->orWhere('email', 'like', $searchTerm);
            });
        }

        // Filtrer par session de formation
        if ($request->has('training_session_id') && !empty($request->training_session_id)) {
            $query->where('training_session_id', $request->training_session_id);
        }

        // Filtrer par domaine de formation
        if ($request->has('domain_id') && !empty($request->domain_id)) {
            $query->whereHas('trainingSession', function($q) use ($request) {
                $q->where('training_domain_id', $request->domain_id);
            });
        }

        // Filtrer par statut
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Filtrer par date d'inscription (début)
        if ($request->has('start_date') && !empty($request->start_date)) {
            $query->whereDate('enrollment_date', '>=', $request->start_date);
        }

        // Filtrer par date d'inscription (fin)
        if ($request->has('end_date') && !empty($request->end_date)) {
            $query->whereDate('enrollment_date', '<=', $request->end_date);
        }

        // Récupérer les inscriptions avec pagination
        $enrollments = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Récupérer toutes les sessions de formation pour le filtre
        $trainingSessions = TrainingSession::orderBy('title')->get();

        // Récupérer tous les domaines de formation pour le filtre
        $domains = TrainingDomain::orderBy('name')->get();

        // Retourner la vue avec les inscriptions et les filtres
        return Inertia::render('Admin/Enrollments/Index', [
            'enrollments' => $enrollments,
            'trainingSessions' => $trainingSessions,
            'domains' => $domains,
            'filters' => [
                'search' => $request->search ?? '',
                'training_session_id' => $request->training_session_id ?? '',
                'domain_id' => $request->domain_id ?? '',
                'status' => $request->status ?? '',
                'start_date' => $request->start_date ?? '',
                'end_date' => $request->end_date ?? '',
            ]
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Récupérer les sessions de formation actives
        $trainingSessions = TrainingSession::with('trainingDomain')
            ->where('active', true)
            ->orderBy('title')
            ->get();

        // Récupérer les apprenants actifs
        $students = User::where('role', 'student')
            ->where('active', true)
            ->orderBy('name')
            ->get();

        // Retourner la vue pour créer une nouvelle inscription
        return Inertia::render('Admin/Enrollments/Create', [
            'trainingSessions' => $trainingSessions,
            'students' => $students
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'training_session_id' => 'required|exists:training_sessions,id',
            'status' => 'required|in:pending,approved,rejected,completed',
            'notes' => 'nullable|string',
            'enrollment_date' => 'nullable|date',
        ]);

        // Ajouter la date d'inscription si elle n'est pas fournie
        if (!isset($validated['enrollment_date'])) {
            $validated['enrollment_date'] = now()->format('Y-m-d');
        }

        // Vérifier si l'inscription existe déjà
        $existingEnrollment = Enrollment::where('user_id', $validated['user_id'])
            ->where('training_session_id', $validated['training_session_id'])
            ->first();

        if ($existingEnrollment) {
            return redirect()->back()->withErrors([
                'user_id' => 'Cet apprenant est déjà inscrit à cette session de formation.'
            ]);
        }

        // Vérifier si la session a un nombre maximum de participants et si elle est pleine
        $trainingSession = TrainingSession::findOrFail($validated['training_session_id']);
        if ($trainingSession->max_participants &&
            $trainingSession->enrollments()->count() >= $trainingSession->max_participants) {
            return redirect()->back()->withErrors([
                'training_session_id' => 'Cette session de formation est complète.'
            ]);
        }

        // Créer l'inscription
        Enrollment::create($validated);

        // Rediriger vers la liste des inscriptions avec un message de succès
        return redirect()->route('admin.enrollments.index')
            ->with('success', 'Inscription créée avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Récupérer l'inscription avec ses relations
        $enrollment = Enrollment::with([
            'user',
            'trainingSession',
            'trainingSession.trainingDomain',
            'trainingSession.trainer',
            'examResults',
            'examResults.exam'
        ])->findOrFail($id);

        // Retourner la vue avec l'inscription
        return Inertia::render('Admin/Enrollments/Show', [
            'enrollment' => $enrollment
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Récupérer l'inscription
        $enrollment = Enrollment::with(['user', 'trainingSession'])->findOrFail($id);

        // Récupérer les sessions de formation
        $trainingSessions = TrainingSession::with('trainingDomain')
            ->orderBy('title')
            ->get();

        // Récupérer les apprenants
        $students = User::where('role', 'student')
            ->orderBy('name')
            ->get();

        // Retourner la vue pour éditer l'inscription
        return Inertia::render('Admin/Enrollments/Edit', [
            'enrollment' => $enrollment,
            'trainingSessions' => $trainingSessions,
            'students' => $students
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Récupérer l'inscription
        $enrollment = Enrollment::findOrFail($id);

        // Valider les données du formulaire
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'training_session_id' => 'required|exists:training_sessions,id',
            'status' => 'required|in:pending,approved,rejected,completed',
            'notes' => 'nullable|string',
            'enrollment_date' => 'nullable|date',
        ]);

        // Ajouter la date d'inscription si elle n'est pas fournie
        if (!isset($validated['enrollment_date'])) {
            // Si l'inscription existe déjà, conserver sa date d'inscription
            if ($enrollment->enrollment_date) {
                $validated['enrollment_date'] = $enrollment->enrollment_date;
            } else {
                $validated['enrollment_date'] = now()->format('Y-m-d');
            }
        }

        // Vérifier si l'inscription existe déjà (sauf pour l'inscription en cours)
        $existingEnrollment = Enrollment::where('user_id', $validated['user_id'])
            ->where('training_session_id', $validated['training_session_id'])
            ->where('id', '!=', $id)
            ->first();

        if ($existingEnrollment) {
            return redirect()->back()->withErrors([
                'user_id' => 'Cet apprenant est déjà inscrit à cette session de formation.'
            ]);
        }

        // Vérifier si la session a un nombre maximum de participants et si elle est pleine
        // (seulement si on change de session)
        if ($enrollment->training_session_id != $validated['training_session_id']) {
            $trainingSession = TrainingSession::findOrFail($validated['training_session_id']);
            if ($trainingSession->max_participants &&
                $trainingSession->enrollments()->count() >= $trainingSession->max_participants) {
                return redirect()->back()->withErrors([
                    'training_session_id' => 'Cette session de formation est complète.'
                ]);
            }
        }

        // Mettre à jour l'inscription
        $enrollment->update($validated);

        // Rediriger vers la liste des inscriptions avec un message de succès
        return redirect()->route('admin.enrollments.index')
            ->with('success', 'Inscription mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Récupérer l'inscription
        $enrollment = Enrollment::findOrFail($id);

        // Vérifier s'il y a des résultats d'examen associés
        if ($enrollment->examResults()->count() > 0) {
            return redirect()->route('admin.enrollments.index')
                ->with('error', 'Impossible de supprimer cette inscription car elle contient des résultats d\'examen.');
        }

        // Supprimer l'inscription
        $enrollment->delete();

        // Rediriger vers la liste des inscriptions avec un message de succès
        return redirect()->route('admin.enrollments.index')
            ->with('success', 'Inscription supprimée avec succès.');
    }

    /**
     * Mettre à jour les informations de paiement d'une inscription
     */
    public function updatePayment(Request $request, string $id)
    {
        // Récupérer l'inscription
        $enrollment = Enrollment::findOrFail($id);
        $oldPaymentStatus = $enrollment->payment_status;

        // Valider les données du formulaire
        $validated = $request->validate([
            'payment_amount' => 'nullable|numeric|min:0',
            'payment_status' => 'required|in:unpaid,pending,partial,paid,refunded,cancelled',
            'payment_method' => 'nullable|string|max:255',
            'payment_date' => 'nullable|date',
            'payment_due_date' => 'nullable|date',
            'payment_notes' => 'nullable|string',
        ]);

        // Mettre à jour les informations de paiement
        $enrollment->update($validated);

        // Si le statut de paiement a changé vers "paid", envoyer un reçu de paiement
        if ($oldPaymentStatus !== 'paid' && $validated['payment_status'] === 'paid') {
            try {
                $emailService = app(\App\Services\PaymentEmailService::class);
                $emailService->sendPaymentReceiptToAdmin($enrollment);
            } catch (\Exception $e) {
                \Log::error('Failed to send payment receipt email: ' . $e->getMessage());
            }
        }

        // Rediriger vers la page de détail de l'inscription avec un message de succès
        return redirect()->route('admin.enrollments.show', $enrollment->id)
            ->with('success', 'Informations de paiement mises à jour avec succès.');
    }
}
