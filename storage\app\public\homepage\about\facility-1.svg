<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="facility-bg" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#2A69E1;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#1E4FBF;stop-opacity:1" />
        </linearGradient>
        <linearGradient id="floor-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
            <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
        </linearGradient>
    </defs>
    
    <!-- Background -->
    <rect width="100%" height="100%" fill="url(#facility-bg)"/>
    
    <!-- Floor -->
    <rect x="0" y="600" width="1200" height="200" fill="url(#floor-gradient)"/>
    
    <!-- Training tables/stations -->
    <g transform="translate(200, 450)">
        <!-- Table 1 -->
        <rect x="0" y="0" width="120" height="80" fill="white" opacity="0.9" rx="5"/>
        <rect x="10" y="10" width="100" height="60" fill="#2A69E1" opacity="0.3" rx="3"/>
        
        <!-- Mannequin representation -->
        <ellipse cx="60" cy="40" rx="25" ry="15" fill="white" opacity="0.8"/>
        <circle cx="60" cy="30" r="8" fill="#2A69E1" opacity="0.6"/>
    </g>
    
    <g transform="translate(400, 450)">
        <!-- Table 2 -->
        <rect x="0" y="0" width="120" height="80" fill="white" opacity="0.9" rx="5"/>
        <rect x="10" y="10" width="100" height="60" fill="#2A69E1" opacity="0.3" rx="3"/>
        
        <!-- Mannequin representation -->
        <ellipse cx="60" cy="40" rx="25" ry="15" fill="white" opacity="0.8"/>
        <circle cx="60" cy="30" r="8" fill="#2A69E1" opacity="0.6"/>
    </g>
    
    <g transform="translate(600, 450)">
        <!-- Table 3 -->
        <rect x="0" y="0" width="120" height="80" fill="white" opacity="0.9" rx="5"/>
        <rect x="10" y="10" width="100" height="60" fill="#2A69E1" opacity="0.3" rx="3"/>
        
        <!-- Mannequin representation -->
        <ellipse cx="60" cy="40" rx="25" ry="15" fill="white" opacity="0.8"/>
        <circle cx="60" cy="30" r="8" fill="#2A69E1" opacity="0.6"/>
    </g>
    
    <g transform="translate(800, 450)">
        <!-- Table 4 -->
        <rect x="0" y="0" width="120" height="80" fill="white" opacity="0.9" rx="5"/>
        <rect x="10" y="10" width="100" height="60" fill="#2A69E1" opacity="0.3" rx="3"/>
        
        <!-- Mannequin representation -->
        <ellipse cx="60" cy="40" rx="25" ry="15" fill="white" opacity="0.8"/>
        <circle cx="60" cy="30" r="8" fill="#2A69E1" opacity="0.6"/>
    </g>
    
    <!-- Instructor area -->
    <g transform="translate(500, 300)">
        <!-- Whiteboard/Screen -->
        <rect x="0" y="0" width="200" height="120" fill="white" opacity="0.9" rx="5"/>
        <rect x="10" y="10" width="180" height="100" fill="#2A69E1" opacity="0.2" rx="3"/>
        
        <!-- Medical cross on board -->
        <g transform="translate(90, 50)">
            <rect x="15" y="5" width="10" height="30" fill="#2A69E1" rx="1"/>
            <rect x="5" y="15" width="30" height="10" fill="#2A69E1" rx="1"/>
        </g>
        
        <!-- Text lines on board -->
        <rect x="30" y="80" width="60" height="3" fill="#2A69E1" opacity="0.4" rx="1"/>
        <rect x="30" y="90" width="80" height="3" fill="#2A69E1" opacity="0.4" rx="1"/>
        <rect x="30" y="100" width="50" height="3" fill="#2A69E1" opacity="0.4" rx="1"/>
    </g>
    
    <!-- Equipment cabinet -->
    <g transform="translate(100, 350)">
        <rect x="0" y="0" width="80" height="180" fill="white" opacity="0.9" rx="5"/>
        <rect x="10" y="10" width="60" height="160" fill="#2A69E1" opacity="0.2" rx="3"/>
        
        <!-- Shelves -->
        <rect x="15" y="40" width="50" height="2" fill="#2A69E1" opacity="0.5"/>
        <rect x="15" y="80" width="50" height="2" fill="#2A69E1" opacity="0.5"/>
        <rect x="15" y="120" width="50" height="2" fill="#2A69E1" opacity="0.5"/>
        
        <!-- Medical supplies -->
        <circle cx="30" cy="25" r="5" fill="white" opacity="0.8"/>
        <circle cx="50" cy="25" r="5" fill="white" opacity="0.8"/>
        <rect x="25" y="55" width="20" height="15" fill="white" opacity="0.8" rx="2"/>
        <rect x="25" y="95" width="20" height="15" fill="white" opacity="0.8" rx="2"/>
    </g>
    
    <!-- Ceiling lights -->
    <g transform="translate(300, 100)">
        <ellipse cx="0" cy="0" rx="40" ry="20" fill="white" opacity="0.6"/>
        <ellipse cx="0" cy="0" rx="30" ry="15" fill="white" opacity="0.8"/>
    </g>
    
    <g transform="translate(600, 100)">
        <ellipse cx="0" cy="0" rx="40" ry="20" fill="white" opacity="0.6"/>
        <ellipse cx="0" cy="0" rx="30" ry="15" fill="white" opacity="0.8"/>
    </g>
    
    <g transform="translate(900, 100)">
        <ellipse cx="0" cy="0" rx="40" ry="20" fill="white" opacity="0.6"/>
        <ellipse cx="0" cy="0" rx="30" ry="15" fill="white" opacity="0.8"/>
    </g>
    
    <!-- Title -->
    <text x="600" y="150" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="48" font-weight="bold">
        Salle de Formation Principale
    </text>
    
    <!-- Subtitle -->
    <text x="600" y="190" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" opacity="0.9">
        Équipée pour 16 étudiants simultanément
    </text>
    
    <!-- Features -->
    <g transform="translate(50, 650)">
        <rect x="0" y="0" width="250" height="60" fill="white" opacity="0.1" rx="8"/>
        <text x="125" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
            4 Stations de Pratique
        </text>
        <text x="125" y="45" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" opacity="0.8">
            Mannequins haute fidélité
        </text>
    </g>
    
    <g transform="translate(350, 650)">
        <rect x="0" y="0" width="250" height="60" fill="white" opacity="0.1" rx="8"/>
        <text x="125" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
            Équipement Médical
        </text>
        <text x="125" y="45" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" opacity="0.8">
            Matériel professionnel certifié
        </text>
    </g>
    
    <g transform="translate(650, 650)">
        <rect x="0" y="0" width="250" height="60" fill="white" opacity="0.1" rx="8"/>
        <text x="125" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
            Espace Modulable
        </text>
        <text x="125" y="45" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" opacity="0.8">
            Configuration adaptable
        </text>
    </g>
    
    <g transform="translate(950, 650)">
        <rect x="0" y="0" width="200" height="60" fill="white" opacity="0.1" rx="8"/>
        <text x="100" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
            Éclairage LED
        </text>
        <text x="100" y="45" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" opacity="0.8">
            Optimal pour la formation
        </text>
    </g>
    
    <!-- Bottom branding -->
    <text x="600" y="780" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" opacity="0.8">
        PCMET - Installations Modernes et Équipées
    </text>
</svg>
