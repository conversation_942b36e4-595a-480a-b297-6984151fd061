<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('certificates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('enrollment_id');
            $table->string('certificate_number')->unique(); // Numéro unique du certificat
            $table->string('pdf_path')->nullable(); // Chemin vers le fichier PDF du certificat
            $table->string('qr_code')->nullable(); // Code QR pour la vérification
            $table->timestamp('issued_at');
            $table->enum('status', ['issued', 'revoked', 'expired'])->default('issued');
            $table->date('expiry_date')->nullable(); // Date d'expiration si applicable
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('certificates');
    }
};
