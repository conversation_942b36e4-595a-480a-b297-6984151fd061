<?php

namespace App\Http\Controllers\Trainer;

use App\Http\Controllers\Controller;
use App\Models\TrainingSession;
use App\Models\Enrollment;
use App\Models\User;
use App\Models\Attendance;
use App\Models\Course;
use App\Models\Exam;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class TrainingSessionController extends Controller
{
    /**
     * Affiche la liste des sessions de formation du formateur
     */
    public function index(Request $request)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Initialiser la requête
        $query = TrainingSession::where('trainer_id', $trainer->id)
            ->where('active', true)
            ->with(['trainingDomain']);

        // Appliquer la recherche si elle existe
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function($q) use ($searchTerm) {
                $q->where('title', 'like', $searchTerm)
                  ->orWhere('description', 'like', $searchTerm)
                  ->orWhereHas('trainingDomain', function($q) use ($searchTerm) {
                      $q->where('name', 'like', $searchTerm);
                  });
            });
        }

        // Filtrer par statut
        if ($request->has('status') && !empty($request->status)) {
            if ($request->status === 'active') {
                $query->where('start_date', '<=', now())
                      ->where('end_date', '>=', now());
            } elseif ($request->status === 'upcoming') {
                $query->where('start_date', '>', now());
            } elseif ($request->status === 'past') {
                $query->where('end_date', '<', now());
            }
        }

        // Filtrer par domaine
        if ($request->has('domain_id') && !empty($request->domain_id)) {
            $query->where('training_domain_id', $request->domain_id);
        }

        // Récupérer les sessions avec pagination
        $sessions = $query->orderBy('start_date', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Récupérer les domaines pour le filtre
        $domains = \App\Models\TrainingDomain::orderBy('name')->get();

        // Retourner la vue avec les sessions
        return Inertia::render('Trainer/Sessions/Index', [
            'sessions' => $sessions,
            'domains' => $domains,
            'filters' => [
                'search' => $request->search ?? '',
                'status' => $request->status ?? '',
                'domain_id' => $request->domain_id ?? '',
            ]
        ]);
    }

    /**
     * Affiche les détails d'une session de formation
     */
    public function show(string $id)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer la session avec ses relations
        $session = TrainingSession::with(['trainingDomain', 'enrollments.user', 'courses', 'exams'])
            ->where('trainer_id', $trainer->id)
            ->where('active', true)
            ->findOrFail($id);

        // Récupérer les inscriptions approuvées
        $enrollments = $session->enrollments()->where('status', 'approved')->with('user')->get();

        // Récupérer les cours associés à cette session
        $courses = $session->courses()->orderBy('order')->get();

        // Récupérer les examens associés à cette session
        $exams = $session->exams()->orderBy('created_at')->get();

        // Récupérer les présences pour cette session
        $attendances = \App\Models\Attendance::where('training_session_id', $session->id)
            ->get()
            ->groupBy('date')
            ->map(function ($items) {
                return $items->keyBy('user_id');
            });

        // Calculer les statistiques de progression
        $progressStats = [
            'total_students' => $enrollments->count(),
            'course_completion' => $this->calculateCourseCompletion($session->id),
            'exam_participation' => $this->calculateExamParticipation($session->id),
            'exam_success_rate' => $this->calculateExamSuccessRate($session->id),
            'attendance_rate' => $this->calculateAttendanceRate($session->id),
        ];

        // Retourner la vue avec les données
        return Inertia::render('Trainer/Sessions/Show', [
            'session' => $session,
            'enrollments' => $enrollments,
            'courses' => $courses,
            'exams' => $exams,
            'attendances' => $attendances,
            'progressStats' => $progressStats,
        ]);
    }

    /**
     * Enregistre les présences pour une session
     */
    public function storeAttendance(Request $request, string $id)
    {
        // Valider les données
        $validated = $request->validate([
            'date' => 'required|date',
            'attendances' => 'required|array',
            'attendances.*.user_id' => 'required|exists:users,id',
            'attendances.*.present' => 'required|boolean',
            'attendances.*.notes' => 'nullable|string',
        ]);

        // Récupérer la session
        $session = TrainingSession::where('trainer_id', Auth::id())
            ->where('active', true)
            ->findOrFail($id);

        // Enregistrer les présences
        foreach ($validated['attendances'] as $attendance) {
            \App\Models\Attendance::updateOrCreate(
                [
                    'training_session_id' => $session->id,
                    'user_id' => $attendance['user_id'],
                    'date' => $validated['date'],
                ],
                [
                    'present' => $attendance['present'],
                    'notes' => $attendance['notes'] ?? null,
                ]
            );
        }

        return redirect()->back()->with('success', 'Présences enregistrées avec succès.');
    }

    /**
     * Génère un rapport de progression pour une session
     */
    public function generateProgressReport(string $id)
    {
        // Récupérer la session
        $session = TrainingSession::where('trainer_id', Auth::id())
            ->where('active', true)
            ->with(['enrollments.user', 'courses', 'exams'])
            ->findOrFail($id);

        // Récupérer les inscriptions approuvées
        $enrollments = $session->enrollments()->where('status', 'approved')->with('user')->get();

        // Préparer les données du rapport
        $reportData = [];

        foreach ($enrollments as $enrollment) {
            $student = $enrollment->user;

            // Calculer la progression des cours
            $courseProgress = \App\Models\CourseProgress::whereIn('course_id', $session->courses->pluck('id'))
                ->where('user_id', $student->id)
                ->get();

            $totalCourses = $session->courses->count();
            $completedCourses = $courseProgress->where('completed', true)->count();
            $courseCompletionRate = $totalCourses > 0 ? round(($completedCourses / $totalCourses) * 100, 2) : 0;

            // Calculer les résultats d'examen
            $examResults = \App\Models\ExamResult::whereIn('exam_id', $session->exams->pluck('id'))
                ->where('user_id', $student->id)
                ->get();

            $totalExams = $session->exams->count();
            $takenExams = $examResults->count();
            $passedExams = $examResults->where('passed', true)->count();
            $examParticipationRate = $totalExams > 0 ? round(($takenExams / $totalExams) * 100, 2) : 0;
            $examSuccessRate = $takenExams > 0 ? round(($passedExams / $takenExams) * 100, 2) : 0;

            // Calculer le taux de présence
            $attendances = \App\Models\Attendance::where('training_session_id', $session->id)
                ->where('user_id', $student->id)
                ->get();

            $totalDays = $attendances->count();
            $presentDays = $attendances->where('present', true)->count();
            $attendanceRate = $totalDays > 0 ? round(($presentDays / $totalDays) * 100, 2) : 0;

            // Ajouter les données au rapport
            $reportData[] = [
                'student' => $student,
                'course_completion' => [
                    'total' => $totalCourses,
                    'completed' => $completedCourses,
                    'rate' => $courseCompletionRate,
                ],
                'exam_results' => [
                    'total' => $totalExams,
                    'taken' => $takenExams,
                    'passed' => $passedExams,
                    'participation_rate' => $examParticipationRate,
                    'success_rate' => $examSuccessRate,
                ],
                'attendance' => [
                    'total_days' => $totalDays,
                    'present_days' => $presentDays,
                    'rate' => $attendanceRate,
                ],
                'overall_progress' => round(($courseCompletionRate + $examSuccessRate + $attendanceRate) / 3, 2),
            ];
        }

        // Trier par progression globale (décroissant)
        usort($reportData, function ($a, $b) {
            return $b['overall_progress'] <=> $a['overall_progress'];
        });

        return Inertia::render('Trainer/Sessions/ProgressReport', [
            'session' => $session,
            'reportData' => $reportData,
        ]);
    }

    /**
     * Calcule le taux de complétion des cours pour une session
     */
    private function calculateCourseCompletion(int $sessionId)
    {
        $session = TrainingSession::with(['enrollments', 'courses'])->findOrFail($sessionId);
        $enrollments = $session->enrollments()->where('status', 'approved')->get();
        $courses = $session->courses;

        if ($enrollments->isEmpty() || $courses->isEmpty()) {
            return 0;
        }

        $totalPossibleCompletions = $enrollments->count() * $courses->count();
        $actualCompletions = \App\Models\CourseProgress::whereIn('course_id', $courses->pluck('id'))
            ->whereIn('user_id', $enrollments->pluck('user_id'))
            ->where('completed', true)
            ->count();

        return $totalPossibleCompletions > 0 ? round(($actualCompletions / $totalPossibleCompletions) * 100, 2) : 0;
    }

    /**
     * Calcule le taux de participation aux examens pour une session
     */
    private function calculateExamParticipation(int $sessionId)
    {
        $session = TrainingSession::with(['enrollments', 'exams'])->findOrFail($sessionId);
        $enrollments = $session->enrollments()->where('status', 'approved')->get();
        $exams = $session->exams;

        if ($enrollments->isEmpty() || $exams->isEmpty()) {
            return 0;
        }

        $totalPossibleParticipations = $enrollments->count() * $exams->count();
        $actualParticipations = \App\Models\ExamResult::whereIn('exam_id', $exams->pluck('id'))
            ->whereIn('user_id', $enrollments->pluck('user_id'))
            ->count();

        return $totalPossibleParticipations > 0 ? round(($actualParticipations / $totalPossibleParticipations) * 100, 2) : 0;
    }

    /**
     * Calcule le taux de réussite aux examens pour une session
     */
    private function calculateExamSuccessRate(int $sessionId)
    {
        $session = TrainingSession::with('exams')->findOrFail($sessionId);
        $exams = $session->exams;

        if ($exams->isEmpty()) {
            return 0;
        }

        $examResults = \App\Models\ExamResult::whereIn('exam_id', $exams->pluck('id'))->get();

        if ($examResults->isEmpty()) {
            return 0;
        }

        $passedResults = $examResults->where('passed', true)->count();

        return round(($passedResults / $examResults->count()) * 100, 2);
    }

    /**
     * Calcule le taux de présence pour une session
     */
    private function calculateAttendanceRate(int $sessionId)
    {
        $attendances = \App\Models\Attendance::where('training_session_id', $sessionId)->get();

        if ($attendances->isEmpty()) {
            return 0;
        }

        $presentCount = $attendances->where('present', true)->count();

        return round(($presentCount / $attendances->count()) * 100, 2);
    }
}
