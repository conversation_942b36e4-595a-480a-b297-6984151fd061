<?php

namespace App\Http\Controllers\Trainer;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\TrainingSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class CourseController extends Controller
{
    /**
     * Affiche la liste des cours du formateur
     */
    public function index(Request $request)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $trainingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->with('trainingDomain')
            ->get();

        $sessionIds = $trainingSessions->pluck('id')->toArray();

        // Initialiser la requête pour les cours
        $query = Course::whereIn('training_session_id', $sessionIds)
            ->with('trainingSession');

        // Appliquer les filtres
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function($q) use ($searchTerm) {
                $q->where('title', 'like', $searchTerm)
                  ->orWhere('description', 'like', $searchTerm);
            });
        }

        if ($request->has('training_session_id') && !empty($request->training_session_id)) {
            $query->where('training_session_id', $request->training_session_id);
        }

        if ($request->has('active') && $request->active !== '') {
            $query->where('active', $request->active);
        }

        // Ajouter les compteurs
        $query->withCount(['modules', 'materials']);

        // Récupérer les cours avec pagination
        $courses = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Retourner la vue avec les cours et les filtres
        return Inertia::render('Trainer/Courses/Index', [
            'courses' => $courses,
            'trainingSessions' => $trainingSessions,
            'filters' => [
                'search' => $request->search ?? '',
                'training_session_id' => $request->training_session_id ?? '',
                'active' => $request->active ?? '',
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $trainingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->with('trainingDomain')
            ->orderBy('start_date', 'desc')
            ->get();

        // Retourner la vue de création de cours
        return Inertia::render('Trainer/Courses/Create', [
            'trainingSessions' => $trainingSessions,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'training_session_id' => 'required|exists:training_sessions,id',
            'active' => 'boolean',
        ]);

        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Vérifier que la session de formation appartient au formateur
        $trainingSession = TrainingSession::where('trainer_id', $trainer->id)
            ->findOrFail($validated['training_session_id']);

        // Déterminer l'ordre du cours
        $maxOrder = Course::where('training_session_id', $validated['training_session_id'])
            ->max('order') ?? 0;

        // Créer le cours
        $course = new Course();
        $course->title = $validated['title'];
        $course->description = $validated['description'] ?? null;
        $course->training_session_id = $validated['training_session_id'];
        $course->order = $maxOrder + 1;
        $course->active = $validated['active'] ?? true;
        $course->save();

        // Rediriger vers la liste des cours
        return redirect()->route('trainer.courses.index')
            ->with('success', 'Cours créé avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $sessionIds = TrainingSession::where('trainer_id', $trainer->id)
            ->pluck('id')
            ->toArray();

        // Récupérer le cours avec ses relations
        $course = Course::with([
            'trainingSession.trainingDomain',
            'modules' => function ($query) {
                $query->orderBy('order');
            },
            'materials' => function ($query) {
                $query->orderBy('order');
            }
        ])
        ->whereIn('training_session_id', $sessionIds)
        ->findOrFail($id);

        return Inertia::render('Trainer/Courses/Show', [
            'course' => $course
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $sessionIds = TrainingSession::where('trainer_id', $trainer->id)
            ->pluck('id')
            ->toArray();

        // Récupérer le cours
        $course = Course::whereIn('training_session_id', $sessionIds)
            ->findOrFail($id);

        // Récupérer toutes les sessions de formation du formateur
        $trainingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->with('trainingDomain')
            ->orderBy('start_date', 'desc')
            ->get();

        return Inertia::render('Trainer/Courses/Edit', [
            'course' => $course,
            'trainingSessions' => $trainingSessions,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'training_session_id' => 'required|exists:training_sessions,id',
            'active' => 'boolean',
        ]);

        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $sessionIds = TrainingSession::where('trainer_id', $trainer->id)
            ->pluck('id')
            ->toArray();

        // Vérifier que la session de formation appartient au formateur
        if (!in_array($validated['training_session_id'], $sessionIds)) {
            abort(403, 'Non autorisé.');
        }

        // Récupérer le cours
        $course = Course::whereIn('training_session_id', $sessionIds)
            ->findOrFail($id);

        // Mettre à jour le cours
        $course->title = $validated['title'];
        $course->description = $validated['description'] ?? null;
        $course->training_session_id = $validated['training_session_id'];
        $course->active = $validated['active'] ?? true;
        $course->save();

        // Rediriger vers la liste des cours
        return redirect()->route('trainer.courses.index')
            ->with('success', 'Cours mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $sessionIds = TrainingSession::where('trainer_id', $trainer->id)
            ->pluck('id')
            ->toArray();

        // Récupérer le cours
        $course = Course::whereIn('training_session_id', $sessionIds)
            ->findOrFail($id);

        // Supprimer le cours
        $course->delete();

        // Rediriger vers la liste des cours
        return redirect()->route('trainer.courses.index')
            ->with('success', 'Cours supprimé avec succès.');
    }
}
