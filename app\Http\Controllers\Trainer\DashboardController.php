<?php

namespace App\Http\Controllers\Trainer;

use App\Http\Controllers\Controller;
use App\Models\TrainingSession;
use App\Models\Course;
use App\Models\Exam;
use App\Models\ExamResult;
use App\Models\Enrollment;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class DashboardController extends Controller
{
    /**
     * Affiche le tableau de bord du formateur
     */
    public function index()
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $trainingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->with('trainingDomain')
            ->get();

        $sessionIds = $trainingSessions->pluck('id')->toArray();

        // Récupérer les sessions actives (en cours)
        $activeSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->count();

        // Récupérer le nombre d'apprenants actifs
        $activeStudents = Enrollment::whereIn('training_session_id', $sessionIds)
            ->where('status', 'approved')
            ->distinct('user_id')
            ->count('user_id');

        // Calculer le taux de réussite aux examens
        $examIds = Exam::whereIn('training_session_id', $sessionIds)->pluck('id')->toArray();
        $totalResults = ExamResult::whereIn('exam_id', $examIds)->whereNotNull('completed_at')->count();
        $passedResults = ExamResult::whereIn('exam_id', $examIds)->where('passed', true)->count();
        $passRate = $totalResults > 0 ? round(($passedResults / $totalResults) * 100, 2) : 0;

        // Récupérer les statistiques pour le tableau de bord
        $stats = [
            'sessions_count' => $trainingSessions->count(),
            'active_sessions_count' => $activeSessions,
            'active_students' => $activeStudents,
            'courses_count' => Course::whereIn('training_session_id', $sessionIds)->count(),
            'exams_count' => count($examIds),
            'pending_results_count' => ExamResult::whereIn('exam_id', $examIds)
                ->whereNotNull('completed_at')
                ->where('passed', false)
                ->whereNull('feedback')
                ->count(),
            'pass_rate' => $passRate,
            'upcoming_sessions' => TrainingSession::where('trainer_id', $trainer->id)
                ->where('start_date', '>', now())
                ->count(),
        ];

        // Récupérer les prochaines sessions de formation
        $upcomingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->where('start_date', '>', now())
            ->with('trainingDomain')
            ->orderBy('start_date', 'asc')
            ->take(5)
            ->get();

        // Récupérer les sessions en cours
        $currentSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->with(['trainingDomain', 'enrollments' => function($query) {
                $query->where('status', 'approved');
            }])
            ->orderBy('end_date', 'asc')
            ->take(5)
            ->get();

        // Récupérer les derniers résultats d'examen à évaluer
        $pendingResults = ExamResult::whereIn('exam_id', $examIds)
            ->whereNotNull('completed_at')
            ->where('passed', false)
            ->whereNull('feedback')
            ->with(['user', 'exam'])
            ->orderBy('completed_at', 'desc')
            ->take(5)
            ->get();

        // Récupérer les événements pour le calendrier
        $calendarEvents = [];

        // Ajouter les sessions de formation
        foreach ($trainingSessions as $session) {
            $calendarEvents[] = [
                'id' => 'session_' . $session->id,
                'title' => $session->title,
                'start' => $session->start_date,
                'end' => date('Y-m-d', strtotime($session->end_date . ' +1 day')), // +1 jour pour l'affichage
                'color' => '#4F46E5', // Indigo
                'url' => route('trainer.courses.index', ['session_id' => $session->id]),
                'type' => 'session'
            ];
        }

        // Ajouter les examens
        $exams = Exam::whereIn('training_session_id', $sessionIds)
            ->where('is_published', true)
            ->get();

        foreach ($exams as $exam) {
            if ($exam->available_from && $exam->available_until) {
                $calendarEvents[] = [
                    'id' => 'exam_' . $exam->id,
                    'title' => 'Examen: ' . $exam->title,
                    'start' => $exam->available_from,
                    'end' => $exam->available_until,
                    'color' => '#DC2626', // Rouge
                    'url' => route('trainer.exams.show', $exam->id),
                    'type' => 'exam'
                ];
            }
        }

        // Récupérer les notifications
        $notifications = [];

        // Nouvelles inscriptions (derniers 7 jours)
        $recentEnrollments = Enrollment::whereIn('training_session_id', $sessionIds)
            ->where('created_at', '>=', now()->subDays(7))
            ->with(['user', 'trainingSession'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        foreach ($recentEnrollments as $enrollment) {
            $notifications[] = [
                'type' => 'enrollment',
                'message' => "Nouvelle inscription: {$enrollment->user->name} s'est inscrit(e) à {$enrollment->trainingSession->title}",
                'date' => $enrollment->created_at,
                'url' => route('trainer.courses.index', ['session_id' => $enrollment->training_session_id])
            ];
        }

        // Examens soumis récemment (derniers 7 jours)
        $recentResults = ExamResult::whereIn('exam_id', $examIds)
            ->where('completed_at', '>=', now()->subDays(7))
            ->whereNull('feedback')
            ->with(['user', 'exam'])
            ->orderBy('completed_at', 'desc')
            ->take(5)
            ->get();

        foreach ($recentResults as $result) {
            $notifications[] = [
                'type' => 'exam_result',
                'message' => "{$result->user->name} a soumis l'examen {$result->exam->title}",
                'date' => $result->completed_at,
                'url' => route('trainer.exam-results.edit', $result->id)
            ];
        }

        // Trier les notifications par date (plus récent en premier)
        usort($notifications, function($a, $b) {
            return $b['date']->timestamp - $a['date']->timestamp;
        });

        // Limiter à 10 notifications
        $notifications = array_slice($notifications, 0, 10);

        // Retourner la vue avec les données
        return Inertia::render('Trainer/Dashboard', [
            'stats' => $stats,
            'upcomingSessions' => $upcomingSessions,
            'currentSessions' => $currentSessions,
            'pendingResults' => $pendingResults,
            'calendarEvents' => $calendarEvents,
            'notifications' => $notifications,
        ]);
    }
}
