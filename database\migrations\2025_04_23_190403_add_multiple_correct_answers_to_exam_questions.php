<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('exam_questions', function (Blueprint $table) {
            // Ajouter un nouveau champ pour stocker plusieurs réponses correctes
            $table->json('correct_answers')->nullable()->after('correct_answer');

            // Ajouter un champ pour indiquer si plusieurs réponses sont autorisées
            $table->boolean('multiple_answers_allowed')->default(false)->after('correct_answers');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exam_questions', function (Blueprint $table) {
            // Supprimer les champs ajoutés
            $table->dropColumn(['correct_answers', 'multiple_answers_allowed']);
        });
    }
};
