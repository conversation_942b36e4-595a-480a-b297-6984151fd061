<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProfileUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', Rule::unique(User::class)->ignore($this->user()->id)],
            'phone' => ['required', 'string', 'max:20'],
            'birth_date' => ['required', 'date'],
            'address' => ['nullable', 'string'],
            'id_card_number' => ['required', 'string', 'max:50'],
            'profession' => ['required', 'string', 'max:100'],
            'company' => ['nullable', 'string', 'max:100'],
            'profile_photo' => ['nullable', function ($attribute, $value, $fail) {
                if ($value === 'DELETE') {
                    return; // Valide pour la suppression
                }
                // Validation pour les fichiers uploadés
                $rules = ['image', 'mimes:jpeg,png,jpg,gif', 'max:2048'];
                $validator = validator(['profile_photo' => $value], ['profile_photo' => $rules]);
                if ($validator->fails()) {
                    $fail($validator->errors()->first('profile_photo'));
                }
            }],
            'discovery_source' => ['required', 'string', 'in:facebook,instagram,tiktok,word_of_mouth,other'],
            'discovery_source_other' => ['nullable', 'string', 'max:100', 'required_if:discovery_source,other'],
        ];
    }
}
