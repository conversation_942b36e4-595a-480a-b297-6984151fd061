<script setup>
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import { Link, useForm, usePage } from '@inertiajs/vue3';
import { ref, computed, watch } from 'vue';

defineProps({
    mustVerifyEmail: {
        type: Boolean,
    },
    status: {
        type: String,
    },
});

const user = usePage().props.auth.user;

const form = useForm({
    name: user.name,
    email: user.email,
    phone: user.phone || '',
    birth_date: user.birth_date || '',
    address: user.address || '',
    id_card_number: user.id_card_number || '',
    profession: user.profession || '',
    company: user.company || '',
    profile_photo: null,
    discovery_source: user.discovery_source || '',
    discovery_source_other: user.discovery_source_other || '',
});

// État pour la prévisualisation de l'image
const photoPreview = ref(null);
const photoInput = ref(null);

// Méthodes pour la gestion de l'image
const selectNewPhoto = () => {
    photoInput.value.click();
};

const updatePhotoPreview = () => {
    const photo = photoInput.value.files[0];

    if (!photo) return;

    form.profile_photo = photo;

    const reader = new FileReader();

    reader.onload = (e) => {
        photoPreview.value = e.target.result;
    };

    reader.readAsDataURL(photo);
};

const deletePhoto = () => {
    // Réinitialiser la prévisualisation
    photoPreview.value = null;

    // Réinitialiser l'input file
    if (photoInput.value) {
        photoInput.value.value = null;
    }

    // Marquer pour suppression et soumettre immédiatement
    form.profile_photo = 'DELETE';

    // Soumettre le formulaire pour supprimer la photo
    form.post(route('profile.update.post'), {
        forceFormData: true,
        preserveScroll: true,
        onSuccess: (page) => {
            // Réinitialiser après succès
            form.profile_photo = null;
            photoPreview.value = null;

            if (photoInput.value) {
                photoInput.value.value = null;
            }
        },
        onError: (errors) => {
            console.error('Erreur lors de la suppression:', errors);
        }
    });
};

// Computed simple pour l'affichage de l'image
const displayImage = computed(() => {
    // 1. Si on a une prévisualisation (nouvelle image sélectionnée), l'utiliser
    if (photoPreview.value) {
        return photoPreview.value;
    }

    // 2. Sinon, utiliser l'image de profil actuelle de l'utilisateur (toujours à jour)
    const currentUser = usePage().props.auth.user;
    if (currentUser.profile_photo) {
        return `/storage/${currentUser.profile_photo}`;
    }

    // 3. Fallback vers l'avatar par défaut
    return '/images/default-avatar.svg';
});

// Initialiser la prévisualisation avec l'image existante
if (user.profile_photo) {
    photoPreview.value = `/storage/${user.profile_photo}`;
}

// Méthode pour soumettre le formulaire
const submitForm = () => {
    // Utiliser la route POST pour gérer les fichiers
    form.post(route('profile.update.post'), {
        forceFormData: true,
        preserveScroll: true,
        onSuccess: (page) => {
            // Réinitialiser seulement le champ du formulaire et l'input file
            form.profile_photo = null;

            // Réinitialiser l'input file
            if (photoInput.value) {
                photoInput.value.value = null;
            }

            // Réinitialiser la prévisualisation pour forcer le refresh
            photoPreview.value = null;
        }
    });
};
</script>

<template>
    <section>
        <header>
            <h2 class="text-lg font-medium text-gray-900">Profile Information</h2>

            <p class="mt-1 text-sm text-gray-600">
                Update your account's profile information and email address.
            </p>
        </header>

        <form @submit.prevent="submitForm" class="mt-6 space-y-6">
            <!-- Section Photo de profil -->
            <div>
                <InputLabel value="Photo de profil" />

                <!-- Photo actuelle ou placeholder -->
                <div class="mt-2 flex items-center space-x-6">
                    <div class="shrink-0">
                        <img
                            :src="displayImage"
                            :alt="user.name"
                            class="h-20 w-20 object-cover rounded-full border-2 border-gray-300"
                        />
                    </div>

                    <div class="flex flex-col space-y-2">
                        <!-- Input file caché -->
                        <input
                            ref="photoInput"
                            type="file"
                            accept="image/*"
                            @change="updatePhotoPreview"
                            class="hidden"
                        />

                        <!-- Boutons d'action -->
                        <div class="flex space-x-2">
                            <button
                                type="button"
                                @click="selectNewPhoto"
                                class="px-3 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Choisir une photo
                            </button>

                            <button
                                v-if="photoPreview || usePage().props.auth.user.profile_photo"
                                type="button"
                                @click="deletePhoto"
                                class="px-3 py-2 bg-red-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            >
                                Supprimer
                            </button>
                        </div>

                        <p class="text-xs text-gray-500">
                            JPG, PNG ou GIF. Taille maximale : 2MB
                        </p>
                    </div>
                </div>

                <InputError class="mt-2" :message="form.errors.profile_photo" />
            </div>

            <div>
                <InputLabel for="name" value="Name" />

                <TextInput
                    id="name"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.name"
                    required
                    autofocus
                    autocomplete="name"
                />

                <InputError class="mt-2" :message="form.errors.name" />
            </div>

            <div>
                <InputLabel for="email" value="Email" />

                <TextInput
                    id="email"
                    type="email"
                    class="mt-1 block w-full"
                    v-model="form.email"
                    required
                    autocomplete="username"
                />

                <InputError class="mt-2" :message="form.errors.email" />
            </div>

            <div v-if="mustVerifyEmail && user.email_verified_at === null">
                <p class="text-sm mt-2 text-gray-800">
                    Your email address is unverified.
                    <Link
                        :href="route('verification.send')"
                        method="post"
                        as="button"
                        class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        Click here to re-send the verification email.
                    </Link>
                </p>

                <div
                    v-show="status === 'verification-link-sent'"
                    class="mt-2 font-medium text-sm text-green-600"
                >
                    A new verification link has been sent to your email address.
                </div>
            </div>

            <!-- Champs obligatoires pour l'inscription -->
            <div>
                <InputLabel for="phone" value="Numéro de téléphone" />
                <TextInput
                    id="phone"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.phone"
                    required
                />
                <InputError class="mt-2" :message="form.errors.phone" />
            </div>

            <div>
                <InputLabel for="birth_date" value="Date de naissance" />
                <TextInput
                    id="birth_date"
                    type="date"
                    class="mt-1 block w-full"
                    v-model="form.birth_date"
                    required
                />
                <InputError class="mt-2" :message="form.errors.birth_date" />
            </div>

            <div>
                <InputLabel for="id_card_number" value="Numéro de carte d'identité" />
                <TextInput
                    id="id_card_number"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.id_card_number"
                    required
                />
                <InputError class="mt-2" :message="form.errors.id_card_number" />
            </div>

            <div>
                <InputLabel for="profession" value="Profession" />
                <TextInput
                    id="profession"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.profession"
                    required
                />
                <InputError class="mt-2" :message="form.errors.profession" />
            </div>

            <div>
                <InputLabel for="company" value="Nom de l'entreprise" />
                <TextInput
                    id="company"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.company"
                />
                <InputError class="mt-2" :message="form.errors.company" />
            </div>

            <div>
                <InputLabel for="address" value="Adresse postale (facultatif)" />
                <textarea
                    id="address"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.address"
                    rows="3"
                ></textarea>
                <InputError class="mt-2" :message="form.errors.address" />
            </div>

            <div>
                <InputLabel for="discovery_source" value="Comment avez-vous découvert notre formation ?" />
                <select
                    id="discovery_source"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.discovery_source"
                    required
                >
                    <option value="">Sélectionnez une option</option>
                    <option value="facebook">Facebook</option>
                    <option value="instagram">Instagram</option>
                    <option value="tiktok">TikTok</option>
                    <option value="word_of_mouth">Bouche à oreille</option>
                    <option value="other">Autre</option>
                </select>
                <InputError class="mt-2" :message="form.errors.discovery_source" />
            </div>

            <div v-if="form.discovery_source === 'other'">
                <InputLabel for="discovery_source_other" value="Précisez" />
                <TextInput
                    id="discovery_source_other"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.discovery_source_other"
                    required
                />
                <InputError class="mt-2" :message="form.errors.discovery_source_other" />
            </div>

            <div class="flex items-center gap-4">
                <PrimaryButton :disabled="form.processing">Save</PrimaryButton>

                <Transition
                    enter-active-class="transition ease-in-out"
                    enter-from-class="opacity-0"
                    leave-active-class="transition ease-in-out"
                    leave-to-class="opacity-0"
                >
                    <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                </Transition>
            </div>
        </form>
    </section>
</template>
