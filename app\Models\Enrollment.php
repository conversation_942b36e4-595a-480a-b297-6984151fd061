<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Enrollment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'training_session_id',
        'status',
        'enrollment_date',
        'notes',
        'payment_amount',
        'payment_confirmed',
        'payment_status',
        'payment_method',
        'payment_proof',
        'payment_date',
        'payment_due_date',
        'payment_notes',
        'payment_instructions',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'enrollment_date' => 'date',
        'payment_amount' => 'decimal:2',
        'payment_confirmed' => 'boolean',
        'payment_date' => 'datetime',
        'payment_due_date' => 'datetime',
    ];

    /**
     * Relation avec l'utilisateur
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relation avec la session de formation
     */
    public function trainingSession()
    {
        return $this->belongsTo(TrainingSession::class);
    }

    /**
     * Relation avec les résultats d'examen
     */
    public function examResults()
    {
        return $this->hasMany(ExamResult::class);
    }

    /**
     * Relation avec les certificats
     */
    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }

    /**
     * Relation avec le certificat (au singulier)
     */
    public function certificate()
    {
        return $this->hasOne(Certificate::class);
    }
}
