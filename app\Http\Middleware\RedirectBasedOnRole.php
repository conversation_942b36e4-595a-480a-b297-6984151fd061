<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RedirectBasedOnRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Si l'utilisateur est authentifié
        if ($request->user()) {
            // Rediriger vers le tableau de bord approprié en fonction du rôle
            if ($request->route()->getName() === 'dashboard') {
                if ($request->user()->isAdmin()) {
                    return redirect()->route('admin.dashboard');
                } elseif ($request->user()->isTrainer()) {
                    return redirect()->route('trainer.dashboard');
                } elseif ($request->user()->isStudent()) {
                    return redirect()->route('student.dashboard');
                }
            }
        }

        return $next($request);
    }
}
