<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\CertificateConsistencyService;
use App\Models\Certificate;
use Illuminate\Support\Facades\Log;

class CertificateConsistencyMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);
        
        // Only check on certificate-related routes
        if ($this->shouldCheckConsistency($request)) {
            $this->performConsistencyCheck($request);
        }
        
        return $response;
    }
    
    /**
     * Determine if we should check certificate consistency for this request
     */
    private function shouldCheckConsistency(Request $request): bool
    {
        $certificateRoutes = [
            'admin.certificates.*',
            'student.certificates.*',
            'certificates.*',
            'admin.students.*'
        ];
        
        foreach ($certificateRoutes as $pattern) {
            if ($request->routeIs($pattern)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Perform certificate consistency check
     */
    private function performConsistencyCheck(Request $request): void
    {
        try {
            // Get certificate ID from route parameters
            $certificateId = $request->route('certificate') ?? $request->route('id');
            
            if ($certificateId) {
                $certificate = Certificate::find($certificateId);
                
                if ($certificate) {
                    $issues = CertificateConsistencyService::validateCertificateConsistency($certificate);
                    
                    if (!empty($issues)) {
                        Log::warning("Certificate consistency issues detected in middleware", [
                            'route' => $request->route()->getName(),
                            'certificate_id' => $certificate->id,
                            'certificate_number' => $certificate->certificate_number,
                            'issues' => $issues,
                            'url' => $request->url()
                        ]);
                        
                        // Auto-fix minor issues
                        CertificateConsistencyService::fixCertificateInconsistencies($certificate);
                    }
                }
            }
            
            // Track certificate access
            CertificateConsistencyService::trackCertificateChange(
                $certificate ?? new Certificate(['id' => 'unknown']),
                'view_access',
                [
                    'route' => $request->route()->getName(),
                    'url' => $request->url()
                ]
            );
            
        } catch (\Exception $e) {
            Log::error("Error in certificate consistency middleware", [
                'error' => $e->getMessage(),
                'route' => $request->route()->getName() ?? 'unknown',
                'url' => $request->url()
            ]);
        }
    }
}
