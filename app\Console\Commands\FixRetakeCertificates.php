<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Certificate;
use App\Models\ExamResult;
use App\Models\Exam;
use App\Models\Enrollment;
use Illuminate\Support\Facades\Log;

class FixRetakeCertificates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'certificates:fix-retakes {--dry-run : Show what would be fixed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix certificates for students who passed retake certification exams but certificates remain in draft status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        $this->info('🔍 Checking for retake certification issues...');

        // Find all passed certification_rattrapage exam results
        $passedRetakeResults = ExamResult::where('passed', true)
            ->whereHas('exam', function ($query) {
                $query->where('exam_type', 'certification_rattrapage');
            })
            ->with(['exam', 'user', 'enrollment'])
            ->get();

        if ($passedRetakeResults->isEmpty()) {
            $this->info('✅ No passed retake certification exams found.');
            return 0;
        }

        $this->info("Found {$passedRetakeResults->count()} passed retake certification exam(s):");

        $headers = ['Student', 'Exam', 'Score', 'Certificate Status', 'Action'];
        $rows = [];
        $fixedCount = 0;

        foreach ($passedRetakeResults as $examResult) {
            // Find the certificate for this enrollment
            $certificate = Certificate::where('enrollment_id', $examResult->enrollment_id)->first();
            
            $certificateStatus = $certificate ? $certificate->status : 'Missing';
            $action = 'No action needed';

            if (!$certificate) {
                $action = $dryRun ? 'Would create certificate' : 'Create certificate';
                if (!$dryRun) {
                    $this->createCertificateForRetake($examResult);
                    $fixedCount++;
                }
            } elseif ($certificate->status === 'draft') {
                $action = $dryRun ? 'Would activate certificate' : 'Activate certificate';
                if (!$dryRun) {
                    $this->activateCertificate($certificate, $examResult);
                    $fixedCount++;
                }
            }

            $rows[] = [
                $examResult->user->name,
                $examResult->exam->title,
                $examResult->score . '%',
                $certificateStatus,
                $action
            ];
        }

        $this->table($headers, $rows);

        if (!$dryRun && $fixedCount > 0) {
            $this->info("✅ Fixed {$fixedCount} retake certification certificate(s)!");
        } elseif ($dryRun && $fixedCount === 0) {
            $this->info('✅ All retake certification certificates are properly configured.');
        }

        return 0;
    }

    /**
     * Create a new certificate for a retake exam result
     */
    private function createCertificateForRetake(ExamResult $examResult)
    {
        try {
            $certificateNumber = Certificate::generateCertificateNumber($examResult->enrollment_id);

            Certificate::create([
                'enrollment_id' => $examResult->enrollment_id,
                'user_id' => $examResult->user_id,
                'training_session_id' => $examResult->exam->training_session_id,
                'exam_result_id' => $examResult->id,
                'certificate_number' => $certificateNumber,
                'status' => 'issued',
                'issued_at' => now(),
                'issue_date' => now(),
            ]);

            // Update enrollment status
            $examResult->enrollment->update(['status' => 'completed']);

            $this->line("  ✅ Created certificate {$certificateNumber} for {$examResult->user->name}");
            
        } catch (\Exception $e) {
            $this->error("  ❌ Failed to create certificate for {$examResult->user->name}: " . $e->getMessage());
        }
    }

    /**
     * Activate an existing draft certificate
     */
    private function activateCertificate(Certificate $certificate, ExamResult $examResult)
    {
        try {
            $certificate->update([
                'status' => 'issued',
                'issued_at' => now(),
                'issue_date' => now(),
                'exam_result_id' => $examResult->id,
            ]);

            // Update enrollment status
            $examResult->enrollment->update(['status' => 'completed']);

            $this->line("  ✅ Activated certificate {$certificate->certificate_number} for {$examResult->user->name}");
            
        } catch (\Exception $e) {
            $this->error("  ❌ Failed to activate certificate for {$examResult->user->name}: " . $e->getMessage());
        }
    }
}
