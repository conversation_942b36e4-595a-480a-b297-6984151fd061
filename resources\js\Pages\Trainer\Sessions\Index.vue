<template>
  <Head title="Mes sessions de formation" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Mes sessions de formation
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Filtres -->
            <div class="mb-6">
              <h3 class="text-lg font-semibold mb-4">Filtres</h3>
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label for="search" class="block text-sm font-medium text-gray-700">Recherche</label>
                  <input
                    type="text"
                    id="search"
                    v-model="filters.search"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    placeholder="Titre, description..."
                  />
                </div>
                <div>
                  <label for="status" class="block text-sm font-medium text-gray-700">Statut</label>
                  <select
                    id="status"
                    v-model="filters.status"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                  >
                    <option value="">Tous</option>
                    <option value="active">En cours</option>
                    <option value="upcoming">À venir</option>
                    <option value="past">Terminées</option>
                  </select>
                </div>
                <div>
                  <label for="domain" class="block text-sm font-medium text-gray-700">Domaine</label>
                  <select
                    id="domain"
                    v-model="filters.domain_id"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                  >
                    <option value="">Tous</option>
                    <option v-for="domain in domains" :key="domain.id" :value="domain.id">
                      {{ domain.name }}
                    </option>
                  </select>
                </div>
                <div class="flex items-end">
                  <button
                    @click="resetFilters"
                    class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                  >
                    Réinitialiser
                  </button>
                </div>
              </div>
            </div>

            <!-- Liste des sessions -->
            <div v-if="sessions.data.length > 0">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Titre</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Domaine</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Période</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="session in sessions.data" :key="session.id">
                      <td class="px-6 py-4 whitespace-nowrap">{{ session.title }}</td>
                      <td class="px-6 py-4 whitespace-nowrap">{{ session.training_domain.name }}</td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        {{ formatDate(session.start_date) }} - {{ formatDate(session.end_date) }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span
                          :class="{
                            'px-2 py-1 text-xs rounded-full': true,
                            'bg-green-100 text-green-800': isActive(session),
                            'bg-yellow-100 text-yellow-800': isUpcoming(session),
                            'bg-gray-100 text-gray-800': isPast(session)
                          }"
                        >
                          {{ getSessionStatus(session) }}
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <Link
                          :href="route('trainer.sessions.show', session.id)"
                          class="text-indigo-600 hover:text-indigo-900 mr-3"
                        >
                          Détails
                        </Link>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Pagination -->
              <div class="mt-6">
                <Pagination :links="sessions.links" />
              </div>
            </div>
            <div v-else class="text-gray-500 text-center py-8">
              Aucune session de formation trouvée.
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, watch } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Pagination from '@/Components/Pagination.vue';
import debounce from 'lodash/debounce';

// Props
const props = defineProps({
  sessions: Object,
  domains: Array,
  filters: Object,
});

// État local pour les filtres
const filters = ref({
  search: props.filters.search,
  status: props.filters.status,
  domain_id: props.filters.domain_id,
});

// Méthodes
const formatDate = (dateString) => {
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const isActive = (session) => {
  const now = new Date();
  const startDate = new Date(session.start_date);
  const endDate = new Date(session.end_date);
  return startDate <= now && endDate >= now;
};

const isUpcoming = (session) => {
  const now = new Date();
  const startDate = new Date(session.start_date);
  return startDate > now;
};

const isPast = (session) => {
  const now = new Date();
  const endDate = new Date(session.end_date);
  return endDate < now;
};

const getSessionStatus = (session) => {
  if (isActive(session)) return 'En cours';
  if (isUpcoming(session)) return 'À venir';
  if (isPast(session)) return 'Terminée';
  return 'Inconnu';
};

const resetFilters = () => {
  filters.value = {
    search: '',
    status: '',
    domain_id: '',
  };
};

// Observer les changements de filtres et mettre à jour l'URL
watch(filters.value, debounce(() => {
  router.get(route('trainer.sessions.index'), filters.value, {
    preserveState: true,
    replace: true,
  });
}, 300));
</script>
