<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ExamQuestion;
use Illuminate\Support\Facades\DB;

class FixQuestionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Début de la réparation des questions...');
        
        // Récupérer toutes les questions à choix multiple
        $questions = ExamQuestion::where('question_type', 'multiple_choice')->get();
        $fixed = 0;
        
        foreach ($questions as $question) {
            $this->command->info("Traitement de la question ID: {$question->id}");
            
            // Options par défaut
            $defaultOptions = [
                'A' => 'Option A',
                'B' => 'Option B',
                'C' => 'Option C',
                'D' => 'Option D'
            ];
            
            // Réponses correctes par défaut
            $defaultCorrectOptions = ['A'];
            
            // Vérifier et corriger les options
            $options = $question->options;
            $correctOptions = $question->correct_options;
            $needsUpdate = false;
            
            // Afficher l'état actuel
            $this->command->info("Options actuelles: " . json_encode($options));
            $this->command->info("Options correctes actuelles: " . json_encode($correctOptions));
            
            // Vérifier si les options sont vides ou mal formatées
            if (empty($options) || !is_array($options) || count($options) === 0) {
                $options = $defaultOptions;
                $needsUpdate = true;
                $this->command->info("Options corrigées: " . json_encode($options));
            }
            
            // Vérifier si les options correctes sont vides ou mal formatées
            if (empty($correctOptions) || !is_array($correctOptions) || count($correctOptions) === 0) {
                $correctOptions = $defaultCorrectOptions;
                $needsUpdate = true;
                $this->command->info("Options correctes corrigées: " . json_encode($correctOptions));
            }
            
            // Mettre à jour la question si nécessaire
            if ($needsUpdate) {
                // Mise à jour directe via DB pour éviter les problèmes de cast
                DB::table('exam_questions')
                    ->where('id', $question->id)
                    ->update([
                        'options' => json_encode($options),
                        'correct_options' => json_encode($correctOptions)
                    ]);
                
                $fixed++;
                $this->command->info("Question ID: {$question->id} mise à jour avec succès.");
            } else {
                $this->command->info("Question ID: {$question->id} n'a pas besoin de mise à jour.");
            }
        }
        
        $this->command->info("Réparation terminée. $fixed questions ont été mises à jour.");
    }
}
