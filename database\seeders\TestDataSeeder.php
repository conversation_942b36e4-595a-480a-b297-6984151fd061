<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\TrainingDomain;
use App\Models\TrainingSession;
use App\Models\User;
use App\Models\Enrollment;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer un domaine de formation si il n'existe pas
        $domain = TrainingDomain::firstOrCreate([
            'name' => 'Premiers Secours'
        ], [
            'description' => 'Formation aux premiers secours et techniques de sauvetage',
            'active' => true
        ]);

        // Créer des sessions de test pour chaque département et niveau
        $departments = ['Secourisme', 'Langue', 'Formation à la carte'];
        $levels = ['Niveau 1', 'Niveau 2', 'Niveau 3'];

        foreach ($departments as $department) {
            foreach ($levels as $level) {
                TrainingSession::firstOrCreate([
                    'title' => "{$department} - {$level}",
                    'department' => $department,
                    'level' => $level
                ], [
                    'description' => "Formation {$department} de {$level}",
                    'training_domain_id' => $domain->id,
                    'start_date' => now()->addDays(rand(7, 30)),
                    'end_date' => now()->addDays(rand(31, 60)),
                    'max_participants' => rand(10, 20),
                    'price' => rand(0, 1) ? rand(50, 200) : null,
                    'location' => 'Centre de formation PCMET',
                    'active' => true
                ]);
            }
        }

        // Créer un utilisateur étudiant de test
        $student = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Étudiant Test',
            'password' => bcrypt('password'),
            'role' => 'student',
            'phone' => '0123456789',
            'address' => 'Adresse test',
            'date_of_birth' => '1990-01-01'
        ]);

        // Créer quelques inscriptions complétées pour tester le système de verrouillage
        $secoursNiveau1 = TrainingSession::where('department', 'Secourisme')
            ->where('level', 'Niveau 1')
            ->first();

        if ($secoursNiveau1) {
            Enrollment::firstOrCreate([
                'user_id' => $student->id,
                'training_session_id' => $secoursNiveau1->id
            ], [
                'status' => 'completed',
                'enrollment_date' => now()->subDays(30)
            ]);
        }

        echo "Données de test créées avec succès!\n";
        echo "Utilisateur test: <EMAIL> / password\n";
        echo "Sessions créées pour tous les départements et niveaux\n";
        echo "Progression test: Secourisme Niveau 1 complété\n";
    }
}
