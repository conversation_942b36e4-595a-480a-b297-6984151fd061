<?php

namespace Database\Seeders;

use App\Models\FirstAidTip;
use App\Models\FirstAidTipMaterial;
use Illuminate\Database\Seeder;

class FirstAidTipSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer quelques conseils en premiers secours
        $tips = [
            [
                'title' => 'Position Latérale de Sécurité (PLS)',
                'description' => 'La Position Latérale de Sécurité est une technique fondamentale pour maintenir les voies respiratoires libres chez une personne inconsciente qui respire.',
                'order' => 1,
                'active' => true,
            ],
            [
                'title' => 'Réanimation Cardio-Pulmonaire (RCP)',
                'description' => 'Techniques de réanimation cardio-pulmonaire pour les adultes, enfants et nourrissons en cas d\'arrêt cardiaque.',
                'order' => 2,
                'active' => true,
            ],
            [
                'title' => 'Gestion des Hémorragies',
                'description' => 'Comment arrêter efficacement les saignements et gérer les différents types d\'hémorragies.',
                'order' => 3,
                'active' => true,
            ],
            [
                'title' => 'Obstruction des Voies Respiratoires',
                'description' => 'Techniques pour dégager les voies respiratoires en cas d\'étouffement chez l\'adulte et l\'enfant.',
                'order' => 4,
                'active' => true,
            ],
            [
                'title' => 'Gestion des Brûlures',
                'description' => 'Premiers secours en cas de brûlures thermiques, chimiques ou électriques.',
                'order' => 5,
                'active' => true,
            ],
        ];

        foreach ($tips as $tipData) {
            $tip = FirstAidTip::create($tipData);

            // Ajouter quelques matériels pour chaque conseil
            $this->createMaterialsForTip($tip);
        }
    }

    private function createMaterialsForTip(FirstAidTip $tip)
    {
        $materials = [];

        switch ($tip->title) {
            case 'Position Latérale de Sécurité (PLS)':
                $materials = [
                    [
                        'title' => 'Guide étape par étape - PLS',
                        'description' => 'Instructions détaillées pour mettre une personne en position latérale de sécurité.',
                        'type' => 'text',
                        'content' => "1. Vérifiez que la personne est inconsciente mais respire\n2. Agenouillez-vous à côté de la victime\n3. Placez le bras le plus proche de vous à angle droit\n4. Amenez l'autre bras sur la poitrine\n5. Pliez la jambe opposée\n6. Faites rouler la personne vers vous\n7. Ajustez la position pour maintenir les voies respiratoires libres",
                        'order' => 1,
                        'active' => true,
                    ],
                    [
                        'title' => 'Vidéo démonstrative - PLS',
                        'description' => 'Démonstration pratique de la mise en PLS',
                        'type' => 'embed_video',
                        'embed_code' => '<iframe width="100%" height="400" src="https://www.youtube.com/embed/ILxjxfB4zNk" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>',
                        'order' => 2,
                        'active' => true,
                        'allow_download' => false,
                    ],
                ];
                break;

            case 'Réanimation Cardio-Pulmonaire (RCP)':
                $materials = [
                    [
                        'title' => 'Protocole RCP Adulte',
                        'description' => 'Protocole complet de réanimation cardio-pulmonaire pour adulte',
                        'type' => 'text',
                        'content' => "PROTOCOLE RCP ADULTE:\n\n1. VÉRIFICATION\n- Vérifiez la conscience (tapotez les épaules)\n- Vérifiez la respiration (regardez, écoutez, sentez)\n- Appelez les secours (15 ou 112)\n\n2. COMPRESSIONS THORACIQUES\n- Placez le talon d'une main au centre de la poitrine\n- Placez l'autre main par-dessus, doigts entrelacés\n- Bras tendus, compressions de 5-6 cm de profondeur\n- Rythme: 100-120 compressions/minute\n\n3. VENTILATIONS\n- Basculez la tête en arrière, soulevez le menton\n- Pincez le nez, couvrez la bouche\n- 2 insufflations de 1 seconde chacune\n\n4. ALTERNANCE\n- 30 compressions : 2 ventilations\n- Continuez jusqu'à l'arrivée des secours",
                        'order' => 1,
                        'active' => true,
                    ],
                    [
                        'title' => 'Vidéo RCP - Technique complète',
                        'description' => 'Démonstration complète de la RCP',
                        'type' => 'embed_video',
                        'embed_code' => '<iframe width="100%" height="400" src="https://www.youtube.com/embed/n5hP4DIBCEE" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>',
                        'order' => 2,
                        'active' => true,
                        'allow_download' => false,
                    ],
                ];
                break;

            case 'Gestion des Hémorragies':
                $materials = [
                    [
                        'title' => 'Techniques d\'arrêt des saignements',
                        'description' => 'Méthodes pour contrôler les hémorragies externes',
                        'type' => 'text',
                        'content' => "GESTION DES HÉMORRAGIES:\n\n1. COMPRESSION DIRECTE\n- Appuyez fermement sur la plaie avec un linge propre\n- Maintenez la pression constante\n- Surélevez le membre si possible\n\n2. COMPRESSION À DISTANCE\n- Si la compression directe est impossible\n- Comprimez l'artère en amont de la blessure\n\n3. GARROT (en dernier recours)\n- Uniquement si hémorragie massive d'un membre\n- Placez 5 cm au-dessus de la plaie\n- Serrez jusqu'à arrêt du saignement\n- Notez l'heure de pose\n\n4. SURVEILLANCE\n- Vérifiez régulièrement l'état de la victime\n- Préparez l'évacuation rapide",
                        'order' => 1,
                        'active' => true,
                    ],
                ];
                break;

            case 'Obstruction des Voies Respiratoires':
                $materials = [
                    [
                        'title' => 'Manœuvre de Heimlich',
                        'description' => 'Technique pour dégager les voies respiratoires en cas d\'étouffement',
                        'type' => 'text',
                        'content' => "MANŒUVRE DE HEIMLICH:\n\n1. ÉVALUATION\n- La personne peut-elle parler ou tousser ?\n- Si non, obstruction complète → action immédiate\n\n2. TECHNIQUE ADULTE/ENFANT\n- Placez-vous derrière la victime\n- Entourez-la avec vos bras\n- Placez un poing sous le sternum\n- Couvrez avec l'autre main\n- Compressions fermes vers l'intérieur et le haut\n- Répétez jusqu'à expulsion du corps étranger\n\n3. NOURRISSON\n- Placez sur l'avant-bras, tête vers le bas\n- 5 tapes dans le dos entre les omoplates\n- Retournez, 5 compressions thoraciques\n- Alternez jusqu'à expulsion\n\n4. VICTIME INCONSCIENTE\n- Allongez au sol\n- Commencez la RCP",
                        'order' => 1,
                        'active' => true,
                    ],
                ];
                break;

            case 'Gestion des Brûlures':
                $materials = [
                    [
                        'title' => 'Classification et traitement des brûlures',
                        'description' => 'Guide pour évaluer et traiter les différents types de brûlures',
                        'type' => 'text',
                        'content' => "GESTION DES BRÛLURES:\n\n1. ÉVALUATION\n- 1er degré: rougeur, douleur (coup de soleil)\n- 2e degré: cloques, douleur intense\n- 3e degré: peau blanche/noire, peu de douleur\n\n2. TRAITEMENT IMMÉDIAT\n- Éloignez de la source de chaleur\n- Refroidissez à l'eau tiède (15-20°C) pendant 20 minutes\n- Ne pas utiliser de glace\n- Retirez bijoux et vêtements non adhérents\n\n3. PROTECTION\n- Couvrez avec un linge propre et humide\n- Ne percez pas les cloques\n- Ne mettez rien d'autre sur la brûlure\n\n4. BRÛLURES CHIMIQUES\n- Rincez abondamment à l'eau\n- Retirez vêtements contaminés\n- Continuez le rinçage pendant le transport\n\n5. ÉVACUATION\n- Brûlures > 10% surface corporelle\n- Brûlures du visage, mains, organes génitaux\n- Brûlures 3e degré\n- Brûlures chimiques ou électriques",
                        'order' => 1,
                        'active' => true,
                    ],
                ];
                break;
        }

        foreach ($materials as $materialData) {
            $materialData['first_aid_tip_id'] = $tip->id;
            FirstAidTipMaterial::create($materialData);
        }
    }
}
