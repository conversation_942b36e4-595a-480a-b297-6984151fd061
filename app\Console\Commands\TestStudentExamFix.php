<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ExamResult;
use App\Models\User;
use App\Models\Exam;
use App\Models\Enrollment;
use App\Models\TrainingSession;

class TestStudentExamFix extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exam:test-fix';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the student exam results display fix';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing Student Exam Results Display Fix');
        $this->info('==========================================');
        
        // Test multiple students
        $studentsWithResults = User::whereHas('examResults')->take(3)->get();
        
        foreach ($studentsWithResults as $student) {
            $this->testStudentExamDisplay($student);
            $this->line('');
        }
        
        $this->info('✅ Test completed successfully!');
        return Command::SUCCESS;
    }
    
    private function testStudentExamDisplay($student)
    {
        $this->info("👨‍🎓 Testing: {$student->name} (ID: {$student->id})");
        
        // Simulate the exact controller logic
        $enrollments = Enrollment::where('user_id', $student->id)
            ->where('status', 'approved')
            ->pluck('id')
            ->toArray();

        $sessionIds = Enrollment::where('user_id', $student->id)
            ->where('status', 'approved')
            ->pluck('training_session_id')
            ->toArray();

        $activeSessionIds = TrainingSession::whereIn('id', $sessionIds)
            ->where('active', true)
            ->pluck('id')
            ->toArray();

        $examResultSessionIds = ExamResult::where('user_id', $student->id)
            ->join('exams', 'exam_results.exam_id', '=', 'exams.id')
            ->whereIn('exams.training_session_id', $sessionIds)
            ->distinct()
            ->pluck('exams.training_session_id')
            ->toArray();

        $allRelevantSessionIds = array_unique(array_merge($activeSessionIds, $examResultSessionIds));

        $exams = Exam::whereIn('training_session_id', $allRelevantSessionIds)
            ->where('is_published', true)
            ->with(['trainingSession', 'questions'])
            ->orderBy('created_at', 'desc')
            ->get();

        $examResults = ExamResult::where('user_id', $student->id)
            ->with('exam')
            ->get()
            ->groupBy('exam_id');

        $this->line("   📊 Data Summary:");
        $this->line("   - Enrollments: " . count($enrollments));
        $this->line("   - Session IDs: " . implode(', ', $sessionIds));
        $this->line("   - Active Sessions: " . implode(', ', $activeSessionIds));
        $this->line("   - Sessions with Results: " . implode(', ', $examResultSessionIds));
        $this->line("   - All Relevant Sessions: " . implode(', ', $allRelevantSessionIds));
        $this->line("   - Found Exams: {$exams->count()}");
        $this->line("   - Exam Result Groups: {$examResults->count()}");

        // Process exam data
        $examData = $exams->map(function($exam) use ($examResults, $activeSessionIds) {
            $results = $examResults->get($exam->id, collect([]));
            $latestResult = $results->sortByDesc('created_at')->first();
            $attempts = $results->count();
            $hasPassed = $results->contains('passed', true);
            $hasFailed = $results->contains('passed', false);
            $inProgress = $results->contains(function($result) {
                return $result->status === 'in_progress';
            });

            $isSessionActive = in_array($exam->training_session_id, $activeSessionIds);
            $status = $this->getExamStatus($exam, $hasPassed, $hasFailed, $inProgress, $attempts, $isSessionActive);

            return [
                'id' => $exam->id,
                'title' => $exam->title,
                'exam_type' => $exam->exam_type,
                'training_session' => $exam->trainingSession->title,
                'attempts' => $attempts,
                'has_passed' => $hasPassed,
                'has_failed' => $hasFailed,
                'status' => $status,
                'latest_score' => $latestResult ? $latestResult->score : null,
                'session_active' => $isSessionActive,
            ];
        });

        // Categorize exams
        $upcomingExams = $examData->filter(fn($exam) => $exam['status'] === 'upcoming');
        $inProgressExams = $examData->filter(fn($exam) => $exam['status'] === 'in_progress');
        $availableExams = $examData->filter(fn($exam) => $exam['status'] === 'available');
        $completedExams = $examData->filter(fn($exam) => in_array($exam['status'], ['passed', 'failed']));

        $this->line("   📋 Exam Categories:");
        $this->line("   - Upcoming: {$upcomingExams->count()}");
        $this->line("   - In Progress: {$inProgressExams->count()}");
        $this->line("   - Available: {$availableExams->count()}");
        $this->line("   - Completed: {$completedExams->count()}");

        if ($completedExams->count() > 0) {
            $this->line("   📝 Completed Exams:");
            foreach ($completedExams as $exam) {
                $sessionStatus = $exam['session_active'] ? 'Active' : 'Inactive';
                $this->line("   - {$exam['title']}: {$exam['status']} (Score: {$exam['latest_score']}%) - Session: {$sessionStatus}");
            }
        }

        // Test result
        if ($completedExams->count() > 0) {
            $this->line("   ✅ SUCCESS: Student can see {$completedExams->count()} completed exam(s)");
        } else {
            $this->line("   ❌ ISSUE: No completed exams found for student with exam results");
        }
    }
    
    private function getExamStatus($exam, $hasPassed, $hasFailed, $inProgress, $attempts, $isSessionActive = true)
    {
        $now = now();

        if ($inProgress) {
            return 'in_progress';
        }

        if ($hasPassed) {
            return 'passed';
        }

        if ($hasFailed && $attempts > 0) {
            return 'failed';
        }

        if (!$isSessionActive) {
            return 'expired';
        }

        if ($exam->available_from && $exam->available_from > $now) {
            return 'upcoming';
        }

        if ($exam->available_until && $exam->available_until < $now) {
            return 'expired';
        }

        return 'available';
    }
}
