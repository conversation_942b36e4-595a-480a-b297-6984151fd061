<template>
  <Head :title="'Résultat d\'examen - ' + examResult.enrollment.user.name" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Détails du résultat d'examen
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Bouton de retour -->
        <div class="mb-6">
          <Link :href="route('admin.exam-results.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Retour à la liste
          </Link>
        </div>

        <!-- Message de succès -->
        <div v-if="$page.props.flash.success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
          <span class="block sm:inline">{{ $page.props.flash.success }}</span>
        </div>

        <!-- Message d'erreur -->
        <div v-if="$page.props.flash.error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <span class="block sm:inline">{{ $page.props.flash.error }}</span>
        </div>

        <!-- Informations du résultat d'examen -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-2xl font-semibold mb-2">Résultat d'examen de {{ examResult.enrollment.user.name }}</h3>
                <div class="mb-2">
                  <span :class="{
                    'px-2 py-1 text-xs rounded-full': true,
                    'bg-green-100 text-green-800': examResult.passed,
                    'bg-red-100 text-red-800': !examResult.passed
                  }">
                    {{ examResult.passed ? 'Réussi' : 'Échoué' }}
                  </span>
                </div>
              </div>
              <div class="flex space-x-2">
                <Link :href="route('admin.exam-results.edit', examResult.id)" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 0L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Modifier
                </Link>
                <form @submit.prevent="validateResult">
                  <button type="submit" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    Valider
                  </button>
                </form>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <!-- Informations sur l'examen -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold mb-3">Informations sur l'examen</h4>
                <div class="mb-2">
                  <span class="font-semibold">Titre:</span> {{ examResult.exam.title }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Formation:</span> {{ examResult.enrollment.training_session.title }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Domaine:</span> {{ examResult.enrollment.training_session.training_domain.name }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Date de l'examen:</span> {{ formatDate(examResult.created_at) }}
                </div>
              </div>

              <!-- Informations sur l'apprenant -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold mb-3">Informations sur l'apprenant</h4>
                <div class="mb-2">
                  <span class="font-semibold">Nom:</span> {{ examResult.enrollment.user.name }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Email:</span> {{ examResult.enrollment.user.email }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Statut de l'inscription:</span> 
                  <span :class="{
                    'px-2 py-1 text-xs rounded-full ml-2': true,
                    'bg-yellow-100 text-yellow-800': examResult.enrollment.status === 'pending',
                    'bg-green-100 text-green-800': examResult.enrollment.status === 'approved',
                    'bg-red-100 text-red-800': examResult.enrollment.status === 'rejected',
                    'bg-blue-100 text-blue-800': examResult.enrollment.status === 'completed'
                  }">
                    {{ formatStatus(examResult.enrollment.status) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Résultat de l'examen -->
            <div class="mt-6 bg-gray-50 p-4 rounded-lg">
              <h4 class="text-lg font-semibold mb-3">Résultat de l'examen</h4>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <span class="font-semibold">Score:</span> {{ examResult.score }}%
                </div>
                <div>
                  <span class="font-semibold">Résultat:</span> 
                  <span :class="{
                    'px-2 py-1 text-xs rounded-full ml-2': true,
                    'bg-green-100 text-green-800': examResult.passed,
                    'bg-red-100 text-red-800': !examResult.passed
                  }">
                    {{ examResult.passed ? 'Réussi' : 'Échoué' }}
                  </span>
                </div>
                <div v-if="certificate">
                  <span class="font-semibold">Certificat:</span> 
                  <Link :href="route('admin.certificates.show', certificate.id)" class="text-blue-600 hover:text-blue-800 ml-2">
                    Voir le certificat
                  </Link>
                </div>
              </div>
              <div class="mt-4" v-if="examResult.feedback">
                <span class="font-semibold">Feedback:</span>
                <div class="bg-white p-4 rounded mt-2">
                  <p>{{ examResult.feedback }}</p>
                </div>
              </div>
            </div>

            <!-- Réponses de l'examen -->
            <div class="mt-6" v-if="examResult.answers">
              <h4 class="text-lg font-semibold mb-3">Réponses de l'examen</h4>
              <div class="bg-gray-50 p-4 rounded-lg">
                <div v-if="parsedAnswers.length > 0">
                  <div v-for="(answer, index) in parsedAnswers" :key="index" class="mb-4 p-4 bg-white rounded-lg">
                    <div class="font-semibold mb-2">Question {{ index + 1 }}: {{ getQuestionText(answer.question_id) }}</div>
                    <div v-if="getQuestionType(answer.question_id) === 'multiple_choice'">
                      <div class="mb-1">Réponse: {{ answer.answer }}</div>
                      <div>Réponse correcte: {{ getCorrectAnswer(answer.question_id) }}</div>
                    </div>
                    <div v-else-if="getQuestionType(answer.question_id) === 'text'">
                      <div class="mb-1">Réponse: {{ answer.answer }}</div>
                    </div>
                    <div v-else-if="getQuestionType(answer.question_id) === 'file'">
                      <div class="mb-1">Fichier soumis: {{ answer.answer }}</div>
                    </div>
                  </div>
                </div>
                <div v-else class="text-gray-500 italic">
                  Aucune réponse enregistrée pour cet examen.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { computed } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  examResult: Object,
  certificate: Object,
});

// Formulaire pour la validation du résultat
const validateForm = useForm({
  passed: props.examResult.passed,
  feedback: props.examResult.feedback || '',
});

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return 'Non définie';
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const formatStatus = (status) => {
  const statusMap = {
    'pending': 'En attente',
    'approved': 'Approuvée',
    'rejected': 'Rejetée',
    'completed': 'Terminée'
  };
  return statusMap[status] || status;
};

const parsedAnswers = computed(() => {
  if (!props.examResult.answers) return [];
  try {
    return JSON.parse(props.examResult.answers);
  } catch (e) {
    console.error('Erreur lors du parsing des réponses:', e);
    return [];
  }
});

const getQuestionText = (questionId) => {
  const question = props.examResult.exam.questions.find(q => q.id === questionId);
  return question ? question.question_text : 'Question inconnue';
};

const getQuestionType = (questionId) => {
  const question = props.examResult.exam.questions.find(q => q.id === questionId);
  return question ? question.question_type : 'unknown';
};

const getCorrectAnswer = (questionId) => {
  const question = props.examResult.exam.questions.find(q => q.id === questionId);
  return question ? question.correct_answer : 'Réponse inconnue';
};

const validateResult = () => {
  validateForm.post(route('admin.exam-results.validate', props.examResult.id));
};
</script>
