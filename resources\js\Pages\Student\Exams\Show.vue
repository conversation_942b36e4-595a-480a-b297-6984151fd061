<template>
  <Head :title="exam.title" />

  <div class="min-h-screen bg-gray-100" :class="{ 'fullscreen-mode': isFullscreen }">
    <!-- En-tête fixe -->
    <header class="bg-white shadow-md fixed top-0 left-0 right-0 z-10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
        <div class="flex items-center">
          <h1 class="text-xl font-semibold text-gray-900">{{ exam.title }}</h1>
          <span v-if="!examStarted" class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
            {{ getExamTypeLabel(exam.exam_type) }}
          </span>
        </div>

        <div class="flex items-center space-x-4">
          <!-- Minuteur -->
          <div v-if="examStarted" class="text-lg font-mono bg-gray-100 px-3 py-1 rounded-md">
            <span :class="{ 'text-red-600': timeRemaining < 300 }">{{ formatTime(timeRemaining) }}</span>
          </div>

          <!-- Bouton plein écran -->
          <button
            @click="toggleFullscreen"
            class="p-2 rounded-md hover:bg-gray-100"
            :title="isFullscreen ? 'Quitter le mode plein écran' : 'Mode plein écran'"
          >
            <svg v-if="isFullscreen" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
            </svg>
          </button>

          <!-- Bouton de pause (si autorisé) -->
          <button
            v-if="examStarted && allowPause"
            @click="togglePause"
            class="p-2 rounded-md hover:bg-gray-100"
            :title="isPaused ? 'Reprendre' : 'Pause'"
          >
            <svg v-if="isPaused" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>

          <!-- Bouton de retour (si examen non commencé) -->
          <Link
            v-if="!examStarted"
            :href="route('student.exams.index')"
            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            Retour
          </Link>
        </div>
      </div>
    </header>

    <!-- Contenu principal -->
    <main class="pt-20 pb-16">
      <!-- Écran d'introduction (si examen non commencé) -->
      <div v-if="!examStarted" class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
          <div class="p-6">
            <h2 class="text-2xl font-bold mb-4">Instructions</h2>

            <div class="mb-6">
              <div class="prose max-w-none" v-html="exam.instructions || 'Aucune instruction spécifique pour cet examen.'"></div>
            </div>

            <div class="bg-blue-50 p-4 rounded-md mb-6">
              <h3 class="text-lg font-semibold mb-2">Informations sur l'examen</h3>
              <ul class="space-y-2">
                <li class="flex justify-between">
                  <span class="font-medium">Type d'examen:</span>
                  <span>{{ getExamTypeLabel(exam.exam_type) }}</span>
                </li>
                <li class="flex justify-between">
                  <span class="font-medium">Durée:</span>
                  <span>{{ exam.duration_minutes }} minutes</span>
                </li>
                <li class="flex justify-between">
                  <span class="font-medium">Nombre de questions:</span>
                  <span>{{ questions.length }}</span>
                </li>
                <li class="flex justify-between">
                  <span class="font-medium">Score minimum requis:</span>
                  <span>{{ exam.passing_score }}%</span>
                </li>
                <li v-if="previousAttempts.length > 0" class="flex justify-between">
                  <span class="font-medium">Tentatives précédentes:</span>
                  <span>{{ previousAttempts.length }}</span>
                </li>
              </ul>
            </div>

            <div v-if="hasPassed" class="bg-green-50 p-4 rounded-md mb-6">
              <div class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h3 class="text-lg font-semibold text-green-800">Vous avez déjà réussi cet examen</h3>
                  <p class="text-green-700">Vous pouvez le repasser pour améliorer votre score si vous le souhaitez.</p>
                </div>
              </div>
            </div>

            <div v-if="inProgressAttempt" class="bg-yellow-50 p-4 rounded-md mb-6">
              <div class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <div>
                  <h3 class="text-lg font-semibold text-yellow-800">Vous avez une tentative en cours</h3>
                  <p class="text-yellow-700">Vous pouvez reprendre là où vous vous êtes arrêté.</p>
                </div>
              </div>
            </div>

            <div class="flex justify-end">
              <button
                @click="startExam"
                class="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                {{ inProgressAttempt ? 'Reprendre l\'examen' : 'Commencer l\'examen' }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Interface d'examen (si examen commencé) -->
      <div v-else class="flex h-full">
        <!-- Barre latérale de navigation entre questions -->
        <div class="w-64 bg-white shadow-md fixed left-0 top-20 bottom-16 overflow-y-auto hidden md:block">
          <div class="p-4">
            <h3 class="text-lg font-semibold mb-4">Questions</h3>
            <div class="grid grid-cols-4 gap-2">
              <button
                v-for="(q, index) in questions"
                :key="q.id"
                @click="currentQuestionIndex = index"
                :class="{
                  'w-10 h-10 rounded-md flex items-center justify-center font-medium': true,
                  'bg-blue-600 text-white': currentQuestionIndex === index,
                  'bg-green-100 text-green-800 border border-green-300': isQuestionAnswered(q.id) && currentQuestionIndex !== index,
                  'bg-yellow-100 text-yellow-800 border border-yellow-300': isQuestionMarked(q.id) && !isQuestionAnswered(q.id) && currentQuestionIndex !== index,
                  'bg-gray-100 text-gray-800 border border-gray-300': !isQuestionAnswered(q.id) && !isQuestionMarked(q.id) && currentQuestionIndex !== index
                }"
              >
                {{ index + 1 }}
              </button>
            </div>

            <div class="mt-6 space-y-2">
              <div class="flex items-center">
                <div class="w-4 h-4 bg-green-100 border border-green-300 rounded mr-2"></div>
                <span class="text-sm">Répondu</span>
              </div>
              <div class="flex items-center">
                <div class="w-4 h-4 bg-yellow-100 border border-yellow-300 rounded mr-2"></div>
                <span class="text-sm">Marqué à revoir</span>
              </div>
              <div class="flex items-center">
                <div class="w-4 h-4 bg-gray-100 border border-gray-300 rounded mr-2"></div>
                <span class="text-sm">Non répondu</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Zone principale avec la question actuelle -->
        <div class="flex-1 md:ml-64">
          <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="bg-white shadow-md rounded-lg overflow-hidden">
              <div class="p-6">
                <!-- Numéro et texte de la question -->
                <div class="mb-6">
                  <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold">Question {{ currentQuestionIndex + 1 }} sur {{ questions.length }}</h2>
                    <button
                      @click="toggleMarkQuestion(currentQuestion.id)"
                      :class="{
                        'flex items-center px-3 py-1 rounded-md text-sm': true,
                        'bg-yellow-100 text-yellow-800': isQuestionMarked(currentQuestion.id),
                        'bg-gray-100 text-gray-800': !isQuestionMarked(currentQuestion.id)
                      }"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                      </svg>
                      {{ isQuestionMarked(currentQuestion.id) ? 'Marquée' : 'Marquer' }}
                    </button>
                  </div>
                  <div class="prose max-w-none">
                    <p>{{ currentQuestion.question_text }}</p>
                  </div>
                </div>

                <!-- Contenu de la question selon son type -->
                <div>
                  <!-- Questions à choix multiple -->
                  <div v-if="currentQuestion.question_type === 'multiple_choice'" class="space-y-3">
                    <div v-for="(option, key) in getOptions(currentQuestion.options)" :key="key" class="flex items-start">
                      <div class="flex items-center h-5">
                        <input
                          :id="`question_${currentQuestion.id}_option_${key}`"
                          type="checkbox"
                          :value="key"
                          v-model="answers[currentQuestion.id]"
                          class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                        >
                      </div>
                      <div class="ml-3 text-sm">
                        <label :for="`question_${currentQuestion.id}_option_${key}`" class="font-medium text-gray-700">{{ option }}</label>
                      </div>
                    </div>
                  </div>

                  <!-- Questions à texte libre -->
                  <div v-else-if="currentQuestion.question_type === 'text'" class="space-y-3">
                    <textarea
                      :id="`question_${currentQuestion.id}_answer`"
                      v-model="textAnswers[currentQuestion.id]"
                      rows="6"
                      class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      placeholder="Votre réponse..."
                    ></textarea>
                  </div>

                  <!-- Questions avec upload de fichier -->
                  <div v-else-if="currentQuestion.question_type === 'file_upload'" class="space-y-3">
                    <div class="flex items-center justify-center w-full">
                      <label
                        :for="`question_${currentQuestion.id}_file`"
                        class="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
                      >
                        <div class="flex flex-col items-center justify-center pt-5 pb-6">
                          <svg class="w-10 h-10 mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                          </svg>
                          <p class="mb-2 text-sm text-gray-500">
                            <span class="font-semibold">Cliquez pour télécharger</span> ou glissez-déposez
                          </p>
                          <p class="text-xs text-gray-500">
                            {{ currentQuestion.file_type_allowed ? `Types acceptés: ${currentQuestion.file_type_allowed}` : 'Tous types de fichiers' }}
                          </p>
                        </div>
                        <input
                          :id="`question_${currentQuestion.id}_file`"
                          type="file"
                          class="hidden"
                          @change="handleFileUpload($event, currentQuestion.id)"
                        />
                      </label>
                    </div>
                    <div v-if="fileAnswers[currentQuestion.id]" class="mt-2">
                      <p class="text-sm text-gray-600">Fichier sélectionné: {{ fileAnswers[currentQuestion.id].name }}</p>
                      <button
                        @click="removeFile(currentQuestion.id)"
                        class="mt-1 text-sm text-red-600 hover:text-red-800"
                      >
                        Supprimer
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Boutons de navigation -->
                <div class="flex justify-between mt-8">
                  <button
                    @click="previousQuestion"
                    :disabled="currentQuestionIndex === 0"
                    :class="{
                      'px-4 py-2 rounded-md': true,
                      'bg-blue-600 text-white hover:bg-blue-700': currentQuestionIndex > 0,
                      'bg-gray-300 text-gray-500 cursor-not-allowed': currentQuestionIndex === 0
                    }"
                  >
                    Précédent
                  </button>
                  <button
                    v-if="currentQuestionIndex < questions.length - 1"
                    @click="nextQuestion"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Suivant
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Pied de page fixe -->
    <footer v-if="examStarted" class="bg-white shadow-md fixed bottom-0 left-0 right-0 z-10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex justify-between items-center">
          <!-- Barre de progression -->
          <div class="w-1/2">
            <div class="bg-gray-200 rounded-full h-2.5">
              <div
                class="bg-blue-600 h-2.5 rounded-full"
                :style="{ width: `${progressPercentage}%` }"
              ></div>
            </div>
            <div class="text-sm text-gray-600 mt-1">
              {{ answeredCount }} sur {{ questions.length }} questions répondues
            </div>
          </div>

          <!-- Bouton de soumission -->
          <button
            @click="showSubmitConfirmation = true"
            :disabled="!canSubmit"
            :class="{
              'px-6 py-3 rounded-md font-medium': true,
              'bg-green-600 text-white hover:bg-green-700': canSubmit,
              'bg-gray-300 text-gray-500 cursor-not-allowed': !canSubmit
            }"
          >
            Terminer l'examen
          </button>
        </div>
      </div>
    </footer>

    <!-- Modal de confirmation de soumission -->
    <div v-if="showSubmitConfirmation" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg overflow-hidden shadow-xl max-w-lg w-full">
        <div class="p-6">
          <h3 class="text-lg font-semibold mb-4">Confirmer la soumission</h3>

          <div class="mb-4">
            <p class="text-gray-700 mb-2">Êtes-vous sûr de vouloir terminer et soumettre cet examen ?</p>
            <p class="text-gray-600 text-sm">Une fois soumis, vous ne pourrez plus modifier vos réponses.</p>
          </div>

          <div class="bg-yellow-50 p-3 rounded-md mb-4">
            <div class="flex">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-600 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
              </svg>
              <div>
                <p class="text-sm text-yellow-700">
                  <span class="font-medium">{{ unansweredCount }}</span> questions n'ont pas de réponse.
                </p>
              </div>
            </div>
          </div>

          <div class="flex justify-end space-x-3">
            <button
              @click="showSubmitConfirmation = false"
              class="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400"
            >
              Continuer l'examen
            </button>
            <button
              @click="submitExam"
              class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Soumettre l'examen
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';

// Props
const props = defineProps({
  exam: Object,
  questions: Array,
  previousAttempts: Array,
  hasPassed: Boolean,
  inProgressAttempt: Object,
});

// État local
const examStarted = ref(false);
const currentQuestionIndex = ref(0);
const timeRemaining = ref(props.exam.duration_minutes * 60);
const startTime = ref(null);
const isFullscreen = ref(false);
const isPaused = ref(false);
const allowPause = ref(false); // Désactivé par défaut
const showSubmitConfirmation = ref(false);
const timer = ref(null);

// Réponses
const answers = ref({});
const textAnswers = ref({});
const fileAnswers = ref({});
const markedQuestions = ref([]);

// Initialiser les réponses
props.questions.forEach(question => {
  if (question.question_type === 'multiple_choice') {
    answers.value[question.id] = [];
  } else if (question.question_type === 'text') {
    textAnswers.value[question.id] = '';
  } else if (question.question_type === 'file_upload') {
    fileAnswers.value[question.id] = null;
  }
});

// Question actuelle
const currentQuestion = computed(() => {
  return props.questions[currentQuestionIndex.value] || {};
});

// Nombre de questions répondues
const answeredCount = computed(() => {
  let count = 0;

  props.questions.forEach(question => {
    if (question.question_type === 'multiple_choice' && answers.value[question.id]?.length > 0) {
      count++;
    } else if (question.question_type === 'text' && textAnswers.value[question.id]?.trim()) {
      count++;
    } else if (question.question_type === 'file_upload' && fileAnswers.value[question.id]) {
      count++;
    }
  });

  return count;
});

// Nombre de questions non répondues
const unansweredCount = computed(() => {
  return props.questions.length - answeredCount.value;
});

// Pourcentage de progression
const progressPercentage = computed(() => {
  return (answeredCount.value / props.questions.length) * 100;
});

// Peut soumettre l'examen
const canSubmit = computed(() => {
  // Autoriser la soumission même si toutes les questions ne sont pas répondues
  return examStarted.value;
});

// Méthodes
const startExam = () => {
  examStarted.value = true;
  startTime.value = new Date();

  // Démarrer le minuteur
  timer.value = setInterval(() => {
    if (!isPaused.value) {
      timeRemaining.value--;

      if (timeRemaining.value <= 0) {
        clearInterval(timer.value);
        submitExam();
      }
    }
  }, 1000);
};

const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  return [
    hours.toString().padStart(2, '0'),
    minutes.toString().padStart(2, '0'),
    secs.toString().padStart(2, '0')
  ].join(':');
};

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen().catch(err => {
      console.error(`Erreur lors du passage en plein écran: ${err.message}`);
    });
    isFullscreen.value = true;
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
      isFullscreen.value = false;
    }
  }
};

const togglePause = () => {
  isPaused.value = !isPaused.value;
};

const nextQuestion = () => {
  if (currentQuestionIndex.value < props.questions.length - 1) {
    currentQuestionIndex.value++;
  }
};

const previousQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--;
  }
};

const isQuestionAnswered = (questionId) => {
  const question = props.questions.find(q => q.id === questionId);

  if (!question) return false;

  if (question.question_type === 'multiple_choice') {
    return answers.value[questionId]?.length > 0;
  } else if (question.question_type === 'text') {
    return textAnswers.value[questionId]?.trim().length > 0;
  } else if (question.question_type === 'file_upload') {
    return fileAnswers.value[questionId] !== null;
  }

  return false;
};

const isQuestionMarked = (questionId) => {
  return markedQuestions.value.includes(questionId);
};

const toggleMarkQuestion = (questionId) => {
  const index = markedQuestions.value.indexOf(questionId);

  if (index === -1) {
    markedQuestions.value.push(questionId);
  } else {
    markedQuestions.value.splice(index, 1);
  }
};

const handleFileUpload = (event, questionId) => {
  const file = event.target.files[0];
  if (file) {
    fileAnswers.value[questionId] = file;
  }
};

const removeFile = (questionId) => {
  fileAnswers.value[questionId] = null;
  // Réinitialiser l'input file
  const fileInput = document.getElementById(`question_${questionId}_file`);
  if (fileInput) {
    fileInput.value = '';
  }
};

const submitExam = () => {
  clearInterval(timer.value);

  // S'assurer que startTime est défini
  if (!startTime.value) {
    startTime.value = new Date();
  }

  // Préparer les données pour la soumission
  const formData = new FormData();

  // Ajouter les réponses selon le type de question
  const formattedAnswers = {};

  props.questions.forEach(question => {
    if (question.question_type === 'multiple_choice') {
      // S'assurer que les réponses sont bien des clés (A, B, C, D) et non des indices
      const selectedOptions = answers.value[question.id] || [];
      // Vérifier si les options sont valides
      const validOptions = Object.keys(getOptions(question.options));
      // Ne garder que les options valides
      const validSelectedOptions = selectedOptions.filter(option => validOptions.includes(option));
      formattedAnswers[question.id] = validSelectedOptions;
    } else if (question.question_type === 'text') {
      formattedAnswers[question.id] = textAnswers.value[question.id] || '';
    } else if (question.question_type === 'file_upload' && fileAnswers.value[question.id]) {
      formData.append(`file_${question.id}`, fileAnswers.value[question.id]);
      formattedAnswers[question.id] = `file_${question.id}`;
    }
  });

  // Afficher un message de débogage
  console.log('Soumission de l\'examen avec les réponses:', formattedAnswers);

  // Utiliser router.post pour soumettre les données
  const data = {
    answers: formattedAnswers,
    started_at: startTime.value.toISOString(),
    completed_at: new Date().toISOString()
  };

  // Si nous avons des fichiers, utiliser FormData
  if (Object.values(fileAnswers.value).some(file => file !== null)) {
    // Ajouter les données au FormData
    formData.append('answers', JSON.stringify(formattedAnswers));
    formData.append('started_at', startTime.value.toISOString());
    formData.append('completed_at', new Date().toISOString());

    // Soumettre avec FormData
    router.post(route('student.exams.submit', props.exam.id), formData, {
      forceFormData: true,
      onSuccess: () => {
        // Rediriger vers la page des résultats
        router.visit(route('student.exams.index'));
      },
      onError: (errors) => {
        console.error('Erreur lors de la soumission de l\'examen:', errors);
        alert('Une erreur est survenue lors de la soumission de l\'examen. Veuillez réessayer.');
      }
    });
  } else {
    // Soumettre avec des données JSON standard
    router.post(route('student.exams.submit', props.exam.id), data, {
      onSuccess: () => {
        // Rediriger vers la page des résultats
        router.visit(route('student.exams.index'));
      },
      onError: (errors) => {
        console.error('Erreur lors de la soumission de l\'examen:', errors);
        alert('Une erreur est survenue lors de la soumission de l\'examen. Veuillez réessayer.');
      }
    });
  }
};

const getExamTypeLabel = (type) => {
  switch (type) {
    case 'certification':
      return 'Certification';
    case 'certification_rattrapage':
      return 'Certification de rattrapage';
    case 'evaluation':
      return 'Évaluation';
    case 'practice':
      return 'Entraînement';
    case 'quiz':
      return 'Quiz';
    default:
      return type;
  }
};

const getOptions = (options) => {
  // Si aucune option n'est fournie, créer des options par défaut
  if (!options) {
    return {
      'A': 'Option A',
      'B': 'Option B',
      'C': 'Option C',
      'D': 'Option D'
    };
  }

  // Si les options sont une chaîne, essayer de les parser en JSON
  if (typeof options === 'string') {
    try {
      return JSON.parse(options);
    } catch (e) {
      console.error('Erreur de parsing des options:', e);
      return {
        'A': 'Option A',
        'B': 'Option B',
        'C': 'Option C',
        'D': 'Option D'
      };
    }
  }

  // Si les options sont déjà un objet, les retourner telles quelles
  if (typeof options === 'object' && options !== null) {
    return options;
  }

  // Par défaut, retourner des options standard
  return {
    'A': 'Option A',
    'B': 'Option B',
    'C': 'Option C',
    'D': 'Option D'
  };
};

// Nettoyage lors de la destruction du composant
onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
  }

  // Quitter le mode plein écran si actif
  if (document.fullscreenElement) {
    document.exitFullscreen();
  }
});

// Écouter les événements de changement de plein écran
onMounted(() => {
  document.addEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement;
  });

  // Démarrer automatiquement l'examen
  if (!examStarted.value) {
    examStarted.value = true;
    startTime.value = new Date();

    // Démarrer le minuteur
    timer.value = setInterval(() => {
      if (!isPaused.value) {
        timeRemaining.value--;

        if (timeRemaining.value <= 0) {
          clearInterval(timer.value);
          submitExam();
        }
      }
    }, 1000);
  }

  // Si une tentative est en cours, reprendre l'examen
  if (props.inProgressAttempt) {
    // Charger les réponses précédentes
    const savedAnswers = props.inProgressAttempt.answers;

    if (savedAnswers) {
      Object.keys(savedAnswers).forEach(questionId => {
        const question = props.questions.find(q => q.id === parseInt(questionId));

        if (question) {
          if (question.question_type === 'multiple_choice') {
            answers.value[questionId] = savedAnswers[questionId] || [];
          } else if (question.question_type === 'text') {
            textAnswers.value[questionId] = savedAnswers[questionId] || '';
          }
          // Les fichiers ne peuvent pas être restaurés
        }
      });
    }

    // Calculer le temps restant
    const startedAt = new Date(props.inProgressAttempt.created_at);
    const elapsedSeconds = Math.floor((new Date() - startedAt) / 1000);
    const totalSeconds = props.exam.duration_minutes * 60;

    timeRemaining.value = Math.max(0, totalSeconds - elapsedSeconds);
  }
});
</script>

<style scoped>
.fullscreen-mode {
  background-color: #f9fafb;
}

@media (max-width: 768px) {
  .md\:ml-64 {
    margin-left: 0;
  }
}
</style>
