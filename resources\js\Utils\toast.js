// Fonction simple pour afficher des notifications toast
// Comme nous n'avons pas de bibliothèque de toast installée, nous allons créer une implémentation simple

export const toast = {
  // Afficher un message d'information
  info: (message, options = {}) => {
    console.info('[INFO]', message);
    return showToast(message, 'info', options);
  },
  
  // Afficher un message de succès
  success: (message, options = {}) => {
    console.info('[SUCCESS]', message);
    return showToast(message, 'success', options);
  },
  
  // Afficher un message d'erreur
  error: (message, options = {}) => {
    console.error('[ERROR]', message);
    return showToast(message, 'error', options);
  },
  
  // Fermer un toast
  dismiss: (id) => {
    const toast = document.getElementById(`toast-${id}`);
    if (toast) {
      toast.remove();
    }
  }
};

// Fonction pour afficher un toast
function showToast(message, type, options = {}) {
  // Générer un ID unique pour le toast
  const id = Date.now();
  
  // Créer l'élément toast
  const toast = document.createElement('div');
  toast.id = `toast-${id}`;
  toast.className = `fixed top-4 right-4 p-4 rounded shadow-lg z-50 ${getBackgroundColor(type)}`;
  toast.style.minWidth = '300px';
  toast.style.maxWidth = '500px';
  
  // Ajouter le message
  toast.innerHTML = `
    <div class="flex justify-between items-center">
      <div class="flex-grow">${message}</div>
      <button class="ml-4 text-white" onclick="document.getElementById('toast-${id}').remove()">×</button>
    </div>
  `;
  
  // Ajouter le toast au DOM
  document.body.appendChild(toast);
  
  // Supprimer automatiquement le toast après un certain délai
  if (options.duration !== 0) {
    setTimeout(() => {
      if (document.getElementById(`toast-${id}`)) {
        document.getElementById(`toast-${id}`).remove();
      }
    }, options.duration || 3000);
  }
  
  return id;
}

// Fonction pour obtenir la couleur de fond en fonction du type
function getBackgroundColor(type) {
  switch (type) {
    case 'success':
      return 'bg-green-500 text-white';
    case 'error':
      return 'bg-red-500 text-white';
    case 'info':
    default:
      return 'bg-blue-500 text-white';
  }
}
