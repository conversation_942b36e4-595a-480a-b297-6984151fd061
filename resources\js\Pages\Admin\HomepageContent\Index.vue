<template>
    <Head title="Gestion du contenu de la page d'accueil" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    Gestion du contenu de la page d'accueil
                </h2>
                <div class="flex space-x-2">
                    <Link :href="route('admin.homepage-content.media')" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                        G<PERSON>rer les médias
                    </Link>
                    <Link :href="route('admin.homepage-content.create')" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Ajouter du contenu
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <!-- Success Message -->
                        <div v-if="$page.props.flash.success" class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                            {{ $page.props.flash.success }}
                        </div>

                        <!-- Content by Section -->
                        <div v-for="(sectionContents, section) in contents" :key="section" class="mb-8">
                            <h3 class="text-lg font-semibold mb-4 capitalize bg-gray-100 p-3 rounded">
                                {{ formatSectionName(section) }}
                            </h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <div v-for="content in sectionContents" :key="content.id" 
                                     class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex justify-between items-start mb-2">
                                        <h4 class="font-medium text-gray-900">{{ content.key }}</h4>
                                        <span class="text-xs px-2 py-1 rounded" 
                                              :class="getTypeClass(content.type)">
                                            {{ content.type }}
                                        </span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div v-if="content.type === 'image'" class="mb-2">
                                            <img :src="'/storage/' + content.value" 
                                                 :alt="content.key" 
                                                 class="w-full h-32 object-cover rounded">
                                        </div>
                                        <div v-else-if="content.type === 'video'" class="mb-2">
                                            <video :src="'/storage/' + content.value" 
                                                   class="w-full h-32 object-cover rounded" 
                                                   controls>
                                            </video>
                                        </div>
                                        <div v-else class="text-sm text-gray-600">
                                            {{ truncateText(content.value, 100) }}
                                        </div>
                                    </div>
                                    
                                    <div class="flex justify-between items-center">
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xs text-gray-500">
                                                Ordre: {{ content.sort_order }}
                                            </span>
                                            <span v-if="!content.is_active" 
                                                  class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                                                Inactif
                                            </span>
                                        </div>
                                        
                                        <div class="flex space-x-2">
                                            <Link :href="route('admin.homepage-content.edit', content.id)" 
                                                  class="text-blue-600 hover:text-blue-900 text-sm">
                                                Modifier
                                            </Link>
                                            <button @click="deleteContent(content)" 
                                                    class="text-red-600 hover:text-red-900 text-sm">
                                                Supprimer
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Empty State -->
                        <div v-if="Object.keys(contents).length === 0" class="text-center py-8">
                            <p class="text-gray-500 mb-4">Aucun contenu trouvé</p>
                            <Link :href="route('admin.homepage-content.create')" 
                                  class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Ajouter le premier contenu
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { router } from '@inertiajs/vue3';

defineProps({
    contents: Object
});

const formatSectionName = (section) => {
    const names = {
        'hero': 'Section Héro',
        'vr_training': 'Formation VR',
        'about': 'À propos',
        'contact': 'Contact'
    };
    return names[section] || section;
};

const getTypeClass = (type) => {
    const classes = {
        'text': 'bg-gray-100 text-gray-800',
        'image': 'bg-green-100 text-green-800',
        'video': 'bg-blue-100 text-blue-800',
        'json': 'bg-purple-100 text-purple-800'
    };
    return classes[type] || 'bg-gray-100 text-gray-800';
};

const truncateText = (text, length) => {
    if (!text) return '';
    return text.length > length ? text.substring(0, length) + '...' : text;
};

const deleteContent = (content) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce contenu ?')) {
        router.delete(route('admin.homepage-content.destroy', content.id));
    }
};
</script>
