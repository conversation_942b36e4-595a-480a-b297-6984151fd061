<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\ExamQuestion;
use App\Models\ExamResult;
use App\Models\Enrollment;
use App\Models\Certificate;
use App\Models\TrainingSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Carbon\Carbon;
use BaconQrCode\Renderer\ImageRenderer;
use BaconQrCode\Renderer\Image\SvgImageBackEnd;
use BaconQrCode\Renderer\RendererStyle\RendererStyle;
use BaconQrCode\Writer;

class ExamController extends Controller
{
    /**
     * Affiche le tableau de bord des examens pour l'apprenant
     */
    public function index(Request $request)
    {
        // Récupérer l'utilisateur connecté (apprenant)
        $student = Auth::user();

        // Récupérer les inscriptions de l'apprenant
        $enrollments = Enrollment::where('user_id', $student->id)
            ->where('status', 'approved')
            ->pluck('id')
            ->toArray();

        // Récupérer les sessions de formation auxquelles l'apprenant est inscrit avec un statut approuvé ou terminé
        $sessionIds = Enrollment::where('user_id', $student->id)
            ->whereIn('status', ['approved', 'completed'])
            ->pluck('training_session_id')
            ->toArray();

        // Séparer les sessions actives et inactives
        $activeSessionIds = TrainingSession::whereIn('id', $sessionIds)
            ->where('active', true)
            ->pluck('id')
            ->toArray();

        // Récupérer les sessions avec des résultats d'examen pour l'apprenant
        $examResultSessionIds = ExamResult::where('user_id', $student->id)
            ->join('exams', 'exam_results.exam_id', '=', 'exams.id')
            ->whereIn('exams.training_session_id', $sessionIds)
            ->distinct()
            ->pluck('exams.training_session_id')
            ->toArray();

        // Combiner les sessions actives et les sessions avec des résultats d'examen
        $allRelevantSessionIds = array_unique(array_merge($activeSessionIds, $examResultSessionIds));

        // Initialiser la requête pour les examens
        $query = Exam::whereIn('training_session_id', $allRelevantSessionIds)
            ->where('is_published', true)
            ->with(['trainingSession', 'questions']);

        // Filtrer par type d'examen si spécifié
        if ($request->has('exam_type') && !empty($request->exam_type)) {
            $query->where('exam_type', $request->exam_type);
        }

        // Filtrer par statut si spécifié
        if ($request->has('status') && !empty($request->status)) {
            if ($request->status === 'upcoming') {
                $query->where('available_from', '>', now());
            } elseif ($request->status === 'available') {
                $query->where(function($q) {
                    $q->whereNull('available_from')
                      ->orWhere('available_from', '<=', now());
                })->where(function($q) {
                    $q->whereNull('available_until')
                      ->orWhere('available_until', '>=', now());
                });
            } elseif ($request->status === 'expired') {
                $query->where('available_until', '<', now());
            }
        }
        // Nous ne filtrons plus par défaut pour afficher tous les examens quand aucun filtre n'est appliqué

        // Récupérer les examens
        $allExams = $query->orderBy('created_at', 'desc')->get();

        // Récupérer les résultats d'examen de l'apprenant
        $examResults = ExamResult::where('user_id', $student->id)
            ->with('exam')
            ->get()
            ->groupBy('exam_id');

        // Filter out retake exams that shouldn't be visible for taking new attempts
        // BUT always include exams that have completed results (so students can see their past results)
        $exams = $allExams->filter(function($exam) use ($student, $examResults) {
            // If it's not a retake exam, always show it
            if ($exam->exam_type !== 'certification_rattrapage') {
                return true;
            }

            // Check if student has any results for this retake exam
            $retakeResults = $examResults->get($exam->id, collect([]));
            $hasRetakeResults = $retakeResults->isNotEmpty();

            // If student has completed this retake exam, always show it (so they can see results)
            if ($hasRetakeResults) {
                return true;
            }

            // For retake exams without results, only show if student has failed the corresponding certification
            $certificationExam = Exam::where('training_session_id', $exam->training_session_id)
                ->where('exam_type', 'certification')
                ->first();

            if (!$certificationExam) {
                return false; // No certification exam found, don't show retake
            }

            // Check if student has failed the certification exam
            $certificationResults = $examResults->get($certificationExam->id, collect([]));
            $hasFailed = $certificationResults->contains('passed', false);
            $hasPassedCertification = $certificationResults->contains('passed', true);

            // Show retake only if student has failed certification and hasn't passed certification
            return $hasFailed && !$hasPassedCertification;
        });

        // Préparer les données pour la vue
        $examData = $exams->map(function($exam) use ($examResults, $activeSessionIds) {
            $results = $examResults->get($exam->id, collect([]));
            $latestResult = $results->sortByDesc('created_at')->first();
            $attempts = $results->count();
            $hasPassed = $results->contains('passed', true);
            $hasFailed = $results->contains('passed', false);
            $inProgress = $results->contains(function($result) {
                return $result->status === 'in_progress';
            });

            // Vérifier si la session est active
            $isSessionActive = in_array($exam->training_session_id, $activeSessionIds);

            $status = $this->getExamStatus($exam, $hasPassed, $hasFailed, $inProgress, $attempts, $isSessionActive);

            return [
                'id' => $exam->id,
                'title' => $exam->title,
                'description' => $exam->description,
                'exam_type' => $exam->exam_type,
                'training_session' => $exam->trainingSession->title,
                'duration_minutes' => $exam->duration_minutes,
                'passing_score' => $exam->passing_score,
                'question_count' => $exam->questions->count(),
                'available_from' => $exam->available_from,
                'available_until' => $exam->available_until,
                'attempts' => $attempts,
                'has_passed' => $hasPassed,
                'has_failed' => $hasFailed,
                'in_progress' => $inProgress,
                'latest_score' => $latestResult ? $latestResult->score : null,
                'latest_attempt' => $latestResult ? $latestResult->created_at : null,
                'latest_result_id' => $latestResult ? $latestResult->id : null,
                'status' => $status,
                'session_active' => $isSessionActive,
            ];
        });

        // Organiser les examens par catégorie
        $upcomingExams = $examData->filter(function($exam) {
            return $exam['status'] === 'upcoming';
        });

        $inProgressExams = $examData->filter(function($exam) {
            return $exam['status'] === 'in_progress';
        });

        $availableExams = $examData->filter(function($exam) {
            return $exam['status'] === 'available';
        });

        $completedExams = $examData->filter(function($exam) {
            return in_array($exam['status'], ['passed', 'failed']);
        });







        // Retourner la vue avec les examens
        return Inertia::render('Student/Exams/Index', [
            'upcomingExams' => $upcomingExams,
            'inProgressExams' => $inProgressExams,
            'availableExams' => $availableExams,
            'completedExams' => $completedExams,
            'filters' => [
                'exam_type' => $request->exam_type ?? '',
                'status' => $request->status ?? '',
            ],
            'examTypes' => [
                ['value' => 'certification', 'label' => 'Certification'],
                ['value' => 'certification_rattrapage', 'label' => 'Certification de rattrapage'],
                ['value' => 'evaluation', 'label' => 'Évaluation'],
                ['value' => 'practice', 'label' => 'Entraînement'],
                ['value' => 'quiz', 'label' => 'Quiz'],
            ],
            'statusOptions' => [
                ['value' => 'upcoming', 'label' => 'À venir'],
                ['value' => 'available', 'label' => 'Disponible'],
                ['value' => 'expired', 'label' => 'Expiré'],
                ['value' => 'passed', 'label' => 'Réussi'],
                ['value' => 'failed', 'label' => 'Échoué'],
            ],
        ]);
    }

    /**
     * Affiche les détails d'un examen et permet de le passer
     */
    public function show(string $id)
    {
        // Récupérer l'utilisateur connecté (apprenant)
        $student = Auth::user();

        // Récupérer l'examen avec ses questions
        $exam = Exam::with(['trainingSession', 'questions' => function($query) {
            $query->orderBy('order');
        }])->findOrFail($id);

        // Vérifier que la session est active
        if (!$exam->trainingSession->active) {
            return redirect()->route('student.exams.index')
                ->with('error', 'Cet examen appartient à une session inactive et n\'est pas disponible.');
        }

        // Vérifier que l'apprenant est inscrit à la session de formation
        $enrollment = Enrollment::where('user_id', $student->id)
            ->where('training_session_id', $exam->training_session_id)
            ->whereIn('status', ['approved', 'completed'])
            ->first();

        if (!$enrollment) {
            return redirect()->route('student.dashboard')
                ->with('error', 'Vous n\'êtes pas inscrit à cette session de formation.');
        }

        // Vérifier que l'examen est publié et disponible
        if (!$exam->is_published) {
            return redirect()->route('student.exams.index')
                ->with('error', 'Cet examen n\'est pas disponible.');
        }

        // Vérifier les dates de disponibilité
        $now = now();
        if ($exam->available_from && $exam->available_from > $now) {
            return redirect()->route('student.exams.index')
                ->with('error', 'Cet examen n\'est pas encore disponible.');
        }
        if ($exam->available_until && $exam->available_until < $now) {
            return redirect()->route('student.exams.index')
                ->with('error', 'Cet examen n\'est plus disponible.');
        }

        // Récupérer les tentatives précédentes
        $previousAttempts = ExamResult::where('user_id', $student->id)
            ->where('exam_id', $exam->id)
            ->orderBy('created_at', 'desc')
            ->get();

        // Vérifier si l'apprenant a déjà réussi l'examen
        $hasPassed = $previousAttempts->contains('passed', true);

        // Vérifier si l'apprenant a déjà passé l'examen (pour tous les types sauf certification_rattrapage)
        if ($previousAttempts->count() > 0 && $exam->exam_type !== 'certification_rattrapage') {
            // Si l'examen est de type certification et que l'apprenant a échoué, il peut passer un examen de rattrapage
            if ($exam->exam_type === 'certification' && !$hasPassed) {
                // Rediriger vers la page des résultats avec un message
                return redirect()->route('student.exams.results', $previousAttempts->first()->id)
                    ->with('message', 'Vous avez déjà passé cet examen de certification. Vous pouvez passer un examen de rattrapage.');
            }
            // Pour les autres types d'examen, l'apprenant ne peut pas repasser l'examen
            else {
                return redirect()->route('student.exams.index')
                    ->with('error', 'Vous avez déjà passé cet examen et ne pouvez pas le repasser.');
            }
        }

        // Si c'est un examen de rattrapage, vérifier que l'apprenant a échoué à un examen de certification
        if ($exam->exam_type === 'certification_rattrapage') {
            // Trouver l'examen de certification correspondant
            $certificationExam = Exam::where('training_session_id', $exam->training_session_id)
                ->where('exam_type', 'certification')
                ->first();

            if (!$certificationExam) {
                return redirect()->route('student.exams.index')
                    ->with('error', 'Aucun examen de certification trouvé pour cette session de formation.');
            }

            // Vérifier si l'apprenant a échoué à l'examen de certification
            $certificationResults = ExamResult::where('user_id', $student->id)
                ->where('exam_id', $certificationExam->id)
                ->orderBy('created_at', 'desc')
                ->get();

            $hasFailed = $certificationResults->contains('passed', false);
            $hasPassedCertification = $certificationResults->contains('passed', true);

            if (!$hasFailed || $hasPassedCertification) {
                return redirect()->route('student.exams.index')
                    ->with('error', 'Vous ne pouvez pas passer cet examen de rattrapage car vous n\'avez pas échoué à l\'examen de certification ou vous l\'avez déjà réussi.');
            }
        }

        // Vérifier si l'apprenant a une tentative en cours
        $inProgressAttempt = $previousAttempts->first(function($attempt) {
            return $attempt->status === 'in_progress';
        });

        // Préparer les questions pour l'interface
        $questions = $exam->questions->map(function($question) {
            return [
                'id' => $question->id,
                'question_text' => $question->question_text,
                'question_type' => $question->question_type,
                'options' => $question->options,
                'points' => $question->points,
                'file_type_allowed' => $question->file_type_allowed,
            ];
        });

        // Retourner la vue avec l'examen et les questions
        return Inertia::render('Student/Exams/Show', [
            'exam' => [
                'id' => $exam->id,
                'title' => $exam->title,
                'description' => $exam->description,
                'exam_type' => $exam->exam_type,
                'training_session' => $exam->trainingSession->title,
                'duration_minutes' => $exam->duration_minutes,
                'passing_score' => $exam->passing_score,
                'instructions' => $exam->instructions,
            ],
            'questions' => $questions,
            'previousAttempts' => $previousAttempts,
            'hasPassed' => $hasPassed,
            'inProgressAttempt' => $inProgressAttempt,
        ]);
    }

    /**
     * Soumet les réponses d'un examen
     */
    public function submit(Request $request, string $id)
    {
        // Si la méthode est GET, rediriger vers la page de l'examen
        if ($request->isMethod('get')) {
            return redirect()->route('student.exams.show', $id)
                ->with('error', 'Vous devez soumettre l\'examen via le formulaire.');
        }

        try {
            // Récupérer l'utilisateur connecté (apprenant)
            $student = Auth::user();

            // Récupérer l'examen avec ses questions
            $exam = Exam::with(['questions', 'trainingSession'])->findOrFail($id);

            // Vérifier que la session est active
            if (!$exam->trainingSession->active) {
                return redirect()->route('student.exams.index')
                    ->with('error', 'Cet examen appartient à une session inactive et n\'est pas disponible.');
            }

            // Vérifier que l'apprenant est inscrit à la session de formation
            $enrollment = Enrollment::where('user_id', $student->id)
                ->where('training_session_id', $exam->training_session_id)
                ->whereIn('status', ['approved', 'completed'])
                ->first();

            if (!$enrollment) {
                return redirect()->route('student.dashboard')
                    ->with('error', 'Vous n\'êtes pas inscrit à cette session de formation.');
            }

            // Valider les données du formulaire avec des règles moins strictes
            $validated = $request->validate([
                'answers' => 'required',
                'started_at' => 'required',
                'completed_at' => 'required',
            ]);

            // S'assurer que answers est un tableau
            if (is_string($validated['answers'])) {
                $validated['answers'] = json_decode($validated['answers'], true);
            }

            // Convertir les dates en objets DateTime si elles sont des chaînes
            if (is_string($validated['started_at'])) {
                $validated['started_at'] = new \DateTime($validated['started_at']);
            }
            if (is_string($validated['completed_at'])) {
                $validated['completed_at'] = new \DateTime($validated['completed_at']);
            }

            // Calculer le score
            $totalPoints = 0;
            $earnedPoints = 0;
            $correctAnswers = 0;
            $incorrectAnswers = 0;

            foreach ($exam->questions as $question) {
                $totalPoints += $question->points;

                // Vérifier si la question a été répondue
                if (isset($validated['answers'][$question->id])) {
                    $userAnswer = $validated['answers'][$question->id];

                    // Traiter différemment selon le type de question
                    if ($question->question_type === 'multiple_choice') {
                        // Convertir les réponses en tableau si ce n'est pas déjà le cas
                        $userAnswerArray = is_array($userAnswer) ? $userAnswer : [$userAnswer];

                        // Récupérer les options correctes depuis la base de données
                        $correctOptionsArray = [];
                        if (property_exists($question, 'correct_options')) {
                            $correctOptionsArray = is_array($question->correct_options) ? $question->correct_options : [$question->correct_options];
                        } elseif (isset($question->correct_answer) && !empty($question->correct_answer)) {
                            // Si correct_options n'existe pas mais correct_answer existe
                            $correctOptionsArray = is_array($question->correct_answer) ? $question->correct_answer : [$question->correct_answer];
                        }

                        // Vérifier si la réponse est correcte
                        // Pour l'instant, nous considérons qu'une réponse est correcte si elle contient au moins une option correcte
                        $isCorrect = false;
                        if (!empty($userAnswerArray) && !empty($correctOptionsArray)) {
                            // Vérifier si au moins une option sélectionnée est correcte
                            foreach ($userAnswerArray as $option) {
                                if (in_array($option, $correctOptionsArray)) {
                                    $isCorrect = true;
                                    break;
                                }
                            }
                        }

                        if ($isCorrect) {
                            $earnedPoints += $question->points;
                            $correctAnswers++;
                        } else {
                            $incorrectAnswers++;
                        }
                    } elseif ($question->question_type === 'text') {
                        // Pour les questions à texte libre, le formateur devra évaluer manuellement
                        // On ne compte pas ces points pour le moment
                        $incorrectAnswers++;
                    } elseif ($question->question_type === 'file_upload') {
                        // Pour les questions avec upload de fichier, le formateur devra évaluer manuellement
                        // On ne compte pas ces points pour le moment
                        $incorrectAnswers++;
                    }
                } else {
                    $incorrectAnswers++;
                }
            }

            // Calculer le score en pourcentage
            $score = $totalPoints > 0 ? round(($earnedPoints / $totalPoints) * 100) : 0;

            // Logs pour le débogage
            \Log::info('Résultats de l\'examen:', [
                'total_points' => $totalPoints,
                'earned_points' => $earnedPoints,
                'correct_answers' => $correctAnswers,
                'incorrect_answers' => $incorrectAnswers,
                'score' => $score,
                'passing_score' => $exam->passing_score
            ]);

            // Déterminer si l'examen est réussi
            $passed = $score >= $exam->passing_score;

            // Créer un nouveau résultat d'examen
            $examResult = new ExamResult([
                'enrollment_id' => $enrollment->id,
                'user_id' => $student->id,
                'exam_id' => $exam->id,
                'score' => $score,
                'answers' => $validated['answers'],
                'passed' => $passed,
                'status' => $passed ? 'passed' : 'failed',
                'attempt_number' => ExamResult::where('user_id', $student->id)
                    ->where('exam_id', $exam->id)
                    ->count() + 1,
                'completed_at' => $validated['completed_at'],
            ]);

            $examResult->save();

            // Certificate creation is now handled by ExamResultObserver
            // when the exam result is created/updated with passed = true



            // Passer l'ID du résultat d'examen dans la réponse
            return redirect()->route('student.exams.results', $examResult->id)
                ->with('examResultId', $examResult->id)
                ->with('success', 'Examen soumis avec succès!');
        } catch (\Exception $e) {
            // Log l'erreur
            \Log::error('Erreur lors de la soumission de l\'examen: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            // Rediriger avec un message d'erreur
            return redirect()->back()->with('error', 'Une erreur est survenue lors de la soumission de l\'examen. Veuillez réessayer.');
        }
    }

    /**
     * Affiche les résultats d'un examen
     */
    public function results(string $id)
    {


        // Récupérer l'utilisateur connecté (apprenant)
        $student = Auth::user();

        // Récupérer le résultat d'examen avec ses relations
        $examResult = ExamResult::with([
            'exam',
            'exam.questions',
            'enrollment',
            'enrollment.trainingSession',
        ])
        ->where('user_id', $student->id)
        ->findOrFail($id);

        // Ajouter des logs pour le débogage
        \Log::info('Résultat d\'examen:', [
            'id' => $examResult->id,
            'score' => $examResult->score,
            'answers' => $examResult->answers,
        ]);

        // Ajouter des logs pour les questions
        foreach ($examResult->exam->questions as $question) {
            \Log::info('Question:', [
                'id' => $question->id,
                'question_text' => $question->question_text,
                'question_type' => $question->question_type,
                'options' => $question->options,
                'correct_options' => $question->correct_options ?? null,
                'correct_answer' => $question->correct_answer ?? null,
            ]);
        }

        // Vérifier si un certificat a été généré
        $certificate = Certificate::where('exam_result_id', $examResult->id)->first();

        // Retourner la vue avec le résultat d'examen
        return Inertia::render('Student/Exams/Results', [
            'examResult' => $examResult,
            'certificate' => $certificate,
        ]);
    }

    /**
     * Détermine le statut d'un examen pour l'apprenant
     */
    private function getExamStatus($exam, $hasPassed, $hasFailed, $inProgress, $attempts, $isSessionActive = true)
    {
        $now = now();

        // Si l'examen est en cours, c'est la priorité
        if ($inProgress) {
            return 'in_progress';
        }

        // Si l'examen a été réussi, il est marqué comme "passed"
        if ($hasPassed) {
            return 'passed';
        }

        // Si l'examen a été tenté mais échoué, il est marqué comme "failed"
        if ($hasFailed && $attempts > 0) {
            return 'failed';
        }

        // Si la session n'est pas active, l'examen n'est plus disponible
        if (!$isSessionActive) {
            return 'expired';
        }

        // Si l'examen a une date de début future, il est "upcoming"
        if ($exam->available_from && $exam->available_from > $now) {
            return 'upcoming';
        }

        // Si l'examen a une date de fin passée, il est "expired"
        if ($exam->available_until && $exam->available_until < $now) {
            return 'expired';
        }

        // Par défaut, si l'examen n'est pas dans les catégories ci-dessus, il est "available"
        // (disponible maintenant ou sans dates de disponibilité)
        return 'available';
    }

    /**
     * Générer le code QR pour un objet certificat.
     */
    private function generateQrCodeForCertificate(Certificate $certificate)
    {
        try {
            // Générer l'URL de vérification
            $verificationUrl = route('certificates.verify', ['number' => $certificate->certificate_number]);

            // Générer le code QR en SVG avec BaconQrCode
            $renderer = new ImageRenderer(
                new RendererStyle(200),
                new SvgImageBackEnd()
            );
            $writer = new Writer($renderer);
            $qrCode = $writer->writeString($verificationUrl);

            // Définir le chemin de stockage
            $qrCodePath = 'certificates/qr/' . $certificate->certificate_number . '.svg';

            // Créer le répertoire s'il n'existe pas
            $directory = 'certificates/qr';
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
            }

            // Sauvegarder le code QR
            Storage::disk('public')->put($qrCodePath, $qrCode);

            // Mettre à jour le certificat avec le chemin du code QR
            $certificate->update(['qr_code' => $qrCodePath]);

        } catch (\Exception $e) {
            // Log l'erreur mais ne pas faire échouer la génération du certificat
            \Log::error('Erreur lors de la génération du code QR: ' . $e->getMessage());
        }
    }
}
