<template>
  <Head title="Résultats d'examens" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Résultats d'examens
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Filtres -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <h4 class="font-medium mb-3">Filtres</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Recherche -->
                <div>
                  <InputLabel for="search" value="Recherche par étudiant" />
                  <TextInput
                    id="search"
                    v-model="searchQuery"
                    type="text"
                    class="mt-1 block w-full"
                    placeholder="Nom ou email..."
                  />
                </div>

                <!-- Session de formation -->
                <div>
                  <InputLabel for="training_session_id" value="Session de formation" />
                  <select
                    id="training_session_id"
                    v-model="sessionFilter"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  >
                    <option value="">Toutes les sessions</option>
                    <option v-for="session in trainingSessions" :key="session.id" :value="session.id">
                      {{ session.title }}
                    </option>
                  </select>
                </div>

                <!-- Examen -->
                <div>
                  <InputLabel for="exam_id" value="Examen" />
                  <select
                    id="exam_id"
                    v-model="examFilter"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  >
                    <option value="">Tous les examens</option>
                    <option v-for="exam in exams" :key="exam.id" :value="exam.id">
                      {{ exam.title }}
                    </option>
                  </select>
                </div>

                <!-- Statut -->
                <div>
                  <InputLabel for="status" value="Statut" />
                  <select
                    id="status"
                    v-model="statusFilter"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  >
                    <option value="">Tous les statuts</option>
                    <option v-for="status in statusOptions" :key="status.value" :value="status.value">
                      {{ status.label }}
                    </option>
                  </select>
                </div>
              </div>

              <!-- Boutons de filtrage -->
              <div class="flex justify-end mt-4">
                <SecondaryButton @click="resetFilters" class="mr-2">
                  Réinitialiser
                </SecondaryButton>
                <PrimaryButton @click="applyFilters">
                  Appliquer les filtres
                </PrimaryButton>
              </div>
            </div>

            <!-- Liste des résultats -->
            <div v-if="results.data.length > 0">
              <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200">
                  <thead>
                    <tr>
                      <th class="py-2 px-4 border-b text-left">Étudiant</th>
                      <th class="py-2 px-4 border-b text-left">Examen</th>
                      <th class="py-2 px-4 border-b text-left">Session</th>
                      <th class="py-2 px-4 border-b text-left">Score</th>
                      <th class="py-2 px-4 border-b text-left">Statut</th>
                      <th class="py-2 px-4 border-b text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="result in results.data" :key="result.id" class="hover:bg-gray-50">
                      <td class="py-2 px-4 border-b">{{ result.student.name }}</td>
                      <td class="py-2 px-4 border-b">{{ result.exam.title }}</td>
                      <td class="py-2 px-4 border-b">{{ result.exam.training_session.title }}</td>
                      <td class="py-2 px-4 border-b">
                        <span v-if="result.score !== null">{{ result.score }} / {{ result.total_points }}</span>
                        <span v-else>-</span>
                      </td>
                      <td class="py-2 px-4 border-b">
                        <span v-if="result.status === 'in_progress'" class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">En cours</span>
                        <span v-else-if="result.status === 'completed'" class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Terminé</span>
                        <span v-else-if="result.status === 'graded'" class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">Noté</span>
                        <span v-else-if="result.status === 'failed'" class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Échoué</span>
                        <span v-else-if="result.status === 'passed'" class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Réussi</span>
                      </td>
                      <td class="py-2 px-4 border-b text-center">
                        <div class="flex justify-center space-x-2">
                          <Link :href="route('trainer.exam-results.show', result.id)" class="text-blue-600 hover:text-blue-900">
                            Voir
                          </Link>
                          <Link v-if="['completed', 'graded'].includes(result.status)" :href="route('trainer.exam-results.edit', result.id)" class="text-indigo-600 hover:text-indigo-900">
                            Noter
                          </Link>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Pagination -->
              <div class="mt-6">
                <Pagination :links="results.links" />
              </div>
            </div>
            <div v-else class="text-center py-8">
              <p class="text-gray-500">Aucun résultat d'examen trouvé.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import Pagination from '@/Components/Pagination.vue';

// Props
const props = defineProps({
  results: Object,
  trainingSessions: Array,
  exams: Array,
  filters: Object,
  statusOptions: Array,
});

// État pour les filtres
const searchQuery = ref(props.filters?.search || '');
const sessionFilter = ref(props.filters?.training_session_id || '');
const examFilter = ref(props.filters?.exam_id || '');
const statusFilter = ref(props.filters?.status || '');

// Méthodes pour les filtres
const applyFilters = () => {
  const params = {};

  // Ajouter les filtres s'ils existent
  if (searchQuery.value) {
    params.search = searchQuery.value;
  }

  if (sessionFilter.value) {
    params.training_session_id = sessionFilter.value;
  }

  if (examFilter.value) {
    params.exam_id = examFilter.value;
  }

  if (statusFilter.value) {
    params.status = statusFilter.value;
  }

  // Naviguer vers l'URL avec les paramètres
  router.get(route('trainer.exam-results.index'), params, {
    preserveState: true,
    replace: true,
    preserveScroll: true
  });
};

const resetFilters = () => {
  searchQuery.value = '';
  sessionFilter.value = '';
  examFilter.value = '';
  statusFilter.value = '';

  // Naviguer vers l'URL sans paramètres
  router.get(route('trainer.exam-results.index'), {}, {
    preserveState: true,
    replace: true,
    preserveScroll: true
  });
};
</script>
