<?php

namespace App\Services;

use App\Models\Certificate;
use App\Models\ExamResult;
use App\Models\TrainingSession;
use App\Models\User;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Barryvdh\DomPDF\Facade\Pdf;

class CertificateService
{
    /**
     * Générer un certificat pour un résultat d'examen réussi
     *
     * @param ExamResult $examResult
     * @return Certificate
     */
    public function generateCertificate(ExamResult $examResult)
    {
        // Vérifier si l'examen a été réussi
        if (!$examResult->passed) {
            throw new \Exception('Impossible de générer un certificat pour un examen non réussi.');
        }

        // Vérifier si un certificat existe déjà
        $existingCertificate = Certificate::where('exam_result_id', $examResult->id)->first();
        if ($existingCertificate) {
            return $existingCertificate;
        }

        // Récupérer les données nécessaires
        $user = $examResult->user;
        $exam = $examResult->exam;
        $trainingSession = $exam->trainingSession;

        // Générer un numéro unique pour le certificat
        $enrollment = $examResult->enrollment;
        $certificateNumber = $this->generateCertificateNumber($enrollment->id);

        // Générer le QR code
        $qrCodePath = $this->generateQrCode($certificateNumber);

        // Générer le PDF du certificat
        $pdfPath = $this->generatePdf($user, $trainingSession, $certificateNumber, $qrCodePath);

        // Créer l'enregistrement du certificat dans la base de données
        $certificate = Certificate::create([
            'user_id' => $user->id,
            'training_session_id' => $trainingSession->id,
            'exam_result_id' => $examResult->id,
            'certificate_number' => $certificateNumber,
            'pdf_path' => $pdfPath,
            'qr_code' => $qrCodePath,
            'issue_date' => now(),
            'expiry_date' => null, // Pas de date d'expiration par défaut
        ]);

        return $certificate;
    }

    /**
     * Générer un numéro unique pour le certificat
     * Use the centralized method from Certificate model
     *
     * @param int $enrollmentId
     * @return string
     */
    private function generateCertificateNumber($enrollmentId)
    {
        return \App\Models\Certificate::generateCertificateNumber($enrollmentId);
    }

    /**
     * Générer un QR code pour le certificat
     *
     * @param string $certificateNumber
     * @return string
     */
    private function generateQrCode(string $certificateNumber)
    {
        $verificationUrl = route('certificates.verify', ['number' => $certificateNumber]);
        $qrCode = QrCode::format('png')->size(200)->generate($verificationUrl);

        $path = "certificates/qr/{$certificateNumber}.png";
        Storage::disk('public')->put($path, $qrCode);

        return $path;
    }

    /**
     * Générer le PDF du certificat
     *
     * @param User $user
     * @param TrainingSession $trainingSession
     * @param string $certificateNumber
     * @param string $qrCodePath
     * @return string
     */
    private function generatePdf(User $user, TrainingSession $trainingSession, string $certificateNumber, string $qrCodePath)
    {
        $data = [
            'user' => $user,
            'trainingSession' => $trainingSession,
            'certificateNumber' => $certificateNumber,
            'qrCodePath' => $qrCodePath,
            'issueDate' => now()->format('d/m/Y'),
        ];

        $pdf = PDF::loadView('certificates.template', $data);
        $pdfPath = "certificates/pdf/{$certificateNumber}.pdf";
        
        Storage::disk('public')->put($pdfPath, $pdf->output());

        return $pdfPath;
    }
}
