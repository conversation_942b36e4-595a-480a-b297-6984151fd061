<?php

require_once 'vendor/autoload.php';

// Initialiser l'application Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Récupérer le certificat ID 5
$certificate = App\Models\Certificate::find(5);

if (!$certificate) {
    echo "Certificat ID 5 non trouvé.\n";
    exit(1);
}

echo "Certificat trouvé:\n";
echo "- ID: " . $certificate->id . "\n";
echo "- Numéro: " . $certificate->certificate_number . "\n";
echo "- PDF Path: " . ($certificate->pdf_path ?? 'null') . "\n";

// Supprimer le PDF existant s'il existe
$pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';
echo "Chemin PDF calculé: " . $pdfPath . "\n";

if (Illuminate\Support\Facades\Storage::disk('public')->exists($pdfPath)) {
    echo "Suppression du PDF existant...\n";
    Illuminate\Support\Facades\Storage::disk('public')->delete($pdfPath);
    echo "PDF supprimé.\n";
} else {
    echo "Aucun PDF existant trouvé.\n";
}

// Réinitialiser le chemin du PDF dans la base de données
$certificate->update(['pdf_path' => null]);
echo "Chemin PDF réinitialisé dans la base de données.\n";

echo "Régénération terminée. Le prochain accès au certificat générera un nouveau PDF avec le design actuel.\n";
echo "Accédez à: http://127.0.0.1:8000/admin/certificates/5/view\n";
