<template>
    <div class="min-h-screen bg-gray-100">
        <Head :title="'Confirmation d\'inscription - Centre de Formation'" />

        <!-- Navigation -->
        <div class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <Link href="/" class="text-xl font-bold text-indigo-600">Centre de Formation</Link>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="ml-3 relative">
                            <Link :href="route('student.dashboard')" class="text-gray-700 hover:text-indigo-600">
                                Tableau de bord
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contenu principal -->
        <main class="py-10">
            <div class="max-w-3xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="text-center mb-8">
                            <div v-if="error" class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            </div>
                            <div v-else class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>

                            <h1 v-if="error" class="text-2xl font-semibold text-gray-900">Problème lors de l'inscription</h1>
                            <h1 v-else class="text-2xl font-semibold text-gray-900">Inscription confirmée !</h1>

                            <p v-if="error" class="mt-2 text-gray-600">{{ error }}</p>
                            <p v-else class="mt-2 text-gray-600">Votre demande d'inscription a été enregistrée avec succès.</p>
                        </div>

                        <!-- Détails de l'inscription -->
                        <div v-if="enrollment" class="bg-gray-50 rounded-lg p-6 mb-6">
                            <h2 class="text-lg font-medium text-gray-900 mb-4">Détails de votre inscription</h2>

                            <div v-if="enrollment.trainingSession" class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <p class="text-sm text-gray-600">Session :</p>
                                    <p class="font-medium">{{ enrollment.trainingSession.title }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Domaine :</p>
                                    <p class="font-medium">{{ enrollment.trainingSession.training_domain?.name }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Formateur :</p>
                                    <p class="font-medium">{{ enrollment.trainingSession.trainer?.name }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Dates :</p>
                                    <p class="font-medium">Du {{ formatDate(enrollment.trainingSession.start_date) }} au {{ formatDate(enrollment.trainingSession.end_date) }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Statut de l'inscription :</p>
                                    <p class="font-medium">
                                        <span v-if="enrollment.status === 'pending'" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            En attente
                                        </span>
                                        <span v-else-if="enrollment.status === 'approved'" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Approuvée
                                        </span>
                                        <span v-else-if="enrollment.status === 'rejected'" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Rejetée
                                        </span>
                                        <span v-else-if="enrollment.status === 'completed'" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Terminée
                                        </span>
                                    </p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Date d'inscription :</p>
                                    <p class="font-medium">{{ formatDate(enrollment.enrollment_date) }}</p>
                                </div>
                            </div>

                            <div v-else class="p-4 bg-yellow-50 rounded-md">
                                <p class="text-yellow-700">Les détails de la session ne sont pas disponibles.</p>
                            </div>

                            <div v-if="enrollment.notes" class="mt-4">
                                <p class="text-sm text-gray-600">Notes :</p>
                                <p class="text-gray-700">{{ enrollment.notes }}</p>
                            </div>
                        </div>

                        <div v-else class="bg-red-50 rounded-lg p-6 mb-6">
                            <p class="text-red-700">Les informations d'inscription ne sont pas disponibles.</p>
                        </div>

                        <!-- Prochaines étapes -->
                        <div v-if="enrollment" class="mb-6">
                            <h2 class="text-lg font-medium text-gray-900 mb-4">Prochaines étapes</h2>

                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-gray-700">Votre demande d'inscription est en cours d'examen par notre équipe.</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-gray-700">Vous recevrez un email de confirmation une fois votre inscription approuvée.</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-gray-700">Vous pouvez suivre l'état de votre inscription dans votre tableau de bord.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-center mt-8">
                            <Link :href="route('student.dashboard')" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Accéder à mon tableau de bord
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';

const props = defineProps({
    enrollment: Object,
    error: String,
});

// Fonction pour formater les dates
const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
};
</script>
