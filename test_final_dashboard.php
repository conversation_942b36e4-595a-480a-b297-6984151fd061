<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Http\Controllers\Student\DashboardController;
use Illuminate\Http\Request;

// Récupérer l'étudiant Sophie <PERSON>
$student = User::where('email', '<EMAIL>')->first();

if (!$student) {
    echo "Étudiant non trouvé\n";
    exit(1);
}

echo "=== TEST FINAL DU DASHBOARD ÉTUDIANT ===\n";
echo "Étudiant: {$student->name} ({$student->email})\n\n";

// Simuler une requête
$request = new Request();
$request->setUserResolver(function () use ($student) {
    return $student;
});

// Créer le contrôleur et récupérer les données
$controller = new DashboardController();

try {
    // Utiliser la réflexion pour accéder aux méthodes privées
    $reflection = new ReflectionClass($controller);
    
    // Test des niveaux complétés d'abord (méthode du modèle User)
    $completedLevels = $student->getCompletedLevelsByDepartment();

    // Test de getSessionsByDepartment
    $getSessionsByDepartmentMethod = $reflection->getMethod('getSessionsByDepartment');
    $getSessionsByDepartmentMethod->setAccessible(true);

    $sessionsByDepartment = $getSessionsByDepartmentMethod->invoke($controller, $student, $completedLevels);
    
    echo "=== SESSIONS PAR DÉPARTEMENT ===\n";
    
    foreach ($sessionsByDepartment as $department => $levels) {
        echo "\n🏢 DÉPARTEMENT: $department\n";
        echo str_repeat("-", 50) . "\n";
        
        foreach ($levels as $level => $sessions) {
            echo "\n  📚 $level:\n";
            
            if (empty($sessions)) {
                echo "    ❌ Aucune session disponible\n";
                continue;
            }
            
            foreach ($sessions as $sessionData) {
                $session = $sessionData['session'];
                $state = $sessionData['state'];
                $reason = $sessionData['reason'];
                $canEnroll = $sessionData['canEnroll'] ? '✅' : '❌';
                
                // Icône selon l'état
                $stateIcon = match($state) {
                    'available' => '🟢',
                    'enrolled' => '✅',
                    'pending' => '🟡',
                    'completed' => '🏆',
                    'rejected' => '❌',
                    'locked' => '🔒',
                    'full' => '🚫',
                    'started' => '▶️',
                    default => '❓'
                };
                
                echo "    $stateIcon {$session->title}\n";
                echo "      État: $state | Inscription: $canEnroll\n";
                echo "      Raison: $reason\n";
                echo "      Date: {$session->start_date} | Prix: {$session->price}€\n";
                echo "      Participants: {$session->enrollments_count}/{$session->max_participants}\n";
                echo "\n";
            }
        }
    }
    
    // Affichage des niveaux complétés (déjà récupérés plus haut)
    
    echo "\n=== NIVEAUX COMPLÉTÉS ===\n";
    foreach ($completedLevels as $department => $levels) {
        echo "🏢 $department: " . implode(', ', $levels) . "\n";
    }
    
    // Test des prérequis
    echo "\n=== TEST DES PRÉREQUIS ===\n";
    $departments = ['Secourisme', 'Langue', 'Formation à la carte'];
    $levels = ['Niveau 1', 'Niveau 2', 'Niveau 3'];
    
    foreach ($departments as $dept) {
        echo "\n🏢 $dept:\n";
        foreach ($levels as $level) {
            $canEnroll = $student->canEnrollInLevel($dept, $level);
            $status = $canEnroll ? '✅ Autorisé' : '❌ Bloqué';
            echo "  $level: $status\n";
        }
    }
    
    echo "\n=== RÉSUMÉ DES INSCRIPTIONS ===\n";
    $enrollments = $student->enrollments()->with('trainingSession')->get();
    
    foreach ($enrollments as $enrollment) {
        $session = $enrollment->trainingSession;
        $statusIcon = match($enrollment->status) {
            'approved' => '✅',
            'pending' => '🟡',
            'completed' => '🏆',
            'rejected' => '❌',
            default => '❓'
        };
        
        echo "$statusIcon {$session->department} - {$session->level}: {$session->title}\n";
        echo "   Statut: {$enrollment->status} | Date: {$enrollment->enrollment_date}\n\n";
    }
    
    echo "=== TEST TERMINÉ AVEC SUCCÈS ===\n";
    echo "Le dashboard étudiant fonctionne correctement !\n";
    echo "Vous pouvez maintenant accéder à: http://localhost:8000/student/dashboard\n";
    
} catch (Exception $e) {
    echo "❌ ERREUR: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
