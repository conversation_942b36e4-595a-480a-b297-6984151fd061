<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Certificate;
use App\Models\Enrollment;
use App\Models\TrainingSession;
use App\Models\TrainingDomain;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use BaconQrCode\Renderer\ImageRenderer;
use BaconQrCode\Renderer\Image\SvgImageBackEnd;
use BaconQrCode\Renderer\RendererStyle\RendererStyle;
use BaconQrCode\Writer;
use Barryvdh\DomPDF\Facade\Pdf;

class CertificateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Initialiser la requête
        $query = Certificate::with(['enrollment', 'enrollment.user', 'enrollment.trainingSession', 'enrollment.trainingSession.trainingDomain']);

        // Appliquer la recherche si elle existe
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->whereHas('enrollment.user', function($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                  ->orWhere('email', 'like', $searchTerm);
            });
        }

        // Filtrer par session de formation
        if ($request->has('training_session_id') && !empty($request->training_session_id)) {
            $query->whereHas('enrollment', function($q) use ($request) {
                $q->where('training_session_id', $request->training_session_id);
            });
        }

        // Filtrer par domaine de formation
        if ($request->has('domain_id') && !empty($request->domain_id)) {
            $query->whereHas('enrollment.trainingSession', function($q) use ($request) {
                $q->where('training_domain_id', $request->domain_id);
            });
        }

        // Filtrer par statut
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Filtrer par date d'émission (début)
        if ($request->has('start_date') && !empty($request->start_date)) {
            $query->whereDate('issued_at', '>=', $request->start_date);
        }

        // Filtrer par date d'émission (fin)
        if ($request->has('end_date') && !empty($request->end_date)) {
            $query->whereDate('issued_at', '<=', $request->end_date);
        }

        // Récupérer les certificats avec pagination
        $certificates = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Récupérer toutes les sessions de formation pour le filtre
        $trainingSessions = TrainingSession::orderBy('title')->get();

        // Récupérer tous les domaines de formation pour le filtre
        $domains = TrainingDomain::orderBy('name')->get();

        // Retourner la vue avec les certificats et les filtres
        return Inertia::render('Admin/Certificates/Index', [
            'certificates' => $certificates,
            'trainingSessions' => $trainingSessions,
            'domains' => $domains,
            'filters' => [
                'search' => $request->search ?? '',
                'training_session_id' => $request->training_session_id ?? '',
                'domain_id' => $request->domain_id ?? '',
                'status' => $request->status ?? '',
                'start_date' => $request->start_date ?? '',
                'end_date' => $request->end_date ?? '',
            ]
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Récupérer les inscriptions approuvées ou complétées qui n'ont pas encore de certificat
        $enrollments = Enrollment::with(['user', 'trainingSession'])
            ->whereIn('status', ['approved', 'completed'])
            ->whereDoesntHave('certificate')
            ->orderBy('created_at', 'desc')
            ->get();

        // Retourner la vue pour créer un nouveau certificat
        return Inertia::render('Admin/Certificates/Create', [
            'enrollments' => $enrollments,
            'statusOptions' => Certificate::getStatusOptions()
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'enrollment_id' => 'required|exists:enrollments,id',
            'issued_at' => 'required|date',
            'expiry_date' => 'nullable|date|after:issued_at',
            'status' => 'required|in:draft,issued,active,revoked,expired',
            'signature_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Vérifier si un certificat existe déjà pour cette inscription
        $existingCertificate = Certificate::where('enrollment_id', $validated['enrollment_id'])->first();
        if ($existingCertificate) {
            return redirect()->back()->withErrors([
                'enrollment_id' => 'Un certificat existe déjà pour cette inscription.'
            ]);
        }

        // Générer un numéro de certificat unique
        $validated['certificate_number'] = Certificate::generateCertificateNumber($validated['enrollment_id']);

        // Gérer l'upload de l'image de signature si elle existe
        if ($request->hasFile('signature_image')) {
            $signaturePath = $request->file('signature_image')->store('signatures', 'public');
            $validated['signature_image'] = $signaturePath;
        }

        // Récupérer l'inscription pour obtenir l'ID de l'utilisateur
        $enrollment = Enrollment::findOrFail($validated['enrollment_id']);
        $validated['user_id'] = $enrollment->user_id;
        $validated['issue_date'] = $validated['issued_at'];

        // Créer le certificat
        $certificate = Certificate::create($validated);

        // Générer automatiquement le code QR
        $this->generateQrCodeForCertificate($certificate);

        // Générer le PDF du certificat
        $this->generatePdf($certificate);

        // Rediriger vers la liste des certificats avec un message de succès
        return redirect()->route('admin.certificates.index')
            ->with('success', 'Certificat créé avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Récupérer le certificat avec ses relations
        $certificate = Certificate::with([
            'enrollment',
            'enrollment.user',
            'enrollment.trainingSession',
            'enrollment.trainingSession.trainingDomain',
            'enrollment.trainingSession.trainer'
        ])->findOrFail($id);

        // Retourner la vue avec le certificat
        return Inertia::render('Admin/Certificates/Show', [
            'certificate' => $certificate
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Récupérer le certificat
        $certificate = Certificate::with(['enrollment', 'enrollment.user', 'enrollment.trainingSession'])->findOrFail($id);

        // Ajouter les dates formatées
        $certificate->formatted_issued_at = $certificate->formatted_issued_at;
        $certificate->formatted_expiry_date = $certificate->formatted_expiry_date;

        // Retourner la vue pour éditer le certificat
        return Inertia::render('Admin/Certificates/Edit', [
            'certificate' => $certificate,
            'statusOptions' => Certificate::getStatusOptions()
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Récupérer le certificat
        $certificate = Certificate::findOrFail($id);

        // Déboguer toutes les données reçues
        \Log::info('Toutes les données reçues:', $request->all());

        // Valider les données du formulaire
        $validated = $request->validate([
            'issued_at' => 'required|date',
            'expiry_date' => 'nullable|date|after:issued_at',
            'status' => 'required|in:draft,issued,active,revoked,expired',
            'signature_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Gérer l'upload de l'image de signature si elle existe
        if ($request->hasFile('signature_image')) {
            // Supprimer l'ancienne image si elle existe
            if ($certificate->signature_image) {
                Storage::disk('public')->delete($certificate->signature_image);
            }

            $signaturePath = $request->file('signature_image')->store('signatures', 'public');
            $validated['signature_image'] = $signaturePath;
        }

        // Créer un tableau de données pour la mise à jour
        $updateData = [
            'issued_at' => $validated['issued_at'],
            'issue_date' => $validated['issued_at'],
            'status' => $validated['status']
        ];

        // Ajouter la date d'expiration si elle existe
        if (!empty($validated['expiry_date'])) {
            $updateData['expiry_date'] = $validated['expiry_date'];
        }

        // Ajouter l'image de signature si elle existe
        if (isset($validated['signature_image'])) {
            $updateData['signature_image'] = $validated['signature_image'];
        }

        // Déboguer les données de mise à jour
        \Log::info('Données de mise à jour:', $updateData);

        // Mettre à jour le certificat
        $certificate->update($updateData);

        // Déboguer le certificat après mise à jour
        \Log::info('Certificat après mise à jour:', [
            'id' => $certificate->id,
            'issued_at' => $certificate->issued_at,
            'expiry_date' => $certificate->expiry_date,
            'status' => $certificate->status,
        ]);

        // Supprimer l'ancien PDF pour forcer la régénération avec les nouvelles données
        $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';
        if (Storage::disk('public')->exists($pdfPath)) {
            Storage::disk('public')->delete($pdfPath);
        }
        $certificate->update(['pdf_path' => null]);

        // Régénérer le PDF du certificat avec les nouvelles données
        $this->generatePdf($certificate);

        // Rediriger vers la liste des certificats avec un message de succès
        return redirect()->route('admin.certificates.index')
            ->with('success', 'Certificat mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Récupérer le certificat
        $certificate = Certificate::findOrFail($id);

        // Supprimer l'image de signature si elle existe
        if ($certificate->signature_image) {
            Storage::disk('public')->delete($certificate->signature_image);
        }

        // Supprimer le PDF du certificat s'il existe
        $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';
        if (Storage::disk('public')->exists($pdfPath)) {
            Storage::disk('public')->delete($pdfPath);
        }

        // Supprimer le certificat
        $certificate->delete();

        // Rediriger vers la liste des certificats avec un message de succès
        return redirect()->route('admin.certificates.index')
            ->with('success', 'Certificat supprimé avec succès.');
    }

    /**
     * Générer le PDF du certificat.
     */
    public function generatePdf(Certificate $certificate)
    {
        try {
            // Augmenter la limite de temps d'exécution à 300 secondes
            set_time_limit(300);

            // Récupérer les données nécessaires pour le certificat
            $enrollment = $certificate->enrollment;
            $user = $enrollment->user;
            $trainingSession = $enrollment->trainingSession;
            $trainingDomain = $trainingSession->trainingDomain;

            // S'assurer que le certificat a un numéro (ne jamais modifier un numéro existant)
            if (empty($certificate->certificate_number)) {
                // Use the centralized certificate number generation method
                $certificate->certificate_number = Certificate::generateCertificateNumber($certificate->enrollment_id);
                $certificate->save();
                Log::warning("Certificate {$certificate->id} had no certificate number, generated: {$certificate->certificate_number}");
            }

            // Générer le QR code s'il n'existe pas
            if (empty($certificate->qr_code)) {
                $this->generateQrCodeForCertificate($certificate);
            }

            // S'assurer que l'utilisateur a un numéro de carte d'identité
            if (empty($user->id_card_number)) {
                $user->id_card_number = str_pad(rand(10000000, 99999999), 8, '0', STR_PAD_LEFT);
                $user->save();
            }

            // Préparer les données pour la vue (très simplifié)
            $data = [
                'certificate' => $certificate,
                'enrollment' => $enrollment,
                'user' => $user,
                'trainingSession' => $trainingSession,
                'trainingDomain' => $trainingDomain
            ];

            // Utiliser le wrapper DomPDF de Laravel
            $pdf = app('dompdf.wrapper');
            $pdf->loadView('certificates.pdf', $data);
            $pdf->setPaper('a4', 'portrait');
            $pdf->setOptions([
                'defaultFont' => 'DejaVu Sans',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => false,
                'isPhpEnabled' => true,
                'fontSubsetting' => false,
                'debugKeepTemp' => false,
                'debugCss' => false,
                'debugLayout' => false,
                'debugLayoutLines' => false,
                'debugLayoutBlocks' => false,
                'debugLayoutInline' => false,
                'debugLayoutPaddingBox' => false
            ]);

            // Sauvegarder le PDF
            $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';
            Storage::disk('public')->put($pdfPath, $pdf->output());

            // Mettre à jour le chemin du PDF dans le certificat
            $certificate->update(['pdf_path' => $pdfPath]);

            return $pdf;

        } catch (\Exception $e) {
            // En cas d'erreur, enregistrer l'erreur dans les logs
            \Log::error('Erreur lors de la génération du PDF: ' . $e->getMessage());

            // Créer un PDF simple avec un message d'erreur
            $errorHtml = '<html><body><h1>Erreur lors de la génération du certificat</h1><p>Une erreur est survenue lors de la génération du certificat. Veuillez réessayer ultérieurement.</p><p>Erreur: ' . $e->getMessage() . '</p></body></html>';
            $pdf = app('dompdf.wrapper');
            $pdf->loadHTML($errorHtml);

            return $pdf;
        }
    }

    /**
     * Télécharger le PDF du certificat.
     */
    public function download(string $id)
    {
        try {
            // Récupérer le certificat
            $certificate = Certificate::findOrFail($id);

            // Vérifier si le PDF existe
            $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';
            $filename = 'certificat_' . $certificate->certificate_number . '.pdf';

            if (!Storage::disk('public')->exists($pdfPath)) {
                // Générer le PDF s'il n'existe pas
                $this->generatePdf($certificate);

                // Vérifier à nouveau si le PDF a été créé
                if (!Storage::disk('public')->exists($pdfPath)) {
                    throw new \Exception("Impossible de générer le PDF");
                }
            }

            // Obtenir le chemin physique complet du fichier
            $fullPath = Storage::disk('public')->path($pdfPath);

            // Vérifier que le fichier existe physiquement
            if (!file_exists($fullPath)) {
                throw new \Exception("Le fichier PDF n'existe pas sur le disque: " . $fullPath);
            }

            // Utiliser la fonction download de Laravel qui est optimisée pour les téléchargements
            return response()->download($fullPath, $filename, [
                'Content-Type' => 'application/pdf',
                'X-Inertia-Location' => null, // Désactiver l'interception par Inertia.js
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);

        } catch (\Exception $e) {
            // En cas d'erreur, enregistrer l'erreur dans les logs
            \Log::error('Erreur lors du téléchargement du PDF: ' . $e->getMessage());
            \Log::error('Trace: ' . $e->getTraceAsString());

            // Rediriger vers la page précédente avec un message d'erreur
            return back()->with('error', 'Une erreur est survenue lors du téléchargement du certificat: ' . $e->getMessage());
        }
    }

    /**
     * Téléchargement direct du PDF sans passer par Inertia.js
     * Cette méthode est appelée par une route spéciale qui n'utilise pas le middleware Inertia
     */
    public function directDownload(string $id)
    {
        try {
            // Récupérer le certificat
            $certificate = Certificate::findOrFail($id);

            // Vérifier si l'utilisateur a le droit d'accéder à ce certificat
            if (!auth()->user()->isAdmin() && auth()->id() !== $certificate->user_id) {
                abort(403, 'Vous n\'êtes pas autorisé à télécharger ce certificat.');
            }

            // Vérifier si le PDF existe
            $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';
            $filename = 'certificat_' . $certificate->certificate_number . '.pdf';

            if (!Storage::disk('public')->exists($pdfPath)) {
                // Générer le PDF s'il n'existe pas
                $this->generatePdf($certificate);

                // Vérifier à nouveau si le PDF a été créé
                if (!Storage::disk('public')->exists($pdfPath)) {
                    throw new \Exception("Impossible de générer le PDF");
                }
            }

            // Obtenir le chemin physique complet du fichier
            $fullPath = Storage::disk('public')->path($pdfPath);

            // Vérifier que le fichier existe physiquement
            if (!file_exists($fullPath)) {
                throw new \Exception("Le fichier PDF n'existe pas sur le disque: " . $fullPath);
            }

            // Forcer le téléchargement avec des en-têtes spécifiques
            return response()->download($fullPath, $filename, [
                'Content-Type' => 'application/pdf',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);

        } catch (\Exception $e) {
            // En cas d'erreur, enregistrer l'erreur dans les logs
            \Log::error('Erreur lors du téléchargement direct du PDF: ' . $e->getMessage());
            \Log::error('Trace: ' . $e->getTraceAsString());

            // Rediriger vers la page précédente avec un message d'erreur
            return back()->with('error', 'Une erreur est survenue lors du téléchargement du certificat: ' . $e->getMessage());
        }
    }

    /**
     * Afficher le PDF du certificat dans le navigateur.
     */
    public function view(string $id)
    {
        try {
            // Récupérer le certificat
            $certificate = Certificate::findOrFail($id);

            // Vérifier si le PDF existe
            $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';
            $filename = 'certificat_' . $certificate->certificate_number . '.pdf';

            if (!Storage::disk('public')->exists($pdfPath)) {
                // Générer le PDF s'il n'existe pas
                $this->generatePdf($certificate);

                // Vérifier à nouveau si le PDF a été créé
                if (!Storage::disk('public')->exists($pdfPath)) {
                    throw new \Exception("Impossible de générer le PDF");
                }
            }

            // Obtenir le chemin physique complet du fichier
            $fullPath = Storage::disk('public')->path($pdfPath);

            // Vérifier que le fichier existe physiquement
            if (!file_exists($fullPath)) {
                throw new \Exception("Le fichier PDF n'existe pas sur le disque: " . $fullPath);
            }

            // Retourner le fichier en utilisant la méthode file de la réponse
            return response()->file($fullPath, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="' . $filename . '"',
                'X-Inertia-Location' => null, // Désactiver l'interception par Inertia.js
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);

        } catch (\Exception $e) {
            // En cas d'erreur, enregistrer l'erreur dans les logs
            \Log::error('Erreur lors de l\'affichage du PDF: ' . $e->getMessage());
            \Log::error('Trace: ' . $e->getTraceAsString());

            // Rediriger vers la page précédente avec un message d'erreur
            return back()->with('error', 'Une erreur est survenue lors de l\'affichage du certificat: ' . $e->getMessage());
        }
    }

    /**
     * Obtenir l'URL publique du PDF du certificat.
     */
    public function getPdfUrl(string $id)
    {
        try {
            // Récupérer le certificat
            $certificate = Certificate::findOrFail($id);

            // Vérifier si le PDF existe
            $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';

            if (!Storage::disk('public')->exists($pdfPath)) {
                // Générer le PDF s'il n'existe pas
                $this->generatePdf($certificate);

                // Vérifier à nouveau si le PDF a été créé
                if (!Storage::disk('public')->exists($pdfPath)) {
                    throw new \Exception("Impossible de générer le PDF");
                }
            }

            // Retourner l'URL publique du PDF
            return response()->json([
                'url' => Storage::disk('public')->url($pdfPath),
                'filename' => 'certificat_' . $certificate->certificate_number . '.pdf'
            ]);

        } catch (\Exception $e) {
            // En cas d'erreur, enregistrer l'erreur dans les logs
            \Log::error('Erreur lors de la récupération de l\'URL du PDF: ' . $e->getMessage());
            \Log::error('Trace: ' . $e->getTraceAsString());

            // Retourner une erreur JSON
            return response()->json(['error' => 'Une erreur est survenue: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Générer le code QR pour un certificat.
     */
    public function generateQrCode(string $id)
    {
        try {
            // Récupérer le certificat
            $certificate = Certificate::findOrFail($id);

            // Générer l'URL de vérification
            $verificationUrl = route('certificates.verify', ['number' => $certificate->certificate_number]);

            // Générer le code QR en SVG avec BaconQrCode
            $renderer = new ImageRenderer(
                new RendererStyle(200),
                new SvgImageBackEnd()
            );
            $writer = new Writer($renderer);
            $qrCode = $writer->writeString($verificationUrl);

            // Définir le chemin de stockage
            $qrCodePath = 'certificates/qr/' . $certificate->certificate_number . '.svg';

            // Créer le répertoire s'il n'existe pas
            $directory = 'certificates/qr';
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
            }

            // Sauvegarder le code QR
            Storage::disk('public')->put($qrCodePath, $qrCode);

            // Mettre à jour le certificat avec le chemin du code QR
            $certificate->update(['qr_code' => $qrCodePath]);

            return response()->json([
                'success' => true,
                'message' => 'Code QR généré avec succès',
                'qr_code_path' => $qrCodePath
            ]);
        } catch (\Exception $e) {
            // En cas d'erreur, enregistrer l'erreur dans les logs
            \Log::error('Erreur lors de la génération du code QR: ' . $e->getMessage());
            \Log::error('Trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la génération du code QR: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Générer le code QR pour un objet certificat.
     */
    private function generateQrCodeForCertificate(Certificate $certificate)
    {
        try {
            // Générer l'URL de vérification
            $verificationUrl = route('certificates.verify', ['number' => $certificate->certificate_number]);

            // Générer le code QR en SVG avec BaconQrCode
            $renderer = new ImageRenderer(
                new RendererStyle(200),
                new SvgImageBackEnd()
            );
            $writer = new Writer($renderer);
            $qrCode = $writer->writeString($verificationUrl);

            // Définir le chemin de stockage
            $qrCodePath = 'certificates/qr/' . $certificate->certificate_number . '.svg';

            // Créer le répertoire s'il n'existe pas
            $directory = 'certificates/qr';
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
            }

            // Sauvegarder le code QR
            Storage::disk('public')->put($qrCodePath, $qrCode);

            // Mettre à jour le certificat avec le chemin du code QR
            $certificate->update(['qr_code' => $qrCodePath]);

        } catch (\Exception $e) {
            // Log l'erreur mais ne pas faire échouer la génération du PDF
            \Log::error('Erreur lors de la génération du code QR: ' . $e->getMessage());
        }
    }

    /**
     * Créer un certificat de test dans la base de données avec l'ID 5.
     */
    public function createTestCertificate()
    {
        try {
            // Vérifier si le certificat de test existe déjà
            $existingCertificate = Certificate::where('certificate_number', 'TEST-2025-001')->first();
            if ($existingCertificate) {
                return redirect()->route('admin.certificates.show', $existingCertificate->id)
                    ->with('success', 'Certificat de test déjà existant, redirection vers la page de visualisation.');
            }

            // Créer ou récupérer un utilisateur de test
            $testUser = \App\Models\User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'Ahmed Ben Ali',
                    'id_card_number' => '01242587',
                    'role' => 'student',
                    'password' => bcrypt('password'),
                    'active' => true
                ]
            );

            // Créer ou récupérer un domaine de formation de test
            $testDomain = \App\Models\TrainingDomain::firstOrCreate(
                ['name' => 'Sécurité et Premiers Secours'],
                ['description' => 'Formation aux premiers secours et sécurité']
            );

            // Créer ou récupérer un formateur de test
            $testTrainer = \App\Models\User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'Dr. Mohamed Formateur',
                    'role' => 'trainer',
                    'password' => bcrypt('password'),
                    'active' => true
                ]
            );

            // Créer une session de formation de test
            $testSession = \App\Models\TrainingSession::firstOrCreate(
                ['title' => 'Formation Premiers Secours'],
                [
                    'description' => 'Formation complète aux premiers secours',
                    'certificate_name' => 'Premiers Secours',
                    'training_objectives' => 'Cette formation a permis au participant de maîtriser les gestes essentiels de premiers secours, d\'assurer la sécurité de la victime et de transmettre une alerte efficace. Les compétences ont été validées par des mises en situation pratiques.',
                    'training_domain_id' => $testDomain->id,
                    'trainer_id' => $testTrainer->id,
                    'start_date' => now()->subDays(30),
                    'end_date' => now()->subDays(1),
                    'max_students' => 20,
                    'price' => 500.00,
                    'active' => true
                ]
            );

            // Créer une inscription de test
            $testEnrollment = \App\Models\Enrollment::firstOrCreate(
                [
                    'user_id' => $testUser->id,
                    'training_session_id' => $testSession->id
                ],
                [
                    'status' => 'completed',
                    'enrollment_date' => now()->subDays(35),
                    'payment_confirmed' => true
                ]
            );

            // Créer le certificat de test
            $testCertificate = Certificate::create([
                'enrollment_id' => $testEnrollment->id,
                'user_id' => $testUser->id,
                'training_session_id' => $testSession->id,
                'certificate_number' => 'TEST-2025-001',
                'issued_at' => now(),
                'expiry_date' => now()->addYears(3),
                'status' => 'issued'
            ]);

            return redirect()->route('admin.certificates.show', $testCertificate->id)
                ->with('success', 'Certificat de test créé avec succès !');

        } catch (\Exception $e) {
            \Log::error('Erreur lors de la création du certificat de test: ' . $e->getMessage());
            return back()->with('error', 'Erreur lors de la création du certificat de test: ' . $e->getMessage());
        }
    }

    /**
     * Générer un certificat de test avec des données fictives.
     */
    public function testPdf()
    {
        try {
            // Créer des données fictives pour le test
            $user = (object) [
                'name' => 'Ahmed Ben Ali',
                'id_card_number' => '01242587'
            ];

            $trainingSession = (object) [
                'title' => 'Formation Premiers Secours',
                'certificate_name' => 'Premiers Secours',
                'training_objectives' => 'Cette formation a permis au participant de maîtriser les gestes essentiels de premiers secours, d\'assurer la sécurité de la victime et de transmettre une alerte efficace. Les compétences ont été validées par des mises en situation pratiques.',
                'start_date' => \Carbon\Carbon::now()->subDays(30),
                'end_date' => \Carbon\Carbon::now()->subDays(1)
            ];

            $trainingDomain = (object) [
                'name' => 'Sécurité et Premiers Secours'
            ];

            $certificate = (object) [
                'certificate_number' => 'TEST-2025-001',
                'issued_at' => now()
            ];

            // Préparer les données pour la vue
            $data = [
                'certificate' => $certificate,
                'user' => $user,
                'trainingSession' => $trainingSession,
                'trainingDomain' => $trainingDomain
            ];

            // Générer le PDF
            $pdf = app('dompdf.wrapper');
            $pdf->loadView('certificates.pdf', $data);
            $pdf->setPaper('a4', 'portrait');
            $pdf->setOptions([
                'defaultFont' => 'DejaVu Sans',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => false,
                'isPhpEnabled' => true,
                'fontSubsetting' => false
            ]);

            // Retourner le PDF pour affichage dans le navigateur
            return $pdf->stream('certificat_test.pdf');

        } catch (\Exception $e) {
            // En cas d'erreur, enregistrer l'erreur dans les logs
            \Log::error('Erreur lors de la génération du PDF de test: ' . $e->getMessage());
            \Log::error('Trace: ' . $e->getTraceAsString());

            // Retourner une erreur
            return response('Erreur lors de la génération du certificat de test: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Forcer la régénération d'un certificat en supprimant le PDF existant.
     */
    public function regenerate(string $id)
    {
        try {
            // Récupérer le certificat
            $certificate = Certificate::findOrFail($id);

            // Supprimer le PDF existant s'il existe
            $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';
            if (Storage::disk('public')->exists($pdfPath)) {
                Storage::disk('public')->delete($pdfPath);
            }

            // Réinitialiser le chemin du PDF dans la base de données
            $certificate->update(['pdf_path' => null]);

            // Générer un nouveau PDF avec le template actuel
            $this->generatePdf($certificate);

            // Rediriger vers la vue du certificat
            return redirect()->route('admin.certificates.view', $id)
                ->with('success', 'Certificat régénéré avec succès avec le nouveau design.');

        } catch (\Exception $e) {
            return redirect()->route('admin.certificates.show', $id)
                ->with('error', 'Erreur lors de la régénération du certificat: ' . $e->getMessage());
        }
    }
}
