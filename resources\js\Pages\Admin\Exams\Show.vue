<template>
  <Head :title="'Examen - ' + exam.title" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Détails de l'examen
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Bouton de retour -->
        <div class="mb-6">
          <Link :href="route('admin.exams.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Retour à la liste
          </Link>
        </div>

        <!-- Informations de l'examen -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-2xl font-semibold mb-4">{{ exam.title }}</h3>
                <div class="mb-2">
                  <span class="font-semibold">Formation:</span> {{ exam.training_session.title }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Domaine:</span> {{ exam.training_session.training_domain.name }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Type d'examen:</span> {{ getExamTypeLabel(exam.exam_type) }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Créé par:</span> {{ exam.creator.name }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Durée:</span> {{ exam.duration_minutes }} minutes
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Score de réussite:</span> {{ exam.passing_score }}%
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Statut:</span>
                  <span :class="{
                    'px-2 py-1 text-xs rounded-full ml-2': true,
                    'bg-green-100 text-green-800': exam.is_published,
                    'bg-yellow-100 text-yellow-800': !exam.is_published
                  }">
                    {{ exam.is_published ? 'Publié' : 'Brouillon' }}
                  </span>
                </div>
                <div class="mb-2" v-if="exam.available_from">
                  <span class="font-semibold">Disponible à partir de:</span> {{ formatDate(exam.available_from) }}
                </div>
                <div class="mb-2" v-if="exam.available_until">
                  <span class="font-semibold">Disponible jusqu'à:</span> {{ formatDate(exam.available_until) }}
                </div>
              </div>
              <div class="flex space-x-2">
                <Link :href="route('admin.exams.edit', exam.id)" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 0L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Modifier
                </Link>
              </div>
            </div>

            <!-- Description -->
            <div class="mt-6" v-if="exam.description">
              <h4 class="text-lg font-semibold mb-2">Description</h4>
              <div class="bg-gray-50 p-4 rounded">
                <p>{{ exam.description }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Statistiques de l'examen -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4">Statistiques</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div class="bg-blue-50 p-4 rounded-lg">
                <div class="text-3xl font-bold text-blue-600">{{ stats.totalAttempts }}</div>
                <div class="text-sm text-gray-600">Tentatives totales</div>
              </div>
              <div class="bg-green-50 p-4 rounded-lg">
                <div class="text-3xl font-bold text-green-600">{{ stats.passedAttempts }}</div>
                <div class="text-sm text-gray-600">Tentatives réussies</div>
              </div>
              <div class="bg-yellow-50 p-4 rounded-lg">
                <div class="text-3xl font-bold text-yellow-600">{{ stats.passRate }}%</div>
                <div class="text-sm text-gray-600">Taux de réussite</div>
              </div>
              <div class="bg-purple-50 p-4 rounded-lg">
                <div class="text-3xl font-bold text-purple-600">{{ stats.averageScore }}</div>
                <div class="text-sm text-gray-600">Score moyen</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Questions de l'examen -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-semibold">Questions ({{ exam.questions.length }})</h3>
              <div class="flex space-x-2">
                <Link :href="route('admin.exam-questions.index', { exam_id: exam.id })" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  Gérer les questions
                </Link>
                <Link :href="route('admin.exams.questions', { exam: exam.id })" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  Voir les questions
                </Link>
                <Link :href="route('admin.fix-questions')" class="inline-flex items-center px-4 py-2 bg-yellow-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-yellow-700 focus:bg-yellow-700 active:bg-yellow-900 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Réparer les questions
                </Link>
              </div>
            </div>

            <div v-if="exam.questions.length > 0" class="space-y-6">
              <div
                v-for="(question, index) in exam.questions"
                :key="question.id"
                class="border rounded-lg overflow-hidden transition-all duration-200 hover:shadow-md"
              >
                <!-- En-tête de la question -->
                <div class="bg-gray-50 p-4 border-b flex justify-between items-center">
                  <div class="flex items-center">
                    <div class="bg-indigo-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 font-semibold">
                      {{ index + 1 }}
                    </div>
                    <div>
                      <div class="font-medium">Question {{ index + 1 }}</div>
                      <div class="flex items-center mt-1 space-x-2">
                        <span
                          :class="{
                            'px-2 py-1 rounded-full text-xs': true,
                            'bg-purple-100 text-purple-800': question.question_type === 'multiple_choice',
                            'bg-indigo-100 text-indigo-800': question.question_type === 'single_choice',
                            'bg-green-100 text-green-800': question.question_type === 'text',
                            'bg-blue-100 text-blue-800': question.question_type === 'file_upload'
                          }"
                        >
                          {{ getQuestionTypeLabel(question.question_type) }}
                        </span>
                        <span class="text-sm text-gray-500">{{ question.points }} point{{ question.points > 1 ? 's' : '' }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="flex space-x-2">
                    <button @click="openEditModal(question)" class="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button @click="confirmDeleteQuestion(question)" class="p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>

                <!-- Corps de la question -->
                <div class="p-4">
                  <div class="text-gray-800 mb-4">{{ question.question_text }}</div>

                  <!-- Affichage des options pour les QCM (multiple_choice et single_choice) -->
                  <div v-if="question.question_type === 'multiple_choice' || question.question_type === 'single_choice'" class="mt-3">
                    <div class="text-sm font-medium text-gray-700 mb-2">Options:</div>

                    <div class="space-y-2">
                      <div
                        v-for="(option, optionKey) in getOptions(question.options)"
                        :key="optionKey"
                        :class="{
                          'p-3 rounded-md flex items-start': true,
                          'bg-green-50 border border-green-200': isCorrectOption(question.correct_options, optionKey),
                          'bg-gray-50 border border-gray-200': !isCorrectOption(question.correct_options, optionKey)
                        }"
                      >
                        <div
                          :class="{
                            'w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0': true,
                            'bg-green-500 text-white': isCorrectOption(question.correct_options, optionKey),
                            'bg-gray-300 text-gray-700': !isCorrectOption(question.correct_options, optionKey)
                          }"
                        >
                          {{ optionKey }}
                        </div>
                        <div class="flex-grow">{{ option }}</div>
                        <div v-if="isCorrectOption(question.correct_options, optionKey)" class="text-green-600 flex items-center ml-2">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                          </svg>
                          <span class="text-sm font-medium">Correcte</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Affichage de la réponse pour les questions à texte libre -->
                  <div v-if="question.question_type === 'text'" class="mt-3">
                    <div class="text-sm font-medium text-gray-700 mb-2">Réponse attendue:</div>
                    <div class="p-3 bg-green-50 border border-green-200 rounded-md text-gray-800">
                      {{ question.correct_answer || 'Aucune réponse spécifique attendue' }}
                    </div>
                  </div>

                  <!-- Affichage des informations pour les questions de type fichier -->
                  <div v-if="question.question_type === 'file_upload'" class="mt-3">
                    <div class="text-sm font-medium text-gray-700 mb-2">Type de fichier attendu:</div>
                    <div class="p-3 bg-blue-50 border border-blue-200 rounded-md text-gray-800 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clip-rule="evenodd" />
                      </svg>
                      {{ question.file_type_allowed || 'Tous types de fichiers' }}
                    </div>
                  </div>

                  <!-- Explication (si disponible) -->
                  <div v-if="question.explanation" class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <div class="text-sm font-medium text-gray-700 mb-1 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                      </svg>
                      Explication:
                    </div>
                    <div class="text-gray-800">{{ question.explanation }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-gray-500 italic">
              Aucune question n'a encore été ajoutée à cet examen.
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal d'édition de question -->
    <Modal :show="editModalOpen" @close="closeEditModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">
          Modifier la question
        </h2>

        <form @submit.prevent="submitEditForm" class="mt-6">
          <div class="mb-4">
            <InputLabel for="question_text" value="Texte de la question" />
            <textarea
              id="question_text"
              v-model="editForm.question_text"
              class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
              rows="3"
              required
            ></textarea>
            <InputError :message="editForm.errors.question_text" class="mt-2" />
          </div>

          <div class="mb-4">
            <InputLabel for="question_type" value="Type de question" />
            <select
              id="question_type"
              v-model="editForm.question_type"
              class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
              required
              @change="handleQuestionTypeChange"
            >
              <option value="multiple_choice">Choix multiple</option>
              <option value="text">Texte libre</option>
              <option value="file_upload">Fichier</option>
            </select>
            <InputError :message="editForm.errors.question_type" class="mt-2" />
          </div>

          <!-- Options pour les questions à choix multiple -->
          <div v-if="editForm.question_type === 'multiple_choice'" class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <InputLabel value="Options" />
              <button
                type="button"
                @click="addOption"
                class="text-sm text-indigo-600 hover:text-indigo-900"
              >
                + Ajouter une option
              </button>
            </div>

            <div v-for="(option, index) in optionsArray" :key="index" class="flex items-start mb-2">
              <div class="flex-grow mr-2">
                <div class="flex items-center">
                  <span class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                    {{ String.fromCharCode(65 + index) }}
                  </span>
                  <input
                    v-model="optionsArray[index].text"
                    type="text"
                    class="block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    placeholder="Texte de l'option"
                    required
                  />
                </div>
              </div>
              <div class="flex items-center">
                <label class="inline-flex items-center mr-2">
                  <input
                    type="checkbox"
                    v-model="optionsArray[index].isCorrect"
                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                  />
                  <span class="ml-2 text-sm text-gray-600">Correcte</span>
                </label>
                <button
                  type="button"
                  @click="removeOption(index)"
                  class="text-red-600 hover:text-red-900"
                  v-if="optionsArray.length > 1"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
            <InputError :message="editForm.errors.options" class="mt-2" />
            <InputError :message="editForm.errors.correct_options" class="mt-2" />
          </div>

          <!-- Réponse pour les questions à texte libre -->
          <div v-if="editForm.question_type === 'text'" class="mb-4">
            <InputLabel for="correct_answer" value="Réponse attendue" />
            <textarea
              id="correct_answer"
              v-model="editForm.correct_answer"
              class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
              rows="2"
            ></textarea>
            <InputError :message="editForm.errors.correct_answer" class="mt-2" />
          </div>

          <!-- Type de fichier pour les questions de type fichier -->
          <div v-if="editForm.question_type === 'file_upload'" class="mb-4">
            <InputLabel for="file_type_allowed" value="Types de fichiers autorisés" />
            <TextInput
              id="file_type_allowed"
              v-model="editForm.file_type_allowed"
              type="text"
              class="mt-1 block w-full"
              placeholder="ex: pdf,doc,docx"
            />
            <p class="text-sm text-gray-500 mt-1">Laissez vide pour autoriser tous les types de fichiers</p>
            <InputError :message="editForm.errors.file_type_allowed" class="mt-2" />
          </div>

          <div class="mb-4">
            <InputLabel for="explanation" value="Explication (optionnelle)" />
            <textarea
              id="explanation"
              v-model="editForm.explanation"
              class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
              rows="2"
              placeholder="Explication qui sera affichée après la réponse"
            ></textarea>
            <InputError :message="editForm.errors.explanation" class="mt-2" />
          </div>

          <div class="mb-4">
            <InputLabel for="points" value="Points" />
            <TextInput
              id="points"
              v-model="editForm.points"
              type="number"
              class="mt-1 block w-full"
              min="1"
              required
            />
            <InputError :message="editForm.errors.points" class="mt-2" />
          </div>

          <div class="flex justify-end mt-6 space-x-3">
            <SecondaryButton @click="closeEditModal">Annuler</SecondaryButton>
            <PrimaryButton :disabled="editForm.processing">Enregistrer</PrimaryButton>
          </div>
        </form>
      </div>
    </Modal>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Modal from '@/Components/Modal.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import { ref, reactive } from 'vue';

// Props
const props = defineProps({
  exam: Object,
  stats: Object,
});

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return 'Non définie';
  const options = { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

// Méthode pour obtenir le libellé du type d'examen
const getExamTypeLabel = (type) => {
  switch (type) {
    case 'certification':
      return 'Certification';
    case 'evaluation':
      return 'Évaluation';
    case 'practice':
      return 'Entraînement';
    case 'quiz':
      return 'Quiz';
    default:
      return type || 'Non défini';
  }
};

// Méthode pour obtenir le libellé du type de question
const getQuestionTypeLabel = (type) => {
  switch (type) {
    case 'multiple_choice':
      return 'Choix multiple';
    case 'single_choice':
      return 'Choix unique';
    case 'text':
      return 'Texte libre';
    case 'file_upload':
      return 'Fichier';
    default:
      return type;
  }
};

// Méthode pour confirmer la suppression d'une question
const confirmDeleteQuestion = (question) => {
  if (confirm(`Êtes-vous sûr de vouloir supprimer la question "${question.question_text.substring(0, 30)}..." ?`)) {
    // Rediriger vers la route de suppression
    window.location.href = route('admin.exam-questions.destroy', question.id);
  }
};

// État pour le modal d'édition
const editModalOpen = ref(false);
const currentQuestion = ref(null);
const optionsArray = ref([]);

// Formulaire d'édition
const editForm = useForm({
  id: null,
  exam_id: props.exam.id,
  question_text: '',
  question_type: 'multiple_choice',
  options: {},
  correct_options: [],
  correct_answer: '',
  file_type_allowed: '',
  explanation: '',
  points: 1,
  order: 0,
});

// Ouvrir le modal d'édition
const openEditModal = (question) => {
  currentQuestion.value = question;

  // Remplir le formulaire avec les données de la question
  editForm.id = question.id;
  editForm.question_text = question.question_text;
  editForm.question_type = question.question_type;
  editForm.explanation = question.explanation || '';
  editForm.points = question.points;
  editForm.order = question.order || 0;

  // Traiter les options selon le type de question
  if (question.question_type === 'multiple_choice') {
    const options = getOptions(question.options);
    const correctOpts = getCorrectOptions(question.correct_options);

    // Convertir les options en tableau pour l'interface
    optionsArray.value = [];
    Object.keys(options).forEach(key => {
      optionsArray.value.push({
        key: key,
        text: options[key],
        isCorrect: correctOpts.includes(key)
      });
    });

    // S'assurer qu'il y a au moins une option
    if (optionsArray.value.length === 0) {
      optionsArray.value = [{ key: 'A', text: '', isCorrect: false }];
    }
  } else if (question.question_type === 'text') {
    editForm.correct_answer = question.correct_answer || '';
  } else if (question.question_type === 'file_upload') {
    editForm.file_type_allowed = question.file_type_allowed || '';
  }

  editModalOpen.value = true;
};

// Fermer le modal d'édition
const closeEditModal = () => {
  editModalOpen.value = false;
  currentQuestion.value = null;
  editForm.reset();
  editForm.clearErrors();
};

// Gérer le changement de type de question
const handleQuestionTypeChange = () => {
  if (editForm.question_type === 'multiple_choice') {
    optionsArray.value = [{ key: 'A', text: '', isCorrect: false }];
  } else {
    optionsArray.value = [];
  }
};

// Ajouter une option
const addOption = () => {
  const nextKey = String.fromCharCode(65 + optionsArray.value.length);
  optionsArray.value.push({ key: nextKey, text: '', isCorrect: false });
};

// Supprimer une option
const removeOption = (index) => {
  optionsArray.value.splice(index, 1);

  // Mettre à jour les clés
  optionsArray.value.forEach((option, idx) => {
    option.key = String.fromCharCode(65 + idx);
  });
};

// Soumettre le formulaire d'édition
const submitEditForm = () => {
  // Préparer les données selon le type de question
  if (editForm.question_type === 'multiple_choice') {
    // Convertir le tableau d'options en objet
    const options = {};
    const correctOptions = [];

    optionsArray.value.forEach(option => {
      options[option.key] = option.text;
      if (option.isCorrect) {
        correctOptions.push(option.key);
      }
    });

    editForm.options = options;
    editForm.correct_options = correctOptions;

    // Vérifier qu'au moins une option est correcte
    if (correctOptions.length === 0) {
      alert('Veuillez sélectionner au moins une option correcte.');
      return;
    }
  }

  // Envoyer le formulaire
  editForm.put(route('admin.exam-questions.update', editForm.id), {
    onSuccess: () => {
      closeEditModal();
      // Recharger la page pour voir les modifications
      window.location.reload();
    }
  });
};

// Fonction utilitaire pour obtenir les options correctes
const getCorrectOptions = (correctOptions) => {
  if (!correctOptions) return [];

  if (typeof correctOptions === 'string') {
    try {
      return JSON.parse(correctOptions);
    } catch (e) {
      return correctOptions.split(',').map(opt => opt.trim());
    }
  }

  return Array.isArray(correctOptions) ? correctOptions : [correctOptions];
};

// Méthode pour obtenir les options d'une question
const getOptions = (options) => {
  // Si aucune option n'est fournie, créer des options par défaut
  if (!options) {
    return {
      'A': 'Option A',
      'B': 'Option B',
      'C': 'Option C',
      'D': 'Option D'
    };
  }

  // Si les options sont déjà un objet, les retourner directement
  if (typeof options === 'object' && !Array.isArray(options)) {
    // Vérifier si l'objet est vide
    if (Object.keys(options).length === 0) {
      return {
        'A': 'Option A',
        'B': 'Option B',
        'C': 'Option C',
        'D': 'Option D'
      };
    }
    return options;
  }

  // Si les options sont une chaîne JSON, les parser
  if (typeof options === 'string') {
    // Si la chaîne est vide, retourner des options par défaut
    if (!options.trim()) {
      return {
        'A': 'Option A',
        'B': 'Option B',
        'C': 'Option C',
        'D': 'Option D'
      };
    }

    try {
      const parsedOptions = JSON.parse(options);

      // Vérifier si le résultat du parsing est un objet non vide
      if (typeof parsedOptions === 'object' && !Array.isArray(parsedOptions) && Object.keys(parsedOptions).length > 0) {
        return parsedOptions;
      }

      // Si c'est un tableau, le convertir en objet
      if (Array.isArray(parsedOptions) && parsedOptions.length > 0) {
        const optionsObj = {};
        parsedOptions.forEach((opt, index) => {
          optionsObj[String.fromCharCode(65 + index)] = opt; // A, B, C, etc.
        });
        return optionsObj;
      }

      // Si le résultat du parsing est vide ou invalide, retourner des options par défaut
      return {
        'A': 'Option A',
        'B': 'Option B',
        'C': 'Option C',
        'D': 'Option D'
      };
    } catch (e) {
      console.error('Erreur lors du parsing des options:', e);

      // Si le parsing échoue, essayer de créer un objet à partir de la chaîne
      // Format attendu: "Option 1, Option 2, Option 3"
      if (options.includes(',')) {
        const optionsArray = options.split(',').map(opt => opt.trim());
        const optionsObj = {};
        optionsArray.forEach((opt, index) => {
          optionsObj[String.fromCharCode(65 + index)] = opt; // A, B, C, etc.
        });
        return optionsObj;
      }

      // Si aucun format reconnu, retourner des options par défaut
      return {
        'A': 'Option A',
        'B': 'Option B',
        'C': 'Option C',
        'D': 'Option D'
      };
    }
  }

  // Si les options sont un tableau, les convertir en objet
  if (Array.isArray(options)) {
    if (options.length === 0) {
      return {
        'A': 'Option A',
        'B': 'Option B',
        'C': 'Option C',
        'D': 'Option D'
      };
    }

    const optionsObj = {};
    options.forEach((opt, index) => {
      optionsObj[String.fromCharCode(65 + index)] = opt; // A, B, C, etc.
    });
    return optionsObj;
  }

  // Si aucun format reconnu, retourner des options par défaut
  return {
    'A': 'Option A',
    'B': 'Option B',
    'C': 'Option C',
    'D': 'Option D'
  };
};

// Méthode pour vérifier si une option est correcte
const isCorrectOption = (correctOptions, optionKey) => {
  // Si aucune option correcte n'est fournie, considérer que l'option A est correcte par défaut
  if (!correctOptions) {
    return optionKey === 'A';
  }

  // Si correctOptions est un tableau
  if (Array.isArray(correctOptions)) {
    // Si le tableau est vide, considérer que l'option A est correcte par défaut
    if (correctOptions.length === 0) {
      return optionKey === 'A';
    }
    return correctOptions.includes(optionKey);
  }

  // Si correctOptions est une chaîne JSON représentant un tableau
  if (typeof correctOptions === 'string') {
    // Si la chaîne est vide, considérer que l'option A est correcte par défaut
    if (!correctOptions.trim()) {
      return optionKey === 'A';
    }

    try {
      const parsedOptions = JSON.parse(correctOptions);

      // Si le résultat du parsing est un tableau
      if (Array.isArray(parsedOptions)) {
        // Si le tableau est vide, considérer que l'option A est correcte par défaut
        if (parsedOptions.length === 0) {
          return optionKey === 'A';
        }
        return parsedOptions.includes(optionKey);
      }

      // Si le résultat du parsing est un objet
      if (typeof parsedOptions === 'object') {
        // Si l'objet est vide, considérer que l'option A est correcte par défaut
        if (Object.keys(parsedOptions).length === 0) {
          return optionKey === 'A';
        }
        return parsedOptions[optionKey] === true;
      }

      // Si le résultat du parsing est une chaîne
      if (typeof parsedOptions === 'string') {
        return parsedOptions === optionKey;
      }

      // Par défaut, considérer que l'option A est correcte
      return optionKey === 'A';
    } catch (e) {
      console.error('Erreur lors du parsing des options correctes:', e);

      // Si le parsing échoue, vérifier si la chaîne contient l'option
      if (correctOptions.includes(',')) {
        // Format: "A,B,C"
        const correctArray = correctOptions.split(',').map(opt => opt.trim());
        return correctArray.includes(optionKey);
      }

      // Si c'est une seule option
      return correctOptions.trim() === optionKey;
    }
  }

  // Si correctOptions est un objet
  if (typeof correctOptions === 'object' && !Array.isArray(correctOptions)) {
    // Si l'objet est vide, considérer que l'option A est correcte par défaut
    if (Object.keys(correctOptions).length === 0) {
      return optionKey === 'A';
    }
    return correctOptions[optionKey] === true;
  }

  // Si correctOptions est une chaîne simple (pour les anciennes questions)
  return correctOptions === optionKey;
};
</script>
