<template>
  <Head title="Formations disponibles" />

  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <Link :href="route('welcome')" class="flex items-center">
              <img src="/images/logo.png" alt="PCMET" class="h-8 w-auto mr-3" />
              <span class="text-xl font-bold text-gray-900">PCMET</span>
            </Link>
          </div>
          <div class="flex items-center space-x-4">
            <Link :href="route('welcome')" class="text-gray-600 hover:text-gray-900">
              Accueil
            </Link>
            <Link v-if="$page.props.auth.user" :href="route('dashboard')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
              Dashboard
            </Link>
            <div v-else class="flex items-center space-x-2">
              <Link :href="route('login')" class="text-gray-600 hover:text-gray-900">
                Connexion
              </Link>
              <Link :href="route('register')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                Inscription
              </Link>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="text-center">
          <h1 class="text-4xl font-bold mb-4">Nos Formations</h1>
          <p class="text-xl text-blue-100 max-w-3xl mx-auto">
            Découvrez toutes nos formations disponibles et trouvez celle qui correspond à vos besoins
          </p>
          
          <!-- Statistiques -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            <div class="bg-white/10 rounded-lg p-4">
              <div class="text-2xl font-bold">{{ stats.total_sessions }}</div>
              <div class="text-blue-100">Sessions disponibles</div>
            </div>
            <div class="bg-white/10 rounded-lg p-4">
              <div class="text-2xl font-bold">{{ stats.departments }}</div>
              <div class="text-blue-100">Départements</div>
            </div>
            <div class="bg-white/10 rounded-lg p-4">
              <div class="text-2xl font-bold">{{ stats.domains }}</div>
              <div class="text-blue-100">Domaines</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
        <h2 class="text-lg font-semibold mb-4">Filtrer les formations</h2>
        
        <form @submit.prevent="applyFilters" class="space-y-4">
          <!-- Recherche -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
              <input
                v-model="localFilters.search"
                type="text"
                placeholder="Titre, description..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <!-- Département -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Département</label>
              <select
                v-model="localFilters.department"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Tous les départements</option>
                <option v-for="dept in departments" :key="dept" :value="dept">{{ dept }}</option>
              </select>
            </div>
            
            <!-- Niveau -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Niveau</label>
              <select
                v-model="localFilters.level"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Tous les niveaux</option>
                <option v-for="level in levels" :key="level" :value="level">{{ level }}</option>
              </select>
            </div>
            
            <!-- Domaine -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Domaine</label>
              <select
                v-model="localFilters.domain_id"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Tous les domaines</option>
                <option v-for="domain in domains" :key="domain.id" :value="domain.id">{{ domain.name }}</option>
              </select>
            </div>
          </div>
          
          <!-- Tri -->
          <div class="flex flex-wrap items-center gap-4">
            <div class="flex items-center gap-2">
              <label class="text-sm font-medium text-gray-700">Trier par:</label>
              <select
                v-model="localFilters.sort"
                class="px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="start_date">Date de début</option>
                <option value="title">Titre</option>
                <option value="price">Prix</option>
                <option value="created_at">Date de création</option>
              </select>
              <select
                v-model="localFilters.order"
                class="px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="asc">Croissant</option>
                <option value="desc">Décroissant</option>
              </select>
            </div>
            
            <div class="flex gap-2">
              <button
                type="submit"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Appliquer
              </button>
              <button
                type="button"
                @click="resetFilters"
                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Réinitialiser
              </button>
            </div>
          </div>
        </form>
      </div>

      <!-- Résultats -->
      <div v-if="sessions.data.length > 0">
        <!-- Grille des formations -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div
            v-for="session in sessions.data"
            :key="session.id"
            class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-300"
          >
            <!-- Image -->
            <div class="h-48 bg-gray-200 relative">
              <img
                v-if="session.image"
                :src="`/storage/${session.image}`"
                :alt="session.title"
                class="w-full h-full object-cover"
                @error="handleImageError"
              />
              <div v-else class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-50 to-blue-100">
                <div class="text-center">
                  <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                  </div>
                  <p class="text-sm text-gray-600">Formation</p>
                </div>
              </div>
              
              <!-- Badge statut -->
              <div class="absolute top-2 right-2">
                <span class="px-2 py-1 text-xs font-semibold rounded-full" :class="getStatusClass(session)">
                  {{ getStatusLabel(session) }}
                </span>
              </div>
            </div>

            <!-- Contenu -->
            <div class="p-6">
              <div class="flex items-center justify-between mb-2">
                <span v-if="session.level" class="px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800 rounded-full">
                  {{ session.level }}
                </span>
                <span class="text-sm text-gray-500">
                  {{ formatDate(session.start_date) }}
                </span>
              </div>

              <h3 class="text-lg font-bold text-gray-900 mb-2 line-clamp-2">{{ session.title }}</h3>
              <p class="text-gray-600 mb-3 line-clamp-3 text-sm">{{ session.description }}</p>

              <!-- Informations -->
              <div class="space-y-2 mb-4">
                <div class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                  {{ session.department }}
                </div>
                <div class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                  {{ session.trainer?.name || 'Non assigné' }}
                </div>
                <div class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                  {{ session.enrollments_count || 0 }} / {{ session.max_participants || 'Illimité' }}
                </div>
              </div>

              <!-- Prix et action -->
              <div class="flex justify-between items-center">
                <div class="text-lg font-bold text-gray-900">
                  {{ session.price ? formatPrice(session.price) : 'Gratuit' }}
                </div>
                <Link
                  :href="route('sessions.show', session.id)"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
                >
                  Voir détails
                </Link>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="sessions.links.length > 3" class="flex justify-center">
          <nav class="flex items-center space-x-1">
            <Link
              v-for="link in sessions.links"
              :key="link.label"
              :href="link.url"
              :class="[
                'px-3 py-2 text-sm rounded-md',
                link.active
                  ? 'bg-blue-600 text-white'
                  : link.url
                  ? 'text-gray-700 hover:bg-gray-100'
                  : 'text-gray-400 cursor-not-allowed'
              ]"
              v-html="link.label"
            />
          </nav>
        </div>
      </div>

      <!-- Aucun résultat -->
      <div v-else class="text-center py-12">
        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.5-.816-6.207-2.175C5.25 12.09 5.25 11.91 5.25 11.709V6.375c0-1.036.84-1.875 1.875-1.875h8.25c1.035 0 1.875.84 1.875 1.875v5.334c0 .201 0 .381-.543 1.116A7.962 7.962 0 0112 15z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Aucune formation trouvée</h3>
        <p class="text-gray-600 mb-4">Essayez de modifier vos critères de recherche</p>
        <button
          @click="resetFilters"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Réinitialiser les filtres
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';

// Props
const props = defineProps({
  sessions: Object,
  domains: Array,
  stats: Object,
  filters: Object,
  departments: Array,
  levels: Array,
});

// État local pour les filtres
const localFilters = reactive({
  search: props.filters.search || '',
  department: props.filters.department || '',
  level: props.filters.level || '',
  domain_id: props.filters.domain_id || '',
  sort: props.filters.sort || 'start_date',
  order: props.filters.order || 'asc',
});

// Méthodes
const applyFilters = () => {
  router.get(route('formations.index'), localFilters, {
    preserveState: true,
    preserveScroll: true,
  });
};

const resetFilters = () => {
  Object.keys(localFilters).forEach(key => {
    if (key === 'sort') {
      localFilters[key] = 'start_date';
    } else if (key === 'order') {
      localFilters[key] = 'asc';
    } else {
      localFilters[key] = '';
    }
  });
  applyFilters();
};

const formatDate = (dateString) => {
  if (!dateString) return 'Date non définie';
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  });
};

const formatPrice = (price) => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR',
  }).format(price);
};

const getStatusClass = (session) => {
  const now = new Date();
  const startDate = new Date(session.start_date);
  const endDate = new Date(session.end_date);

  if (now < startDate) {
    return 'bg-yellow-100 text-yellow-800';
  } else if (now >= startDate && now <= endDate) {
    return 'bg-green-100 text-green-800';
  } else {
    return 'bg-gray-100 text-gray-800';
  }
};

const getStatusLabel = (session) => {
  const now = new Date();
  const startDate = new Date(session.start_date);
  const endDate = new Date(session.end_date);

  if (now < startDate) {
    return 'À venir';
  } else if (now >= startDate && now <= endDate) {
    return 'En cours';
  } else {
    return 'Terminée';
  }
};

const handleImageError = (event) => {
  event.target.src = '/images/default-session.png';
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
