<template>
  <Head title="Gestion des certificats" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Gestion des certificats
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- En-tête avec bouton d'ajout -->
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">Liste des certificats</h3>
              <Link :href="route('admin.certificates.create')" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Créer un certificat
              </Link>
            </div>

            <!-- Message de succès -->
            <div v-if="$page.props.flash.success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
              <span class="block sm:inline">{{ $page.props.flash.success }}</span>
            </div>

            <!-- Message d'erreur -->
            <div v-if="$page.props.flash.error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
              <span class="block sm:inline">{{ $page.props.flash.error }}</span>
            </div>

            <!-- Formulaire de recherche et filtrage -->
            <div class="bg-gray-100 p-4 rounded-lg mb-6 border border-gray-200">
              <h3 class="text-lg font-medium mb-4">Recherche et filtrage</h3>
              <form @submit.prevent="applyFilters">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                  <!-- Barre de recherche -->
                  <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
                    <input
                      type="text"
                      id="search"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="Rechercher par nom ou email d'apprenant..."
                      v-model="searchQuery"
                    />
                  </div>

                  <!-- Filtre par session de formation -->
                  <div>
                    <label for="training_session" class="block text-sm font-medium text-gray-700 mb-1">Session de formation</label>
                    <select
                      id="training_session"
                      v-model="sessionFilter"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Toutes les sessions</option>
                      <option v-for="session in trainingSessions" :key="session.id" :value="session.id">
                        {{ session.title }}
                      </option>
                    </select>
                  </div>

                  <!-- Filtre par domaine de formation -->
                  <div>
                    <label for="domain" class="block text-sm font-medium text-gray-700 mb-1">Domaine de formation</label>
                    <select
                      id="domain"
                      v-model="domainFilter"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Tous les domaines</option>
                      <option v-for="domain in domains" :key="domain.id" :value="domain.id">
                        {{ domain.name }}
                      </option>
                    </select>
                  </div>

                  <!-- Filtre par statut -->
                  <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                    <select
                      id="status"
                      v-model="statusFilter"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Tous les statuts</option>
                      <option value="draft">Brouillon</option>
                      <option value="issued">Émis</option>
                      <option value="active">Actif</option>
                      <option value="revoked">Révoqué</option>
                      <option value="expired">Expiré</option>
                    </select>
                  </div>

                  <!-- Filtre par date d'émission (début) -->
                  <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Date d'émission (début)</label>
                    <input
                      type="date"
                      id="start_date"
                      v-model="startDateFilter"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>

                  <!-- Filtre par date d'émission (fin) -->
                  <div>
                    <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">Date d'émission (fin)</label>
                    <input
                      type="date"
                      id="end_date"
                      v-model="endDateFilter"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                </div>

                <!-- Boutons -->
                <div class="flex justify-end space-x-2">
                  <button
                    type="submit"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Filtrer
                  </button>
                  <button
                    type="button"
                    class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                    @click="resetFilters"
                  >
                    Réinitialiser
                  </button>
                </div>
              </form>
            </div>

            <!-- Tableau des certificats -->
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Apprenant</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Formation</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Numéro de certificat</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'émission</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="certificate in certificates.data" :key="certificate.id">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">{{ certificate.enrollment.user.name }}</div>
                      <div class="text-sm text-gray-500">{{ certificate.enrollment.user.email }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ certificate.enrollment.training_session.title }}</div>
                      <div class="text-sm text-gray-500">{{ certificate.enrollment.training_session.training_domain.name }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ certificate.certificate_number }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500">{{ formatDate(certificate.issued_at) }}</div>
                      <div v-if="certificate.expiry_date" class="text-sm text-gray-500">
                        Expire le: {{ formatDate(certificate.expiry_date) }}
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="{
                        'px-2 py-1 text-xs rounded-full': true,
                        'bg-gray-100 text-gray-800': certificate.status === 'draft',
                        'bg-blue-100 text-blue-800': certificate.status === 'issued',
                        'bg-green-100 text-green-800': certificate.status === 'active',
                        'bg-red-100 text-red-800': certificate.status === 'revoked',
                        'bg-orange-100 text-orange-800': certificate.status === 'expired'
                      }">
                        {{ formatStatus(certificate.status) }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div class="flex space-x-2">
                        <Link :href="route('admin.certificates.show', certificate.id)" class="text-indigo-600 hover:text-indigo-900">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </Link>
                        <Link :href="route('admin.certificates.edit', certificate.id)" class="text-blue-600 hover:text-blue-900">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 0L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </Link>
                        <a :href="`/admin/certificates/${certificate.id}/download`" class="text-green-600 hover:text-green-900" download>
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                          </svg>
                        </a>
                        <a :href="`/admin/certificates/${certificate.id}/view`" target="_blank" class="text-indigo-600 hover:text-indigo-900">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </a>
                        <button @click="confirmDelete(certificate)" class="text-red-600 hover:text-red-900">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr v-if="certificates.data.length === 0">
                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                      Aucun certificat trouvé.
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4">
              <Pagination :links="certificates.links" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmation de suppression -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg overflow-hidden shadow-xl max-w-md w-full">
        <div class="p-6">
          <h3 class="text-lg font-semibold mb-4">Confirmer la suppression</h3>
          <p class="mb-4">Êtes-vous sûr de vouloir supprimer le certificat de <span class="font-semibold">{{ certificateToDelete?.enrollment.user.name }}</span> pour la formation <span class="font-semibold">{{ certificateToDelete?.enrollment.training_session.title }}</span> ?</p>
          <div class="flex justify-end space-x-3">
            <button @click="showDeleteModal = false" class="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400">
              Annuler
            </button>
            <button @click="deleteCertificate" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
              Supprimer
            </button>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, useForm, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Pagination from '@/Components/Pagination.vue';

// Props
const props = defineProps({
  certificates: Object,
  trainingSessions: Array,
  domains: Array,
  filters: Object,
});

// État pour la modal de suppression
const showDeleteModal = ref(false);
const certificateToDelete = ref(null);

// État pour les filtres
const searchQuery = ref(props.filters?.search || '');
const sessionFilter = ref(props.filters?.training_session_id || '');
const domainFilter = ref(props.filters?.domain_id || '');
const statusFilter = ref(props.filters?.status || '');
const startDateFilter = ref(props.filters?.start_date || '');
const endDateFilter = ref(props.filters?.end_date || '');

// Formulaire pour la suppression
const deleteForm = useForm({});

// Méthodes pour les filtres
const applyFilters = () => {
  const params = {};

  // Ajouter la recherche si elle existe
  if (searchQuery.value) {
    params.search = searchQuery.value;
  }

  // Ajouter le filtre de session si il existe
  if (sessionFilter.value) {
    params.training_session_id = sessionFilter.value;
  }

  // Ajouter le filtre de domaine s'il existe
  if (domainFilter.value) {
    params.domain_id = domainFilter.value;
  }

  // Ajouter le filtre de statut s'il existe
  if (statusFilter.value) {
    params.status = statusFilter.value;
  }

  // Ajouter le filtre de date de début s'il existe
  if (startDateFilter.value) {
    params.start_date = startDateFilter.value;
  }

  // Ajouter le filtre de date de fin s'il existe
  if (endDateFilter.value) {
    params.end_date = endDateFilter.value;
  }

  // Naviguer vers l'URL avec les paramètres
  router.get(route('admin.certificates.index'), params, {
    preserveState: true,
    replace: true,
    preserveScroll: true
  });
};

const resetFilters = () => {
  searchQuery.value = '';
  sessionFilter.value = '';
  domainFilter.value = '';
  statusFilter.value = '';
  startDateFilter.value = '';
  endDateFilter.value = '';

  // Naviguer vers l'URL sans paramètres
  router.get(route('admin.certificates.index'), {}, {
    preserveState: true,
    replace: true,
    preserveScroll: true
  });
};

// Méthodes pour le formatage et la suppression
const formatDate = (dateString) => {
  if (!dateString) return 'Non définie';
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const formatStatus = (status) => {
  const statusMap = {
    'draft': 'Brouillon',
    'issued': 'Émis',
    'active': 'Actif',
    'revoked': 'Révoqué',
    'expired': 'Expiré'
  };
  return statusMap[status] || status;
};

const confirmDelete = (certificate) => {
  certificateToDelete.value = certificate;
  showDeleteModal.value = true;
};

const deleteCertificate = () => {
  deleteForm.delete(route('admin.certificates.destroy', certificateToDelete.value.id), {
    onSuccess: () => {
      showDeleteModal.value = false;
      certificateToDelete.value = null;
    },
  });
};

// Fonction pour télécharger le PDF en utilisant l'URL directe
const downloadPdf = async (certificate) => {
  try {
    // Récupérer l'URL du PDF
    const response = await fetch(`/admin/certificates/${certificate.id}/pdf-url`);

    // Vérifier si la requête a réussi
    if (!response.ok) {
      throw new Error(`Erreur HTTP: ${response.status}`);
    }

    // Récupérer les données JSON
    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    // Créer un élément <a> temporaire
    const link = document.createElement('a');
    link.href = data.url;
    link.download = data.filename;
    link.target = '_blank';

    // Ajouter l'élément au DOM
    document.body.appendChild(link);

    // Simuler un clic sur le lien
    link.click();

    // Supprimer l'élément du DOM
    document.body.removeChild(link);

    console.log('Téléchargement lancé');
  } catch (error) {
    console.error('Erreur lors du téléchargement:', error);
  }
};

</script>
