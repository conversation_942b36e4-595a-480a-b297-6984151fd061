<template>
    <Head title="Gestion des médias - Page d'accueil" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    Gestion des médias - Page d'accueil
                </h2>
                <div class="flex space-x-2">
                    <Link :href="route('admin.homepage-content.index')" 
                          class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                        Retour au contenu
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <!-- Success Message -->
                        <div v-if="$page.props.flash.success" class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                            {{ $page.props.flash.success }}
                        </div>

                        <!-- Statistics Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                            <div class="bg-blue-50 rounded-lg p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-blue-600">Total Médias</p>
                                        <p class="text-2xl font-semibold text-blue-900">{{ stats.total }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-green-50 rounded-lg p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-green-600">Images</p>
                                        <p class="text-2xl font-semibold text-green-900">{{ stats.images }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-purple-50 rounded-lg p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-purple-600">Vidéos</p>
                                        <p class="text-2xl font-semibold text-purple-900">{{ stats.videos }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-yellow-50 rounded-lg p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h4a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-yellow-600">Espace utilisé</p>
                                        <p class="text-2xl font-semibold text-yellow-900">{{ stats.storage }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Media Gallery -->
                        <MediaGallery
                            :media="mediaItems"
                            @media-selected="handleMediaSelected"
                            @media-uploaded="handleMediaUploaded"
                            @media-deleted="handleMediaDeleted"
                        />

                        <!-- Upload Progress -->
                        <div v-if="uploadProgress.show" class="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4 w-80">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="text-sm font-medium text-gray-900">Upload en cours</h4>
                                <button @click="uploadProgress.show = false" class="text-gray-400 hover:text-gray-600">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                            <div class="space-y-2">
                                <div v-for="file in uploadProgress.files" :key="file.name" class="flex items-center justify-between text-sm">
                                    <span class="truncate">{{ file.name }}</span>
                                    <span class="text-gray-500">{{ file.progress }}%</span>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" :style="{ width: uploadProgress.overall + '%' }"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import MediaGallery from '@/Components/MediaGallery.vue';
import { ref, computed } from 'vue';
import { router } from '@inertiajs/vue3';

const props = defineProps({
    contents: Object
});

const selectedMediaIds = ref([]);
const uploadProgress = ref({
    show: false,
    files: [],
    overall: 0
});

// Extract media items from contents
const mediaItems = computed(() => {
    const items = [];
    Object.values(props.contents).forEach(sectionContents => {
        sectionContents.forEach(content => {
            if (content.type === 'image' || content.type === 'video') {
                items.push(content);
            }
        });
    });
    return items;
});

// Calculate statistics
const stats = computed(() => {
    const total = mediaItems.value.length;
    const images = mediaItems.value.filter(item => item.type === 'image').length;
    const videos = mediaItems.value.filter(item => item.type === 'video').length;
    
    // Mock storage calculation - in real app, this would come from backend
    const storage = '12.5 MB';
    
    return {
        total,
        images,
        videos,
        storage
    };
});

const handleMediaSelected = (mediaIds) => {
    selectedMediaIds.value = mediaIds;
};

const handleMediaUploaded = (files) => {
    // Show upload progress
    uploadProgress.value = {
        show: true,
        files: files.map(file => ({ name: file.name, progress: 0 })),
        overall: 0
    };
    
    // Simulate upload progress
    const interval = setInterval(() => {
        uploadProgress.value.files.forEach(file => {
            if (file.progress < 100) {
                file.progress += Math.random() * 20;
                if (file.progress > 100) file.progress = 100;
            }
        });
        
        const totalProgress = uploadProgress.value.files.reduce((sum, file) => sum + file.progress, 0);
        uploadProgress.value.overall = totalProgress / uploadProgress.value.files.length;
        
        if (uploadProgress.value.overall >= 100) {
            clearInterval(interval);
            setTimeout(() => {
                uploadProgress.value.show = false;
                // Refresh page to show new media
                router.reload();
            }, 1000);
        }
    }, 500);
};

const handleMediaDeleted = (mediaIds) => {
    const ids = Array.isArray(mediaIds) ? mediaIds : [mediaIds];
    
    // Delete media items
    ids.forEach(id => {
        router.delete(route('admin.homepage-content.destroy', id), {
            preserveScroll: true,
            onSuccess: () => {
                // Remove from selection if selected
                const index = selectedMediaIds.value.indexOf(id);
                if (index > -1) {
                    selectedMediaIds.value.splice(index, 1);
                }
            }
        });
    });
};
</script>
