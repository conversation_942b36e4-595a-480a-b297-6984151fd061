<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExamQuestion extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'exam_id',
        'question_text',
        'question_type',
        'options',
        'correct_options',
        'correct_answer',
        'explanation',
        'file_type_allowed',
        'points',
        'order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'options' => 'json',
        'correct_options' => 'json',
        'points' => 'integer',
        'order' => 'integer',
    ];

    /**
     * Get the options attribute.
     *
     * @param  string|null  $value
     * @return array
     */
    public function getOptionsAttribute($value)
    {
        if (empty($value)) {
            return [
                'A' => 'Option A',
                'B' => 'Option B',
                'C' => 'Option C',
                'D' => 'Option D'
            ];
        }

        if (is_string($value)) {
            try {
                $decoded = json_decode($value, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    return $decoded;
                }
            } catch (\Exception $e) {
                // En cas d'erreur, retourner des options par défaut
            }
        }

        if (is_array($value) && count($value) > 0) {
            return $value;
        }

        return [
            'A' => 'Option A',
            'B' => 'Option B',
            'C' => 'Option C',
            'D' => 'Option D'
        ];
    }

    /**
     * Get the correct_options attribute.
     *
     * @param  string|null  $value
     * @return array
     */
    public function getCorrectOptionsAttribute($value)
    {
        if (empty($value)) {
            return [];
        }

        if (is_string($value)) {
            try {
                $decoded = json_decode($value, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    return $decoded;
                }
            } catch (\Exception $e) {
                // En cas d'erreur, retourner un tableau vide
                return [];
            }
        }

        if (is_array($value) && count($value) > 0) {
            return $value;
        }

        return [];
    }

    /**
     * Relation avec l'examen
     */
    public function exam()
    {
        return $this->belongsTo(Exam::class);
    }
}
