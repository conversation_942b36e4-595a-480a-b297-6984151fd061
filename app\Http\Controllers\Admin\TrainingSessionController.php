<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TimeSlot;
use App\Models\TrainingDomain;
use App\Models\TrainingSession;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class TrainingSessionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Initialiser la requête
        $query = TrainingSession::with(['trainingDomain', 'trainer']);

        // Appliquer la recherche si elle existe
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function($q) use ($searchTerm) {
                $q->where('title', 'like', $searchTerm)
                  ->orWhere('description', 'like', $searchTerm)
                  ->orWhereHas('trainingDomain', function($q) use ($searchTerm) {
                      $q->where('name', 'like', $searchTerm);
                  })
                  ->orWhereHas('trainer', function($q) use ($searchTerm) {
                      $q->where('name', 'like', $searchTerm);
                  });
            });
        }

        // Filtrer par domaine de formation
        if ($request->has('domain_id') && !empty($request->domain_id)) {
            $query->where('training_domain_id', $request->domain_id);
        }

        // Filtrer par formateur
        if ($request->has('trainer_id') && !empty($request->trainer_id)) {
            $query->where('trainer_id', $request->trainer_id);
        }

        // Filtrer par statut
        if ($request->has('status') && !empty($request->status)) {
            $query->where('active', $request->status === 'active');
        }

        // Filtrer par date de début
        if ($request->has('start_date') && !empty($request->start_date)) {
            $query->where('start_date', '>=', $request->start_date);
        }

        // Filtrer par date de fin
        if ($request->has('end_date') && !empty($request->end_date)) {
            $query->where('end_date', '<=', $request->end_date);
        }

        // Récupérer les sessions de formation avec pagination
        $sessions = $query->orderBy('start_date', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Récupérer tous les domaines pour le filtre
        $domains = TrainingDomain::orderBy('name')->get();

        // Récupérer tous les formateurs pour le filtre
        $trainers = User::where('role', 'trainer')
            ->orderBy('name')
            ->get();

        // Retourner la vue avec les sessions de formation et les filtres
        return Inertia::render('Admin/TrainingSessions/Index', [
            'sessions' => $sessions,
            'domains' => $domains,
            'trainers' => $trainers,
            'filters' => [
                'search' => $request->search ?? '',
                'domain_id' => $request->domain_id ?? '',
                'trainer_id' => $request->trainer_id ?? '',
                'status' => $request->status ?? '',
                'start_date' => $request->start_date ?? '',
                'end_date' => $request->end_date ?? '',
            ]
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Récupérer tous les domaines de formation actifs
        $domains = TrainingDomain::where('active', true)->orderBy('name')->get();

        // Récupérer tous les formateurs actifs
        $trainers = User::where('role', 'trainer')
            ->where('active', true)
            ->orderBy('name')
            ->get();

        // Retourner la vue pour créer une nouvelle session de formation
        return Inertia::render('Admin/TrainingSessions/Create', [
            'domains' => $domains,
            'trainers' => $trainers,
            'departments' => TrainingSession::DEPARTMENTS,
            'levels' => TrainingSession::LEVELS
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'certificate_name' => 'nullable|string|max:255',
            'training_objectives' => 'nullable|string',
            'training_domain_id' => 'required|exists:training_domains,id',
            'department' => 'required|in:' . implode(',', TrainingSession::DEPARTMENTS),
            'level' => 'required|in:' . implode(',', TrainingSession::LEVELS),
            'trainer_id' => 'required|exists:users,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'location' => 'nullable|string|max:255',
            'max_participants' => 'nullable|integer|min:1',
            'price' => 'nullable|numeric|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'active' => 'boolean',
            'time_slots' => 'nullable|array',
            'time_slots.*.name' => 'nullable|string|max:255',
            'time_slots.*.start_time' => 'required_with:time_slots|date_format:H:i',
            'time_slots.*.end_time' => 'required_with:time_slots|date_format:H:i|after:time_slots.*.start_time',
            'time_slots.*.max_participants' => 'nullable|integer|min:1',
            'time_slots.*.active' => 'boolean',
        ]);

        // Extraire les créneaux horaires des données validées
        $timeSlots = $validated['time_slots'] ?? [];
        unset($validated['time_slots']);

        // Gérer l'upload de l'image si elle existe
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('training-sessions', 'public');
            $validated['image'] = $imagePath;
        }

        // Créer la session de formation
        $session = TrainingSession::create($validated);

        // Créer les créneaux horaires si fournis
        if (!empty($timeSlots)) {
            foreach ($timeSlots as $slotData) {
                $session->timeSlots()->create([
                    'name' => $slotData['name'] ?? null,
                    'start_time' => $slotData['start_time'],
                    'end_time' => $slotData['end_time'],
                    'max_participants' => $slotData['max_participants'] ?? null,
                    'active' => $slotData['active'] ?? true,
                ]);
            }
        }

        // Rediriger vers la liste des sessions de formation avec un message de succès
        return redirect()->route('admin.training-sessions.index')
            ->with('success', 'Session de formation créée avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Récupérer la session de formation avec son domaine, son formateur et ses créneaux horaires
        $session = TrainingSession::with(['trainingDomain', 'trainer', 'timeSlots'])->findOrFail($id);

        // Récupérer les inscriptions associées à cette session
        $enrollments = $session->enrollments()->with('user')->get();

        // Récupérer les examens associés à cette session
        $exams = $session->exams()->with('creator')->get();

        // Retourner la vue avec la session de formation et ses données associées
        return Inertia::render('Admin/TrainingSessions/Show', [
            'session' => $session,
            'enrollments' => $enrollments,
            'exams' => $exams
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Récupérer la session de formation avec ses créneaux horaires
        $session = TrainingSession::with('timeSlots')->findOrFail($id);

        // Récupérer tous les domaines de formation
        $domains = TrainingDomain::orderBy('name')->get();

        // Récupérer tous les formateurs
        $trainers = User::where('role', 'trainer')
            ->orderBy('name')
            ->get();

        // Retourner la vue pour éditer la session de formation
        return Inertia::render('Admin/TrainingSessions/Edit', [
            'session' => $session,
            'domains' => $domains,
            'trainers' => $trainers,
            'departments' => TrainingSession::DEPARTMENTS,
            'levels' => TrainingSession::LEVELS
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Récupérer la session de formation
        $session = TrainingSession::findOrFail($id);

        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'certificate_name' => 'nullable|string|max:255',
            'training_objectives' => 'nullable|string',
            'training_domain_id' => 'required|exists:training_domains,id',
            'department' => 'required|in:' . implode(',', TrainingSession::DEPARTMENTS),
            'level' => 'required|in:' . implode(',', TrainingSession::LEVELS),
            'trainer_id' => 'required|exists:users,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'location' => 'nullable|string|max:255',
            'max_participants' => 'nullable|integer|min:1',
            'price' => 'nullable|numeric|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'active' => 'boolean',
            'time_slots' => 'nullable|array',
            'time_slots.*.id' => 'nullable|exists:time_slots,id',
            'time_slots.*.name' => 'nullable|string|max:255',
            'time_slots.*.start_time' => 'required_with:time_slots|date_format:H:i',
            'time_slots.*.end_time' => 'required_with:time_slots|date_format:H:i|after:time_slots.*.start_time',
            'time_slots.*.max_participants' => 'nullable|integer|min:1',
            'time_slots.*.active' => 'boolean',
        ]);

        // Extraire les créneaux horaires des données validées
        $timeSlots = $validated['time_slots'] ?? [];
        unset($validated['time_slots']);

        // Gérer l'upload de l'image si elle existe
        if ($request->hasFile('image')) {
            // Supprimer l'ancienne image si elle existe
            if ($session->image) {
                Storage::disk('public')->delete($session->image);
            }

            $imagePath = $request->file('image')->store('training-sessions', 'public');
            $validated['image'] = $imagePath;
        }

        // Mettre à jour la session de formation
        $session->update($validated);

        // Gérer les créneaux horaires
        if (!empty($timeSlots)) {
            // Récupérer les IDs des créneaux existants
            $existingSlotIds = collect($timeSlots)->pluck('id')->filter()->toArray();

            // Supprimer les créneaux qui ne sont plus dans la liste
            $session->timeSlots()->whereNotIn('id', $existingSlotIds)->delete();

            // Créer ou mettre à jour les créneaux
            foreach ($timeSlots as $slotData) {
                if (isset($slotData['id']) && $slotData['id']) {
                    // Mettre à jour le créneau existant
                    $session->timeSlots()->where('id', $slotData['id'])->update([
                        'name' => $slotData['name'] ?? null,
                        'start_time' => $slotData['start_time'],
                        'end_time' => $slotData['end_time'],
                        'max_participants' => $slotData['max_participants'] ?? null,
                        'active' => $slotData['active'] ?? true,
                    ]);
                } else {
                    // Créer un nouveau créneau
                    $session->timeSlots()->create([
                        'name' => $slotData['name'] ?? null,
                        'start_time' => $slotData['start_time'],
                        'end_time' => $slotData['end_time'],
                        'max_participants' => $slotData['max_participants'] ?? null,
                        'active' => $slotData['active'] ?? true,
                    ]);
                }
            }
        } else {
            // Si aucun créneau n'est fourni, supprimer tous les créneaux existants
            $session->timeSlots()->delete();
        }

        // Rediriger vers la liste des sessions de formation avec un message de succès
        return redirect()->route('admin.training-sessions.index')
            ->with('success', 'Session de formation mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Récupérer la session de formation
        $session = TrainingSession::findOrFail($id);

        // Vérifier s'il y a des inscriptions associées
        if ($session->enrollments()->count() > 0) {
            return redirect()->route('admin.training-sessions.index')
                ->with('error', 'Impossible de supprimer cette session car elle contient des inscriptions.');
        }

        // Vérifier s'il y a des examens associés
        if ($session->exams()->count() > 0) {
            return redirect()->route('admin.training-sessions.index')
                ->with('error', 'Impossible de supprimer cette session car elle contient des examens.');
        }

        // Supprimer l'image si elle existe
        if ($session->image) {
            Storage::disk('public')->delete($session->image);
        }

        // Supprimer la session de formation
        $session->delete();

        // Rediriger vers la liste des sessions de formation avec un message de succès
        return redirect()->route('admin.training-sessions.index')
            ->with('success', 'Session de formation supprimée avec succès.');
    }
}
