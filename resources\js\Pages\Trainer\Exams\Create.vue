<template>
  <Head title="Créer un examen" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Créer un examen
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <form @submit.prevent="submit">
              <!-- Titre -->
              <div class="mb-4">
                <InputLabel for="title" value="Titre" />
                <TextInput
                  id="title"
                  type="text"
                  class="mt-1 block w-full"
                  v-model="form.title"
                  required
                  autofocus
                />
                <InputError class="mt-2" :message="form.errors.title" />
              </div>

              <!-- Description -->
              <div class="mb-4">
                <InputLabel for="description" value="Description" />
                <textarea
                  id="description"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  v-model="form.description"
                  rows="3"
                ></textarea>
                <InputError class="mt-2" :message="form.errors.description" />
              </div>

              <!-- Session de formation -->
              <div class="mb-4">
                <InputLabel for="training_session_id" value="Session de formation" />
                <select
                  id="training_session_id"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  v-model="form.training_session_id"
                  required
                >
                  <option value="" disabled selected>Sélectionnez une session</option>
                  <option v-for="session in trainingSessions" :key="session.id" :value="session.id">
                    {{ session.title }} ({{ session.training_domain.name }})
                  </option>
                </select>
                <InputError class="mt-2" :message="form.errors.training_session_id" />
              </div>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <!-- Durée -->
                <div>
                  <InputLabel for="duration_minutes" value="Durée (minutes)" />
                  <TextInput
                    id="duration_minutes"
                    type="number"
                    class="mt-1 block w-full"
                    v-model="form.duration_minutes"
                    min="1"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.duration_minutes" />
                </div>

                <!-- Score de réussite -->
                <div>
                  <InputLabel for="passing_score" value="Score de réussite (%)" />
                  <TextInput
                    id="passing_score"
                    type="number"
                    class="mt-1 block w-full"
                    v-model="form.passing_score"
                    min="0"
                    max="100"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.passing_score" />
                </div>

                <!-- Statut -->
                <div>
                  <InputLabel for="status" value="Statut" />
                  <select
                    id="status"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.status"
                    required
                  >
                    <option v-for="status in statusOptions" :key="status.value" :value="status.value">
                      {{ status.label }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.status" />
                </div>
              </div>

              <!-- Instructions -->
              <div class="mb-6">
                <InputLabel for="instructions" value="Instructions" />
                <textarea
                  id="instructions"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  v-model="form.instructions"
                  rows="4"
                ></textarea>
                <InputError class="mt-2" :message="form.errors.instructions" />
              </div>

              <!-- Boutons -->
              <div class="flex justify-end">
                <Link :href="route('trainer.exams.index')" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150 mr-2">
                  Annuler
                </Link>
                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                  Créer
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  trainingSessions: Array,
  statusOptions: Array,
});

// Formulaire
const form = useForm({
  title: '',
  description: '',
  training_session_id: '',
  duration_minutes: 60,
  passing_score: 70,
  status: 'draft',
  instructions: '',
});

// Méthodes
const submit = () => {
  form.post(route('trainer.exams.store'));
};
</script>
