<?php

// Connexion à la base de données
$host = '127.0.0.1';
$db   = 'laravel';
$user = 'root';
$pass = '';
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;dbname=$db;charset=$charset";
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

try {
    $pdo = new PDO($dsn, $user, $pass, $options);
} catch (\PDOException $e) {
    throw new \PDOException($e->getMessage(), (int)$e->getCode());
}

// Récupérer les questions de l'examen 16
$stmt = $pdo->prepare("SELECT * FROM exam_questions WHERE exam_id = 16");
$stmt->execute();
$questions = $stmt->fetchAll();

// Afficher les détails des questions
foreach ($questions as $q) {
    echo "Question ID: " . $q['id'] . "\n";
    echo "Question Text: " . $q['question_text'] . "\n";
    echo "Question Type: " . $q['question_type'] . "\n";
    echo "Options: " . $q['options'] . "\n";
    echo "Correct Options: " . $q['correct_options'] . "\n";
    echo "-------------------\n";
}

// Mettre à jour la question 3 pour s'assurer que correct_options est correctement défini
$stmt = $pdo->prepare("UPDATE exam_questions SET correct_options = '[\"A\"]' WHERE id = 3");
$stmt->execute();
echo "Question 3 mise à jour avec correct_options = [\"A\"]\n";

// Vérifier la mise à jour
$stmt = $pdo->prepare("SELECT * FROM exam_questions WHERE id = 3");
$stmt->execute();
$question = $stmt->fetch();
echo "Après mise à jour - Question ID: " . $question['id'] . "\n";
echo "Correct Options: " . $question['correct_options'] . "\n";
