<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\TrainingSession;
use App\Models\User;
use App\Models\Enrollment;

// Récupérer un étudiant
$student = User::where('role', 'student')->first();

if (!$student) {
    echo "Erreur: Aucun étudiant trouvé\n";
    exit(1);
}

echo "Test de progression pour l'étudiant: {$student->name} ({$student->email})\n\n";

// Récupérer les sessions de Secourisme Niveau 1
$niveau1Session = TrainingSession::where('department', 'Secourisme')
    ->where('level', 'Niveau 1')
    ->first();

if ($niveau1Session) {
    // Créer une inscription complétée pour le Niveau 1
    $enrollment = Enrollment::updateOrCreate(
        [
            'user_id' => $student->id,
            'training_session_id' => $niveau1Session->id,
        ],
        [
            'status' => 'completed',
            'enrollment_date' => now()->subDays(30),
            'payment_confirmed' => true,
            'payment_amount' => $niveau1Session->price,
        ]
    );
    
    echo "Inscription créée/mise à jour pour Secourisme Niveau 1: {$enrollment->status}\n";
}

// Tester les méthodes de progression
echo "\n=== Test des méthodes de progression ===\n";

// Test hasCompletedLevel
$hasCompleted1 = $student->hasCompletedLevel('Secourisme', 'Niveau 1');
$hasCompleted2 = $student->hasCompletedLevel('Secourisme', 'Niveau 2');

echo "A terminé Secourisme Niveau 1: " . ($hasCompleted1 ? 'OUI' : 'NON') . "\n";
echo "A terminé Secourisme Niveau 2: " . ($hasCompleted2 ? 'OUI' : 'NON') . "\n";

// Test canEnrollInLevel
$canEnroll1 = $student->canEnrollInLevel('Secourisme', 'Niveau 1');
$canEnroll2 = $student->canEnrollInLevel('Secourisme', 'Niveau 2');
$canEnroll3 = $student->canEnrollInLevel('Secourisme', 'Niveau 3');

echo "Peut s'inscrire à Secourisme Niveau 1: " . ($canEnroll1 ? 'OUI' : 'NON') . "\n";
echo "Peut s'inscrire à Secourisme Niveau 2: " . ($canEnroll2 ? 'OUI' : 'NON') . "\n";
echo "Peut s'inscrire à Secourisme Niveau 3: " . ($canEnroll3 ? 'OUI' : 'NON') . "\n";

// Test getCompletedLevelsByDepartment
$completedLevels = $student->getCompletedLevelsByDepartment();
echo "\nNiveaux complétés par département:\n";
foreach ($completedLevels as $department => $levels) {
    echo "- $department: " . implode(', ', $levels) . "\n";
}

// Test getNextAvailableLevel
$nextLevel = $student->getNextAvailableLevel('Secourisme');
echo "\nProchain niveau disponible en Secourisme: " . ($nextLevel ?: 'Tous les niveaux complétés') . "\n";

echo "\nTest terminé!\n";
