<template>
  <Head title="Ajouter un matériel pédagogique" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Ajouter un matériel pédagogique
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link
                :href="route('admin.course-materials.index', module ? { module_id: module.id } : { course_id: course.id })"
                class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à la liste
              </Link>
            </div>

            <!-- Informations sur le cours et le module -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <h3 class="text-lg font-semibold mb-2">Cours: {{ course.title }}</h3>
              <p><span class="font-medium">Session de formation:</span> {{ course.training_session.title }}</p>

              <div v-if="module" class="mt-4 pt-4 border-t border-gray-200">
                <h4 class="text-md font-semibold mb-2">Module: {{ module.title }}</h4>
                <p v-if="module.description"><span class="font-medium">Description:</span> {{ module.description }}</p>
              </div>
            </div>

            <!-- Formulaire d'ajout de matériel -->
            <form @submit.prevent="submit" enctype="multipart/form-data">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Titre -->
                <div>
                  <InputLabel for="title" value="Titre du matériel" />
                  <TextInput
                    id="title"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.title"
                    required
                    autofocus
                  />
                  <InputError class="mt-2" :message="form.errors.title" />
                </div>

                <!-- Type de matériel -->
                <div>
                  <InputLabel for="type" value="Type de matériel" />
                  <select
                    id="type"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.type"
                    required
                    @change="handleTypeChange"
                  >
                    <option value="">Sélectionner un type</option>
                    <option v-for="type in materialTypes" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.type" />
                </div>

                <!-- Description -->
                <div class="md:col-span-2">
                  <InputLabel for="description" value="Description" />
                  <textarea
                    id="description"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.description"
                    rows="3"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.description" />
                </div>

                <!-- Contenu texte (si type = text) -->
                <div v-if="form.type === 'text'" class="md:col-span-2">
                  <InputLabel for="content" value="Contenu" />
                  <textarea
                    id="content"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.content"
                    rows="10"
                    required
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.content" />
                </div>

                <!-- URL pour vidéos externes (YouTube, Dailymotion, Vimeo) -->
                <div v-if="form.type === 'embed_video'" class="md:col-span-2">
                  <InputLabel for="video_url" value="URL de la vidéo" />
                  <TextInput
                    id="video_url"
                    type="url"
                    class="mt-1 block w-full"
                    v-model="form.video_url"
                    placeholder="https://www.youtube.com/watch?v=VIDEO_ID"
                    required
                  />
                  <p class="text-sm text-gray-500 mt-1">
                    Collez simplement l'URL de la vidéo YouTube, Dailymotion ou Vimeo.
                    Exemples:
                    <br>- YouTube: https://www.youtube.com/watch?v=VIDEO_ID
                    <br>- Dailymotion: https://www.dailymotion.com/video/VIDEO_ID
                    <br>- Vimeo: https://vimeo.com/VIDEO_ID
                  </p>
                  <InputError class="mt-2" :message="form.errors.video_url" />
                </div>

                <!-- Fichier (si type = pdf, video, audio, image, archive) -->
                <div v-if="['pdf', 'video', 'audio', 'image', 'archive'].includes(form.type)" class="md:col-span-2">
                  <InputLabel for="file" :value="`Fichier ${formatMaterialType(form.type)}`" />
                  <input
                    id="file"
                    type="file"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
                    @input="form.file = $event.target.files[0]"
                    :accept="getAcceptTypes(form.type)"
                    required
                  />
                  <p class="text-sm text-gray-500 mt-1">
                    {{ getFileTypeHelp(form.type) }}
                  </p>
                  <InputError class="mt-2" :message="form.errors.file" />
                </div>

                <!-- Fichiers multiples pour galerie d'images -->
                <div v-if="form.type === 'gallery'" class="md:col-span-2">
                  <InputLabel for="gallery_files" value="Images pour la galerie" />
                  <input
                    id="gallery_files"
                    type="file"
                    multiple
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
                    @change="handleGalleryFiles"
                    accept="image/*"
                    required
                  />
                  <p class="text-sm text-gray-500 mt-1">
                    Formats acceptés: JPEG, PNG, GIF, etc. Vous pouvez sélectionner plusieurs images à la fois.
                  </p>
                  <div v-if="selectedFiles.length > 0" class="mt-2">
                    <p class="font-medium">{{ selectedFiles.length }} image(s) sélectionnée(s) :</p>
                    <ul class="list-disc pl-5 mt-1">
                      <li v-for="(file, index) in selectedFiles" :key="index" class="text-sm">
                        {{ file.name }} ({{ formatFileSize(file.size) }})
                      </li>
                    </ul>
                  </div>
                  <InputError class="mt-2" :message="form.errors.gallery_files" />
                </div>

                <!-- Ordre -->
                <div>
                  <InputLabel for="order" value="Ordre d'affichage" />
                  <TextInput
                    id="order"
                    type="number"
                    class="mt-1 block w-full"
                    v-model="form.order"
                    min="0"
                  />
                  <InputError class="mt-2" :message="form.errors.order" />
                </div>

                <!-- Options -->
                <div class="md:col-span-2">
                  <div class="flex flex-col space-y-2">
                    <div class="flex items-center">
                      <input
                        id="active"
                        type="checkbox"
                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                        v-model="form.active"
                      />
                      <InputLabel for="active" value="Actif" class="ml-2" />
                    </div>
                    <div v-if="['pdf', 'video', 'audio', 'image', 'archive', 'gallery'].includes(form.type)" class="flex items-center">
                      <input
                        id="allow_download"
                        type="checkbox"
                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                        v-model="form.allow_download"
                      />
                      <InputLabel for="allow_download" value="Autoriser le téléchargement" class="ml-2" />
                    </div>
                    <div v-if="['pdf', 'video', 'audio', 'image', 'gallery', 'embed_video'].includes(form.type)" class="flex items-center">
                      <input
                        id="allow_online_viewing"
                        type="checkbox"
                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                        v-model="form.allow_online_viewing"
                      />
                      <InputLabel for="allow_online_viewing" value="Autoriser la visualisation en ligne" class="ml-2" />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="flex items-center justify-end mt-6">
                <PrimaryButton class="ml-4" :disabled="form.processing">
                  Ajouter le matériel
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import axios from 'axios';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  course: Object,
  module: Object,
  materialTypes: Array,
});

// État local pour les fichiers de la galerie
const selectedFiles = ref([]);

// Formulaire
const form = useForm({
  title: '',
  description: '',
  course_id: props.course.id,
  module_id: props.module?.id || null,
  type: '',
  content: '',
  embed_code: '',
  video_url: '',
  file: null,
  files: [], // Pour les fichiers multiples de la galerie
  order: 0,
  active: true,
  allow_download: true,
  allow_online_viewing: true,
});

// Méthodes
const formatMaterialType = (type) => {
  const types = {
    'text': 'Texte',
    'pdf': 'Document PDF',
    'video': 'Vidéo',
    'audio': 'Audio',
    'image': 'Image',
    'archive': 'Archive (ZIP, RAR, etc.)',
    'gallery': 'Galerie d\'images',
    'embed_video': 'Vidéo externe'
  };
  return types[type] || type;
};

const getAcceptTypes = (type) => {
  const acceptTypes = {
    'pdf': '.pdf',
    'video': 'video/*',
    'audio': 'audio/*',
    'image': 'image/*',
    'gallery': 'image/*',
    'archive': '.zip,.rar,.7z,.tar,.gz'
  };
  return acceptTypes[type] || '';
};

const getFileTypeHelp = (type) => {
  const helpText = {
    'pdf': 'Formats acceptés: PDF. Taille illimitée.',
    'video': 'Formats acceptés: MP4, WebM, etc. Taille illimitée.',
    'audio': 'Formats acceptés: MP3, WAV, etc. Taille illimitée.',
    'image': 'Formats acceptés: JPEG, PNG, GIF, etc. Taille illimitée.',
    'gallery': 'Formats acceptés: JPEG, PNG, GIF, etc. Vous pourrez ajouter d\'autres images plus tard.',
    'archive': 'Formats acceptés: ZIP, RAR, 7Z, TAR, GZ, etc. Taille illimitée.'
  };
  return helpText[type] || '';
};

const handleTypeChange = () => {
  // Réinitialiser les champs spécifiques au type
  form.content = '';
  form.embed_code = '';
  form.video_url = '';
  form.file = null;
  form.files = [];
  selectedFiles.value = [];

  // Ajuster les options en fonction du type
  if (form.type === 'embed_video') {
    form.allow_download = false;
  } else if (form.type === 'archive') {
    form.allow_online_viewing = false;
  } else {
    form.allow_download = true;
    form.allow_online_viewing = true;
  }
};

// Gérer la sélection de fichiers multiples pour la galerie
const handleGalleryFiles = (event) => {
  const files = Array.from(event.target.files);
  selectedFiles.value = files;
  form.files = files;
};

// Formater la taille des fichiers
const formatFileSize = (bytes) => {
  if (bytes < 1024) return bytes + ' octets';
  else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' Ko';
  else return (bytes / 1048576).toFixed(1) + ' Mo';
};

const submit = () => {
  // Si c'est une galerie, utiliser FormData pour envoyer plusieurs fichiers
  if (form.type === 'gallery' && selectedFiles.value.length > 0) {
    const formData = new FormData();
    formData.append('title', form.title);
    formData.append('description', form.description || '');
    formData.append('course_id', form.course_id);
    if (form.module_id) {
      formData.append('module_id', form.module_id);
    }
    formData.append('type', form.type);
    formData.append('order', form.order);
    formData.append('active', form.active ? 1 : 0);
    formData.append('allow_download', form.allow_download ? 1 : 0);
    formData.append('allow_online_viewing', form.allow_online_viewing ? 1 : 0);

    // Ajouter tous les fichiers
    selectedFiles.value.forEach((file, index) => {
      formData.append(`gallery_files[]`, file);
    });

    // Utiliser Axios directement pour avoir plus de contrôle sur la requête
    // Récupérer le token CSRF de manière sécurisée
    let csrfToken = '';
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    if (metaTag) {
      csrfToken = metaTag.getAttribute('content') || '';
    }

    axios.post(route('admin.course-materials.store'), formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'X-CSRF-TOKEN': csrfToken
      }
    }).then(response => {
      if (form.module_id) {
        window.location.href = route('admin.course-materials.index', { module_id: form.module_id });
      } else {
        window.location.href = route('admin.course-materials.index', { course_id: form.course_id });
      }
    }).catch(error => {
      if (error.response && error.response.data && error.response.data.errors) {
        form.errors = error.response.data.errors;
      } else {
        console.error('Erreur lors de l\'envoi des fichiers:', error);
      }
    });
  } else {
    form.post(route('admin.course-materials.store'));
  }
};
</script>
