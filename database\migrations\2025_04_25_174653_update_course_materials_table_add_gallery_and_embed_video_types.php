<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Modifier le type enum pour inclure les nouveaux types de médias
        DB::statement("ALTER TABLE course_materials MODIFY COLUMN type ENUM('pdf', 'video', 'text', 'archive', 'image', 'audio', 'gallery', 'embed_video')");

        // Ajouter un champ pour stocker les métadonnées spécifiques aux galeries et vidéos intégrées
        if (!Schema::hasColumn('course_materials', 'embed_code')) {
            Schema::table('course_materials', function (Blueprint $table) {
                $table->text('embed_code')->nullable()->after('content');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restaurer le type enum sans les nouveaux types
        DB::statement("ALTER TABLE course_materials MODIFY COLUMN type ENUM('pdf', 'video', 'text', 'archive', 'image', 'audio')");

        // Supprimer le champ embed_code s'il existe
        if (Schema::hasColumn('course_materials', 'embed_code')) {
            Schema::table('course_materials', function (Blueprint $table) {
                $table->dropColumn('embed_code');
            });
        }
    }
};
