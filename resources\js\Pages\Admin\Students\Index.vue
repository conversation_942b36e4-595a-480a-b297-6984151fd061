<template>
    <Head title="Gestion des Apprenants" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    Gestion des Apprenants
                </h2>
                <div class="flex space-x-2">
                    <button
                        @click="exportPdf"
                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                        Exporter PDF
                    </button>
                    <button
                        v-if="selectedStudents.length > 0"
                        @click="showBulkActions = true"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                        Actions groupées ({{ selectedStudents.length }})
                    </button>
                </div>
            </div>
        </template>

        <!-- Statistiques -->
        <div class="py-6">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <UsersIcon class="h-8 w-8 text-blue-600" />
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Apprenants</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ stats.total_students }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <CheckCircleIcon class="h-8 w-8 text-green-600" />
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Apprenants Actifs</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ stats.active_students }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <AcademicCapIcon class="h-8 w-8 text-purple-600" />
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Inscriptions</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ stats.total_enrollments }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <TrophyIcon class="h-8 w-8 text-yellow-600" />
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Cours Terminés</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ stats.completed_courses }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filtres et recherche -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Recherche</label>
                                <input
                                    v-model="searchForm.search"
                                    type="text"
                                    placeholder="Nom, email, téléphone..."
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    @input="debounceSearch"
                                />
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Statut</label>
                                <select
                                    v-model="searchForm.status"
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    @change="search"
                                >
                                    <option value="">Tous</option>
                                    <option value="active">Actifs</option>
                                    <option value="inactive">Inactifs</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Source de découverte</label>
                                <select
                                    v-model="searchForm.discovery_source"
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    @change="search"
                                >
                                    <option value="">Toutes</option>
                                    <option v-for="(label, value) in discovery_sources" :key="value" :value="value">
                                        {{ label }}
                                    </option>
                                </select>
                            </div>
                            <div class="flex items-end">
                                <button
                                    @click="resetFilters"
                                    class="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium"
                                >
                                    Réinitialiser
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Liste des apprenants -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left">
                                            <input
                                                type="checkbox"
                                                @change="toggleSelectAll"
                                                :checked="allSelected"
                                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            />
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Photo
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="sort('name')">
                                            Nom
                                            <ChevronUpIcon v-if="searchForm.sort === 'name' && searchForm.direction === 'asc'" class="inline w-4 h-4" />
                                            <ChevronDownIcon v-if="searchForm.sort === 'name' && searchForm.direction === 'desc'" class="inline w-4 h-4" />
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Contact
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Inscriptions
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Certificats
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="sort('created_at')">
                                            Inscription
                                            <ChevronUpIcon v-if="searchForm.sort === 'created_at' && searchForm.direction === 'asc'" class="inline w-4 h-4" />
                                            <ChevronDownIcon v-if="searchForm.sort === 'created_at' && searchForm.direction === 'desc'" class="inline w-4 h-4" />
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Statut
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="student in students.data" :key="student.id" class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input
                                                type="checkbox"
                                                :value="student.id"
                                                v-model="selectedStudents"
                                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            />
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <img
                                                :src="student.profile_photo ? `/storage/${student.profile_photo}` : '/images/default-avatar.svg'"
                                                :alt="student.name"
                                                class="h-10 w-10 rounded-full object-cover"
                                            />
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ student.name }}</div>
                                            <div class="text-sm text-gray-500">{{ student.profession || 'Non renseigné' }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ student.email }}</div>
                                            <div class="text-sm text-gray-500">{{ student.phone || 'Non renseigné' }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ student.enrollments_count || (student.enrollments ? student.enrollments.length : 0) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ student.certificates_count || (student.certificates ? student.certificates.length : 0) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ formatDate(student.created_at) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="[
                                                'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                                                student.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                            ]">
                                                {{ student.active ? 'Actif' : 'Inactif' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <Link
                                                    :href="route('admin.students.show', student.id)"
                                                    class="text-indigo-600 hover:text-indigo-900"
                                                >
                                                    Voir
                                                </Link>
                                                <a
                                                    :href="route('admin.students.card', student.id)"
                                                    target="_blank"
                                                    class="text-purple-600 hover:text-purple-900"
                                                    title="Carte Apprenant"
                                                >
                                                    <CreditCardIcon class="h-4 w-4" />
                                                </a>
                                                <a
                                                    :href="route('admin.students.sheet.view', student.id)"
                                                    target="_blank"
                                                    class="text-blue-600 hover:text-blue-900"
                                                    title="Voir Fiche Apprenant"
                                                >
                                                    📄
                                                </a>
                                                <a
                                                    :href="route('admin.students.sheet', student.id)"
                                                    class="text-red-600 hover:text-red-900"
                                                    title="Télécharger PDF"
                                                >
                                                    <DocumentArrowDownIcon class="h-4 w-4" />
                                                </a>
                                                <a
                                                    :href="route('admin.students.qr-code', student.id)"
                                                    class="text-green-600 hover:text-green-900"
                                                    target="_blank"
                                                    title="QR Code"
                                                >
                                                    <QrCodeIcon class="h-4 w-4" />
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="mt-6">
                            <Pagination :links="students.links" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Actions groupées -->
        <Modal :show="showBulkActions" @close="showBulkActions = false">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Actions groupées</h3>
                <p class="text-sm text-gray-600 mb-4">
                    {{ selectedStudents.length }} apprenant(s) sélectionné(s)
                </p>
                <div class="space-y-3">
                    <button
                        @click="bulkAction('activate')"
                        class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                        Activer
                    </button>
                    <button
                        @click="bulkAction('deactivate')"
                        class="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                        Désactiver
                    </button>
                    <button
                        @click="bulkAction('delete')"
                        class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                        Supprimer
                    </button>
                </div>
                <div class="mt-4 flex justify-end">
                    <button
                        @click="showBulkActions = false"
                        class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md text-sm font-medium"
                    >
                        Annuler
                    </button>
                </div>
            </div>
        </Modal>
    </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3';
import { ref, computed, watch } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Pagination from '@/Components/Pagination.vue';
import Modal from '@/Components/Modal.vue';
import {
    UsersIcon,
    CheckCircleIcon,
    AcademicCapIcon,
    TrophyIcon,
    ChevronUpIcon,
    ChevronDownIcon,
    CreditCardIcon,
    DocumentArrowDownIcon,
    QrCodeIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
    students: Object,
    stats: Object,
    filters: Object,
    discovery_sources: Object
});

// État réactif
const selectedStudents = ref([]);
const showBulkActions = ref(false);
const searchForm = ref({
    search: props.filters.search || '',
    status: props.filters.status || '',
    discovery_source: props.filters.discovery_source || '',
    sort: props.filters.sort || 'created_at',
    direction: props.filters.direction || 'desc'
});

// Computed
const allSelected = computed(() => {
    return props.students.data.length > 0 && selectedStudents.value.length === props.students.data.length;
});

// Méthodes
const toggleSelectAll = () => {
    if (allSelected.value) {
        selectedStudents.value = [];
    } else {
        selectedStudents.value = props.students.data.map(student => student.id);
    }
};

const search = () => {
    router.get(route('admin.students.index'), searchForm.value, {
        preserveState: true,
        replace: true
    });
};

let searchTimeout;
const debounceSearch = () => {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        search();
    }, 300);
};

const sort = (field) => {
    if (searchForm.value.sort === field) {
        searchForm.value.direction = searchForm.value.direction === 'asc' ? 'desc' : 'asc';
    } else {
        searchForm.value.sort = field;
        searchForm.value.direction = 'asc';
    }
    search();
};

const resetFilters = () => {
    searchForm.value = {
        search: '',
        status: '',
        discovery_source: '',
        sort: 'created_at',
        direction: 'desc'
    };
    search();
};

const exportPdf = () => {
    window.open(route('admin.students.export-pdf', searchForm.value), '_blank');
};

const bulkAction = (action) => {
    if (confirm(`Êtes-vous sûr de vouloir ${action} ces apprenants ?`)) {
        router.post(route('admin.students.bulk-action'), {
            action: action,
            student_ids: selectedStudents.value
        }, {
            onSuccess: () => {
                selectedStudents.value = [];
                showBulkActions.value = false;
            }
        });
    }
};

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('fr-FR');
};
</script>
