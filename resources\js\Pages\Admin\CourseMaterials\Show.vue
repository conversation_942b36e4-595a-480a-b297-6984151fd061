<template>
  <Head :title="material.title" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Détails du matériel pédagogique
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link :href="route('admin.course-materials.index', { course_id: material.course_id })" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à la liste
              </Link>
            </div>

            <!-- Informations sur le matériel -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 class="text-lg font-semibold mb-2">Informations sur le matériel</h3>
                  <p><span class="font-medium">Titre:</span> {{ material.title }}</p>
                  <p><span class="font-medium">Cours:</span> {{ material.course.title }}</p>
                  <p>
                    <span class="font-medium">Type:</span>
                    <span
                      :class="[
                        'px-2 inline-flex text-xs leading-5 font-semibold rounded-full ml-2',
                        getTypeClass(material.type)
                      ]"
                    >
                      {{ formatMaterialType(material.type) }}
                    </span>
                  </p>
                  <p><span class="font-medium">Ordre d'affichage:</span> {{ material.order }}</p>
                  <p>
                    <span class="font-medium">Statut:</span>
                    <span
                      :class="[
                        'px-2 inline-flex text-xs leading-5 font-semibold rounded-full ml-2',
                        material.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      ]"
                    >
                      {{ material.active ? 'Actif' : 'Inactif' }}
                    </span>
                  </p>
                  <div v-if="material.file_path">
                    <p><span class="font-medium">Fichier:</span> {{ getFileName(material.file_path) }}</p>
                    <p v-if="material.file_size"><span class="font-medium">Taille:</span> {{ formatFileSize(material.file_size) }}</p>
                    <p><span class="font-medium">Téléchargement:</span> {{ material.allow_download ? 'Autorisé' : 'Non autorisé' }}</p>
                    <p><span class="font-medium">Visualisation en ligne:</span> {{ material.allow_online_viewing ? 'Autorisée' : 'Non autorisée' }}</p>
                  </div>
                </div>
                <div>
                  <h3 class="text-lg font-semibold mb-2">Description</h3>
                  <p class="text-gray-700">{{ material.description || 'Aucune description' }}</p>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">Contenu du matériel</h3>
              <div class="flex space-x-2">
                <a v-if="material.file_path && material.allow_download"
                  :href="`/admin/course-materials/${material.id}/download`"
                  class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                >
                  Télécharger
                </a>
                <Link
                  :href="route('admin.course-materials.edit', material.id)"
                  class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                >
                  Modifier
                </Link>
              </div>
            </div>

            <!-- Contenu du matériel -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <!-- Contenu texte -->
              <div v-if="material.type === 'text'" class="prose max-w-none">
                <div class="whitespace-pre-wrap">{{ material.content }}</div>
              </div>

              <!-- PDF -->
              <div v-else-if="material.type === 'pdf' && material.file_path && material.allow_online_viewing" class="flex flex-col items-center">
                <div class="bg-gray-100 p-4 rounded-lg mb-4 w-full text-center">
                  <p class="text-gray-700 mb-2">Le document PDF ne peut pas être affiché directement. Veuillez utiliser l'une des options ci-dessous :</p>
                  <div class="flex justify-center space-x-4">
                    <a
                      :href="`/admin/course-materials/${material.id}/view`"
                      target="_blank"
                      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      Ouvrir dans un nouvel onglet
                    </a>
                    <a
                      :href="`/admin/course-materials/${material.id}/download`"
                      class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                    >
                      Télécharger le PDF
                    </a>
                  </div>
                </div>
                <div class="border border-gray-300 rounded-lg p-8 flex flex-col items-center justify-center w-full h-64">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-red-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <p class="text-lg font-medium">{{ material.title }}</p>
                  <p class="text-sm text-gray-500">{{ formatFileSize(material.file_size) }}</p>
                </div>
              </div>

              <!-- Vidéo -->
              <div v-else-if="material.type === 'video' && material.file_path && material.allow_online_viewing" class="flex flex-col">
                <video
                  controls
                  controlsList="nodownload"
                  preload="metadata"
                  class="w-full h-full object-contain"
                >
                  <source :src="`/admin/course-materials/${material.id}/view`" :type="material.mime_type">
                  Votre navigateur ne prend pas en charge la lecture de vidéos.
                </video>
                <div class="mt-4 text-sm text-gray-500">
                  Utilisez les contrôles de lecture pour avancer ou reculer la vidéo.
                  Vous pouvez également cliquer directement sur la barre de progression pour vous déplacer dans la vidéo.
                </div>
                <div class="mt-4 flex justify-center space-x-4">
                  <a
                    :href="`/admin/course-materials/${material.id}/view`"
                    target="_blank"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Ouvrir dans un nouvel onglet
                  </a>
                  <a
                    v-if="material.allow_download"
                    :href="`/admin/course-materials/${material.id}/download`"
                    class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Télécharger la vidéo
                  </a>
                </div>
              </div>

              <!-- Audio -->
              <div v-else-if="material.type === 'audio' && material.file_path && material.allow_online_viewing" class="py-4">
                <audio
                  controls
                  class="w-full"
                >
                  <source :src="`/admin/course-materials/${material.id}/view`" :type="material.mime_type">
                  Votre navigateur ne prend pas en charge la lecture audio.
                </audio>
                <div class="mt-4 flex justify-center space-x-4">
                  <a
                    :href="`/admin/course-materials/${material.id}/view`"
                    target="_blank"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Ouvrir dans un nouvel onglet
                  </a>
                  <a
                    v-if="material.allow_download"
                    :href="`/admin/course-materials/${material.id}/download`"
                    class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Télécharger l'audio
                  </a>
                </div>
              </div>

              <!-- Image -->
              <div v-else-if="material.type === 'image' && material.file_path && material.allow_online_viewing" class="flex flex-col items-center">
                <img
                  :src="`/admin/course-materials/${material.id}/view`"
                  :alt="material.title"
                  class="max-h-96 object-contain"
                  @error="handleMainImageError"
                >
                <div class="mt-4 flex justify-center space-x-4">
                  <a
                    :href="`/admin/course-materials/${material.id}/view`"
                    target="_blank"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Ouvrir dans un nouvel onglet
                  </a>
                  <a
                    v-if="material.allow_download"
                    :href="`/admin/course-materials/${material.id}/download`"
                    class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Télécharger l'image
                  </a>
                </div>
              </div>

              <!-- Galerie d'images -->
              <div v-else-if="material.type === 'gallery' && material.allow_online_viewing" class="py-4">
                <div v-if="parsedMetadata && parsedMetadata.images && parsedMetadata.images.length > 0">
                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div v-for="(image, index) in parsedMetadata.images" :key="index" class="relative group">
                      <img
                        :src="`/admin/course-materials/${material.id}/view?image_index=${index}`"
                        :alt="image.caption || material.title"
                        class="w-full h-48 object-cover rounded-lg cursor-pointer"
                        @click="openLightbox(index)"
                        @error="handleImageError($event, index)"
                      >
                      <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2 rounded-b-lg">
                        {{ image.caption || `Image ${index + 1}` }}
                      </div>
                    </div>
                  </div>

                  <!-- Lightbox pour afficher les images en plein écran -->
                  <div v-if="lightboxOpen" class="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center" @click="closeLightbox">
                    <div class="relative max-w-4xl max-h-screen p-4">
                      <img
                        :src="`/admin/course-materials/${material.id}/view?image_index=${currentImageIndex}`"
                        :alt="parsedMetadata.images[currentImageIndex].caption || material.title"
                        class="max-h-[80vh] max-w-full object-contain"
                        @error="handleLightboxImageError"
                      >
                      <div class="absolute bottom-4 left-0 right-0 text-center text-white">
                        {{ parsedMetadata.images[currentImageIndex].caption || `Image ${currentImageIndex + 1}` }}
                      </div>
                      <button @click.stop="prevImage" class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full">
                        &lt;
                      </button>
                      <button @click.stop="nextImage" class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full">
                        &gt;
                      </button>
                      <button @click.stop="closeLightbox" class="absolute top-4 right-4 text-white text-2xl">&times;</button>
                    </div>
                  </div>
                </div>
                <div v-else class="text-center py-8 text-gray-500">
                  <p class="text-lg font-medium">Aucune image n'est disponible dans cette galerie.</p>
                  <p class="mt-2">Métadonnées: {{ JSON.stringify(material.metadata) }}</p>
                </div>
              </div>

              <!-- Vidéo externe (YouTube, Dailymotion, Vimeo) -->
              <div v-else-if="material.type === 'embed_video' && material.allow_online_viewing" class="aspect-w-16 aspect-h-9">
                <div v-if="material.embed_code" class="w-full" v-html="material.embed_code"></div>
                <div v-else-if="material.content && getEmbedUrl(material.content)" class="w-full">
                  <iframe
                    :src="getEmbedUrl(material.content)"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen
                    class="w-full h-full"
                  ></iframe>
                </div>
                <div v-else class="text-center py-8 text-gray-500">
                  <p class="text-lg font-medium">Aucune vidéo externe disponible ou URL invalide.</p>
                  <p class="text-sm text-gray-500 mt-2">
                    Contenu: {{ material.content }}<br>
                    Code d'intégration: {{ material.embed_code ? 'Présent' : 'Absent' }}
                  </p>
                </div>
              </div>

              <!-- Message si la visualisation n'est pas autorisée -->
              <div v-else-if="material.file_path && !material.allow_online_viewing" class="text-center py-8 text-gray-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <p class="text-lg font-medium">La visualisation en ligne n'est pas autorisée pour ce matériel.</p>
                <p v-if="material.allow_download" class="mt-2">
                  Vous pouvez cependant
                  <a :href="`/admin/course-materials/${material.id}/download`" class="text-blue-600 hover:underline">
                    télécharger le fichier
                  </a>.
                </p>
              </div>

              <!-- Message si aucun contenu n'est disponible -->
              <div v-else class="text-center py-8 text-gray-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                </svg>
                <p class="text-lg font-medium">Aucun contenu disponible pour ce matériel.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import { ref } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  material: Object,
});

// État pour la galerie d'images
const lightboxOpen = ref(false);
const currentImageIndex = ref(0);
const imageErrors = ref({});

// Traitement des métadonnées JSON
const parsedMetadata = ref(null);

// Analyser les métadonnées au chargement du composant
if (props.material && props.material.metadata) {
  try {
    let metadata;
    if (typeof props.material.metadata === 'string') {
      metadata = JSON.parse(props.material.metadata);
    } else {
      metadata = props.material.metadata;
    }

    // Normaliser la structure des métadonnées
    parsedMetadata.value = { images: [] };

    // Traiter différentes structures possibles
    if (metadata.images && Array.isArray(metadata.images)) {
      // Format standard admin
      parsedMetadata.value.images = metadata.images.map((img, index) => {
        if (typeof img === 'string') {
          return { path: img, caption: `Image ${index + 1}` };
        }
        return { ...img, caption: img.caption || `Image ${index + 1}` };
      });
    } else if (metadata.gallery_paths && Array.isArray(metadata.gallery_paths)) {
      // Format utilisé dans l'interface formateur
      parsedMetadata.value.images = metadata.gallery_paths.map((path, index) => {
        return { path: path, caption: `Image ${index + 1}` };
      });
    } else if (Array.isArray(metadata)) {
      // Si les métadonnées sont directement un tableau
      parsedMetadata.value.images = metadata.map((item, index) => {
        if (typeof item === 'string') {
          return { path: item, caption: `Image ${index + 1}` };
        }
        return { ...item, caption: item.caption || `Image ${index + 1}` };
      });
    }

    console.log('Métadonnées normalisées:', parsedMetadata.value);
  } catch (error) {
    console.error('Erreur lors de l\'analyse des métadonnées:', error);
    parsedMetadata.value = { images: [] };
  }
} else {
  parsedMetadata.value = { images: [] };
}

// Méthodes pour la galerie d'images
const openLightbox = (index) => {
  currentImageIndex.value = index;
  lightboxOpen.value = true;
};

const closeLightbox = () => {
  lightboxOpen.value = false;
};

const nextImage = (e) => {
  e.stopPropagation();
  if (parsedMetadata.value && parsedMetadata.value.images) {
    currentImageIndex.value = (currentImageIndex.value + 1) % parsedMetadata.value.images.length;
  }
};

const prevImage = (e) => {
  e.stopPropagation();
  if (parsedMetadata.value && parsedMetadata.value.images) {
    currentImageIndex.value = (currentImageIndex.value - 1 + parsedMetadata.value.images.length) % parsedMetadata.value.images.length;
  }
};

// Gestion des erreurs d'image
const handleImageError = (event, index) => {
  console.error(`Erreur de chargement de l'image ${index}:`, event);
  imageErrors.value[index] = true;
  event.target.src = '/images/image-not-found.png'; // Image de remplacement
};

const handleLightboxImageError = (event) => {
  console.error(`Erreur de chargement de l'image en lightbox:`, event);
  event.target.src = '/images/image-not-found.png'; // Image de remplacement
};

const handleMainImageError = (event) => {
  console.error(`Erreur de chargement de l'image principale:`, event);
  event.target.src = '/images/image-not-found.png'; // Image de remplacement
};

// Méthodes
const formatMaterialType = (type) => {
  const types = {
    'text': 'Texte',
    'pdf': 'Document PDF',
    'video': 'Vidéo',
    'audio': 'Audio',
    'image': 'Image',
    'archive': 'Archive',
    'gallery': 'Galerie d\'images',
    'embed_video': 'Vidéo externe'
  };
  return types[type] || type;
};

const getTypeClass = (type) => {
  const classes = {
    'text': 'bg-gray-100 text-gray-800',
    'pdf': 'bg-red-100 text-red-800',
    'video': 'bg-blue-100 text-blue-800',
    'audio': 'bg-purple-100 text-purple-800',
    'image': 'bg-yellow-100 text-yellow-800',
    'archive': 'bg-green-100 text-green-800',
    'gallery': 'bg-pink-100 text-pink-800',
    'embed_video': 'bg-indigo-100 text-indigo-800'
  };
  return classes[type] || 'bg-gray-100 text-gray-800';
};

const getFileName = (path) => {
  if (!path) return '';
  return path.split('/').pop();
};

const formatFileSize = (sizeInKB) => {
  if (sizeInKB < 1024) {
    return `${Math.round(sizeInKB)} Ko`;
  } else {
    return `${(sizeInKB / 1024).toFixed(2)} Mo`;
  }
};

// Méthode pour obtenir l'URL d'intégration pour les vidéos externes
const getEmbedUrl = (url) => {
  if (!url) return '';

  try {
    // YouTube
    if (url.includes('youtube.com/watch') || url.includes('youtu.be')) {
      let videoId;
      if (url.includes('youtube.com/watch')) {
        const urlObj = new URL(url);
        videoId = urlObj.searchParams.get('v');
      } else {
        videoId = url.split('/').pop().split('?')[0];
      }

      if (!videoId) {
        console.error('Impossible d\'extraire l\'ID de la vidéo YouTube:', url);
        return '';
      }

      return `https://www.youtube.com/embed/${videoId}`;
    }

    // Dailymotion
    if (url.includes('dailymotion.com')) {
      const parts = url.split('/');
      const lastPart = parts.pop() || '';
      const videoId = lastPart.split('_')[0];

      if (!videoId) {
        console.error('Impossible d\'extraire l\'ID de la vidéo Dailymotion:', url);
        return '';
      }

      return `https://www.dailymotion.com/embed/video/${videoId}`;
    }

    // Vimeo
    if (url.includes('vimeo.com')) {
      const parts = url.split('/');
      const videoId = parts.pop();

      if (!videoId || isNaN(Number(videoId))) {
        console.error('Impossible d\'extraire l\'ID de la vidéo Vimeo:', url);
        return '';
      }

      return `https://player.vimeo.com/video/${videoId}`;
    }

    // Si l'URL est déjà une URL d'intégration, la retourner telle quelle
    if (url.includes('/embed/') || url.includes('player.')) {
      return url;
    }

    console.warn('URL de vidéo non reconnue:', url);
    return url;
  } catch (error) {
    console.error('Erreur lors de l\'analyse de l\'URL de la vidéo:', error);
    return '';
  }
};
</script>
