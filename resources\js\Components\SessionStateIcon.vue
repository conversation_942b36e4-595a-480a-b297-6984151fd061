<template>
  <div class="flex items-center justify-center w-8 h-8 rounded-full" :class="getBackgroundClass()">
    <!-- État disponible -->
    <CheckIcon v-if="state === 'available'" class="w-4 h-4" :class="getIconClass()" />
    
    <!-- État inscrit -->
    <UserIcon v-else-if="state === 'enrolled'" class="w-4 h-4" :class="getIconClass()" />
    
    <!-- État en attente -->
    <ClockIcon v-else-if="state === 'pending'" class="w-4 h-4" :class="getIconClass()" />
    
    <!-- État terminé -->
    <TrophyIcon v-else-if="state === 'completed'" class="w-4 h-4" :class="getIconClass()" />
    
    <!-- <PERSON>tat verrouillé -->
    <LockClosedIcon v-else-if="state === 'locked'" class="w-4 h-4" :class="getIconClass()" />
    
    <!-- Session complète -->
    <UsersIcon v-else-if="state === 'full'" class="w-4 h-4" :class="getIconClass()" />
    
    <!-- Session commencée -->
    <PlayIcon v-else-if="state === 'started'" class="w-4 h-4" :class="getIconClass()" />
    
    <!-- État par défaut -->
    <QuestionMarkCircleIcon v-else class="w-4 h-4" :class="getIconClass()" />
  </div>
</template>

<script setup>
import {
  CheckIcon,
  UserIcon,
  ClockIcon,
  TrophyIcon,
  LockClosedIcon,
  UsersIcon,
  PlayIcon,
  QuestionMarkCircleIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  state: {
    type: String,
    required: true
  }
});

// Méthodes
const getBackgroundClass = () => {
  const classes = {
    'available': 'bg-blue-100',
    'enrolled': 'bg-green-100',
    'pending': 'bg-yellow-100',
    'completed': 'bg-purple-100',
    'locked': 'bg-gray-100',
    'full': 'bg-orange-100',
    'started': 'bg-gray-100'
  };
  return classes[props.state] || 'bg-gray-100';
};

const getIconClass = () => {
  const classes = {
    'available': 'text-blue-600',
    'enrolled': 'text-green-600',
    'pending': 'text-yellow-600',
    'completed': 'text-purple-600',
    'locked': 'text-gray-500',
    'full': 'text-orange-600',
    'started': 'text-gray-600'
  };
  return classes[props.state] || 'text-gray-500';
};
</script>
