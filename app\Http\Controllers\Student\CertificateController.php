<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Models\Certificate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class CertificateController extends Controller
{
    /**
     * Affiche la liste des certificats de l'apprenant
     */
    public function index()
    {
        $user = Auth::user();

        // Only show active and issued certificates to students
        $certificates = Certificate::where('user_id', $user->id)
            ->visibleToStudents()
            ->with(['trainingSession.trainingDomain'])
            ->orderBy('issued_at', 'desc')
            ->get();

        return Inertia::render('Student/Certificate/Index', [
            'certificates' => $certificates
        ]);
    }

    /**
     * Visualise un certificat dans le navigateur
     */
    public function view(Certificate $certificate)
    {
        // Vérifier que le certificat appartient à l'utilisateur connecté
        if ($certificate->user_id !== Auth::id()) {
            abort(403, 'Vous n\'êtes pas autorisé à visualiser ce certificat.');
        }

        // Vérifier que le certificat est visible (actif ou émis)
        if (!$certificate->isVisibleToStudent()) {
            abort(403, 'Ce certificat n\'est pas encore disponible.');
        }

        // Detect mobile device
        $userAgent = request()->header('User-Agent');
        $isMobile = preg_match('/Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i', $userAgent);

        // For mobile devices, add specific headers to improve PDF viewing
        if ($isMobile) {
            return $this->viewMobile($certificate);
        }

        try {
            // Vérifier si le certificat a un numéro de certificat
            if (empty($certificate->certificate_number)) {
                throw new \Exception('Le certificat n\'a pas de numéro valide.');
            }

            // Définir le chemin du PDF basé sur le numéro de certificat
            $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';

            // Vérifier si le PDF existe
            if (!Storage::disk('public')->exists($pdfPath)) {
                // Si le PDF n'existe pas, essayer de le générer via l'admin controller
                $adminController = new \App\Http\Controllers\Admin\CertificateController();
                $adminController->generatePdf($certificate);

                // Vérifier à nouveau si le PDF a été créé
                if (!Storage::disk('public')->exists($pdfPath)) {
                    throw new \Exception("Le certificat PDF n'est pas disponible.");
                }
            }

            // Retourner le PDF pour affichage dans le navigateur
            $fullPath = Storage::disk('public')->path($pdfPath);

            return response()->file($fullPath, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="Certificat_' . $certificate->certificate_number . '.pdf"'
            ]);

        } catch (\Exception $e) {
            // En cas d'erreur, enregistrer l'erreur dans les logs
            \Log::error('Erreur lors de la visualisation du certificat: ' . $e->getMessage());
            \Log::error('Trace: ' . $e->getTraceAsString());

            // Retourner une erreur
            abort(500, 'Erreur lors de la visualisation du certificat: ' . $e->getMessage());
        }
    }

    /**
     * Visualise un certificat optimisé pour mobile
     */
    private function viewMobile(Certificate $certificate)
    {
        try {
            // Vérifier si le certificat a un numéro de certificat
            if (empty($certificate->certificate_number)) {
                throw new \Exception('Le certificat n\'a pas de numéro valide.');
            }

            // Définir le chemin du PDF basé sur le numéro de certificat
            $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';

            // Vérifier si le PDF existe
            if (!Storage::disk('public')->exists($pdfPath)) {
                throw new \Exception('Le certificat PDF n\'est pas disponible.');
            }

            // Obtenir le chemin physique complet du fichier
            $fullPath = Storage::disk('public')->path($pdfPath);

            // Vérifier que le fichier existe physiquement
            if (!file_exists($fullPath)) {
                throw new \Exception("Le fichier PDF n'existe pas sur le disque: " . $fullPath);
            }

            // Headers optimisés pour mobile avec response()->file()
            return response()->file($fullPath, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="Certificat_' . $certificate->certificate_number . '.pdf"',
                'Cache-Control' => 'public, max-age=3600', // Cache for mobile
                'X-Frame-Options' => 'SAMEORIGIN',
                'X-Content-Type-Options' => 'nosniff',
                'X-Mobile-Optimized' => 'true'
            ]);

        } catch (\Exception $e) {
            // En cas d'erreur, rediriger vers la page de téléchargement
            \Log::error('Erreur lors de la visualisation mobile du certificat: ' . $e->getMessage());
            return redirect()->route('student.certificates.download', $certificate->id);
        }
    }

    /**
     * Télécharge un certificat
     */
    public function download(Certificate $certificate)
    {
        // Vérifier que le certificat appartient à l'utilisateur connecté
        if ($certificate->user_id !== Auth::id()) {
            abort(403, 'Vous n\'êtes pas autorisé à télécharger ce certificat.');
        }

        // Vérifier que le certificat est téléchargeable (actif ou émis)
        if (!$certificate->isDownloadable()) {
            abort(403, 'Ce certificat n\'est pas encore disponible au téléchargement.');
        }

        try {
            // Vérifier si le certificat a un numéro de certificat
            if (empty($certificate->certificate_number)) {
                throw new \Exception('Le certificat n\'a pas de numéro valide.');
            }

            // Définir le chemin du PDF basé sur le numéro de certificat
            $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';

            // Vérifier si le fichier existe
            if (!Storage::disk('public')->exists($pdfPath)) {
                // Si le fichier n'existe pas, essayer de le générer
                // Utiliser le wrapper DomPDF de Laravel pour générer le PDF
                $enrollment = $certificate->enrollment;
                $user = $certificate->user;
                $trainingSession = $certificate->trainingSession;
                $trainingDomain = $trainingSession->trainingDomain;

                // Préparer les données pour la vue
                $data = [
                    'certificate' => $certificate,
                    'enrollment' => $enrollment,
                    'user' => $user,
                    'trainingSession' => $trainingSession,
                    'trainingDomain' => $trainingDomain
                ];

                // Générer le PDF
                $pdf = app('dompdf.wrapper');
                $pdf->loadView('certificates.pdf', $data);
                $pdf->setPaper('a4', 'portrait');
                $pdf->setOptions([
                    'defaultFont' => 'sans-serif',
                    'isHtml5ParserEnabled' => true,
                    'isRemoteEnabled' => false
                ]);

                // Sauvegarder le PDF
                Storage::disk('public')->put($pdfPath, $pdf->output());

                // Mettre à jour le chemin du PDF dans la base de données
                $certificate->update(['pdf_path' => $pdfPath]);
            } else if (empty($certificate->pdf_path)) {
                // Le fichier existe mais le chemin n'est pas dans la base de données
                $certificate->update(['pdf_path' => $pdfPath]);
            }

            // Vérifier à nouveau que le fichier existe
            if (!Storage::disk('public')->exists($pdfPath)) {
                throw new \Exception('Le fichier du certificat est introuvable après génération.');
            }

            // Obtenir le chemin physique complet du fichier
            $fullPath = Storage::disk('public')->path($pdfPath);

            // Vérifier que le fichier existe physiquement
            if (!file_exists($fullPath)) {
                throw new \Exception("Le fichier PDF n'existe pas sur le disque: " . $fullPath);
            }

            // Générer un nom de fichier pour le téléchargement
            $filename = 'Certificat_' . $certificate->certificate_number . '.pdf';

            // Retourner le fichier pour téléchargement
            return response()->download($fullPath, $filename, [
                'Content-Type' => 'application/pdf',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);
        } catch (\Exception $e) {
            // En cas d'erreur, enregistrer l'erreur dans les logs
            \Log::error('Erreur lors du téléchargement du certificat: ' . $e->getMessage());
            \Log::error('Trace: ' . $e->getTraceAsString());

            // Retourner une erreur
            abort(500, 'Erreur lors du téléchargement du certificat: ' . $e->getMessage());
        }
    }
}
