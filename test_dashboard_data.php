<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Http\Controllers\Student\DashboardController;
use Illuminate\Support\Facades\Auth;

// Récupérer un étudiant
$student = User::where('role', 'student')->first();

if (!$student) {
    echo "Erreur: Aucun étudiant trouvé\n";
    exit(1);
}

echo "Test des données du dashboard pour: {$student->name}\n\n";

// Simuler la connexion
Auth::login($student);

// Créer une instance du contrôleur
$controller = new DashboardController();

// Tester la méthode getSessionsByDepartment
$completedLevels = $student->getCompletedLevelsByDepartment();
echo "Niveaux complétés:\n";
print_r($completedLevels);

// Utiliser la réflexion pour accéder à la méthode privée
$reflection = new ReflectionClass($controller);
$method = $reflection->getMethod('getSessionsByDepartment');
$method->setAccessible(true);

try {
    $sessionsByDepartment = $method->invoke($controller, $student, $completedLevels);
    
    echo "\nSessions par département:\n";
    foreach ($sessionsByDepartment as $department => $levels) {
        echo "\n=== $department ===\n";
        foreach ($levels as $level => $sessions) {
            echo "  $level: " . count($sessions) . " session(s)\n";
            foreach ($sessions as $sessionData) {
                echo "    - {$sessionData['session']->title} ({$sessionData['state']}): {$sessionData['reason']}\n";
            }
        }
    }
} catch (Exception $e) {
    echo "Erreur lors du test: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

echo "\nTest terminé!\n";
