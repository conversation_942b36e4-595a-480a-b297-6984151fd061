<template>
  <Head :title="domain.name" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          {{ domain.name }}
        </h2>
        <div class="flex space-x-2">
          <Link :href="route('admin.training-domains.edit', domain.id)" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
            Modifier
          </Link>
          <Link :href="route('admin.training-domains.index')" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
            Retour à la liste
          </Link>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 text-gray-900">
            <div class="flex flex-col md:flex-row">
              <!-- Image du domaine -->
              <div v-if="domain.image" class="md:w-1/3 mb-4 md:mb-0 md:mr-6">
                <img :src="'/storage/' + domain.image" alt="Image du domaine" class="w-full h-auto rounded-lg" />
              </div>
              
              <!-- Informations du domaine -->
              <div class="md:w-2/3">
                <div class="mb-4">
                  <h3 class="text-lg font-semibold mb-2">Description</h3>
                  <p class="text-gray-700">{{ domain.description || 'Aucune description' }}</p>
                </div>
                
                <div class="mb-4">
                  <h3 class="text-lg font-semibold mb-2">Statut</h3>
                  <span :class="{
                    'px-3 py-1 text-sm rounded-full': true,
                    'bg-green-100 text-green-800': domain.active,
                    'bg-red-100 text-red-800': !domain.active
                  }">
                    {{ domain.active ? 'Actif' : 'Inactif' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sessions de formation associées -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Sessions de formation associées</h3>
            
            <div v-if="sessions.length > 0" class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Titre</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Formateur</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dates</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="session in sessions" :key="session.id">
                    <td class="px-6 py-4 whitespace-nowrap">{{ session.title }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ session.trainer?.name || 'Non assigné' }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      {{ formatDate(session.start_date) }} - {{ formatDate(session.end_date) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="{
                        'px-2 py-1 text-xs rounded-full': true,
                        'bg-green-100 text-green-800': session.active,
                        'bg-red-100 text-red-800': !session.active
                      }">
                        {{ session.active ? 'Actif' : 'Inactif' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                      <Link :href="route('admin.training-sessions.show', session.id)" class="text-blue-600 hover:text-blue-800">
                        Voir
                      </Link>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-else class="text-gray-500 italic">
              Aucune session de formation associée à ce domaine.
            </div>
            
            <div class="mt-4 text-right">
              <Link :href="route('admin.training-sessions.create')" class="text-blue-600 hover:text-blue-800">
                Créer une nouvelle session
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  domain: Object,
  sessions: Array,
});

// Méthodes
const formatDate = (dateString) => {
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};
</script>
