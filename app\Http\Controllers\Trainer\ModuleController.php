<?php

namespace App\Http\Controllers\Trainer;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Module;
use App\Models\TrainingSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ModuleController extends Controller
{
    /**
     * Affiche la liste des modules pour un cours
     */
    public function index(Request $request)
    {
        $courseId = $request->query('course_id');

        if (!$courseId) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Veuillez sélectionner un cours pour voir ses modules.');
        }

        // Vérifier que le cours appartient bien au formateur
        $course = Course::with(['trainingSession', 'modules.materials'])
            ->findOrFail($courseId);
            
        $trainingSession = TrainingSession::findOrFail($course->training_session_id);
        
        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce cours.');
        }

        return Inertia::render('Trainer/Modules/Index', [
            'course' => $course,
            'modules' => $course->modules()->with('materials')->orderBy('order')->get(),
        ]);
    }

    /**
     * Affiche le formulaire de création d'un module
     */
    public function create(Request $request)
    {
        $courseId = $request->query('course_id');

        if (!$courseId) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Veuillez sélectionner un cours pour ajouter un module.');
        }

        // Vérifier que le cours appartient bien au formateur
        $course = Course::with('trainingSession')->findOrFail($courseId);
        $trainingSession = TrainingSession::findOrFail($course->training_session_id);
        
        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce cours.');
        }

        // Déterminer l'ordre du prochain module
        $nextOrder = Module::where('course_id', $courseId)->max('order') + 1;

        return Inertia::render('Trainer/Modules/Create', [
            'course' => $course,
            'nextOrder' => $nextOrder,
        ]);
    }

    /**
     * Enregistre un nouveau module
     */
    public function store(Request $request)
    {
        // Valider les données
        $validated = $request->validate([
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'order' => 'required|integer|min:1',
            'is_published' => 'boolean',
            'publish_date' => 'nullable|date',
        ]);

        // Vérifier que le cours appartient bien au formateur
        $course = Course::findOrFail($validated['course_id']);
        $trainingSession = TrainingSession::findOrFail($course->training_session_id);
        
        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce cours.');
        }

        // Créer le module
        Module::create($validated);

        return redirect()->route('trainer.modules.index', ['course_id' => $validated['course_id']])
            ->with('success', 'Module créé avec succès.');
    }

    /**
     * Affiche un module
     */
    public function show(string $id)
    {
        $module = Module::with(['course.trainingSession', 'materials'])->findOrFail($id);
        
        // Vérifier que le module appartient bien au formateur
        $trainingSession = TrainingSession::findOrFail($module->course->training_session_id);
        
        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce module.');
        }

        return Inertia::render('Trainer/Modules/Show', [
            'module' => $module,
            'course' => $module->course,
            'materials' => $module->materials()->orderBy('order')->get(),
        ]);
    }

    /**
     * Affiche le formulaire de modification d'un module
     */
    public function edit(string $id)
    {
        $module = Module::with('course.trainingSession')->findOrFail($id);
        
        // Vérifier que le module appartient bien au formateur
        $trainingSession = TrainingSession::findOrFail($module->course->training_session_id);
        
        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce module.');
        }

        return Inertia::render('Trainer/Modules/Edit', [
            'module' => $module,
            'course' => $module->course,
        ]);
    }

    /**
     * Met à jour un module
     */
    public function update(Request $request, string $id)
    {
        $module = Module::with('course.trainingSession')->findOrFail($id);
        
        // Vérifier que le module appartient bien au formateur
        $trainingSession = TrainingSession::findOrFail($module->course->training_session_id);
        
        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce module.');
        }

        // Valider les données
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'order' => 'required|integer|min:1',
            'is_published' => 'boolean',
            'publish_date' => 'nullable|date',
        ]);

        // Mettre à jour le module
        $module->update($validated);

        return redirect()->route('trainer.modules.index', ['course_id' => $module->course_id])
            ->with('success', 'Module mis à jour avec succès.');
    }

    /**
     * Supprime un module
     */
    public function destroy(string $id)
    {
        $module = Module::with('course.trainingSession')->findOrFail($id);
        
        // Vérifier que le module appartient bien au formateur
        $trainingSession = TrainingSession::findOrFail($module->course->training_session_id);
        
        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce module.');
        }

        $courseId = $module->course_id;

        // Supprimer le module
        $module->delete();

        return redirect()->route('trainer.modules.index', ['course_id' => $courseId])
            ->with('success', 'Module supprimé avec succès.');
    }
}
