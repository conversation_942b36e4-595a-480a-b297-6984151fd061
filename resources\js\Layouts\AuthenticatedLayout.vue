<script setup>
import { ref } from 'vue';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';
import NavLink from '@/Components/NavLink.vue';
import NotificationDropdown from '@/Components/NotificationDropdown.vue';
import { Link } from '@inertiajs/vue3';
import {
  HomeIcon, UsersIcon, AcademicCapIcon, ClipboardDocumentListIcon, DocumentTextIcon, Cog6ToothIcon, BellIcon, BookOpenIcon, UserGroupIcon, CheckBadgeIcon, ChartBarIcon, MegaphoneIcon, HeartIcon
} from '@heroicons/vue/24/outline';

const sidebarOpen = ref(false); // mobile
const sidebarCollapsed = ref(false); // desktop

const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
};
const toggleSidebarMobile = () => {
  sidebarOpen.value = !sidebarOpen.value;
};

const menuItems = [
  // Tableau de bord unique pour tous les rôles
  { label: 'Tableau de bord', icon: HomeIcon, route: 'dashboard', roles: ['admin','trainer','student'] },
  // Admin
  { label: 'Domaines', icon: BookOpenIcon, route: 'admin.training-domains.index', roles: ['admin'] },
  { label: 'Sessions', icon: ClipboardDocumentListIcon, route: 'admin.training-sessions.index', roles: ['admin'] },
  { label: 'Formateurs', icon: AcademicCapIcon, route: 'admin.trainers.index', roles: ['admin'] },
  { label: 'Gestion des Apprenants', icon: UsersIcon, route: 'admin.students.index', roles: ['admin'] },
  { label: 'Inscriptions', icon: UsersIcon, route: 'admin.enrollments.index', roles: ['admin'] },
  { label: 'Annonces', icon: BellIcon, route: 'admin.announcements.index', roles: ['admin'] },
  { label: 'Examens', icon: DocumentTextIcon, route: 'admin.exams.index', roles: ['admin'] },
  { label: 'Résultats', icon: ChartBarIcon, route: 'admin.exam-results.index', roles: ['admin'] },
  { label: 'Certificats', icon: CheckBadgeIcon, route: 'admin.certificates.index', roles: ['admin'] },
  { label: 'Cours', icon: ClipboardDocumentListIcon, route: 'admin.courses.index', roles: ['admin'] },
  { label: 'Conseils en premiers secours', icon: HeartIcon, route: 'admin.first-aid-tips.index', roles: ['admin'] },
  { label: 'Contenu page d\'accueil', icon: HomeIcon, route: 'admin.homepage-content.index', roles: ['admin'] },
  // Trainer
  { label: 'Mes sessions', icon: ClipboardDocumentListIcon, route: 'trainer.sessions.index', roles: ['trainer'] },
  { label: 'Mes cours', icon: BookOpenIcon, route: 'trainer.courses.index', roles: ['trainer'] },
  { label: 'Mes examens', icon: DocumentTextIcon, route: 'trainer.exams.index', roles: ['trainer'] },
  { label: 'Banque de questions', icon: ChartBarIcon, route: 'trainer.question-bank.index', roles: ['trainer'] },
  { label: 'Évaluations', icon: CheckBadgeIcon, route: 'trainer.evaluations.index', roles: ['trainer'] },
  { label: 'Résultats', icon: ChartBarIcon, route: 'trainer.exam-results.index', roles: ['trainer'] },
  { label: 'Annonces', icon: MegaphoneIcon, route: 'announcements.index', roles: ['trainer'] },
  // Student
  { label: 'Sessions disponibles', icon: ClipboardDocumentListIcon, route: 'student.sessions.index', roles: ['student'] },
  { label: 'Mes inscriptions', icon: UserGroupIcon, route: 'student.enrollments.index', roles: ['student'] },
  { label: 'Mes cours', icon: BookOpenIcon, route: 'student.course-progress.index', roles: ['student'] },
  { label: 'Conseils en premiers secours', icon: HeartIcon, route: 'student.first-aid-tips.index', roles: ['student'] },
  { label: 'Mes examens', icon: DocumentTextIcon, route: 'student.exams.index', roles: ['student'] },
  { label: 'Mes certificats', icon: CheckBadgeIcon, route: 'student.certificates', roles: ['student'] },
  { label: 'Annonces', icon: MegaphoneIcon, route: 'announcements.index', roles: ['student'] },
];
</script>

<template>
  <div class="min-h-screen flex bg-gray-100">
    <!-- Sidebar -->
    <aside :class="[
      'fixed z-30 inset-y-0 left-0 flex flex-col bg-white border-r shadow-lg transition-all duration-300',
      sidebarCollapsed ? 'w-20' : 'w-64',
      'h-screen',
      'hidden md:flex'
    ]">
      <div class="flex items-center justify-between h-16 px-4 border-b">
        <Link :href="route('dashboard')">
          <ApplicationLogo :class="sidebarCollapsed ? 'h-8 w-8' : 'h-12 w-auto'" />
        </Link>
        <button @click="toggleSidebar" class="p-2 rounded hover:bg-gray-100 transition md:block hidden">
          <svg v-if="!sidebarCollapsed" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 12H5" /></svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14" /></svg>
        </button>
      </div>
      <nav class="flex-1 py-4 space-y-2">
        <template v-for="item in menuItems" :key="item.label">
          <Link v-if="item.roles.includes($page.props.auth.user.role)" :href="route(item.route)" :class="[
            'flex items-center gap-3 px-4 py-2 rounded-lg mx-2 transition',
            sidebarCollapsed ? 'justify-center' : '',
            route().current(item.route) ? 'bg-blue-600 text-white shadow' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700'
          ]">
            <component :is="item.icon" class="w-6 h-6" />
            <span v-if="!sidebarCollapsed" class="truncate">{{ item.label }}</span>
          </Link>
        </template>
      </nav>
      <!-- Paramètres ou autres liens bas de sidebar -->
      <div class="mt-auto p-4">
        <Link :href="route('profile.edit')" :class="[
          'flex items-center gap-3 px-4 py-2 rounded-lg transition',
          sidebarCollapsed ? 'justify-center' : '',
          'text-gray-500 hover:bg-gray-100 hover:text-blue-700'
        ]">
          <Cog6ToothIcon class="w-6 h-6" />
          <span v-if="!sidebarCollapsed">Paramètres</span>
        </Link>
      </div>
    </aside>

    <!-- Overlay mobile -->
    <div v-if="sidebarOpen" class="fixed inset-0 z-30 bg-black bg-opacity-50 md:hidden transition-opacity duration-300" @click="toggleSidebarMobile"></div>
    <!-- Sidebar mobile améliorée -->
    <aside :class="[
      'fixed z-40 inset-y-0 left-0 flex flex-col bg-white border-r shadow-lg transition-all duration-300',
      sidebarOpen ? 'w-64 translate-x-0' : 'w-64 -translate-x-full',
      'h-screen',
      'md:hidden',
      'transform'
    ]">
      <div class="flex items-center justify-between h-16 px-4 border-b">
        <Link :href="route('dashboard')">
          <ApplicationLogo class="h-12 w-auto" />
        </Link>
        <button @click="toggleSidebarMobile" class="p-2 rounded hover:bg-gray-100 transition">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
        </button>
      </div>
      <nav class="flex-1 py-6 space-y-2 overflow-y-auto">
        <template v-for="item in menuItems" :key="item.label">
          <Link v-if="item.roles.includes($page.props.auth.user.role)" :href="route(item.route)" class="flex items-center gap-4 px-6 py-3 rounded-lg mx-2 text-lg transition text-gray-700 hover:bg-blue-50 hover:text-blue-700">
            <component :is="item.icon" class="w-7 h-7" />
            <span class="truncate">{{ item.label }}</span>
          </Link>
        </template>
      </nav>
      <div class="mt-auto p-6">
        <Link :href="route('profile.edit')" class="flex items-center gap-4 px-6 py-3 rounded-lg text-lg transition text-gray-500 hover:bg-gray-100 hover:text-blue-700">
          <Cog6ToothIcon class="w-7 h-7" />
          <span>Paramètres</span>
        </Link>
      </div>
    </aside>

    <!-- Main content -->
    <div :class="[
      'flex-1 flex flex-col min-h-screen transition-all duration-300',
      sidebarCollapsed ? 'md:ml-20' : 'md:ml-64',
      'ml-0'
    ]">
      <!-- Topbar amélioré -->
      <header class="h-16 flex items-center justify-between px-4 bg-white border-b shadow-sm sticky top-0 z-10">
        <button @click="toggleSidebarMobile" class="md:hidden p-2 rounded hover:bg-gray-100 transition mr-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
        </button>
        <div class="flex-1 flex items-center justify-center">
          <slot name="header" />
        </div>
        <div class="flex items-center gap-4">
          <!-- Quick Access to Homepage Content (Admin only) -->
          <Link v-if="$page.props.auth.user.role === 'admin'"
                :href="route('admin.homepage-content.index')"
                class="inline-flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                title="Gérer la page d'accueil">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
            </svg>
            <span class="hidden sm:inline">Page d'accueil</span>
          </Link>

          <!-- Notification Dropdown -->
          <NotificationDropdown />

          <Dropdown align="right" width="48">
            <template #trigger>
              <span class="inline-flex rounded-md">
                <button type="button" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none transition ease-in-out duration-150">
                  {{ $page.props.auth.user.name }}
                  <svg class="ms-2 -me-0.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </button>
              </span>
            </template>
            <template #content>
              <DropdownLink :href="route('profile.edit')"> Profile </DropdownLink>
              <DropdownLink :href="route('logout')" method="post" as="button">Log Out</DropdownLink>
            </template>
          </Dropdown>
        </div>
      </header>
      <main class="flex-1 p-4">
        <slot />
      </main>
    </div>
  </div>
</template>
