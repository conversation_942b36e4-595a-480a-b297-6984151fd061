<?php

// Test simple pour déboguer le problème de profil

echo "=== TEST DEBUG PROFIL ===\n";

// Simuler les données du formulaire
$formData = [
    'name' => 'ahmed ayari',
    'email' => '<EMAIL>',
    'phone' => '5252545248',
    'birth_date' => '1990-10-20',
    'address' => '',
    'id_card_number' => '',
    'profession' => '',
    'company' => '',
    'discovery_source' => 'facebook',
    'discovery_source_other' => ''
];

echo "Données du formulaire :\n";
print_r($formData);

// Vérifier les règles de validation
$rules = [
    'name' => ['required', 'string', 'max:255'],
    'email' => ['required', 'string', 'lowercase', 'email', 'max:255'],
    'phone' => ['required', 'string', 'max:20'],
    'birth_date' => ['required', 'date'],
    'address' => ['nullable', 'string'],
    'id_card_number' => ['required', 'string', 'max:50'],
    'profession' => ['required', 'string', 'max:100'],
    'company' => ['nullable', 'string', 'max:100'],
    'discovery_source' => ['required', 'string', 'in:facebook,instagram,tiktok,word_of_mouth,other'],
    'discovery_source_other' => ['nullable', 'string', 'max:100', 'required_if:discovery_source,other'],
];

echo "\nRègles de validation :\n";
print_r($rules);

// Vérifier quels champs échouent
$errors = [];

foreach ($rules as $field => $fieldRules) {
    $value = $formData[$field] ?? null;
    
    foreach ($fieldRules as $rule) {
        if (is_string($rule)) {
            if ($rule === 'required' && empty($value)) {
                $errors[$field][] = "Le champ $field est requis";
            }
        }
    }
}

echo "\nErreurs détectées :\n";
print_r($errors);

echo "\n=== FIN TEST ===\n";
