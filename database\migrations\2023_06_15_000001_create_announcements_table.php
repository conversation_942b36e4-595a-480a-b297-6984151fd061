<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('announcements', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('content');
            $table->foreignId('author_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('training_session_id')->nullable()->constrained()->onDelete('cascade');
            $table->enum('type', ['general', 'session_specific'])->default('general');
            $table->enum('visibility', ['all', 'students', 'trainers', 'admins'])->default('all');
            $table->boolean('is_important')->default(false);
            $table->boolean('is_published')->default(true);
            $table->timestamp('publish_date')->useCurrent();
            $table->timestamp('expiry_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('announcements');
    }
};
