<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\TrainingSession;

class UpdateTrainingSessionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Mettre à jour toutes les sessions existantes avec des valeurs par défaut
        $sessions = TrainingSession::whereNull('department')->orWhereNull('level')->get();

        foreach ($sessions as $session) {
            $session->update([
                'department' => $session->department ?? 'Secourisme',
                'level' => $session->level ?? 'Niveau 1'
            ]);
        }

        $this->command->info('Sessions de formation mises à jour avec succès.');
    }
}
