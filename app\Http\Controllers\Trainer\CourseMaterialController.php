<?php

namespace App\Http\Controllers\Trainer;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\CourseMaterial;
use App\Models\Module;
use App\Models\TrainingSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class CourseMaterialController extends Controller
{
    /**
     * Affiche la liste des matériels de cours pour un module
     */
    public function index(Request $request)
    {
        $moduleId = $request->query('module_id');
        $courseId = $request->query('course_id');

        if (!$moduleId && !$courseId) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Veuillez sélectionner un cours ou un module pour voir ses matériels pédagogiques.');
        }

        if ($moduleId) {
            // Afficher les matériels d'un module spécifique
            $module = Module::with(['course.trainingSession'])->findOrFail($moduleId);

            // Vérifier que le module appartient bien au formateur
            $trainingSession = TrainingSession::findOrFail($module->course->training_session_id);

            if ($trainingSession->trainer_id !== Auth::id()) {
                return redirect()->route('trainer.courses.index')
                    ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce module.');
            }

            $materials = CourseMaterial::where('module_id', $moduleId)
                ->orderBy('order')
                ->get();

            return Inertia::render('Trainer/CourseMaterials/Index', [
                'module' => $module,
                'course' => $module->course,
                'materials' => $materials,
            ]);
        } else {
            // Afficher les matériels d'un cours (sans module)
            $course = Course::with('trainingSession')->findOrFail($courseId);

            // Vérifier que le cours appartient bien au formateur
            $trainingSession = TrainingSession::findOrFail($course->training_session_id);

            if ($trainingSession->trainer_id !== Auth::id()) {
                return redirect()->route('trainer.courses.index')
                    ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce cours.');
            }

            $materials = CourseMaterial::where('course_id', $courseId)
                ->whereNull('module_id')
                ->orderBy('order')
                ->get();

            return Inertia::render('Trainer/CourseMaterials/Index', [
                'course' => $course,
                'materials' => $materials,
            ]);
        }
    }

    /**
     * Affiche le formulaire de création d'un matériel de cours
     */
    public function create(Request $request)
    {
        $moduleId = $request->query('module_id');
        $courseId = $request->query('course_id');

        if (!$moduleId && !$courseId) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Veuillez sélectionner un cours ou un module pour ajouter un matériel pédagogique.');
        }

        if ($moduleId) {
            // Ajouter un matériel à un module spécifique
            $module = Module::with(['course.trainingSession'])->findOrFail($moduleId);

            // Vérifier que le module appartient bien au formateur
            $trainingSession = TrainingSession::findOrFail($module->course->training_session_id);

            if ($trainingSession->trainer_id !== Auth::id()) {
                return redirect()->route('trainer.courses.index')
                    ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce module.');
            }

            // Déterminer l'ordre du prochain matériel
            $nextOrder = CourseMaterial::where('module_id', $moduleId)->max('order') + 1;

            return Inertia::render('Trainer/CourseMaterials/Create', [
                'module' => $module,
                'course' => $module->course,
                'nextOrder' => $nextOrder,
                'materialTypes' => $this->getMaterialTypes(),
            ]);
        } else {
            // Ajouter un matériel à un cours (sans module)
            $course = Course::with('trainingSession')->findOrFail($courseId);

            // Vérifier que le cours appartient bien au formateur
            $trainingSession = TrainingSession::findOrFail($course->training_session_id);

            if ($trainingSession->trainer_id !== Auth::id()) {
                return redirect()->route('trainer.courses.index')
                    ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce cours.');
            }

            // Déterminer l'ordre du prochain matériel
            $nextOrder = CourseMaterial::where('course_id', $courseId)
                ->whereNull('module_id')
                ->max('order') + 1;

            return Inertia::render('Trainer/CourseMaterials/Create', [
                'course' => $course,
                'nextOrder' => $nextOrder,
                'materialTypes' => $this->getMaterialTypes(),
            ]);
        }
    }

    /**
     * Enregistre un nouveau matériel de cours
     */
    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'course_id' => 'required|exists:courses,id',
            'module_id' => 'nullable|exists:modules,id',
            'type' => 'required|in:text,pdf,video,audio,image,archive,gallery,embed_video',
            'content' => 'nullable|string',
            'embed_code' => 'nullable|string',
            'video_url' => 'nullable|url',
            'file' => 'nullable|file',
            'gallery_files.*' => 'nullable|file|image',
            'order' => 'nullable|integer|min:0',
            'active' => 'boolean',
            'allow_download' => 'boolean',
            'allow_online_viewing' => 'boolean',
        ]);

        // Vérifier que le cours appartient bien au formateur
        $course = Course::findOrFail($validated['course_id']);
        $trainingSession = TrainingSession::findOrFail($course->training_session_id);

        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce cours.');
        }

        // Si un module est spécifié, vérifier qu'il appartient bien au cours
        if (isset($validated['module_id'])) {
            $module = Module::findOrFail($validated['module_id']);
            if ($module->course_id !== $validated['course_id']) {
                return redirect()->back()->withErrors([
                    'module_id' => 'Le module sélectionné n\'appartient pas à ce cours.'
                ]);
            }
        }

        // Préparer les données du matériel
        $materialData = [
            'title' => $validated['title'],
            'description' => $validated['description'] ?? null,
            'course_id' => $validated['course_id'],
            'module_id' => $validated['module_id'] ?? null,
            'type' => $validated['type'],
            'order' => $validated['order'] ?? 0,
            'active' => $validated['active'] ?? true,
            'allow_download' => $validated['allow_download'] ?? true,
            'allow_online_viewing' => $validated['allow_online_viewing'] ?? true,
        ];

        // Traiter le contenu en fonction du type
        if ($validated['type'] === 'text') {
            $materialData['content'] = $validated['content'] ?? '';
        } elseif ($validated['type'] === 'embed_video') {
            // Pour les vidéos externes, stocker l'URL ou le code d'intégration
            if (isset($validated['video_url'])) {
                $materialData['content'] = $validated['video_url'];
            } elseif (isset($validated['embed_code'])) {
                $materialData['embed_code'] = $validated['embed_code'];
            }
        } elseif ($validated['type'] === 'gallery') {
            // Pour les galeries, traiter les fichiers multiples
            if ($request->hasFile('gallery_files')) {
                $galleryPaths = [];
                foreach ($request->file('gallery_files') as $file) {
                    $path = $file->store('course-materials/gallery', 'public');
                    $galleryPaths[] = $path;
                }
                $materialData['metadata'] = json_encode(['gallery_paths' => $galleryPaths]);
            }
        } elseif ($request->hasFile('file')) {
            // Pour les autres types avec fichier
            $file = $request->file('file');
            $path = $file->store('course-materials/' . $validated['type'], 'public');
            $materialData['file_path'] = $path;
            $materialData['mime_type'] = $file->getMimeType();
            $materialData['file_size'] = $file->getSize() / 1024; // Convertir en Ko

            // Générer une miniature pour les images
            if ($validated['type'] === 'image') {
                $materialData['thumbnail_path'] = $path;
            }
        }

        // Créer le matériel de cours
        CourseMaterial::create($materialData);

        // Rediriger vers la liste des matériels
        if (isset($validated['module_id'])) {
            return redirect()->route('trainer.course-materials.index', ['module_id' => $validated['module_id']])
                ->with('success', 'Matériel pédagogique ajouté avec succès.');
        } else {
            return redirect()->route('trainer.course-materials.index', ['course_id' => $validated['course_id']])
                ->with('success', 'Matériel pédagogique ajouté avec succès.');
        }
    }

    /**
     * Affiche un matériel de cours
     */
    public function show(string $id)
    {
        $material = CourseMaterial::with(['course.trainingSession', 'module'])->findOrFail($id);

        // Vérifier que le matériel appartient bien au formateur
        $trainingSession = TrainingSession::findOrFail($material->course->training_session_id);

        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce matériel.');
        }

        return Inertia::render('Trainer/CourseMaterials/Show', [
            'material' => $material,
            'course' => $material->course,
            'module' => $material->module,
        ]);
    }

    /**
     * Affiche le formulaire de modification d'un matériel de cours
     */
    public function edit(string $id)
    {
        $material = CourseMaterial::with(['course.trainingSession', 'module'])->findOrFail($id);

        // Vérifier que le matériel appartient bien au formateur
        $trainingSession = TrainingSession::findOrFail($material->course->training_session_id);

        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce matériel.');
        }

        // Récupérer les modules du cours pour le sélecteur
        $modules = Module::where('course_id', $material->course_id)
            ->orderBy('order')
            ->get();

        return Inertia::render('Trainer/CourseMaterials/Edit', [
            'material' => $material,
            'course' => $material->course,
            'module' => $material->module,
            'modules' => $modules,
            'materialTypes' => $this->getMaterialTypes(),
        ]);
    }

    /**
     * Met à jour un matériel de cours
     */
    public function update(Request $request, string $id)
    {
        $material = CourseMaterial::with('course.trainingSession')->findOrFail($id);

        // Vérifier que le matériel appartient bien au formateur
        $trainingSession = TrainingSession::findOrFail($material->course->training_session_id);

        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce matériel.');
        }

        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'module_id' => 'nullable|exists:modules,id',
            'type' => 'required|in:text,pdf,video,audio,image,archive,gallery,embed_video',
            'content' => 'nullable|string',
            'embed_code' => 'nullable|string',
            'video_url' => 'nullable|url',
            'file' => 'nullable|file',
            'gallery_files.*' => 'nullable|file|image',
            'order' => 'nullable|integer|min:0',
            'active' => 'boolean',
            'allow_download' => 'boolean',
            'allow_online_viewing' => 'boolean',
        ]);

        // Si un module est spécifié, vérifier qu'il appartient bien au cours
        if (isset($validated['module_id'])) {
            $module = Module::findOrFail($validated['module_id']);
            if ($module->course_id !== $material->course_id) {
                return redirect()->back()->withErrors([
                    'module_id' => 'Le module sélectionné n\'appartient pas à ce cours.'
                ]);
            }
        }

        // Préparer les données du matériel
        $materialData = [
            'title' => $validated['title'],
            'description' => $validated['description'] ?? null,
            'module_id' => $validated['module_id'] ?? null,
            'type' => $validated['type'],
            'order' => $validated['order'] ?? $material->order,
            'active' => $validated['active'] ?? true,
            'allow_download' => $validated['allow_download'] ?? true,
            'allow_online_viewing' => $validated['allow_online_viewing'] ?? true,
        ];

        // Traiter le contenu en fonction du type
        if ($validated['type'] === 'text') {
            $materialData['content'] = $validated['content'] ?? '';
            $materialData['file_path'] = null;
            $materialData['embed_code'] = null;
        } elseif ($validated['type'] === 'embed_video') {
            // Pour les vidéos externes, stocker l'URL ou le code d'intégration
            if (isset($validated['video_url']) && !empty($validated['video_url'])) {
                $materialData['content'] = $validated['video_url'];
                // Journaliser l'URL pour le débogage
                \Log::info('URL de vidéo externe enregistrée', [
                    'material_id' => $id,
                    'video_url' => $validated['video_url']
                ]);
            } elseif (isset($validated['embed_code']) && !empty($validated['embed_code'])) {
                $materialData['embed_code'] = $validated['embed_code'];
                // Journaliser le code d'intégration pour le débogage
                \Log::info('Code d\'intégration de vidéo externe enregistré', [
                    'material_id' => $id,
                    'embed_code' => $validated['embed_code']
                ]);
            } else {
                // Si aucune URL ou code d'intégration n'est fourni, conserver les valeurs existantes
                if (!empty($material->content)) {
                    $materialData['content'] = $material->content;
                }
                if (!empty($material->embed_code)) {
                    $materialData['embed_code'] = $material->embed_code;
                }
            }
            $materialData['file_path'] = null;
        } elseif ($validated['type'] === 'gallery') {
            // Pour les galeries, traiter les fichiers multiples
            if ($request->hasFile('gallery_files')) {
                $galleryPaths = [];
                foreach ($request->file('gallery_files') as $file) {
                    $path = $file->store('course-materials/gallery', 'public');
                    $galleryPaths[] = $path;

                    // Journaliser l'ajout d'image pour le débogage
                    \Log::info('Image ajoutée à la galerie', [
                        'material_id' => $id,
                        'file_name' => $file->getClientOriginalName(),
                        'path' => $path
                    ]);
                }

                // Fusionner avec les chemins existants si nécessaire
                $existingPaths = [];
                if ($material->metadata) {
                    $metadata = is_array($material->metadata) ? $material->metadata : json_decode($material->metadata, true);
                    if (is_array($metadata) && isset($metadata['gallery_paths'])) {
                        $existingPaths = $metadata['gallery_paths'];

                        // Vérifier que les chemins existants sont valides
                        foreach ($existingPaths as $key => $path) {
                            if (!Storage::disk('public')->exists($path)) {
                                \Log::warning('Chemin d\'image de galerie invalide détecté et supprimé', [
                                    'material_id' => $id,
                                    'path' => $path
                                ]);
                                unset($existingPaths[$key]);
                            }
                        }

                        // Réindexer le tableau après suppression des chemins invalides
                        $existingPaths = array_values($existingPaths);
                    }
                }

                $mergedPaths = array_merge($existingPaths, $galleryPaths);
                $materialData['metadata'] = json_encode(['gallery_paths' => $mergedPaths]);

                // Journaliser les métadonnées mises à jour pour le débogage
                \Log::info('Métadonnées de galerie mises à jour', [
                    'material_id' => $id,
                    'existing_paths_count' => count($existingPaths),
                    'new_paths_count' => count($galleryPaths),
                    'total_paths_count' => count($mergedPaths)
                ]);
            } else if (!$material->metadata || empty(json_decode($material->metadata, true)['gallery_paths'])) {
                // Si aucune image n'est fournie et qu'il n'y a pas d'images existantes, initialiser un tableau vide
                $materialData['metadata'] = json_encode(['gallery_paths' => []]);

                \Log::info('Galerie initialisée sans images', [
                    'material_id' => $id
                ]);
            }
        } elseif ($request->hasFile('file')) {
            // Pour les autres types avec fichier
            $file = $request->file('file');
            $path = $file->store('course-materials/' . $validated['type'], 'public');
            $materialData['file_path'] = $path;
            $materialData['mime_type'] = $file->getMimeType();
            $materialData['file_size'] = $file->getSize() / 1024; // Convertir en Ko
            $materialData['embed_code'] = null;

            // Générer une miniature pour les images
            if ($validated['type'] === 'image') {
                $materialData['thumbnail_path'] = $path;
            }
        }

        // Mettre à jour le matériel de cours
        $material->update($materialData);

        // Rediriger vers la liste des matériels
        if ($material->module_id) {
            return redirect()->route('trainer.course-materials.index', ['module_id' => $material->module_id])
                ->with('success', 'Matériel pédagogique mis à jour avec succès.');
        } else {
            return redirect()->route('trainer.course-materials.index', ['course_id' => $material->course_id])
                ->with('success', 'Matériel pédagogique mis à jour avec succès.');
        }
    }

    /**
     * Supprime un matériel de cours
     */
    public function destroy(string $id)
    {
        $material = CourseMaterial::with('course.trainingSession')->findOrFail($id);

        // Vérifier que le matériel appartient bien au formateur
        $trainingSession = TrainingSession::findOrFail($material->course->training_session_id);

        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce matériel.');
        }

        $moduleId = $material->module_id;
        $courseId = $material->course_id;

        // Supprimer le fichier associé si nécessaire
        if ($material->file_path) {
            Storage::disk('public')->delete($material->file_path);
        }

        // Supprimer les fichiers de galerie si nécessaire
        if ($material->type === 'gallery' && $material->metadata) {
            $metadata = json_decode($material->metadata, true);
            if (isset($metadata['gallery_paths'])) {
                foreach ($metadata['gallery_paths'] as $path) {
                    Storage::disk('public')->delete($path);
                }
            }
        }

        // Supprimer le matériel
        $material->delete();

        // Rediriger vers la liste des matériels
        if ($moduleId) {
            return redirect()->route('trainer.course-materials.index', ['module_id' => $moduleId])
                ->with('success', 'Matériel pédagogique supprimé avec succès.');
        } else {
            return redirect()->route('trainer.course-materials.index', ['course_id' => $courseId])
                ->with('success', 'Matériel pédagogique supprimé avec succès.');
        }
    }

    /**
     * Télécharger un fichier associé à un matériel de cours
     */
    public function download(string $id)
    {
        $material = CourseMaterial::with('course.trainingSession')->findOrFail($id);

        // Vérifier que le matériel appartient bien au formateur
        $trainingSession = TrainingSession::findOrFail($material->course->training_session_id);

        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce matériel.');
        }

        // Vérifier si le téléchargement est autorisé
        if (!$material->allow_download) {
            return redirect()->back()->with('error', 'Le téléchargement de ce matériel n\'est pas autorisé.');
        }

        // Vérifier si un fichier est associé
        if (!$material->file_path) {
            return redirect()->back()->with('error', 'Aucun fichier n\'est associé à ce matériel.');
        }

        // Télécharger le fichier
        return Storage::disk('public')->download($material->file_path, $material->title);
    }

    /**
     * Afficher le fichier associé à un matériel de cours
     */
    public function view(Request $request, string $id)
    {
        $material = CourseMaterial::with('course.trainingSession')->findOrFail($id);

        // Vérifier que le matériel appartient bien au formateur
        $trainingSession = TrainingSession::findOrFail($material->course->training_session_id);

        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.courses.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce matériel.');
        }

        // Vérifier si la visualisation en ligne est autorisée
        if (!$material->allow_online_viewing) {
            return redirect()->back()->with('error', 'La visualisation en ligne de ce matériel n\'est pas autorisée.');
        }

        // Vérifier si un fichier est associé
        if (!$material->file_path && $material->type !== 'gallery') {
            return redirect()->back()->with('error', 'Aucun fichier n\'est associé à ce matériel.');
        }

        // Traitement spécial pour les galeries d'images
        if ($material->type === 'gallery') {
            $imageIndex = $request->query('image_index', 0);
            $metadata = is_array($material->metadata) ? $material->metadata : json_decode($material->metadata, true);

            // Journaliser les métadonnées pour le débogage
            \Log::info('Métadonnées de la galerie', [
                'material_id' => $material->id,
                'metadata' => $metadata
            ]);

            // Extraire les chemins d'images en fonction de la structure des métadonnées
            $galleryPaths = [];

            if (is_array($metadata)) {
                if (isset($metadata['gallery_paths']) && is_array($metadata['gallery_paths'])) {
                    $galleryPaths = $metadata['gallery_paths'];
                } elseif (isset($metadata['images']) && is_array($metadata['images'])) {
                    // Format utilisé dans l'interface admin
                    $galleryPaths = array_map(function($img) {
                        return is_array($img) ? ($img['path'] ?? '') : $img;
                    }, $metadata['images']);
                } elseif (is_array($metadata) && !isset($metadata['gallery_paths']) && !isset($metadata['images'])) {
                    // Si les métadonnées sont directement un tableau de chemins
                    $galleryPaths = array_values($metadata);
                }
            }

            // Filtrer les chemins vides
            $galleryPaths = array_filter($galleryPaths);

            if (empty($galleryPaths)) {
                \Log::warning('Aucune image trouvée dans la galerie', [
                    'material_id' => $material->id,
                    'metadata' => $metadata
                ]);
                return response()->json(['error' => 'Aucune image trouvée dans la galerie'], 404);
            }

            // Vérifier si l'index demandé existe
            if (!isset($galleryPaths[$imageIndex])) {
                \Log::warning('Image non trouvée à cet index', [
                    'material_id' => $material->id,
                    'image_index' => $imageIndex,
                    'available_indices' => array_keys($galleryPaths)
                ]);
                return response()->json(['error' => 'Image non trouvée à cet index'], 404);
            }

            $imagePath = $galleryPaths[$imageIndex];
            $filePath = Storage::disk('public')->path($imagePath);

            // Vérifier si le fichier existe
            if (!file_exists($filePath)) {
                \Log::error('Image de galerie non trouvée', [
                    'material_id' => $material->id,
                    'image_index' => $imageIndex,
                    'image_path' => $imagePath,
                    'full_path' => $filePath
                ]);
                return response()->json(['error' => 'Image non trouvée'], 404);
            }

            // Déterminer le type MIME
            $mimeType = mime_content_type($filePath) ?: 'image/jpeg';

            // Journaliser les informations pour le débogage
            \Log::info('Visualisation d\'image de galerie', [
                'material_id' => $material->id,
                'image_index' => $imageIndex,
                'image_path' => $imagePath,
                'full_path' => $filePath,
                'mime_type' => $mimeType
            ]);

            return response()->file($filePath, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => 'inline; filename="' . basename($imagePath) . '"',
            ]);
        }

        // Pour les autres types de matériels
        $filePath = Storage::disk('public')->path($material->file_path);

        // Vérifier si le fichier existe
        if (!file_exists($filePath)) {
            \Log::error('Fichier non trouvé', [
                'material_id' => $material->id,
                'file_path' => $material->file_path,
                'full_path' => $filePath
            ]);
            return response()->json(['error' => 'Fichier non trouvé'], 404);
        }

        // Journaliser les informations pour le débogage
        \Log::info('Visualisation de fichier', [
            'material_id' => $material->id,
            'file_path' => $material->file_path,
            'full_path' => $filePath,
            'mime_type' => $material->mime_type,
            'exists' => file_exists($filePath)
        ]);

        // Afficher le fichier avec le bon type MIME
        return response()->file($filePath, [
            'Content-Type' => $material->mime_type ?? $this->getMimeTypeFromExtension($filePath),
            'Content-Disposition' => 'inline; filename="' . basename($material->file_path) . '"',
        ]);
    }

    /**
     * Détermine le type MIME à partir de l'extension du fichier
     */
    private function getMimeTypeFromExtension($filePath)
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        $mimeTypes = [
            'pdf' => 'application/pdf',
            'mp4' => 'video/mp4',
            'webm' => 'video/webm',
            'ogg' => 'video/ogg',
            'mp3' => 'audio/mpeg',
            'wav' => 'audio/wav',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'zip' => 'application/zip',
            'rar' => 'application/x-rar-compressed',
            '7z' => 'application/x-7z-compressed',
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }

    /**
     * Retourne les types de matériels disponibles
     */
    private function getMaterialTypes()
    {
        return [
            ['value' => 'text', 'label' => 'Texte'],
            ['value' => 'pdf', 'label' => 'Document PDF'],
            ['value' => 'video', 'label' => 'Vidéo'],
            ['value' => 'audio', 'label' => 'Audio (podcast)'],
            ['value' => 'image', 'label' => 'Image'],
            ['value' => 'archive', 'label' => 'Archive (ZIP, RAR, etc.)'],
            ['value' => 'gallery', 'label' => 'Galerie d\'images'],
            ['value' => 'embed_video', 'label' => 'Vidéo externe (YouTube, Dailymotion, Vimeo)']
        ];
    }
}
