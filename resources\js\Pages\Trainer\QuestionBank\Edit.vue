<template>
  <Head title="Modifier une question" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Modifier une question
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Liens de navigation -->
            <div class="mb-6">
              <Link :href="route('trainer.question-bank.index')" class="text-indigo-600 hover:text-indigo-900">
                &larr; Retour à la banque de questions
              </Link>
            </div>

            <!-- Formulaire de modification de question -->
            <form @submit.prevent="submit">
              <div class="grid grid-cols-1 gap-6">
                <!-- Examen -->
                <div>
                  <InputLabel for="exam_id" value="Examen" />
                  <select
                    id="exam_id"
                    v-model="form.exam_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    required
                  >
                    <option value="">Sélectionnez un examen</option>
                    <optgroup 
                      v-for="session in trainingSessions" 
                      :key="session.id" 
                      :label="session.title"
                    >
                      <option 
                        v-for="exam in examsForSession(session.id)" 
                        :key="exam.id" 
                        :value="exam.id"
                      >
                        {{ exam.title }}
                      </option>
                    </optgroup>
                  </select>
                  <InputError class="mt-2" :message="form.errors.exam_id" />
                </div>

                <!-- Type de question -->
                <div>
                  <InputLabel for="question_type" value="Type de question" />
                  <select
                    id="question_type"
                    v-model="form.question_type"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    required
                  >
                    <option value="multiple_choice">Choix multiple (QCM)</option>
                    <option value="text">Texte libre</option>
                    <option value="file">Soumission de fichier</option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.question_type" />
                </div>

                <!-- Texte de la question -->
                <div>
                  <InputLabel for="question_text" value="Texte de la question" />
                  <textarea
                    id="question_text"
                    v-model="form.question_text"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    rows="3"
                    required
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.question_text" />
                </div>

                <!-- Options pour QCM -->
                <div v-if="form.question_type === 'multiple_choice'">
                  <InputLabel value="Options de réponse" />
                  <div class="mt-2 space-y-3">
                    <div v-for="(option, index) in options" :key="index" class="flex items-center">
                      <div class="flex-grow">
                        <div class="flex items-center">
                          <input
                            :id="`option_correct_${index}`"
                            type="checkbox"
                            v-model="correctAnswers"
                            :value="index"
                            class="mr-2 rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                            :disabled="!form.multiple_answers_allowed && correctAnswers.length > 0 && !correctAnswers.includes(index)"
                          />
                          <TextInput
                            :id="`option_${index}`"
                            v-model="options[index]"
                            type="text"
                            class="block w-full"
                            placeholder="Option de réponse"
                            required
                          />
                        </div>
                      </div>
                      <button
                        type="button"
                        @click="removeOption(index)"
                        class="ml-2 text-red-600 hover:text-red-900"
                        :disabled="options.length <= 2"
                      >
                        <XMarkIcon class="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                  <div class="mt-2">
                    <button
                      type="button"
                      @click="addOption"
                      class="text-sm text-indigo-600 hover:text-indigo-900"
                      :disabled="options.length >= 6"
                    >
                      + Ajouter une option
                    </button>
                  </div>
                  <div class="mt-4">
                    <div class="flex items-center">
                      <input
                        id="multiple_answers_allowed"
                        type="checkbox"
                        v-model="form.multiple_answers_allowed"
                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                      />
                      <label for="multiple_answers_allowed" class="ml-2 text-sm text-gray-600">
                        Autoriser plusieurs réponses correctes
                      </label>
                    </div>
                  </div>
                  <InputError class="mt-2" :message="form.errors.options" />
                </div>

                <!-- Réponse correcte pour texte libre -->
                <div v-if="form.question_type === 'text'">
                  <InputLabel for="correct_answer" value="Réponse correcte (facultatif, pour référence)" />
                  <textarea
                    id="correct_answer"
                    v-model="form.correct_answer"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    rows="3"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.correct_answer" />
                </div>

                <!-- Points -->
                <div>
                  <InputLabel for="points" value="Points" />
                  <TextInput
                    id="points"
                    v-model="form.points"
                    type="number"
                    class="mt-1 block w-full"
                    min="1"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.points" />
                </div>

                <!-- Ordre -->
                <div>
                  <InputLabel for="order" value="Ordre" />
                  <TextInput
                    id="order"
                    v-model="form.order"
                    type="number"
                    class="mt-1 block w-full"
                    min="1"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.order" />
                </div>

                <!-- Boutons de soumission -->
                <div class="flex items-center justify-end mt-4">
                  <Link
                    :href="route('trainer.question-bank.index')"
                    class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-4"
                  >
                    Annuler
                  </Link>
                  <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    Mettre à jour la question
                  </PrimaryButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref, computed, watch, onMounted } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import { XMarkIcon } from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  question: Object,
  trainingSessions: Array,
  exams: Array,
});

// Options pour les QCM
const options = ref([]);

// Tableau pour stocker les réponses correctes multiples
const correctAnswers = ref([]);

// Formulaire
const form = useForm({
  exam_id: props.question.exam_id,
  question_text: props.question.question_text,
  question_type: props.question.question_type,
  options: {},
  correct_answer: props.question.correct_answer || '',
  correct_answers: [],
  multiple_answers_allowed: props.question.multiple_answers_allowed || false,
  points: props.question.points,
  order: props.question.order,
});

// Initialiser les options et les réponses correctes
onMounted(() => {
  if (props.question.question_type === 'multiple_choice' && props.question.options) {
    // Convertir les options en tableau si nécessaire
    options.value = Array.isArray(props.question.options) 
      ? [...props.question.options] 
      : typeof props.question.options === 'object' 
        ? Object.values(props.question.options) 
        : [];
    
    // S'assurer qu'il y a au moins 2 options
    if (options.value.length < 2) {
      options.value = [...options.value, ...Array(2 - options.value.length).fill('')];
    }
    
    // Initialiser les réponses correctes
    if (props.question.multiple_answers_allowed && Array.isArray(props.question.correct_answer)) {
      correctAnswers.value = props.question.correct_answer.map(Number);
    } else if (!props.question.multiple_answers_allowed && props.question.correct_answer !== null) {
      const correctIndex = parseInt(props.question.correct_answer);
      if (!isNaN(correctIndex)) {
        correctAnswers.value = [correctIndex];
      }
    }
  } else {
    // Initialiser avec des options vides pour les autres types de questions
    options.value = ['', ''];
  }
});

// Computed
const examsForSession = (sessionId) => {
  return props.exams.filter(exam => exam.training_session_id === sessionId);
};

// Méthodes
const addOption = () => {
  if (options.value.length < 6) { // Limiter à 6 options maximum
    options.value.push('');
  }
};

const removeOption = (index) => {
  if (options.value.length > 2) { // Garder au moins 2 options
    options.value.splice(index, 1);
    
    // Mettre à jour les réponses correctes
    const newCorrectAnswers = [];
    correctAnswers.value.forEach(answerIndex => {
      if (answerIndex < index) {
        newCorrectAnswers.push(answerIndex);
      } else if (answerIndex > index) {
        newCorrectAnswers.push(answerIndex - 1);
      }
    });
    correctAnswers.value = newCorrectAnswers;
  }
};

const submit = () => {
  // Préparer les options pour les QCM
  if (form.question_type === 'multiple_choice') {
    form.options = options.value;
    
    if (form.multiple_answers_allowed) {
      form.correct_answers = correctAnswers.value;
    } else {
      form.correct_answer = correctAnswers.value.length > 0 ? correctAnswers.value[0].toString() : '';
    }
  }
  
  form.put(route('trainer.question-bank.update', props.question.id));
};

// Watchers
watch(() => form.multiple_answers_allowed, (newValue) => {
  if (!newValue && correctAnswers.value.length > 1) {
    // Si on désactive les réponses multiples, ne garder que la première réponse correcte
    correctAnswers.value = correctAnswers.value.slice(0, 1);
  }
});

watch(() => form.question_type, (newValue, oldValue) => {
  if (newValue !== 'multiple_choice') {
    // Réinitialiser les options et les réponses correctes si ce n'est pas un QCM
    options.value = ['', ''];
    correctAnswers.value = [];
    form.multiple_answers_allowed = false;
  } else if (oldValue !== 'multiple_choice' && options.value.length < 2) {
    // Si on passe à un QCM, s'assurer qu'il y a au moins 2 options
    options.value = ['', ''];
  }
});
</script>
