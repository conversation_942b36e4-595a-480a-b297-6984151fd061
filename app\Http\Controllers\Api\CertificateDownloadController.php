<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Certificate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Admin\CertificateController;

class CertificateDownloadController extends Controller
{
    /**
     * Télécharger le PDF du certificat via AJAX.
     */
    public function download(Request $request, $id)
    {
        try {
            // Récupérer le certificat
            $certificate = Certificate::findOrFail($id);

            // Vérifier si l'utilisateur a le droit d'accéder à ce certificat
            if (!auth()->user()->hasRole('admin') && auth()->id() !== $certificate->user_id) {
                return response()->json(['error' => 'Vous n\'êtes pas autorisé à télécharger ce certificat.'], 403);
            }

            // Vérifier si le PDF existe
            $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';
            
            if (!Storage::disk('public')->exists($pdfPath)) {
                // Générer le PDF s'il n'existe pas
                $certificateController = new CertificateController();
                $certificateController->generatePdf($certificate);
                
                // Vérifier à nouveau si le PDF a été créé
                if (!Storage::disk('public')->exists($pdfPath)) {
                    return response()->json(['error' => 'Impossible de générer le PDF'], 500);
                }
            }
            
            // Obtenir le contenu du fichier
            $content = Storage::disk('public')->get($pdfPath);
            
            // Renvoyer le contenu brut du PDF avec les en-têtes appropriés
            return response($content)
                ->header('Content-Type', 'application/pdf')
                ->header('Content-Length', strlen($content));
            
        } catch (\Exception $e) {
            // En cas d'erreur, enregistrer l'erreur dans les logs
            \Log::error('Erreur lors du téléchargement AJAX du PDF: ' . $e->getMessage());
            \Log::error('Trace: ' . $e->getTraceAsString());

            // Renvoyer une erreur JSON
            return response()->json(['error' => 'Une erreur est survenue lors du téléchargement du certificat: ' . $e->getMessage()], 500);
        }
    }
}
