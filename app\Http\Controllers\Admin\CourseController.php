<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\TrainingSession;
use App\Models\TrainingDomain;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CourseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Initialiser la requête
        $query = Course::with(['trainingSession', 'trainingSession.trainingDomain', 'materials']);

        // Appliquer la recherche si elle existe
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where('title', 'like', $searchTerm);
        }

        // Filtrer par session de formation
        if ($request->has('training_session_id') && !empty($request->training_session_id)) {
            $query->where('training_session_id', $request->training_session_id);
        }

        // Filtrer par domaine de formation
        if ($request->has('domain_id') && !empty($request->domain_id)) {
            $query->whereHas('trainingSession', function($q) use ($request) {
                $q->where('training_domain_id', $request->domain_id);
            });
        }

        // Filtrer par statut
        if ($request->has('active') && $request->active !== '') {
            $query->where('active', $request->active === 'true');
        }

        // Filtrer par présence de matériels
        if ($request->has('has_materials') && $request->has_materials !== '') {
            if ($request->has_materials === 'true') {
                $query->has('materials');
            } else {
                $query->doesntHave('materials');
            }
        }

        // Récupérer les cours avec pagination
        $courses = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Récupérer toutes les sessions de formation pour le filtre
        $trainingSessions = TrainingSession::orderBy('title')->get();

        // Récupérer tous les domaines de formation pour le filtre
        $domains = TrainingDomain::orderBy('name')->get();

        // Retourner la vue avec les cours et les filtres
        return Inertia::render('Admin/Courses/Index', [
            'courses' => $courses,
            'trainingSessions' => $trainingSessions,
            'domains' => $domains,
            'filters' => [
                'search' => $request->search ?? '',
                'training_session_id' => $request->training_session_id ?? '',
                'domain_id' => $request->domain_id ?? '',
                'active' => $request->active ?? '',
                'has_materials' => $request->has_materials ?? '',
            ]
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Récupérer toutes les sessions de formation actives
        $trainingSessions = TrainingSession::with(['trainingDomain', 'trainer'])
            ->where('active', true)
            ->orderBy('title')
            ->get();

        return Inertia::render('Admin/Courses/Create', [
            'trainingSessions' => $trainingSessions
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'training_session_id' => 'required|exists:training_sessions,id',
            'order' => 'nullable|integer|min:0',
            'active' => 'boolean',
        ]);

        // Créer le cours
        Course::create($validated);

        // Rediriger vers la liste des cours avec un message de succès
        return redirect()->route('admin.courses.index')
            ->with('success', 'Cours créé avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Récupérer le cours avec ses relations
        $course = Course::with([
            'trainingSession.trainingDomain',
            'trainingSession.trainer',
            'materials' => function ($query) {
                $query->orderBy('order');
            }
        ])->findOrFail($id);

        return Inertia::render('Admin/Courses/Show', [
            'course' => $course
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Récupérer le cours
        $course = Course::findOrFail($id);

        // Récupérer toutes les sessions de formation actives
        $trainingSessions = TrainingSession::with(['trainingDomain', 'trainer'])
            ->orderBy('title')
            ->get();

        return Inertia::render('Admin/Courses/Edit', [
            'course' => $course,
            'trainingSessions' => $trainingSessions
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Récupérer le cours
        $course = Course::findOrFail($id);

        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'training_session_id' => 'required|exists:training_sessions,id',
            'order' => 'nullable|integer|min:0',
            'active' => 'boolean',
        ]);

        // Mettre à jour le cours
        $course->update($validated);

        // Rediriger vers la liste des cours avec un message de succès
        return redirect()->route('admin.courses.index')
            ->with('success', 'Cours mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Récupérer le cours
        $course = Course::findOrFail($id);

        // Vérifier si le cours a des matériels associés
        if ($course->materials()->count() > 0) {
            return redirect()->route('admin.courses.index')
                ->with('error', 'Impossible de supprimer ce cours car il contient des matériels pédagogiques.');
        }

        // Supprimer le cours
        $course->delete();

        // Rediriger vers la liste des cours avec un message de succès
        return redirect()->route('admin.courses.index')
            ->with('success', 'Cours supprimé avec succès.');
    }
}
