<?php

namespace App\Http\Controllers;

use App\Models\TrainingSession;
use App\Models\TrainingDomain;
use App\Models\HomepageContent;
use Illuminate\Http\Request;
use Inertia\Inertia;

class HomeController extends Controller
{
    /**
     * Affiche la page d'accueil avec les sessions de formation
     */
    public function index()
    {
        // Récupérer les sessions de formation actives et à venir
        $sessions = TrainingSession::with(['training_domain', 'trainer'])
            ->withCount('enrollments')
            ->where('active', true)
            ->where('start_date', '>=', now()->startOfDay())
            ->orderBy('start_date')
            ->take(6)
            ->get();

        // Récupérer les domaines de formation pour le filtrage
        $domains = TrainingDomain::orderBy('name')
            ->get();

        // Récupérer le contenu dynamique de la page d'accueil
        $homepageContent = [
            'hero' => HomepageContent::getBySection('hero'),
            'vr_training' => HomepageContent::getBySection('vr_training'),
            'about' => HomepageContent::getBySection('about'),
            'contact' => HomepageContent::getBySection('contact'),
        ];

        return Inertia::render('Welcome', [
            'canLogin' => \Route::has('login'),
            'canRegister' => \Route::has('register'),
            'sessions' => $sessions,
            'domains' => $domains,
            'homepageContent' => $homepageContent,
            'flash' => [
                'error' => session('error'),
                'success' => session('success'),
            ],
        ]);
    }

    /**
     * Affiche les détails d'une session de formation
     */
    public function showSession($id)
    {
        try {
            // Récupérer la session de formation avec ses relations
            $session = TrainingSession::with(['training_domain', 'trainer', 'courses'])
                ->where('active', true)
                ->findOrFail($id);

            // Récupérer d'autres sessions du même domaine pour les suggestions
            $relatedSessions = TrainingSession::where('training_domain_id', $session->training_domain_id)
                ->where('id', '!=', $session->id)
                ->where('active', true)
                ->with(['training_domain'])
                ->take(3)
                ->get();

            return Inertia::render('Sessions/Show', [
                'session' => $session,
                'relatedSessions' => $relatedSessions,
                'canLogin' => \Route::has('login'),
                'canRegister' => \Route::has('register'),
            ]);
        } catch (\Exception $e) {
            // En cas d'erreur, rediriger vers la page d'accueil avec un message d'erreur
            return redirect()->route('welcome')->with('error', 'La session demandée n\'existe pas ou n\'est pas disponible.');
        }
    }

    /**
     * Affiche la page publique des formations avec filtrage et recherche
     */
    public function formations(Request $request)
    {
        // Récupérer les paramètres de filtrage
        $search = $request->get('search', '');
        $department = $request->get('department', '');
        $level = $request->get('level', '');
        $domain_id = $request->get('domain_id', '');
        $sort = $request->get('sort', 'start_date');
        $order = $request->get('order', 'asc');

        // Construire la requête
        $query = TrainingSession::with(['training_domain', 'trainer'])
            ->withCount('enrollments')
            ->where('active', true)
            ->where('start_date', '>=', now()->startOfDay());

        // Appliquer les filtres
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('training_domain', function ($domainQuery) use ($search) {
                      $domainQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        if ($department) {
            $query->where('department', $department);
        }

        if ($level) {
            $query->where('level', $level);
        }

        if ($domain_id) {
            $query->where('training_domain_id', $domain_id);
        }

        // Appliquer le tri
        $validSorts = ['start_date', 'title', 'price', 'created_at'];
        if (in_array($sort, $validSorts)) {
            $query->orderBy($sort, $order === 'desc' ? 'desc' : 'asc');
        } else {
            $query->orderBy('start_date', 'asc');
        }

        // Paginer les résultats
        $sessions = $query->paginate(12)->withQueryString();

        // Récupérer les domaines pour le filtrage
        $domains = TrainingDomain::where('active', true)->orderBy('name')->get();

        // Statistiques
        $stats = [
            'total_sessions' => TrainingSession::where('active', true)->where('start_date', '>=', now()->startOfDay())->count(),
            'departments' => TrainingSession::where('active', true)->where('start_date', '>=', now()->startOfDay())->distinct('department')->count('department'),
            'domains' => $domains->count(),
        ];

        return Inertia::render('Formations/Index', [
            'sessions' => $sessions,
            'domains' => $domains,
            'stats' => $stats,
            'filters' => [
                'search' => $search,
                'department' => $department,
                'level' => $level,
                'domain_id' => $domain_id,
                'sort' => $sort,
                'order' => $order,
            ],
            'departments' => TrainingSession::DEPARTMENTS,
            'levels' => TrainingSession::LEVELS,
        ]);
    }

    /**
     * Affiche la liste des sessions de formation disponibles
     */
    public function listSessions(Request $request)
    {
        $query = TrainingSession::with(['training_domain', 'trainer'])
            ->where('active', true);

        // Filtrage par domaine si spécifié
        if ($request->has('domain')) {
            $query->where('training_domain_id', $request->domain);
        }

        // Filtrage par date si spécifié
        if ($request->has('date')) {
            $query->where('start_date', '>=', $request->date);
        }

        // Tri par date de début
        $sessions = $query->orderBy('start_date')->paginate(9);

        // Récupérer tous les domaines pour le filtre
        $domains = TrainingDomain::orderBy('name')->get();

        return Inertia::render('Sessions/Index', [
            'sessions' => $sessions,
            'domains' => $domains,
            'filters' => $request->only(['domain', 'date']),
            'canLogin' => \Route::has('login'),
            'canRegister' => \Route::has('register'),
        ]);
    }
}
