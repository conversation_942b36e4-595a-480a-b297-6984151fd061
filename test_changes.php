<?php

// Charger l'environnement Laravel
require __DIR__.'/vendor/autoload.php';
$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Vérifier les champs de la table users
echo "=== Vérification des champs de la table users ===\n";
$columns = \Illuminate\Support\Facades\Schema::getColumnListing('users');
echo "Colonnes de la table users : " . implode(', ', $columns) . "\n\n";

// Vérifier si les nouveaux champs sont présents
$requiredFields = ['birth_date', 'id_card_number', 'profession', 'company', 'discovery_source', 'discovery_source_other'];
$missingFields = array_diff($requiredFields, $columns);
if (empty($missingFields)) {
    echo "Tous les champs requis sont présents dans la table users.\n";
} else {
    echo "Champs manquants : " . implode(', ', $missingFields) . "\n";
}

// Vérifier les sessions de formation
echo "\n=== Vérification des sessions de formation ===\n";
$sessions = \App\Models\TrainingSession::with(['trainingDomain', 'trainer'])->get();
echo "Nombre de sessions de formation : " . $sessions->count() . "\n";

if ($sessions->count() > 0) {
    echo "Première session : \n";
    $firstSession = $sessions->first();
    echo "- Titre : " . $firstSession->title . "\n";
    echo "- Domaine : " . $firstSession->trainingDomain->name . "\n";
    echo "- Formateur : " . $firstSession->trainer->name . "\n";
    echo "- Date de début : " . $firstSession->start_date . "\n";
    echo "- Date de fin : " . $firstSession->end_date . "\n";
}

// Vérifier les domaines de formation
echo "\n=== Vérification des domaines de formation ===\n";
$domains = \App\Models\TrainingDomain::all();
echo "Nombre de domaines de formation : " . $domains->count() . "\n";

if ($domains->count() > 0) {
    echo "Liste des domaines : \n";
    foreach ($domains as $domain) {
        echo "- " . $domain->name . "\n";
    }
}

// Vérifier les utilisateurs
echo "\n=== Vérification des utilisateurs ===\n";
$users = \App\Models\User::all();
echo "Nombre d'utilisateurs : " . $users->count() . "\n";

if ($users->count() > 0) {
    echo "Premier utilisateur : \n";
    $firstUser = $users->first();
    echo "- Nom : " . $firstUser->name . "\n";
    echo "- Email : " . $firstUser->email . "\n";
    echo "- Rôle : " . $firstUser->role . "\n";
    echo "- Date de naissance : " . $firstUser->birth_date . "\n";
    echo "- Téléphone : " . $firstUser->phone . "\n";
    echo "- Numéro de carte d'identité : " . $firstUser->id_card_number . "\n";
    echo "- Profession : " . $firstUser->profession . "\n";
    echo "- Entreprise : " . $firstUser->company . "\n";
    echo "- Source de découverte : " . $firstUser->discovery_source . "\n";
}

// Vérifier les routes
echo "\n=== Vérification des routes ===\n";
$routes = \Illuminate\Support\Facades\Route::getRoutes();
$homeRoute = null;
$profileRoutes = [];

foreach ($routes as $route) {
    if ($route->uri() === '/') {
        $homeRoute = $route;
    }
    if (strpos($route->getName(), 'profile.') === 0) {
        $profileRoutes[] = $route;
    }
}

if ($homeRoute) {
    echo "Route de la page d'accueil : " . $homeRoute->getActionName() . "\n";
} else {
    echo "Route de la page d'accueil non trouvée.\n";
}

echo "Routes du profil : \n";
foreach ($profileRoutes as $route) {
    echo "- " . $route->getName() . " : " . $route->getActionName() . "\n";
}

echo "\n=== Test terminé ===\n";
