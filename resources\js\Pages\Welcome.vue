<script setup>
import { <PERSON>, <PERSON> } from '@inertiajs/vue3';
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
    canLogin: <PERSON><PERSON><PERSON>,
    canRegister: <PERSON><PERSON>an,
    sessions: Array,
    domains: Array,
    flash: Object,
    homepageContent: Object,
});

// Debug logs removed for production

// État pour le filtrage
const selectedDomain = ref('');
const searchQuery = ref('');

// Sessions filtrées
const filteredSessions = computed(() => {
    let result = props.sessions || [];

    if (selectedDomain.value) {
        result = result.filter(session => session.training_domain.id == selectedDomain.value);
    }

    if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(session =>
            session.title.toLowerCase().includes(query) ||
            session.description.toLowerCase().includes(query) ||
            session.training_domain.name.toLowerCase().includes(query)
        );
    }

    return result;
});

// Formatage de la date
const formatDate = (dateString) => {
    if (!dateString) return '';
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('fr-FR', options);
};

// Smooth scroll functionality
const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
};

// Floating navigation
const showFloatingNav = ref(false);

const handleScroll = () => {
    showFloatingNav.value = window.scrollY > 300;
};

const scrollToTop = () => {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
};

onMounted(() => {
    window.addEventListener('scroll', handleScroll);
});

onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll);
});

// Get dynamic content helper
const getContent = (section, key, defaultValue = '') => {
    if (!props.homepageContent || !props.homepageContent[section]) {
        return defaultValue;
    }

    const content = props.homepageContent[section][key];
    return content ? content.value : defaultValue;
};

// Gestion des images des sessions
const getSessionImageUrl = (imagePath) => {
    if (!imagePath) {
        return '/images/default-session.png';
    }

    // Vérifier si le chemin commence déjà par /storage
    if (imagePath.startsWith('/storage/')) {
        return imagePath;
    }

    // Sinon, ajouter le préfixe /storage/
    return `/storage/${imagePath}`;
};

const handleImageError = (event) => {
    console.error('Erreur de chargement de l\'image:', event);
    // Remplacer par une image par défaut en cas d'erreur
    event.target.src = '/images/default-session.png';
};

// Fonction pour récupérer les statistiques de la section À propos
const getAboutStats = (key, defaultValue = '') => {
    const statsContent = getContent('about', 'stats', '{}');
    try {
        const stats = typeof statsContent === 'string' ? JSON.parse(statsContent) : statsContent;
        return stats[key] || defaultValue;
    } catch (e) {
        return defaultValue;
    }
};
</script>

<template>
    <Head title="Accueil - PCMET Horizon Qualité" />

    <div class="min-h-screen bg-white">
        <!-- Header -->
        <header class="bg-white shadow-lg sticky top-0 z-50 border-b" style="border-color: rgba(42, 105, 225, 0.2);">
            <div class="max-w-7xl mx-auto flex items-center justify-between py-4 px-4 md:px-8">
                <div class="flex items-center gap-3">
                    <div class="flex items-center gap-3">
                        <img src="/logo-pcmet.png" alt="PCMET Logo" class="h-12 w-auto rounded-xl shadow-lg" />
                        <div>
                            <span class="text-xl font-bold text-gray-900">PCMET</span>
                            <div class="text-sm font-semibold" style="color: #2A69E1;">Centre de Formation Premiers Secours</div>
                        </div>
                    </div>
                </div>
                <nav class="hidden lg:flex gap-8 text-gray-700 font-medium">
                    <a @click="scrollToSection('formations')" class="transition-colors duration-200 flex items-center gap-2 cursor-pointer" style="color: inherit;" onmouseover="this.style.color='#2A69E1';" onmouseout="this.style.color='inherit';">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        Formations
                    </a>
                    <a @click="scrollToSection('vr-training')" class="transition-colors duration-200 flex items-center gap-2 cursor-pointer" style="color: inherit;" onmouseover="this.style.color='#2A69E1';" onmouseout="this.style.color='inherit';">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        Formation VR
                    </a>
                    <a @click="scrollToSection('about')" class="transition-colors duration-200 flex items-center gap-2 cursor-pointer" style="color: inherit;" onmouseover="this.style.color='#2A69E1';" onmouseout="this.style.color='inherit';">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        À propos
                    </a>
                    <a @click="scrollToSection('contact')" class="transition-colors duration-200 flex items-center gap-2 cursor-pointer" style="color: inherit;" onmouseover="this.style.color='#2A69E1';" onmouseout="this.style.color='inherit';">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Contact
                    </a>
                </nav>
                <div class="flex items-center gap-4">
                    <Link v-if="canLogin && !$page.props.auth.user" :href="route('login')" class="text-gray-700 font-medium transition-colors duration-200" style="color: inherit;" onmouseover="this.style.color='#2A69E1';" onmouseout="this.style.color='inherit';">Connexion</Link>
                    <Link v-if="canRegister && !$page.props.auth.user" :href="route('register')" class="text-white px-6 py-2.5 rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" style="background: linear-gradient(to right, #2A69E1, #1E4FBF);" onmouseover="this.style.background='linear-gradient(to right, #1E4FBF, #1A3A9F)';" onmouseout="this.style.background='linear-gradient(to right, #2A69E1, #1E4FBF)';">Inscription</Link>
                    <Link v-if="$page.props.auth.user" :href="route('dashboard')" class="text-white px-6 py-2.5 rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" style="background: linear-gradient(to right, #2A69E1, #1E4FBF);" onmouseover="this.style.background='linear-gradient(to right, #1E4FBF, #1A3A9F)';" onmouseout="this.style.background='linear-gradient(to right, #2A69E1, #1E4FBF)';">Tableau de bord</Link>

                    <!-- Mobile menu button -->
                    <button class="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="relative bg-gradient-to-br from-red-50 via-white to-red-50 py-20 px-4 overflow-hidden">
            <!-- Background Pattern -->
            <div class="absolute inset-0 opacity-5">
                <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                            <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#ef4444" stroke-width="0.5"/>
                        </pattern>
                    </defs>
                    <rect width="100" height="100" fill="url(#grid)" />
                </svg>
            </div>

            <div class="max-w-7xl mx-auto relative">
                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <!-- Content -->
                    <div class="space-y-8">
                        <div class="space-y-4">
                            <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-semibold" style="background-color: rgba(42, 105, 225, 0.1); color: #2A69E1;">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Centre de Formation Certifié
                            </div>
                            <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                                {{ getContent('hero', 'title', 'Formation Premiers Secours Professionnelle') }}
                            </h1>
                            <p class="text-xl text-gray-600 leading-relaxed max-w-lg">
                                {{ getContent('hero', 'description', 'Maîtrisez les gestes qui sauvent avec nos formations certifiées. Apprenez les techniques de premiers secours avec des experts qualifiés et une technologie VR innovante.') }}
                            </p>
                        </div>

                        <!-- Key Features -->
                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                            <div class="flex items-center gap-3 bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                                <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-900">Certification</div>
                                    <div class="text-sm text-gray-600">Reconnue</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-3 bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-900">Formation VR</div>
                                    <div class="text-sm text-gray-600">Innovante</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-3 bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-900">Experts</div>
                                    <div class="text-sm text-gray-600">Qualifiés</div>
                                </div>
                            </div>
                        </div>

                        <!-- CTA Buttons -->
                        <div class="flex flex-col sm:flex-row gap-4">
                            <button @click="scrollToSection('formations')" class="inline-flex items-center justify-center gap-2 text-white px-8 py-4 rounded-lg font-semibold text-lg shadow-lg transition-all duration-200 transform hover:-translate-y-1 hover:shadow-xl cursor-pointer" style="background: linear-gradient(to right, #2A69E1, #1E4FBF);">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                Voir nos formations
                            </button>
                            <button @click="scrollToSection('vr-training')" class="inline-flex items-center justify-center gap-2 bg-white px-8 py-4 rounded-lg font-semibold text-lg border-2 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg cursor-pointer" style="border-color: #2A69E1; color: #2A69E1;" onmouseover="this.style.borderColor='#1E4FBF'; this.style.color='#1E4FBF';" onmouseout="this.style.borderColor='#2A69E1'; this.style.color='#2A69E1';">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                Découvrir la VR
                            </button>
                        </div>
                    </div>

                    <!-- Visual Content -->
                    <div class="relative">
                        <div class="relative z-10">
                            <!-- Main Image - Instructor -->
                            <div class="rounded-2xl p-8 shadow-2xl" style="background: linear-gradient(135deg, rgba(42, 105, 225, 0.1), rgba(42, 105, 225, 0.2));">
                                <div class="bg-white rounded-xl p-6 shadow-lg">
                                    <div class="h-64 rounded-lg overflow-hidden relative">
                                        <img
                                            v-if="getContent('hero', 'instructor_image')"
                                            :src="`/storage/${getContent('hero', 'instructor_image')}`"
                                            :alt="getContent('hero', 'instructor_image', {})?.metadata?.alt || 'Instructeur PCMET'"
                                            class="w-full h-full object-cover"
                                        />
                                        <div v-else class="flex items-center justify-center h-full bg-gradient-to-br from-blue-50 to-blue-100">
                                            <div class="text-center">
                                                <div class="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4" style="background-color: #2A69E1;">
                                                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                                    </svg>
                                                </div>
                                                <h3 class="text-xl font-bold text-gray-900 mb-2">Formation Premiers Secours</h3>
                                                <p class="text-gray-600">Apprenez les gestes qui sauvent</p>
                                            </div>
                                        </div>
                                        <!-- Overlay with certification badge -->
                                        <div class="absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-semibold text-white" style="background-color: #2A69E1;">
                                            Instructeur Certifié
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Floating Elements -->
                        <div class="absolute -top-4 -right-4 w-24 h-24 bg-blue-500 rounded-full flex items-center justify-center shadow-lg z-20">
                            <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-green-500 rounded-full flex items-center justify-center shadow-lg z-20">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- VR Training Section -->
        <section id="vr-training" class="py-20 bg-gradient-to-br from-blue-50 to-indigo-100">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-16">
                    <div class="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-semibold mb-4">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Innovation Technologique
                    </div>
                    <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        {{ getContent('vr_training', 'title', 'Formation VR Réalité Virtuelle') }}
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        {{ getContent('vr_training', 'description', 'Découvrez notre approche révolutionnaire de la formation aux premiers secours grâce à la réalité virtuelle. Une expérience immersive pour un apprentissage optimal.') }}
                    </p>
                </div>

                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <!-- VR Features -->
                    <div class="space-y-8">
                        <div class="space-y-6">
                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-gray-900 mb-2">Caméras VR 360°</h3>
                                    <p class="text-gray-600">
                                        Nos caméras VR capturent des scénarios réalistes d'urgence médicale
                                        pour une formation immersive et interactive.
                                    </p>
                                </div>
                            </div>

                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-gray-900 mb-2">Apprentissage Interactif</h3>
                                    <p class="text-gray-600">
                                        Pratiquez les gestes de premiers secours dans un environnement
                                        virtuel sécurisé avant les situations réelles.
                                    </p>
                                </div>
                            </div>

                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-gray-900 mb-2">Suivi des Performances</h3>
                                    <p class="text-gray-600">
                                        Analysez vos performances en temps réel et recevez des retours
                                        personnalisés pour améliorer vos compétences.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                            <div class="flex items-center gap-4 mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-lg font-bold text-gray-900">Technologie de Pointe</h4>
                                    <p class="text-gray-600">Équipement VR professionnel dernière génération</p>
                                </div>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-center">
                                <div>
                                    <div class="text-2xl font-bold text-blue-600">360°</div>
                                    <div class="text-sm text-gray-600">Vision complète</div>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-green-600">4K</div>
                                    <div class="text-sm text-gray-600">Haute définition</div>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold text-purple-600">Real-time</div>
                                    <div class="text-sm text-gray-600">Temps réel</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- VR Visual -->
                    <div class="relative">
                        <div class="bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl p-8 shadow-2xl">
                            <div class="bg-white rounded-xl p-6">
                                <div class="h-80 rounded-lg relative overflow-hidden">
                                    <video
                                        v-if="getContent('vr_training', 'presentation_video')"
                                        :src="`/storage/${getContent('vr_training', 'presentation_video')}`"
                                        class="w-full h-full object-cover"
                                        controls
                                        poster="/storage/homepage/vr/demo-vr-training.svg"
                                    >
                                        Votre navigateur ne supporte pas la lecture vidéo.
                                    </video>
                                    <div v-else class="flex items-center justify-center h-full bg-gradient-to-br from-blue-50 to-purple-50">
                                        <!-- VR Headset Illustration -->
                                        <div class="text-center z-10">
                                            <div class="w-32 h-32 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg" style="background: linear-gradient(135deg, #2A69E1, #8B5CF6);">
                                                <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                </svg>
                                            </div>
                                            <h3 class="text-xl font-bold text-gray-900 mb-2">Formation VR Immersive</h3>
                                            <p class="text-gray-600">Expérience d'apprentissage révolutionnaire</p>
                                        </div>

                                        <!-- Background Pattern -->
                                        <div class="absolute inset-0 opacity-10">
                                            <div class="absolute top-4 left-4 w-8 h-8 rounded-full" style="background-color: #2A69E1;"></div>
                                            <div class="absolute top-12 right-8 w-6 h-6 bg-purple-500 rounded-full"></div>
                                            <div class="absolute bottom-8 left-8 w-4 h-4 bg-green-500 rounded-full"></div>
                                            <div class="absolute bottom-4 right-4 w-10 h-10 rounded-full" style="background-color: rgba(42, 105, 225, 0.6);"></div>
                                        </div>
                                    </div>

                                    <!-- Play button overlay for video -->
                                    <div v-if="getContent('vr_training', 'presentation_video')" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                                        <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                                            <svg class="w-8 h-8 ml-1" style="color: #2A69E1;" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M8 5v14l11-7z"/>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Floating Stats -->
                        <div class="absolute -top-6 -left-6 bg-white rounded-xl p-4 shadow-lg border border-gray-100">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">95%</div>
                                <div class="text-sm text-gray-600">Efficacité</div>
                            </div>
                        </div>
                        <div class="absolute -bottom-6 -right-6 bg-white rounded-xl p-4 shadow-lg border border-gray-100">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">100+</div>
                                <div class="text-sm text-gray-600">Scénarios</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Sessions Section -->
        <section id="formations" class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-16">
                    <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-semibold mb-4" style="background-color: rgba(42, 105, 225, 0.1); color: #2A69E1;">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        Formations Disponibles
                    </div>
                    <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        Nos Formations
                        <span class="block" style="color: #2A69E1;">Premiers Secours</span>
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Choisissez parmi nos formations certifiées et apprenez les gestes qui sauvent
                        avec nos experts qualifiés. Inscrivez-vous dès maintenant.
                    </p>
                </div>

            <!-- Filtres -->
            <div class="mt-10 flex flex-col md:flex-row gap-4 justify-center">
                <div class="w-full md:w-1/3">
                    <label for="search" class="block text-sm font-medium text-gray-700">Recherche</label>
                    <input
                        type="text"
                        id="search"
                        v-model="searchQuery"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="Rechercher une formation..."
                    />
                </div>
                <div class="w-full md:w-1/3">
                    <label for="domain" class="block text-sm font-medium text-gray-700">Domaine</label>
                    <select
                        id="domain"
                        v-model="selectedDomain"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    >
                        <option value="">Tous les domaines</option>
                        <option v-for="domain in domains" :key="domain.id" :value="domain.id">
                            {{ domain.name }}
                        </option>
                    </select>
                </div>
            </div>

            <!-- Liste des sessions -->
            <div class="mt-12">
                <h3 class="text-xl font-semibold mb-4">Sessions disponibles ({{ filteredSessions.length }})</h3>

                <div v-if="filteredSessions.length > 0" class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                    <div v-for="session in filteredSessions" :key="session.id" class="group bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden">
                        <!-- Image de la session -->
                        <div class="relative h-48 overflow-hidden">
                            <img
                                v-if="session.image"
                                :src="getSessionImageUrl(session.image)"
                                :alt="session.title"
                                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                @error="handleImageError"
                            />
                            <div v-else class="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <!-- Badge du domaine superposé -->
                            <div class="absolute top-4 left-4 bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg">
                                {{ session.training_domain.name }}
                            </div>
                            <!-- Badge du prix superposé -->
                            <div class="absolute top-4 right-4 bg-white text-red-600 px-3 py-1 rounded-full font-bold text-sm shadow-lg">
                                {{ session.price ? session.price + ' DT' : 'Gratuit' }}
                            </div>
                        </div>

                        <!-- Contenu principal -->
                        <div class="p-6">
                            <h4 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-red-600 transition-colors duration-200">{{ session.title }}</h4>

                            <!-- Dates de la formation -->
                            <div class="flex items-center text-gray-600 mb-4 bg-gray-50 p-3 rounded-lg">
                                <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Du {{ formatDate(session.start_date) }}</p>
                                    <p class="text-sm text-gray-600">Au {{ formatDate(session.end_date) }}</p>
                                </div>
                            </div>

                            <!-- Description avec limite de lignes -->
                            <p class="text-gray-600 line-clamp-3 mb-4 leading-relaxed">{{ session.description }}</p>

                            <!-- Formateur -->
                            <div class="flex items-center mb-6 bg-blue-50 p-3 rounded-lg" v-if="session.trainer">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ session.trainer.name }}</p>
                                    <p class="text-sm text-gray-600">Formateur certifié</p>
                                </div>
                            </div>

                            <!-- Boutons d'action -->
                            <div class="flex flex-col gap-3">
                                <Link :href="route('sessions.show', session.id)" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-4 rounded-lg text-center transition-all duration-200 flex items-center justify-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    Voir les détails
                                </Link>
                                <Link :href="route('session.enrollment.create', session.id)" class="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-semibold py-3 px-4 rounded-lg text-center transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-lg flex items-center justify-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    S'inscrire maintenant
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-else class="mt-12 text-center">
                    <p class="text-gray-500 text-lg">Aucune session de formation disponible.</p>
                </div>

            </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="py-20 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4">
                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <div>
                        <div class="inline-flex items-center gap-2 bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm font-semibold mb-6">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            {{ getContent('about', 'badge', 'À Propos de PCMET') }}
                        </div>
                        <h2 class="text-4xl font-bold text-gray-900 mb-6">
                            {{ getContent('about', 'title', 'Experts en Formation Premiers Secours') }}
                        </h2>
                        <p class="text-lg text-gray-600 mb-8 leading-relaxed">
                            {{ getContent('about', 'description', 'PCMET Horizon Qualité est un centre de formation spécialisé dans l\'enseignement des premiers secours. Nous combinons expertise traditionnelle et technologies innovantes pour offrir la meilleure formation possible.') }}
                        </p>

                        <div class="grid grid-cols-2 gap-6 mb-8">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-red-600 mb-2">{{ getAboutStats('students_trained', '500+') }}</div>
                                <div class="text-gray-600">Personnes formées</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-blue-600 mb-2">{{ getAboutStats('years_experience', '15+') }}</div>
                                <div class="text-gray-600">Années d'expérience</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-green-600 mb-2">{{ getAboutStats('success_rate', '98%') }}</div>
                                <div class="text-gray-600">Taux de réussite</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-purple-600 mb-2">{{ getAboutStats('support_availability', '24/7') }}</div>
                                <div class="text-gray-600">Support disponible</div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-8 shadow-lg">
                        <h3 class="text-2xl font-bold text-gray-900 mb-6">Nos Certifications</h3>
                        <div class="space-y-4">
                            <div class="flex items-center gap-4 p-4 bg-red-50 rounded-lg">
                                <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-900">Formation Certifiée</div>
                                    <div class="text-sm text-gray-600">Agréé par les autorités compétentes</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-4 p-4 bg-blue-50 rounded-lg">
                                <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-900">Formateurs Qualifiés</div>
                                    <div class="text-sm text-gray-600">Experts certifiés en premiers secours</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-4 p-4 bg-green-50 rounded-lg">
                                <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-900">Technologie Avancée</div>
                                    <div class="text-sm text-gray-600">Équipement VR dernière génération</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="py-20 bg-gradient-to-br from-blue-500 to-blue-600 text-white">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-4xl font-bold mb-4">{{ getContent('contact', 'title', 'Contactez-nous') }}</h2>
                    <p class="text-xl text-blue-100">{{ getContent('contact', 'subtitle', 'Prêt à commencer votre formation ? Nous sommes là pour vous aider.') }}</p>
                </div>

                <div class="grid lg:grid-cols-3 gap-8">
                    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 text-center">
                        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Adresse</h3>
                        <p class="text-red-100" v-html="getContent('contact', 'address', '58 Rue Des Jacinthes<br>2000, Tunisie')"></p>
                    </div>

                    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 text-center">
                        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Téléphone</h3>
                        <p class="text-red-100">
                            <a :href="'tel:' + getContent('contact', 'phone', '55000511').replace(/\s/g, '')" class="hover:text-white transition-colors duration-200">{{ getContent('contact', 'phone', '55 000 511') }}</a>
                        </p>
                    </div>

                    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 text-center">
                        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Email</h3>
                        <p class="text-red-100">
                            <a :href="'mailto:' + getContent('contact', 'email', '<EMAIL>')" class="hover:text-white transition-colors duration-200">{{ getContent('contact', 'email', '<EMAIL>') }}</a>
                        </p>
                    </div>
                </div>

                <div class="text-center mt-12">
                    <button @click="scrollToSection('formations')" class="inline-flex items-center gap-2 bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg cursor-pointer">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Commencer ma formation
                    </button>
                </div>
            </div>
        </section>

        <!-- Floating Navigation -->
        <div v-if="showFloatingNav" class="fixed bottom-8 right-8 z-50 flex flex-col gap-4">
            <!-- Scroll to Top Button -->
            <button @click="scrollToTop" class="w-12 h-12 bg-red-500 hover:bg-red-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1 flex items-center justify-center">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                </svg>
            </button>

            <!-- Floating Quick Nav -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-2">
                <div class="flex flex-col gap-2">
                    <button @click="scrollToSection('formations')" class="w-10 h-10 rounded-lg hover:bg-red-50 hover:text-red-600 transition-colors duration-200 flex items-center justify-center text-gray-600" title="Formations">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </button>
                    <button @click="scrollToSection('vr-training')" class="w-10 h-10 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 flex items-center justify-center text-gray-600" title="Formation VR">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </button>
                    <button @click="scrollToSection('about')" class="w-10 h-10 rounded-lg hover:bg-gray-50 hover:text-gray-800 transition-colors duration-200 flex items-center justify-center text-gray-600" title="À propos">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </button>
                    <button @click="scrollToSection('contact')" class="w-10 h-10 rounded-lg hover:bg-red-50 hover:text-red-600 transition-colors duration-200 flex items-center justify-center text-gray-600" title="Contact">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="bg-gray-900 text-white py-12">
            <div class="max-w-7xl mx-auto px-4">
                <div class="grid lg:grid-cols-4 gap-8 mb-8">
                    <div class="lg:col-span-2">
                        <div class="flex items-center gap-3 mb-4">
                            <img src="/logo-pcmet.png" alt="PCMET Logo" class="h-12 w-auto rounded-xl shadow-lg" />
                            <div>
                                <div class="text-xl font-bold">PCMET</div>
                                <div class="text-sm" style="color: #2A69E1;">Centre de Formation Premiers Secours</div>
                            </div>
                        </div>
                        <p class="text-gray-400 mb-6 max-w-md">
                            Votre partenaire de confiance pour apprendre les gestes qui sauvent.
                            Formation certifiée avec technologie VR innovante.
                        </p>
                        <div class="flex gap-4">
                            <a href="https://facebook.com/" target="_blank" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center transition-colors duration-200" style="color: inherit;" onmouseover="this.style.backgroundColor='#2A69E1';" onmouseout="this.style.backgroundColor='rgb(31, 41, 55)';">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                            </a>
                            <a href="https://instagram.com/" target="_blank" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center transition-colors duration-200" style="color: inherit;" onmouseover="this.style.backgroundColor='#2A69E1';" onmouseout="this.style.backgroundColor='rgb(31, 41, 55)';">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"/>
                                </svg>
                            </a>
                            <a href="https://youtube.com/" target="_blank" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center transition-colors duration-200" style="color: inherit;" onmouseover="this.style.backgroundColor='#2A69E1';" onmouseout="this.style.backgroundColor='rgb(31, 41, 55)';">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-bold mb-4">Formations</h3>
                        <ul class="space-y-2 text-gray-400">
                            <li><button @click="scrollToSection('formations')" class="hover:text-white transition-colors duration-200 text-left">Premiers Secours</button></li>
                            <li><button @click="scrollToSection('vr-training')" class="hover:text-white transition-colors duration-200 text-left">Formation VR</button></li>
                            <li><button @click="scrollToSection('formations')" class="hover:text-white transition-colors duration-200 text-left">Certification</button></li>
                            <li><button @click="scrollToSection('formations')" class="hover:text-white transition-colors duration-200 text-left">Formation Continue</button></li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="text-lg font-bold mb-4">Support</h3>
                        <ul class="space-y-2 text-gray-400">
                            <li><button @click="scrollToSection('contact')" class="hover:text-white transition-colors duration-200 text-left">Contact</button></li>
                            <li><button @click="scrollToSection('about')" class="hover:text-white transition-colors duration-200 text-left">À propos</button></li>
                            <li><a href="#" class="hover:text-white transition-colors duration-200">FAQ</a></li>
                            <li><a href="#" class="hover:text-white transition-colors duration-200">Support</a></li>
                        </ul>
                    </div>
                </div>

                <div class="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
                    <div class="text-gray-400 text-sm">
                        © 2024 PCMET Horizon Qualité. Tous droits réservés.
                    </div>
                    <div class="flex gap-6 text-sm text-gray-400 mt-4 md:mt-0">
                        <a href="#" class="hover:text-white transition-colors duration-200">Politique de confidentialité</a>
                        <a href="#" class="hover:text-white transition-colors duration-200">Conditions d'utilisation</a>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</template>

<style>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
