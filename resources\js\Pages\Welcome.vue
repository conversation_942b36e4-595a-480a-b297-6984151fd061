<script setup>
import { <PERSON>, <PERSON> } from '@inertiajs/vue3';
import { ref, computed } from 'vue';

const props = defineProps({
    canLogin: <PERSON><PERSON><PERSON>,
    canRegister: <PERSON>ole<PERSON>,
    sessions: Array,
    domains: Array,
    flash: Object,
});

console.log('Sessions:', props.sessions);
console.log('Domains:', props.domains);

// État pour le filtrage
const selectedDomain = ref('');
const searchQuery = ref('');

// Sessions filtrées
const filteredSessions = computed(() => {
    let result = props.sessions || [];

    if (selectedDomain.value) {
        result = result.filter(session => session.training_domain.id == selectedDomain.value);
    }

    if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(session =>
            session.title.toLowerCase().includes(query) ||
            session.description.toLowerCase().includes(query) ||
            session.training_domain.name.toLowerCase().includes(query)
        );
    }

    return result;
});

// Formatage de la date
const formatDate = (dateString) => {
    if (!dateString) return '';
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('fr-FR', options);
};
</script>

<template>
    <Head title="Accueil - PCMET Horizon Qualité" />

    <div class="min-h-screen bg-gray-50">
        <!-- Header -->
        <header class="bg-white shadow-sm sticky top-0 z-20">
            <div class="max-w-7xl mx-auto flex items-center justify-between py-4 px-4 md:px-8">
                <div class="flex items-center gap-3">
                    <img src="/logo-pcmet.png" alt="PCMET Logo" class="h-12 w-auto rounded-xl shadow-md" />
                    <span class="text-xl font-bold text-blue-900">PCMET Horizon Qualité</span>
                </div>
                <nav class="hidden md:flex gap-8 text-gray-700 font-semibold">
                    <a href="#sessions" class="hover:text-blue-700 transition">Formations</a>
                    <a href="#contact" class="hover:text-blue-700 transition">Contact</a>
                    <Link v-if="canLogin && !$page.props.auth.user" :href="route('login')" class="ml-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">Connexion</Link>
                    <Link v-if="canRegister && !$page.props.auth.user" :href="route('register')" class="ml-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition">Inscription</Link>
                    <Link v-if="$page.props.auth.user" :href="route('dashboard')" class="ml-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">Tableau de bord</Link>
                </nav>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="relative bg-gradient-to-br from-blue-100 to-white py-16 px-4">
            <div class="max-w-6xl mx-auto flex flex-col md:flex-row items-center gap-10">
                <div class="flex-1">
                    <h1 class="text-4xl md:text-5xl font-extrabold text-blue-900 mb-4">Formations professionnelles</h1>
                    <p class="text-lg md:text-xl text-gray-700 mb-6">Développez vos compétences avec nos formations de qualité. Nos experts vous accompagnent dans votre parcours professionnel.</p>
                    <a href="#sessions" class="inline-block px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg shadow hover:bg-blue-700 transition">Voir nos formations</a>
                </div>
                <div class="flex-1 flex justify-center">
                    <img src="/logo-pcmet.png" alt="PCMET" class="h-40 w-auto rounded-2xl shadow-lg border-4 border-blue-200 bg-white" />
                </div>
            </div>
        </section>

        <!-- Sessions Section -->
        <div id="sessions" class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h2 class="text-3xl font-extrabold text-blue-900 sm:text-4xl">Nos prochaines sessions de formation</h2>
                <p class="mt-3 max-w-2xl mx-auto text-xl text-gray-500 sm:mt-4">
                    Découvrez nos sessions à venir et inscrivez-vous dès maintenant.
                </p>
            </div>

            <!-- Filtres -->
            <div class="mt-10 flex flex-col md:flex-row gap-4 justify-center">
                <div class="w-full md:w-1/3">
                    <label for="search" class="block text-sm font-medium text-gray-700">Recherche</label>
                    <input
                        type="text"
                        id="search"
                        v-model="searchQuery"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="Rechercher une formation..."
                    />
                </div>
                <div class="w-full md:w-1/3">
                    <label for="domain" class="block text-sm font-medium text-gray-700">Domaine</label>
                    <select
                        id="domain"
                        v-model="selectedDomain"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    >
                        <option value="">Tous les domaines</option>
                        <option v-for="domain in domains" :key="domain.id" :value="domain.id">
                            {{ domain.name }}
                        </option>
                    </select>
                </div>
            </div>

            <!-- Liste des sessions -->
            <div class="mt-12">
                <h3 class="text-xl font-semibold mb-4">Sessions disponibles ({{ filteredSessions.length }})</h3>

                <div v-if="filteredSessions.length > 0" class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                    <div v-for="session in filteredSessions" :key="session.id" class="bg-white overflow-hidden shadow-lg rounded-lg border border-gray-200 hover:shadow-xl transition-shadow duration-300">
                        <!-- En-tête de la carte avec le domaine -->
                        <div class="bg-blue-600 text-white px-4 py-2 flex justify-between items-center">
                            <span class="font-medium">{{ session.training_domain.name }}</span>
                            <span class="text-xs bg-white text-blue-700 px-2 py-1 rounded-full font-bold">
                                {{ session.price ? session.price + ' DT' : 'Gratuit' }}
                            </span>
                        </div>

                        <!-- Contenu principal -->
                        <div class="p-5">
                            <h4 class="text-xl font-bold text-gray-900 mb-2">{{ session.title }}</h4>

                            <!-- Dates de la formation -->
                            <div class="flex items-center text-gray-600 mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <div>
                                    <p class="text-sm">Du <span class="font-semibold">{{ formatDate(session.start_date) }}</span></p>
                                    <p class="text-sm">Au <span class="font-semibold">{{ formatDate(session.end_date) }}</span></p>
                                </div>
                            </div>

                            <!-- Description avec limite de lignes -->
                            <p class="text-sm text-gray-600 line-clamp-3 mb-4">{{ session.description }}</p>

                            <!-- Formateur -->
                            <div class="flex items-center mb-4" v-if="session.trainer">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                <p class="text-sm text-gray-600">Formateur: <span class="font-semibold">{{ session.trainer.name }}</span></p>
                            </div>

                            <!-- Boutons d'action -->
                            <div class="mt-5 flex space-x-2">
                                <Link :href="route('sessions.show', session.id)" class="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-700 font-medium py-2 px-4 rounded-md text-sm text-center transition-colors duration-300">
                                    Voir les détails
                                </Link>
                                <Link :href="route('session.enrollment.create', session.id)" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md text-sm text-center transition-colors duration-300">
                                    S'inscrire à cette session
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-else class="mt-12 text-center">
                    <p class="text-gray-500 text-lg">Aucune session de formation disponible.</p>
                </div>

                <div class="mt-12 bg-gray-50 p-6 rounded-lg border border-gray-200">
                    <h3 class="text-xl font-semibold mb-6">Domaines de formation ({{ props.domains ? props.domains.length : 0 }})</h3>

                    <div v-if="props.domains && props.domains.length > 0" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                        <div v-for="domain in props.domains" :key="domain.id"
                             class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-300 flex flex-col items-center justify-center text-center cursor-pointer"
                             @click="selectedDomain = domain.id">
                            <!-- Icône du domaine (à personnaliser selon vos besoins) -->
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                </svg>
                            </div>
                            <!-- Nom du domaine -->
                            <h4 class="font-medium text-gray-800">{{ domain.name }}</h4>
                            <!-- Nombre de sessions (à implémenter si disponible) -->
                            <p class="text-xs text-gray-500 mt-1">Plusieurs sessions disponibles</p>
                        </div>
                    </div>

                    <p v-else class="text-gray-500 text-center">Aucun domaine de formation disponible.</p>

                    <!-- Bouton pour réinitialiser le filtre -->
                    <div v-if="selectedDomain" class="mt-4 text-center">
                        <button @click="selectedDomain = ''" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            Voir tous les domaines
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact rapide -->
        <section id="contact" class="bg-white py-12 px-4">
            <div class="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
                <div class="flex flex-col items-center text-center">
                    <span class="text-2xl font-bold text-blue-900 mb-2">Contactez-nous</span>
                    <div class="text-gray-700 mb-1">58 Rue Des Jacinthes, 2000, Tunisie</div>
                    <div class="text-gray-700 mb-1">Tél : <a href="tel:55000511" class="text-blue-600 hover:underline">55000511</a></div>
                    <div class="text-gray-700 mb-1">Email : <a href="mailto:<EMAIL>" class="text-blue-600 hover:underline"><EMAIL></a></div>
                </div>
                <div class="md:col-span-2 flex flex-col items-center md:items-start">
                    <span class="text-lg font-semibold text-blue-700 mb-2">Prêt à rejoindre notre formation ?</span>
                    <a href="#sessions" class="inline-block px-8 py-3 bg-green-600 text-white font-semibold rounded-lg shadow hover:bg-green-700 transition">Pré-inscription</a>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="bg-blue-900 text-white py-6 mt-8">
            <div class="max-w-7xl mx-auto flex flex-col md:flex-row items-center justify-between px-4 gap-4">
                <div class="text-sm">© 2024 PCMET. Tous droits réservés.</div>
                <div class="flex gap-4">
                    <a href="https://facebook.com/" target="_blank" class="hover:text-blue-300">Facebook</a>
                    <a href="https://instagram.com/" target="_blank" class="hover:text-blue-300">Instagram</a>
                    <a href="https://youtube.com/" target="_blank" class="hover:text-blue-300">YouTube</a>
                </div>
            </div>
        </footer>
    </div>
</template>

<style>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
