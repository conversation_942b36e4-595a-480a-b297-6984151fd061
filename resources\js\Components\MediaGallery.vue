<template>
    <div class="space-y-6">
        <!-- Gallery Header -->
        <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">Galerie Média</h3>
            <button
                @click="showUploadModal = true"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm"
            >
                Ajouter des médias
            </button>
        </div>

        <!-- Filter Tabs -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
                <button
                    v-for="filter in filters"
                    :key="filter.key"
                    @click="activeFilter = filter.key"
                    :class="[
                        activeFilter === filter.key
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                        'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
                    ]"
                >
                    {{ filter.label }}
                    <span v-if="filter.count" class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                        {{ filter.count }}
                    </span>
                </button>
            </nav>
        </div>

        <!-- Media Grid -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            <div
                v-for="media in filteredMedia"
                :key="media.id"
                @click="selectMedia(media)"
                :class="[
                    'relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all duration-200',
                    selectedMedia.includes(media.id)
                        ? 'border-blue-500 ring-2 ring-blue-200'
                        : 'border-gray-200 hover:border-gray-300'
                ]"
            >
                <!-- Media Preview -->
                <div class="aspect-square bg-gray-100 flex items-center justify-center">
                    <img
                        v-if="media.type === 'image'"
                        :src="getMediaUrl(media.value)"
                        :alt="media.key"
                        class="w-full h-full object-cover"
                    />
                    <div
                        v-else-if="media.type === 'video'"
                        class="relative w-full h-full bg-black flex items-center justify-center"
                    >
                        <video
                            :src="getMediaUrl(media.value)"
                            class="w-full h-full object-cover"
                            muted
                        ></video>
                        <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
                            </svg>
                        </div>
                    </div>
                    <div v-else class="text-gray-400">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                </div>

                <!-- Media Info Overlay -->
                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-end">
                    <div class="w-full p-2 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <p class="text-xs font-medium truncate">{{ media.key }}</p>
                        <p class="text-xs text-gray-300">{{ media.section }}</p>
                    </div>
                </div>

                <!-- Selection Indicator -->
                <div
                    v-if="selectedMedia.includes(media.id)"
                    class="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center"
                >
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>

                <!-- Actions Menu -->
                <div class="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <div class="relative">
                        <button
                            @click.stop="toggleActionsMenu(media.id)"
                            class="w-6 h-6 bg-white bg-opacity-90 rounded-full flex items-center justify-center hover:bg-opacity-100"
                        >
                            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                            </svg>
                        </button>
                        
                        <!-- Actions Dropdown -->
                        <div
                            v-if="activeActionsMenu === media.id"
                            class="absolute top-8 left-0 w-32 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10"
                        >
                            <button
                                @click="editMedia(media)"
                                class="block w-full text-left px-3 py-1 text-sm text-gray-700 hover:bg-gray-100"
                            >
                                Modifier
                            </button>
                            <button
                                @click="copyMediaUrl(media)"
                                class="block w-full text-left px-3 py-1 text-sm text-gray-700 hover:bg-gray-100"
                            >
                                Copier URL
                            </button>
                            <button
                                @click="deleteMedia(media)"
                                class="block w-full text-left px-3 py-1 text-sm text-red-600 hover:bg-red-50"
                            >
                                Supprimer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div v-if="filteredMedia.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Aucun média trouvé</h3>
            <p class="mt-1 text-sm text-gray-500">Commencez par ajouter des images ou vidéos.</p>
            <div class="mt-6">
                <button
                    @click="showUploadModal = true"
                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                    Ajouter des médias
                </button>
            </div>
        </div>

        <!-- Bulk Actions -->
        <div v-if="selectedMedia.length > 0" class="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg border border-gray-200 px-4 py-2 flex items-center space-x-4">
            <span class="text-sm text-gray-600">{{ selectedMedia.length }} sélectionné(s)</span>
            <button
                @click="bulkDelete"
                class="text-sm text-red-600 hover:text-red-800 font-medium"
            >
                Supprimer
            </button>
            <button
                @click="clearSelection"
                class="text-sm text-gray-600 hover:text-gray-800 font-medium"
            >
                Annuler
            </button>
        </div>

        <!-- Upload Modal -->
        <Modal :show="showUploadModal" @close="showUploadModal = false">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Ajouter des médias</h3>
                
                <MediaUpload
                    label="Sélectionner des fichiers"
                    accept="image/*,video/*"
                    :multiple="true"
                    v-model="uploadFiles"
                    @file-selected="handleFileSelected"
                />
                
                <div class="mt-6 flex justify-end space-x-3">
                    <button
                        @click="showUploadModal = false"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                    >
                        Annuler
                    </button>
                    <button
                        @click="uploadMedia"
                        :disabled="!uploadFiles || uploadFiles.length === 0"
                        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
                    >
                        Uploader
                    </button>
                </div>
            </div>
        </Modal>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import MediaUpload from '@/Components/MediaUpload.vue';
import Modal from '@/Components/Modal.vue';

const props = defineProps({
    media: {
        type: Array,
        default: () => []
    },
    selectable: {
        type: Boolean,
        default: true
    }
});

const emit = defineEmits(['media-selected', 'media-uploaded', 'media-deleted']);

const activeFilter = ref('all');
const selectedMedia = ref([]);
const activeActionsMenu = ref(null);
const showUploadModal = ref(false);
const uploadFiles = ref([]);

const filters = computed(() => {
    const counts = props.media.reduce((acc, item) => {
        acc[item.type] = (acc[item.type] || 0) + 1;
        return acc;
    }, {});

    return [
        { key: 'all', label: 'Tous', count: props.media.length },
        { key: 'image', label: 'Images', count: counts.image || 0 },
        { key: 'video', label: 'Vidéos', count: counts.video || 0 }
    ];
});

const filteredMedia = computed(() => {
    if (activeFilter.value === 'all') {
        return props.media;
    }
    return props.media.filter(item => item.type === activeFilter.value);
});

const selectMedia = (media) => {
    if (!props.selectable) return;
    
    const index = selectedMedia.value.indexOf(media.id);
    if (index > -1) {
        selectedMedia.value.splice(index, 1);
    } else {
        selectedMedia.value.push(media.id);
    }
    
    emit('media-selected', selectedMedia.value);
};

const toggleActionsMenu = (mediaId) => {
    activeActionsMenu.value = activeActionsMenu.value === mediaId ? null : mediaId;
};

const editMedia = (media) => {
    // Emit edit event or navigate to edit page
    window.location.href = `/admin/homepage-content/${media.id}/edit`;
};

const copyMediaUrl = (media) => {
    const url = getMediaUrl(media.value);
    navigator.clipboard.writeText(url).then(() => {
        // Show success message
        alert('URL copiée dans le presse-papiers');
    });
};

const deleteMedia = (media) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce média ?')) {
        emit('media-deleted', media.id);
    }
};

const bulkDelete = () => {
    if (confirm(`Êtes-vous sûr de vouloir supprimer ${selectedMedia.value.length} média(s) ?`)) {
        emit('media-deleted', selectedMedia.value);
        clearSelection();
    }
};

const clearSelection = () => {
    selectedMedia.value = [];
    emit('media-selected', []);
};

const getMediaUrl = (path) => {
    return `/storage/${path}`;
};

const handleFileSelected = (files) => {
    uploadFiles.value = files;
};

const uploadMedia = () => {
    // Handle upload logic
    emit('media-uploaded', uploadFiles.value);
    showUploadModal.value = false;
    uploadFiles.value = [];
};

// Close actions menu when clicking outside
const handleClickOutside = (event) => {
    if (!event.target.closest('.relative')) {
        activeActionsMenu.value = null;
    }
};

onMounted(() => {
    document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});
</script>
