<template>
  <Head title="Modifier un cours" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Modifier un cours
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link :href="route('admin.courses.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à la liste
              </Link>
            </div>

            <!-- Formulaire de modification de cours -->
            <form @submit.prevent="submit">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Titre -->
                <div>
                  <InputLabel for="title" value="Titre du cours" />
                  <TextInput
                    id="title"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.title"
                    required
                    autofocus
                  />
                  <InputError class="mt-2" :message="form.errors.title" />
                </div>

                <!-- Session de formation -->
                <div>
                  <InputLabel for="training_session_id" value="Session de formation" />
                  <select
                    id="training_session_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.training_session_id"
                    required
                  >
                    <option value="">Sélectionner une session</option>
                    <option v-for="session in trainingSessions" :key="session.id" :value="session.id">
                      {{ session.title }} ({{ session.training_domain.name }})
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.training_session_id" />
                </div>

                <!-- Description -->
                <div class="md:col-span-2">
                  <InputLabel for="description" value="Description" />
                  <textarea
                    id="description"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.description"
                    rows="4"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.description" />
                </div>

                <!-- Ordre -->
                <div>
                  <InputLabel for="order" value="Ordre d'affichage" />
                  <TextInput
                    id="order"
                    type="number"
                    class="mt-1 block w-full"
                    v-model="form.order"
                    min="0"
                  />
                  <InputError class="mt-2" :message="form.errors.order" />
                </div>

                <!-- Statut -->
                <div>
                  <div class="flex items-center mt-6">
                    <input
                      id="active"
                      type="checkbox"
                      class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                      v-model="form.active"
                    />
                    <InputLabel for="active" value="Actif" class="ml-2" />
                  </div>
                  <InputError class="mt-2" :message="form.errors.active" />
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="flex items-center justify-end mt-6">
                <Link 
                  :href="route('admin.course-materials.index', { course_id: course.id })" 
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 mr-2"
                >
                  Gérer les matériels
                </Link>
                <PrimaryButton class="ml-4" :disabled="form.processing">
                  Mettre à jour le cours
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  course: Object,
  trainingSessions: Array,
});

// Formulaire
const form = useForm({
  title: props.course.title,
  description: props.course.description || '',
  training_session_id: props.course.training_session_id,
  order: props.course.order,
  active: props.course.active,
});

// Méthodes
const submit = () => {
  form.put(route('admin.courses.update', props.course.id));
};
</script>
