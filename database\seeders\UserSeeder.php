<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer un administrateur
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'active' => true,
        ]);

        // Créer des formateurs
        $trainers = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'trainer',
                'active' => true,
                'phone' => '0123456789',
                'address' => '123 Rue de la Formation, Paris',
                'bio' => 'Expert en développement web avec 10 ans d\'expérience dans l\'industrie.',
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'trainer',
                'active' => true,
                'phone' => '0123456790',
                'address' => '456 Avenue de l\'Education, Lyon',
                'bio' => 'Spécialiste en sécurité informatique avec une expertise en cyberdéfense.',
            ],
            [
                'name' => 'Pierre Leroy',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'trainer',
                'active' => true,
                'phone' => '0123456791',
                'address' => '789 Boulevard du Savoir, Marseille',
                'bio' => 'Formateur certifié en gestion de projet et méthodologies agiles.',
            ],
        ];

        foreach ($trainers as $trainer) {
            User::create($trainer);
        }

        // Créer des apprenants
        $students = [
            [
                'name' => 'Sophie Bernard',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'student',
                'active' => true,
            ],
            [
                'name' => 'Thomas Petit',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'student',
                'active' => true,
            ],
            [
                'name' => 'Julie Moreau',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'student',
                'active' => true,
            ],
            [
                'name' => 'Nicolas Dubois',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'student',
                'active' => true,
            ],
            [
                'name' => 'Camille Roux',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'student',
                'active' => true,
            ],
        ];

        foreach ($students as $student) {
            User::create($student);
        }
    }
}
