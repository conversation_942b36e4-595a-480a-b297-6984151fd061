<template>
  <Head title="Modifier un module" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Modifier le module: {{ module.title }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Liens de navigation -->
            <div class="mb-6">
              <Link :href="route('admin.modules.index', { course_id: course.id })" class="text-indigo-600 hover:text-indigo-900">
                &larr; Retour aux modules
              </Link>
            </div>

            <!-- Formulaire de modification de module -->
            <form @submit.prevent="submit">
              <div class="grid grid-cols-1 gap-6">
                <!-- Titre -->
                <div>
                  <InputLabel for="title" value="Titre du module" />
                  <TextInput
                    id="title"
                    v-model="form.title"
                    type="text"
                    class="mt-1 block w-full"
                    required
                    autofocus
                  />
                  <InputError class="mt-2" :message="form.errors.title" />
                </div>

                <!-- Description -->
                <div>
                  <InputLabel for="description" value="Description" />
                  <textarea
                    id="description"
                    v-model="form.description"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    rows="3"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.description" />
                </div>

                <!-- Ordre -->
                <div>
                  <InputLabel for="order" value="Ordre d'affichage" />
                  <TextInput
                    id="order"
                    v-model="form.order"
                    type="number"
                    class="mt-1 block w-full"
                    min="1"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.order" />
                </div>

                <!-- Publication -->
                <div>
                  <div class="flex items-center">
                    <input
                      id="is_published"
                      v-model="form.is_published"
                      type="checkbox"
                      class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                    />
                    <InputLabel for="is_published" value="Publier le module" class="ml-2" />
                  </div>
                  <InputError class="mt-2" :message="form.errors.is_published" />
                </div>

                <!-- Date de publication -->
                <div>
                  <InputLabel for="publish_date" value="Date de publication" />
                  <TextInput
                    id="publish_date"
                    v-model="form.publish_date"
                    type="date"
                    class="mt-1 block w-full"
                    :disabled="!form.is_published"
                  />
                  <InputError class="mt-2" :message="form.errors.publish_date" />
                </div>

                <!-- Boutons de soumission -->
                <div class="flex items-center justify-end mt-4">
                  <Link
                    :href="route('admin.modules.index', { course_id: course.id })"
                    class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-4"
                  >
                    Annuler
                  </Link>
                  <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    Mettre à jour le module
                  </PrimaryButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  module: Object,
  course: Object,
});

// Formatage des dates pour les champs datetime-local
const formatDateForInput = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toISOString().slice(0, 10);
};

// Formulaire
const form = useForm({
  title: props.module.title,
  description: props.module.description || '',
  order: props.module.order,
  is_published: props.module.is_published,
  publish_date: formatDateForInput(props.module.publish_date),
});

// Méthodes
const submit = () => {
  form.put(route('admin.modules.update', props.module.id));
};
</script>
