<script setup>
import { <PERSON>, <PERSON> } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import SessionLayout from '@/Layouts/SessionLayout.vue';
import {
  CalendarIcon,
  ClockIcon,
  UserGroupIcon,
  UserPlusIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  AcademicCapIcon
} from '@heroicons/vue/24/outline';

const props = defineProps({
    canLogin: Boolean,
    canRegister: <PERSON>olean,
    session: Object,
    relatedSessions: Array,
});

// Formatage de la date
const formatDate = (dateString) => {
    if (!dateString) return '';
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('fr-FR', options);
};

// Calcul de la durée de la formation en jours
const durationInDays = computed(() => {
    if (!props.session || !props.session.start_date || !props.session.end_date) return 0;
    const start = new Date(props.session.start_date);
    const end = new Date(props.session.end_date);
    const diffTime = Math.abs(end - start);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 pour inclure le jour de début
});
</script>

<template>
    <Head :title="(props.session && props.session.title ? props.session.title : 'Session') + ' - Centre de Formation'" />

    <SessionLayout>
        <div class="container mx-auto px-4 py-8">
            <!-- En-tête de la session -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ session.title }}</h1>
                        <div class="flex items-center gap-4 text-gray-600">
                            <span class="flex items-center">
                                <CalendarIcon class="w-5 h-5 mr-2" />
                                {{ formatDate(session.start_date) }} - {{ formatDate(session.end_date) }}
                            </span>
                            <span class="flex items-center">
                                <ClockIcon class="w-5 h-5 mr-2" />
                                {{ session.duration }} heures
                            </span>
                            <span class="flex items-center">
                                <UserGroupIcon class="w-5 h-5 mr-2" />
                                {{ session.max_participants }} participants max
                            </span>
                        </div>
                    </div>
                    <Link
                        :href="route('session.enrollment.create', session.id)"
                        class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition flex items-center gap-2"
                    >
                        <UserPlusIcon class="w-5 h-5" />
                        S'inscrire à cette formation
                                </Link>
            </div>
        </div>

        <!-- Contenu principal -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Colonne principale -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- Description -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Description</h2>
                        <div class="prose max-w-none" v-html="session.description"></div>
                                </div>

                    <!-- Objectifs -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Objectifs</h2>
                        <div class="prose max-w-none" v-html="session.objectives"></div>
                                </div>

                    <!-- Programme -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Programme</h2>
                        <div class="prose max-w-none" v-html="session.program"></div>
                </div>
            </div>

                <!-- Sidebar -->
                <div class="space-y-8">
                    <!-- Informations clés -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">Informations clés</h2>
                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <MapPinIcon class="w-5 h-5 text-blue-600 mt-1" />
                                <div>
                                    <h3 class="font-semibold text-gray-900">Lieu</h3>
                                    <p class="text-gray-600">{{ session.location }}</p>
                            </div>
                            </div>
                            <div class="flex items-start gap-3">
                                <CurrencyDollarIcon class="w-5 h-5 text-blue-600 mt-1" />
                                <div>
                                    <h3 class="font-semibold text-gray-900">Prix</h3>
                                    <p class="text-gray-600">{{ session.price }} TND</p>
                                </div>
                                            </div>
                            <div class="flex items-start gap-3">
                                <AcademicCapIcon class="w-5 h-5 text-blue-600 mt-1" />
                                <div>
                                    <h3 class="font-semibold text-gray-900">Formateur</h3>
                                    <p class="text-gray-600">{{ session.trainer?.name }}</p>
                                </div>
                            </div>
                            </div>
                        </div>

                        <!-- Sessions similaires -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">Sessions similaires</h2>
                        <div class="space-y-4">
                            <div v-for="relatedSession in relatedSessions" :key="relatedSession.id" class="border-b border-gray-200 pb-4 last:border-0 last:pb-0">
                                <Link :href="route('sessions.show', relatedSession.id)" class="block hover:bg-gray-50 p-2 rounded-lg transition">
                                    <h3 class="font-semibold text-gray-900 mb-1">{{ relatedSession.title }}</h3>
                                    <p class="text-sm text-gray-600">{{ formatDate(relatedSession.start_date) }}</p>
                                        </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
                    </div>
    </SessionLayout>
</template>

<style>
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>

<style scoped>
.whitespace-pre-line {
    white-space: pre-line;
}
</style>
