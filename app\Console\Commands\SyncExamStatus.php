<?php

namespace App\Console\Commands;

use App\Models\Exam;
use Illuminate\Console\Command;

class SyncExamStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exams:sync-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronise les champs is_published et status pour tous les examens';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Début de la synchronisation des statuts d\'examens...');

        // Récupérer tous les examens
        $exams = Exam::all();
        $count = 0;

        foreach ($exams as $exam) {
            $updated = false;

            // Synchroniser is_published avec status
            if ($exam->is_published && $exam->status !== 'published') {
                $exam->status = 'published';
                $updated = true;
            } elseif (!$exam->is_published && $exam->status === 'published') {
                $exam->is_published = true;
                $updated = true;
            } elseif (!$exam->is_published && $exam->status !== 'draft' && $exam->status !== 'closed') {
                $exam->status = 'draft';
                $updated = true;
            }

            // Sauvegarder si des modifications ont été apportées
            if ($updated) {
                $exam->save();
                $count++;
                $this->info("Examen ID {$exam->id} ({$exam->title}) mis à jour : is_published = {$exam->is_published}, status = {$exam->status}");
            }
        }

        $this->info("Synchronisation terminée. {$count} examens ont été mis à jour.");

        return Command::SUCCESS;
    }
}
