<template>
  <Head title="Modifier un cours" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Modifier le cours: {{ course.title }}
        </h2>
        <Link :href="route('trainer.courses.index')" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
          Retour à la liste
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <form @submit.prevent="submit">
              <!-- Titre du cours -->
              <div class="mb-4">
                <InputLabel for="title" value="Titre du cours" />
                <TextInput
                  id="title"
                  type="text"
                  class="mt-1 block w-full"
                  v-model="form.title"
                  required
                  autofocus
                />
                <InputError class="mt-2" :message="form.errors.title" />
              </div>

              <!-- Description du cours -->
              <div class="mb-4">
                <InputLabel for="description" value="Description" />
                <textarea
                  id="description"
                  v-model="form.description"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  rows="4"
                ></textarea>
                <InputError class="mt-2" :message="form.errors.description" />
              </div>

              <!-- Session de formation -->
              <div class="mb-4">
                <InputLabel for="training_session_id" value="Session de formation" />
                <select
                  id="training_session_id"
                  v-model="form.training_session_id"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  required
                >
                  <option value="" disabled>Sélectionnez une session</option>
                  <option v-for="session in trainingSessions" :key="session.id" :value="session.id">
                    {{ session.title }} ({{ session.training_domain.name }})
                  </option>
                </select>
                <InputError class="mt-2" :message="form.errors.training_session_id" />
              </div>

              <!-- Statut du cours -->
              <div class="mb-6">
                <div class="flex items-center">
                  <input
                    id="active"
                    type="checkbox"
                    v-model="form.active"
                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                  />
                  <InputLabel for="active" value="Actif" class="ml-2" />
                </div>
                <InputError class="mt-2" :message="form.errors.active" />
              </div>

              <!-- Boutons -->
              <div class="flex justify-end">
                <Link
                  :href="route('trainer.courses.index')"
                  class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150 mr-2"
                >
                  Annuler
                </Link>
                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                  Mettre à jour
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  course: Object,
  trainingSessions: Array,
});

// Formulaire
const form = useForm({
  title: props.course.title,
  description: props.course.description || '',
  training_session_id: props.course.training_session_id,
  active: props.course.active,
});

// Méthode de soumission
const submit = () => {
  form.put(route('trainer.courses.update', props.course.id));
};
</script>
