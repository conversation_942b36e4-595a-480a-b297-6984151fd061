<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('homepage_contents', function (Blueprint $table) {
            $table->id();
            $table->string('section'); // hero, vr_training, about, contact
            $table->string('key'); // title, subtitle, description, image, video, etc.
            $table->text('value')->nullable();
            $table->string('type')->default('text'); // text, image, video, json
            $table->json('metadata')->nullable(); // Additional data like alt text, video settings
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->unique(['section', 'key']);
            $table->index(['section', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('homepage_contents');
    }
};
