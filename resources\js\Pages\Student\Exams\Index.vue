<template>
  <Head title="Mes examens" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Mes examens
        </h2>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Bouton de retour -->
        <div class="mb-4">
          <Link :href="route('student.dashboard')" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Retour au tableau de bord
          </Link>
        </div>

        <!-- Messages flash -->
        <div v-if="$page.props.flash.success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
          <span class="block sm:inline">{{ $page.props.flash.success }}</span>
        </div>
        <div v-if="$page.props.flash.error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <span class="block sm:inline">{{ $page.props.flash.error }}</span>
        </div>

        <!-- Filtres -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4">Filtres</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="exam_type" class="block text-sm font-medium text-gray-700 mb-1">Type d'examen</label>
                <select
                  id="exam_type"
                  v-model="filters.exam_type"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md"
                  @change="applyFilters"
                >
                  <option value="">Tous les types</option>
                  <option v-for="type in examTypes" :key="type.value" :value="type.value">
                    {{ type.label }}
                  </option>
                </select>
              </div>
              <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                <select
                  id="status"
                  v-model="filters.status"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md"
                  @change="applyFilters"
                >
                  <option value="">Tous les statuts</option>
                  <option v-for="status in statusOptions" :key="status.value" :value="status.value">
                    {{ status.label }}
                  </option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Examens en cours -->
        <div v-if="inProgressExams.length > 0" class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4 text-blue-600">Examens en cours</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div v-for="exam in inProgressExams" :key="exam.id" class="border rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                <div class="bg-blue-50 p-4 border-b">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <div class="mr-2">
                        <ExamTypeIcon :type="exam.exam_type" class="h-8 w-8" />
                      </div>
                      <div>
                        <h4 class="font-semibold text-lg">{{ exam.title }}</h4>
                        <p class="text-sm text-gray-600">{{ exam.training_session }}</p>
                      </div>
                    </div>
                    <div>
                      <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">En cours</span>
                    </div>
                  </div>
                </div>
                <div class="p-4">
                  <div class="mb-3">
                    <div class="flex justify-between text-sm mb-1">
                      <span>Type:</span>
                      <span class="font-medium">{{ getExamTypeLabel(exam.exam_type) }}</span>
                    </div>
                    <div class="flex justify-between text-sm mb-1">
                      <span>Durée:</span>
                      <span class="font-medium">{{ exam.duration_minutes }} minutes</span>
                    </div>
                    <div class="flex justify-between text-sm mb-1">
                      <span>Questions:</span>
                      <span class="font-medium">{{ exam.question_count }}</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span>Score minimum:</span>
                      <span class="font-medium">{{ exam.passing_score }}%</span>
                    </div>
                  </div>
                  <div class="mt-4">
                    <Link :href="route('student.exams.show', exam.id)" class="w-full block text-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                      Continuer l'examen
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Examens disponibles -->
        <div v-if="availableExams.length > 0" class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4 text-green-600">Examens disponibles</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div v-for="exam in availableExams" :key="exam.id" class="border rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                <div class="bg-green-50 p-4 border-b">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <div class="mr-2">
                        <ExamTypeIcon :type="exam.exam_type" class="h-8 w-8" />
                      </div>
                      <div>
                        <h4 class="font-semibold text-lg">{{ exam.title }}</h4>
                        <p class="text-sm text-gray-600">{{ exam.training_session }}</p>
                      </div>
                    </div>
                    <div>
                      <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Disponible</span>
                    </div>
                  </div>
                </div>
                <div class="p-4">
                  <div class="mb-3">
                    <div class="flex justify-between text-sm mb-1">
                      <span>Type:</span>
                      <span class="font-medium">{{ getExamTypeLabel(exam.exam_type) }}</span>
                    </div>
                    <div class="flex justify-between text-sm mb-1">
                      <span>Durée:</span>
                      <span class="font-medium">{{ exam.duration_minutes }} minutes</span>
                    </div>
                    <div class="flex justify-between text-sm mb-1">
                      <span>Questions:</span>
                      <span class="font-medium">{{ exam.question_count }}</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span>Score minimum:</span>
                      <span class="font-medium">{{ exam.passing_score }}%</span>
                    </div>
                  </div>
                  <div class="mt-4">
                    <Link :href="route('student.exams.show', exam.id)" class="w-full block text-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                      Commencer l'examen
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Examens à venir -->
        <div v-if="upcomingExams.length > 0" class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4 text-purple-600">Examens à venir</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div v-for="exam in upcomingExams" :key="exam.id" class="border rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                <div class="bg-purple-50 p-4 border-b">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <div class="mr-2">
                        <ExamTypeIcon :type="exam.exam_type" class="h-8 w-8" />
                      </div>
                      <div>
                        <h4 class="font-semibold text-lg">{{ exam.title }}</h4>
                        <p class="text-sm text-gray-600">{{ exam.training_session }}</p>
                      </div>
                    </div>
                    <div>
                      <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">À venir</span>
                    </div>
                  </div>
                </div>
                <div class="p-4">
                  <div class="mb-3">
                    <div class="flex justify-between text-sm mb-1">
                      <span>Type:</span>
                      <span class="font-medium">{{ getExamTypeLabel(exam.exam_type) }}</span>
                    </div>
                    <div class="flex justify-between text-sm mb-1">
                      <span>Durée:</span>
                      <span class="font-medium">{{ exam.duration_minutes }} minutes</span>
                    </div>
                    <div class="flex justify-between text-sm mb-1">
                      <span>Questions:</span>
                      <span class="font-medium">{{ exam.question_count }}</span>
                    </div>
                    <div class="flex justify-between text-sm mb-1">
                      <span>Score minimum:</span>
                      <span class="font-medium">{{ exam.passing_score }}%</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span>Disponible à partir de:</span>
                      <span class="font-medium">{{ formatDate(exam.available_from) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Examens terminés -->
        <div v-if="completedExams.length > 0" class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4 text-gray-600">Examens terminés</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div v-for="exam in completedExams" :key="exam.id" class="border rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                <div :class="{
                  'p-4 border-b': true,
                  'bg-green-50': exam.has_passed,
                  'bg-red-50': !exam.has_passed
                }">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <div class="mr-2">
                        <ExamTypeIcon :type="exam.exam_type" class="h-8 w-8" />
                      </div>
                      <div>
                        <h4 class="font-semibold text-lg">{{ exam.title }}</h4>
                        <p class="text-sm text-gray-600">{{ exam.training_session }}</p>
                      </div>
                    </div>
                    <div>
                      <span :class="{
                        'px-2 py-1 text-xs rounded-full': true,
                        'bg-green-100 text-green-800': exam.has_passed,
                        'bg-red-100 text-red-800': !exam.has_passed
                      }">
                        {{ exam.has_passed ? 'Réussi' : 'Échoué' }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="p-4">
                  <div class="mb-3">
                    <div class="flex justify-between text-sm mb-1">
                      <span>Type:</span>
                      <span class="font-medium">{{ getExamTypeLabel(exam.exam_type) }}</span>
                    </div>
                    <div class="flex justify-between text-sm mb-1">
                      <span>Score:</span>
                      <span :class="{
                        'font-medium': true,
                        'text-green-600': exam.has_passed,
                        'text-red-600': !exam.has_passed
                      }">{{ exam.latest_score }}%</span>
                    </div>
                    <div class="flex justify-between text-sm mb-1">
                      <span>Tentatives:</span>
                      <span class="font-medium">{{ exam.attempts }}</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span>Dernière tentative:</span>
                      <span class="font-medium">{{ formatDate(exam.latest_attempt) }}</span>
                    </div>
                  </div>
                  <div class="mt-4 flex space-x-2">
                    <Link :href="route('student.exams.results', exam.latest_result_id)" class="flex-1 block text-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                      Voir les résultats
                    </Link>
                    <Link v-if="!exam.has_passed && exam.exam_type === 'certification'" :href="route('student.exams.show', exam.id)" class="flex-1 block text-center px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
                      Réessayer
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Message si aucun examen -->
        <div v-if="!upcomingExams.length && !inProgressExams.length && !availableExams.length && !completedExams.length" class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-center text-gray-500">
            <p class="text-lg">Aucun examen disponible pour le moment.</p>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import ExamTypeIcon from '@/Components/ExamTypeIcon.vue';

// Props
const props = defineProps({
  upcomingExams: Array,
  inProgressExams: Array,
  availableExams: Array,
  completedExams: Array,
  filters: Object,
  examTypes: Array,
  statusOptions: Array,
});

// État local pour les filtres
const filters = ref({
  exam_type: props.filters.exam_type,
  status: props.filters.status,
});

// Méthodes
const applyFilters = () => {
  router.get(route('student.exams.index'), {
    exam_type: filters.value.exam_type,
    status: filters.value.status,
  }, {
    preserveState: true,
    replace: true,
  });
};

const getExamTypeLabel = (type) => {
  switch (type) {
    case 'certification':
      return 'Certification';
    case 'certification_rattrapage':
      return 'Certification de rattrapage';
    case 'evaluation':
      return 'Évaluation';
    case 'practice':
      return 'Entraînement';
    case 'quiz':
      return 'Quiz';
    default:
      return type;
  }
};

const formatDate = (dateString) => {
  if (!dateString) return 'Non définie';
  const options = { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};
</script>
