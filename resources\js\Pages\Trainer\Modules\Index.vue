<template>
  <Head :title="`Modules - ${course.title}`" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Modules du cours: {{ course.title }}
        </h2>
        <Link :href="route('trainer.modules.create', { course_id: course.id })" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
          Ajouter un module
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Informations sur le cours -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 class="text-lg font-semibold mb-2">Informations sur le cours</h3>
                  <p><span class="font-medium">Session de formation:</span> {{ course.training_session.title }}</p>
                  <p><span class="font-medium">Description:</span> {{ course.description || 'Aucune description' }}</p>
                </div>
                <div class="flex justify-end items-start">
                  <Link :href="route('trainer.courses.index')" class="text-indigo-600 hover:text-indigo-900">
                    &larr; Retour aux cours
                  </Link>
                </div>
              </div>
            </div>

            <!-- Liste des modules -->
            <div v-if="modules.length > 0">
              <h3 class="text-lg font-semibold mb-4">Modules du cours</h3>
              <div class="space-y-4">
                <div v-for="module in modules" :key="module.id" class="border rounded-lg p-4 hover:bg-gray-50">
                  <div class="flex justify-between items-start">
                    <div>
                      <h4 class="font-semibold text-lg">{{ module.title }}</h4>
                      <p class="text-sm text-gray-600 mb-2">{{ module.description || 'Aucune description' }}</p>
                      <div class="flex items-center space-x-2 text-sm">
                        <span class="text-gray-600">Ordre: {{ module.order }}</span>
                        <span class="text-gray-600">|</span>
                        <span :class="module.is_published ? 'text-green-600' : 'text-red-600'">
                          {{ module.is_published ? 'Publié' : 'Non publié' }}
                        </span>
                        <span v-if="module.publish_date" class="text-gray-600">|</span>
                        <span v-if="module.publish_date" class="text-gray-600">
                          Publication: {{ formatDate(module.publish_date) }}
                        </span>
                      </div>
                      <div class="mt-2">
                        <span class="text-sm text-gray-600">{{ module.materials.length }} matériel(s)</span>
                      </div>
                    </div>
                    <div class="flex space-x-2">
                      <Link :href="route('trainer.course-materials.index', { module_id: module.id })" class="px-3 py-1 bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                        Matériels
                      </Link>
                      <Link :href="route('trainer.modules.edit', module.id)" class="px-3 py-1 bg-indigo-100 text-indigo-800 rounded hover:bg-indigo-200">
                        Modifier
                      </Link>
                      <button @click="confirmDelete(module)" class="px-3 py-1 bg-red-100 text-red-800 rounded hover:bg-red-200">
                        Supprimer
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <p class="text-gray-500">Aucun module n'a été créé pour ce cours.</p>
              <Link :href="route('trainer.modules.create', { course_id: course.id })" class="mt-4 inline-block px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                Créer le premier module
              </Link>
            </div>

            <!-- Matériels hors modules -->
            <div class="mt-8">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Matériels hors modules</h3>
                <Link :href="route('trainer.course-materials.index', { course_id: course.id })" class="text-indigo-600 hover:text-indigo-900">
                  Gérer les matériels hors modules
                </Link>
              </div>
              <div class="border rounded-lg p-4">
                <p class="text-sm text-gray-600">
                  Les matériels hors modules sont des ressources qui ne sont pas associées à un module spécifique.
                  Ils peuvent être utilisés pour des ressources générales du cours.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de suppression -->
    <Modal :show="showDeleteModal" @close="closeDeleteModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">
          Confirmer la suppression
        </h2>
        <p class="mt-1 text-sm text-gray-600">
          Êtes-vous sûr de vouloir supprimer ce module ? Cette action est irréversible et supprimera également tous les matériels associés.
        </p>
        <div class="mt-6 flex justify-end">
          <SecondaryButton @click="closeDeleteModal" class="mr-2">
            Annuler
          </SecondaryButton>
          <DangerButton @click="deleteModule" :class="{ 'opacity-25': deleteForm.processing }" :disabled="deleteForm.processing">
            Supprimer
          </DangerButton>
        </div>
      </div>
    </Modal>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';

// Props
const props = defineProps({
  course: Object,
  modules: Array,
});

// État pour la modal de suppression
const showDeleteModal = ref(false);
const moduleToDelete = ref(null);

// Formulaire pour la suppression
const deleteForm = useForm({});

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return '';
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const confirmDelete = (module) => {
  moduleToDelete.value = module;
  showDeleteModal.value = true;
};

const closeDeleteModal = () => {
  showDeleteModal.value = false;
  moduleToDelete.value = null;
};

const deleteModule = () => {
  if (moduleToDelete.value) {
    deleteForm.delete(route('trainer.modules.destroy', moduleToDelete.value.id), {
      onSuccess: () => {
        closeDeleteModal();
      },
    });
  }
};
</script>
