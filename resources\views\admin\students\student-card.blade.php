<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carte secouriste - {{ $student->name }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .card-container {
            perspective: 1000px;
        }

        .student-card {
            width: 323px; /* 85.60mm * 3.78 (conversion mm to px) */
            height: 204px; /* 53.98mm * 3.78 */
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
            transform-style: preserve-3d;
            transition: transform 0.3s ease;
        }

        .card-container:hover .student-card {
            transform: rotateY(5deg) rotateX(5deg);
        }

        .card-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        /* Formes géométriques décoratives */
        .geometric-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .shape {
            position: absolute;
            background: rgba(19, 191, 219, 0.08);
            border-radius: 50%;
        }

        .shape-1 {
            width: 80px;
            height: 80px;
            top: -40px;
            right: -40px;
            background: rgba(19, 191, 219, 0.08);
        }

        .shape-2 {
            width: 60px;
            height: 60px;
            bottom: -30px;
            left: -30px;
            background: rgba(19, 191, 219, 0.08);
        }

        .shape-3 {
            width: 40px;
            height: 40px;
            top: 50%;
            right: 10px;
            transform: translateY(-50%) rotate(45deg);
            border-radius: 8px;
            background: rgba(19, 191, 219, 0.08);
        }

        .shape-4 {
            width: 30px;
            height: 30px;
            bottom: 20px;
            right: 80px;
            background: rgba(19, 191, 219, 0.08);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px 10px;
            position: relative;
            z-index: 2;
        }

        .logo {
            color: #4f46e5;
            font-size: 12px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo img {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            object-fit: contain;
            
            padding: 2px;
        }

        .center-name {
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .card-type {
            color: #7c3aed;
            font-size: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .card-body {
            padding: 0 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            position: relative;
            z-index: 2;
        }

        .student-photo {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            border: 3px solid rgba(79, 70, 229, 0.3);
            object-fit: cover;
            background: rgba(79, 70, 229, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #4f46e5;
            font-size: 24px;
        }

        .student-info {
            flex: 1;
            color: #333;
        }

        .student-name {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #4f46e5;
            text-shadow: none;
        }

        .student-id {
            font-size: 11px;
            color: #666;
            margin-bottom: 2px;
            font-family: 'Courier New', monospace;
            margin-bottom: 7px;
        }

        .student-status {
            font-size: 8px;
            color: #7c3aed;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 3px;
        }

        .student-contact {
            font-size: 11px;
            color: #666;
            line-height: 1.2;
            margin-bottom: 7px;
        }

        .contact-icon {
            display: inline-block;
            width: 8px;
            margin-right: 6px;
            text-align: center;
        }

        .qr-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .qr-code {
            width: 50px;
            height: 50px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .qr-code img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .qr-label {
            font-size: 8px;
            color: #666;
            text-align: center;
        }

        .card-footer {
            position: absolute;
            bottom: 15px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #999;
            font-size: 8px;
            z-index: 2;
        }

        .issue-date {
            font-size: 8px;
            text-align: center;
        }

        /* Effet holographique */
        .student-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .card-container:hover .student-card::before {
            transform: translateX(100%);
        }

        /* Actions */
        .actions {
            margin-top: 30px;
            text-align: center;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: white;
            color: #4f46e5;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .btn-secondary:hover {
            background: white;
            color: #4f46e5;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }

            .actions {
                display: none;
            }

            .card-container {
                margin: 20px auto;
            }
        }
    </style>
</head>
<body>
    <div class="card-container">
        <div class="student-card">
            <div class="card-background"></div>

            <!-- Formes géométriques décoratives -->
            <div class="geometric-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
            </div>

            <div class="card-header">
                <div class="logo">
                    <img src="{{ asset('images/2.png') }}" alt="PCMET Logo">
                    <div class="center-name">PCMET Horizon Qualité</div>
                </div>
                <div class="card-type">Carte Secouriste</div>
            </div>

            <div class="card-body">
                <div class="student-photo">
                    @if($student->profile_photo)
                        <img src="{{ asset('storage/' . $student->profile_photo) }}" alt="{{ $student->name }}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                    @else
                        👤
                    @endif
                </div>

                <div class="student-info">
                    <div class="student-name">{{ $student->name }}</div>
                    <div class="student-id">ID: {{ $student->student_id }}</div>
                    

                    @if($student->phone)
                        <div class="student-contact">
                            <span class="contact-icon">📞</span>{{ $student->phone }}
                        </div>
                    @endif

                    @if($student->email)
                        <div class="student-contact">
                            <span class="contact-icon">✉️</span>{{ $student->email }}
                        </div>
                    @endif

                    @if($student->address)
                        <div class="student-contact">
                            <span class="contact-icon">📍</span>{{ Str::limit($student->address, 50) }}
                        </div>
                    @endif
                    

                </div>

                <div class="qr-section">
                    <div class="qr-code">
                        <img src="{{ asset('storage/' . $qrCodePath) }}" alt="QR Code">
                    </div>
                    <div class="qr-label">Scanner</div>
                </div>
            </div>

            <div class="card-footer">
                <div class="issue-date">
                    Émise le {{ now()->format('d/m/Y') }}
                </div>
            </div>
        </div>
    </div>

    <div class="actions">
        <button onclick="downloadCardAsPNG()" class="btn">📥 Télécharger</button>
        <a href="{{ route('admin.students.show', $student->id) }}" class="btn btn-secondary">← Retour au Profil</a>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
        async function downloadCardAsPNG() {
            const downloadBtn = document.querySelector('button[onclick="downloadCardAsPNG()"]');
            const originalText = downloadBtn.innerHTML;

            try {
                // Changer le texte du bouton pour indiquer le traitement
                downloadBtn.innerHTML = '⏳ Génération...';
                downloadBtn.disabled = true;

                // Masquer les boutons d'action pendant la capture
                const actionsDiv = document.querySelector('.actions');
                actionsDiv.style.display = 'none';

                // Attendre un peu pour que le DOM se mette à jour
                await new Promise(resolve => setTimeout(resolve, 100));

                // Capturer la carte avec des paramètres optimisés
                const cardElement = document.querySelector('.student-card');
                const canvas = await html2canvas(cardElement, {
                    backgroundColor: null,
                    scale: 4, // Très haute résolution pour une qualité optimale
                    useCORS: true,
                    allowTaint: true,
                    width: 323,
                    height: 204,
                    logging: false,
                    imageTimeout: 15000,
                    removeContainer: true
                });

                // Réafficher les boutons
                actionsDiv.style.display = 'flex';

                // Créer le nom de fichier sécurisé
                const studentName = '{{ $student->name }}'.replace(/[^a-zA-Z0-9]/g, '-');
                const studentId = '{{ $student->student_id }}';
                const fileName = `carte-${studentName}-${studentId}.png`;

                // Créer le lien de téléchargement
                const link = document.createElement('a');
                link.download = fileName;
                link.href = canvas.toDataURL('image/png', 1.0); // Qualité maximale

                // Déclencher le téléchargement
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Restaurer le bouton
                downloadBtn.innerHTML = '✅ Téléchargé !';
                setTimeout(() => {
                    downloadBtn.innerHTML = originalText;
                    downloadBtn.disabled = false;
                }, 2000);

            } catch (error) {
                console.error('Erreur lors de la génération de l\'image:', error);

                // Réafficher les boutons en cas d'erreur
                const actionsDiv = document.querySelector('.actions');
                actionsDiv.style.display = 'flex';

                // Restaurer le bouton et afficher l'erreur
                downloadBtn.innerHTML = '❌ Erreur';
                downloadBtn.disabled = false;

                setTimeout(() => {
                    downloadBtn.innerHTML = originalText;
                }, 3000);

                alert('Erreur lors de la génération de l\'image. Veuillez réessayer ou contacter le support technique.');
            }
        }
    </script>
</body>
</html>
