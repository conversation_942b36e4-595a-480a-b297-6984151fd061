<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Enrollment;
use App\Models\TrainingSession;
use App\Models\User;
use App\Models\ExamResult;
use App\Models\Certificate;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class DashboardController extends Controller
{
    /**
     * Affiche le tableau de bord de l'administrateur
     */
    public function index(Request $request)
    {
        // Récupérer les dates de début et de fin ou utiliser les valeurs par défaut
        $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date')) : Carbon::today();
        $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date')) : Carbon::today()->subDays(30);

        // Statistiques générales
        $stats = [
            'total_students' => User::where('role', 'student')->count(),
            'total_trainers' => User::where('role', 'trainer')->count(),
            'total_sessions' => TrainingSession::count(),
            'active_sessions' => TrainingSession::where('active', true)
                ->where('start_date', '<=', $endDate)
                ->where('end_date', '>=', $startDate)
                ->count(),
        ];

        // Statistiques d'inscriptions
        $enrollmentStats = [
            'total' => Enrollment::whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])->count(),
            'pending' => Enrollment::where('status', 'pending')->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])->count(),
            'approved' => Enrollment::where('status', 'approved')->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])->count(),
            'rejected' => Enrollment::where('status', 'rejected')->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])->count(),
            'completed' => Enrollment::where('status', 'completed')->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])->count(),
        ];

        // Statistiques de paiement
        $paymentStats = [
            'total_amount' => Enrollment::whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])->sum('payment_amount'),
            'paid_amount' => Enrollment::where('payment_status', 'paid')->whereBetween('payment_date', [$startDate->startOfDay(), $endDate->endOfDay()])->sum('payment_amount'),
            'pending_amount' => Enrollment::where('payment_status', 'pending')->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])->sum('payment_amount'),
            'unpaid_amount' => Enrollment::where('payment_status', 'unpaid')->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])->sum('payment_amount'),
            'paid_count' => Enrollment::where('payment_status', 'paid')->whereBetween('payment_date', [$startDate->startOfDay(), $endDate->endOfDay()])->count(),
            'pending_count' => Enrollment::where('payment_status', 'pending')->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])->count(),
            'unpaid_count' => Enrollment::where('payment_status', 'unpaid')->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])->count(),
        ];

        // Dernières inscriptions
        $recentEnrollments = Enrollment::with(['user', 'trainingSession'])
            ->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Derniers paiements
        $recentPayments = Enrollment::with(['user', 'trainingSession'])
            ->whereIn('payment_status', ['paid', 'pending'])
            ->whereBetween('payment_date', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->orderBy('payment_date', 'desc')
            ->take(5)
            ->get();

        // Données pour le graphique d'inscriptions mensuelles
        $enrollmentsByMonth = $this->getEnrollmentsByMonth($startDate, $endDate);

        // Données pour le graphique de revenus mensuels
        $revenueByMonth = $this->getRevenueByMonth($startDate, $endDate);

        // Retourner la vue avec les données
        return Inertia::render('Admin/Dashboard', [
            'stats' => $stats,
            'enrollmentStats' => $enrollmentStats,
            'paymentStats' => $paymentStats,
            'recentEnrollments' => $recentEnrollments,
            'recentPayments' => $recentPayments,
            'enrollmentsByMonth' => $enrollmentsByMonth,
            'revenueByMonth' => $revenueByMonth,
            'startDate' => $startDate->format('Y-m-d'),
            'endDate' => $endDate->format('Y-m-d'),
        ]);
    }

    /**
     * Récupère les inscriptions par mois pour la période spécifiée
     *
     * @param Carbon\Carbon $startDate Date de début pour le filtre
     * @param Carbon\Carbon $endDate Date de fin pour le filtre
     * @return Collection
     */
    private function getEnrollmentsByMonth($startDate, $endDate)
    {
        $months = collect();
        $period = $this->getMonthPeriod($startDate, $endDate);

        // Initialiser les mois dans la période
        foreach ($period as $date) {
            $months->push([
                'month' => $date->format('M'),
                'year' => $date->format('Y'),
                'count' => 0,
            ]);
        }

        // Récupérer les inscriptions par mois
        $enrollments = Enrollment::select(
            DB::raw('MONTH(created_at) as month'),
            DB::raw('YEAR(created_at) as year'),
            DB::raw('COUNT(*) as count')
        )
            ->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        // Mettre à jour les compteurs
        foreach ($enrollments as $enrollment) {
            $monthName = Carbon::createFromDate($enrollment->year, $enrollment->month, 1)->format('M');
            $year = $enrollment->year;

            $index = $months->search(function ($item) use ($monthName, $year) {
                return $item['month'] === $monthName && $item['year'] == $year;
            });

            if ($index !== false) {
                $months = $months->map(function ($item, $key) use ($index, $enrollment) {
                    if ($key === $index) {
                        $item['count'] = $enrollment->count;
                    }
                    return $item;
                });
            }
        }

        return $months;
    }

    /**
     * Récupère les revenus par mois pour la période spécifiée
     *
     * @param Carbon\Carbon $startDate Date de début pour le filtre
     * @param Carbon\Carbon $endDate Date de fin pour le filtre
     * @return Collection
     */
    private function getRevenueByMonth($startDate, $endDate)
    {
        $months = collect();
        $period = $this->getMonthPeriod($startDate, $endDate);

        // Initialiser les mois dans la période
        foreach ($period as $date) {
            $months->push([
                'month' => $date->format('M'),
                'year' => $date->format('Y'),
                'amount' => 0,
            ]);
        }

        // Récupérer les revenus par mois
        $revenues = Enrollment::select(
            DB::raw('MONTH(payment_date) as month'),
            DB::raw('YEAR(payment_date) as year'),
            DB::raw('SUM(payment_amount) as amount')
        )
            ->where('payment_status', 'paid')
            ->whereBetween('payment_date', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        // Mettre à jour les montants
        foreach ($revenues as $revenue) {
            $monthName = Carbon::createFromDate($revenue->year, $revenue->month, 1)->format('M');
            $year = $revenue->year;

            $index = $months->search(function ($item) use ($monthName, $year) {
                return $item['month'] === $monthName && $item['year'] == $year;
            });

            if ($index !== false) {
                $months = $months->map(function ($item, $key) use ($index, $revenue) {
                    if ($key === $index) {
                        $item['amount'] = $revenue->amount;
                    }
                    return $item;
                });
            }
        }

        return $months;
    }

    /**
     * Génère une collection de dates représentant le premier jour de chaque mois
     * entre la date de début et la date de fin
     *
     * @param Carbon\Carbon $startDate Date de début
     * @param Carbon\Carbon $endDate Date de fin
     * @return array
     */
    private function getMonthPeriod($startDate, $endDate)
    {
        $period = [];
        $currentDate = (clone $startDate)->startOfMonth();
        $lastDate = (clone $endDate)->startOfMonth();

        // Si la période est trop longue, limiter à 12 mois maximum
        $maxMonths = 12;
        $monthCount = 0;

        while ($currentDate->lte($lastDate) && $monthCount < $maxMonths) {
            $period[] = clone $currentDate;
            $currentDate->addMonth();
            $monthCount++;
        }

        // Si aucun mois n'a été trouvé (dates trop proches), ajouter au moins le mois de la date de début
        if (empty($period)) {
            $period[] = (clone $startDate)->startOfMonth();
        }

        return $period;
    }
}
