<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Enrollment;
use App\Models\ExamResult;

class FixEnrollmentStatuses extends Command
{
    protected $signature = 'fix:enrollment-statuses';
    protected $description = 'Fix enrollment statuses for students with exam results';

    public function handle()
    {
        $this->info('🔧 Checking for enrollment status issues...');
        
        // Find students with exam results but non-approved enrollments
        $studentsWithResults = ExamResult::select('user_id')
            ->distinct()
            ->get()
            ->pluck('user_id');
        
        $this->line("Students with exam results: {$studentsWithResults->count()}");
        
        $issuesFound = 0;
        $issuesFixed = 0;
        
        foreach ($studentsWithResults as $userId) {
            $enrollments = Enrollment::where('user_id', $userId)
                ->where('status', '!=', 'approved')
                ->with(['user', 'trainingSession'])
                ->get();
            
            foreach ($enrollments as $enrollment) {
                $issuesFound++;
                $this->warn("❌ Issue found: {$enrollment->user->name} - Enrollment status: '{$enrollment->status}' for session: {$enrollment->trainingSession->title}");
                
                if ($this->confirm("Fix this enrollment status to 'approved'?", true)) {
                    $enrollment->status = 'approved';
                    $enrollment->save();
                    $issuesFixed++;
                    $this->info("✅ Fixed enrollment for {$enrollment->user->name}");
                }
            }
        }
        
        $this->info("Summary:");
        $this->line("- Issues found: {$issuesFound}");
        $this->line("- Issues fixed: {$issuesFixed}");
        
        if ($issuesFixed > 0) {
            $this->info("🎉 Fixed {$issuesFixed} enrollment status issues!");
        } else {
            $this->info("✅ No enrollment status issues found or fixed.");
        }
        
        return Command::SUCCESS;
    }
}
