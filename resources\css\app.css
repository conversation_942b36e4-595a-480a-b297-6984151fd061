@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile-optimized certificate viewing */
@layer utilities {
  .certificate-iframe {
    @apply w-full h-full border-0;
    min-height: 400px;
    background: #f9fafb;
  }

  @media (max-width: 640px) {
    .certificate-iframe {
      min-height: 250px;
      /* Mobile-specific iframe optimizations */
      -webkit-overflow-scrolling: touch;
      overflow: auto;
    }
  }

  @media (max-width: 480px) {
    .certificate-iframe {
      min-height: 200px;
    }
  }

  /* Improve PDF viewing on mobile */
  .certificate-modal {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
    padding: 0.5rem;
    /* Prevent body scroll when modal is open */
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  @media (min-width: 640px) {
    .certificate-modal {
      padding: 1rem;
    }
  }

  /* Mobile-friendly modal content */
  .certificate-modal-content {
    @apply bg-white rounded-lg shadow-xl w-full overflow-hidden flex flex-col;
    max-width: 100vw;
    max-height: 95vh;
    /* Ensure proper mobile rendering */
    position: relative;
    margin: auto;
  }

  @media (min-width: 1024px) {
    .certificate-modal-content {
      max-width: 72rem; /* max-w-6xl */
    }
  }

  /* Touch-friendly buttons */
  .touch-button {
    @apply px-4 py-3 rounded-md transition-colors flex items-center justify-center;
    min-height: 44px; /* iOS recommended touch target size */
    min-width: 44px;
    /* Improve touch response */
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    user-select: none;
    -webkit-user-select: none;
  }

  /* Mobile-specific PDF container */
  .mobile-pdf-container {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    background: #f3f4f6;
  }

  @media (max-width: 640px) {
    .mobile-pdf-container {
      /* Add mobile-specific styling for PDF containers */
      min-height: 250px;
      max-height: 60vh;
    }

    .mobile-pdf-container iframe {
      /* Scale down PDF for better mobile viewing */
      transform: scale(0.8);
      transform-origin: top left;
      width: 125%;
      height: 125%;
    }
  }

  /* Responsive text sizing */
  .responsive-text-sm {
    @apply text-xs;
  }

  @media (min-width: 640px) {
    .responsive-text-sm {
      @apply text-sm;
    }
  }

  .responsive-text-base {
    @apply text-sm;
  }

  @media (min-width: 640px) {
    .responsive-text-base {
      @apply text-base;
    }
  }

  .responsive-text-lg {
    @apply text-base;
  }

  @media (min-width: 640px) {
    .responsive-text-lg {
      @apply text-lg;
    }
  }

  /* Mobile modal animations */
  @media (max-width: 640px) {
    .certificate-modal {
      animation: mobileModalFadeIn 0.3s ease-out;
    }

    .certificate-modal-content {
      animation: mobileModalSlideUp 0.3s ease-out;
    }
  }

  @keyframes mobileModalFadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes mobileModalSlideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Prevent zoom on input focus (iOS) */
  @media (max-width: 640px) {
    input, select, textarea {
      font-size: 16px !important;
    }
  }
}
