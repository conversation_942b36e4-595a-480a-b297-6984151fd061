<template>
  <Head title="Sessions de formation" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Sessions de formation disponibles
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Filtres et recherche -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4">Filtrer les sessions</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <!-- Recherche -->
              <div>
                <InputLabel for="search" value="Recherche" />
                <TextInput
                  id="search"
                  v-model="searchQuery"
                  type="text"
                  class="mt-1 block w-full"
                  placeholder="Titre, description..."
                />
              </div>

              <!-- Département -->
              <div>
                <InputLabel for="department" value="Département" />
                <select
                  id="department"
                  v-model="selectedDepartment"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                >
                  <option value="">Tous les départements</option>
                  <option value="Secourisme">Secourisme</option>
                  <option value="Langue">Langue</option>
                  <option value="Formation à la carte">Formation à la carte</option>
                </select>
              </div>

              <!-- Niveau -->
              <div>
                <InputLabel for="level" value="Niveau" />
                <select
                  id="level"
                  v-model="selectedLevel"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                >
                  <option value="">Tous les niveaux</option>
                  <option value="Niveau 1">Niveau 1</option>
                  <option value="Niveau 2">Niveau 2</option>
                  <option value="Niveau 3">Niveau 3</option>
                  <option value="Niveau 4">Niveau 4</option>
                  <option value="Niveau 5">Niveau 5</option>
                  <option value="Niveau 6">Niveau 6</option>
                  <option value="Niveau 7">Niveau 7</option>
                </select>
              </div>

              <!-- Prix -->
              <div>
                <InputLabel for="price" value="Prix" />
                <select
                  id="price"
                  v-model="priceFilter"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                >
                  <option value="">Tous les prix</option>
                  <option value="free">Gratuit</option>
                  <option value="paid">Payant</option>
                </select>
              </div>
            </div>

            <!-- Boutons de tri -->
            <div class="flex items-center gap-4">
              <div class="flex items-center gap-2">
                <span class="text-sm font-medium text-gray-700">Trier par:</span>
                <select
                  v-model="sortBy"
                  class="px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="start_date">Date de début</option>
                  <option value="title">Titre</option>
                  <option value="price">Prix</option>
                </select>
                <button
                  @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'"
                  class="p-1 text-gray-500 hover:text-gray-700"
                >
                  <svg v-if="sortOrder === 'asc'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"></path>
                  </svg>
                  <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Sessions en grille -->
        <div v-if="sortedAndFilteredSessions.length > 0">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-900">
              {{ sortedAndFilteredSessions.length }} session{{ sortedAndFilteredSessions.length > 1 ? 's' : '' }} trouvée{{ sortedAndFilteredSessions.length > 1 ? 's' : '' }}
            </h2>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div v-for="session in sortedAndFilteredSessions" :key="session.id" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-300">
              <!-- Image de la session -->
              <div class="h-48 bg-gray-200 relative">
                <img
                  v-if="session.image"
                  :src="`/storage/${session.image}`"
                  :alt="session.title"
                  class="w-full h-full object-cover"
                  @error="handleImageError"
                >
                <div v-else class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-50 to-blue-100">
                  <div class="text-center">
                    <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2">
                      <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                      </svg>
                    </div>
                    <p class="text-sm text-gray-600">Formation</p>
                  </div>
                </div>

                <!-- Badge statut -->
                <div class="absolute top-2 right-2">
                  <span class="px-2 py-1 text-xs font-semibold rounded-full" :class="getStatusClass(session)">
                    {{ getStatusLabel(session) }}
                  </span>
                </div>

                <!-- Badge département -->
                <div class="absolute top-2 left-2">
                  <span class="px-2 py-1 text-xs font-semibold rounded-full" :class="getDepartmentBadgeClass(session.department)">
                    {{ session.department }}
                  </span>
                </div>
              </div>

              <!-- Contenu -->
              <div class="p-6">
                <div class="flex items-center justify-between mb-2">
                  <span v-if="session.level" class="px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800 rounded-full">
                    {{ session.level }}
                  </span>
                  <span class="text-sm text-gray-500">
                    {{ formatDate(session.start_date) }}
                  </span>
                </div>

                <h3 class="text-lg font-bold text-gray-900 mb-2 line-clamp-2">{{ session.title }}</h3>
                <p class="text-gray-600 mb-3 line-clamp-3 text-sm">{{ session.description }}</p>

                <!-- Informations -->
                <div class="space-y-2 mb-4">
                  <div class="flex items-center text-sm text-gray-600">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    {{ session.trainer?.name || 'Non assigné' }}
                  </div>
                  <div class="flex items-center text-sm text-gray-600">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    {{ session.enrollments_count || 0 }} / {{ session.max_participants || 'Illimité' }}
                  </div>
                  <div class="flex items-center text-sm text-gray-600">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    {{ session.training_domain?.name || 'Domaine non défini' }}
                  </div>
                </div>

                <!-- Prix et action -->
                <div class="flex justify-between items-center">
                  <div class="text-lg font-bold text-gray-900">
                    {{ session.price ? formatPrice(session.price) : 'Gratuit' }}
                  </div>
                  <Link
                    :href="route('student.sessions.show', session.id)"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
                  >
                    Voir détails
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900 text-center">
            <p class="text-lg font-medium">Aucune session de formation disponible.</p>
            <p class="mt-2">Veuillez réessayer plus tard ou modifier vos critères de recherche.</p>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import {
  HeartIcon,
  AcademicCapIcon,
  ArrowRightIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  sessions: Object,
  domains: Array,
  filters: Object,
});

// État local
const selectedDepartment = ref('');
const selectedLevel = ref('');
const searchQuery = ref('');
const priceFilter = ref('');
const sortBy = ref('start_date');
const sortOrder = ref('asc');

// Computed properties
const departments = computed(() => ['Secourisme', 'Langue', 'Formation à la carte']);

const filteredSessions = computed(() => {
  let result = props.sessions.data || [];

  // Filtrer par département
  if (selectedDepartment.value) {
    result = result.filter(session => session.department === selectedDepartment.value);
  }

  // Filtrer par niveau
  if (selectedLevel.value) {
    result = result.filter(session => session.level === selectedLevel.value);
  }

  // Filtrer par recherche
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(session =>
      session.title.toLowerCase().includes(query) ||
      session.description.toLowerCase().includes(query) ||
      (session.training_domain?.name && session.training_domain.name.toLowerCase().includes(query))
    );
  }

  // Filtrer par prix
  if (priceFilter.value === 'free') {
    result = result.filter(session => !session.price || session.price === 0);
  } else if (priceFilter.value === 'paid') {
    result = result.filter(session => session.price && session.price > 0);
  }

  return result;
});

const sortedAndFilteredSessions = computed(() => {
  let result = [...filteredSessions.value];

  // Trier les résultats
  result.sort((a, b) => {
    let aValue, bValue;

    switch (sortBy.value) {
      case 'title':
        aValue = a.title.toLowerCase();
        bValue = b.title.toLowerCase();
        break;
      case 'price':
        aValue = a.price || 0;
        bValue = b.price || 0;
        break;
      case 'start_date':
      default:
        aValue = new Date(a.start_date);
        bValue = new Date(b.start_date);
        break;
    }

    if (sortOrder.value === 'desc') {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    } else {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    }
  });

  return result;
});

// Méthodes
const getDepartmentBadgeClass = (department) => {
  const classes = {
    'Secourisme': 'bg-red-100 text-red-800',
    'Langue': 'bg-blue-100 text-blue-800',
    'Formation à la carte': 'bg-green-100 text-green-800'
  };
  return classes[department] || 'bg-gray-100 text-gray-800';
};



const formatDate = (dateString) => {
  if (!dateString) return 'Date non définie';
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  });
};

const formatPrice = (price) => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR',
  }).format(price);
};

const getStatusClass = (session) => {
  const now = new Date();
  const startDate = new Date(session.start_date);
  const endDate = new Date(session.end_date);

  if (now < startDate) {
    return 'bg-yellow-100 text-yellow-800'; // À venir
  } else if (now >= startDate && now <= endDate) {
    return 'bg-green-100 text-green-800'; // En cours
  } else {
    return 'bg-gray-100 text-gray-800'; // Terminée
  }
};

const getStatusLabel = (session) => {
  const now = new Date();
  const startDate = new Date(session.start_date);
  const endDate = new Date(session.end_date);

  if (now < startDate) {
    return 'À venir';
  } else if (now >= startDate && now <= endDate) {
    return 'En cours';
  } else {
    return 'Terminée';
  }
};

const handleImageError = (event) => {
  event.target.src = '/images/default-session.png';
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
