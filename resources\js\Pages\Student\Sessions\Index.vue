<template>
  <Head title="Sessions de formation" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Sessions de formation disponibles
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Filtres -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4">Filtres</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <InputLabel for="search" value="Recherche" />
                <TextInput
                  id="search"
                  v-model="filters.search"
                  type="text"
                  class="mt-1 block w-full"
                  placeholder="Rechercher par titre ou description"
                  @keyup.enter="applyFilters"
                />
              </div>
              <div>
                <InputLabel for="domain" value="Domaine de formation" />
                <select
                  id="domain"
                  v-model="filters.domain_id"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                >
                  <option value="">Tous les domaines</option>
                  <option v-for="domain in domains" :key="domain.id" :value="domain.id">
                    {{ domain.name }}
                  </option>
                </select>
              </div>
              <div class="flex items-end">
                <button
                  @click="applyFilters"
                  class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                >
                  Filtrer
                </button>
                <button
                  @click="resetFilters"
                  class="ml-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                >
                  Réinitialiser
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Liste des sessions -->
        <div v-if="sessions.data.length > 0">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div v-for="session in sessions.data" :key="session.id" class="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl transition-shadow duration-300">
              <!-- Image de la session -->
              <div class="h-48 bg-gray-200 relative">
                <img 
                  v-if="session.image" 
                  :src="'/storage/' + session.image" 
                  :alt="session.title" 
                  class="w-full h-full object-cover"
                  @error="handleImageError($event, session.id)"
                >
                <div v-else class="w-full h-full flex items-center justify-center bg-gray-100">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <div class="absolute top-2 right-2">
                  <span class="px-2 py-1 text-xs font-semibold rounded-full" :class="getStatusClass(session)">
                    {{ getStatusLabel(session) }}
                  </span>
                </div>
              </div>
              
              <div class="p-6">
                <div class="flex items-center justify-between mb-2">
                  <span class="px-3 py-1 text-xs font-semibold text-blue-600 bg-blue-100 rounded-full">
                    {{ session.training_domain?.name || 'Non catégorisé' }}
                  </span>
                  <span class="text-sm text-gray-500">
                    {{ formatDate(session.start_date) }}
                  </span>
                </div>
                
                <h3 class="text-xl font-bold text-gray-900 mb-2 line-clamp-2">{{ session.title }}</h3>
                <p class="text-gray-600 mb-4 line-clamp-3">{{ session.description }}</p>
                
                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    {{ session.trainer?.name || 'Non assigné' }}
                  </div>
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    {{ session.enrolled_count || 0 }} / {{ session.max_students || '∞' }}
                  </div>
                </div>
                
                <div class="flex justify-between items-center">
                  <div class="text-sm">
                    <span class="font-semibold">Prix:</span> 
                    {{ session.price ? formatPrice(session.price) : 'Gratuit' }}
                  </div>
                  <Link 
                    :href="route('student.sessions.show', session.id)" 
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                  >
                    Voir les détails
                  </Link>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Pagination -->
          <div class="mt-6">
            <Pagination :links="sessions.links" />
          </div>
        </div>
        
        <div v-else class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900 text-center">
            <p class="text-lg font-medium">Aucune session de formation disponible.</p>
            <p class="mt-2">Veuillez réessayer plus tard ou modifier vos critères de recherche.</p>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, watch } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import Pagination from '@/Components/Pagination.vue';

// Props
const props = defineProps({
  sessions: Object,
  domains: Array,
  filters: Object,
});

// État local pour les filtres
const filters = ref({
  search: props.filters.search || '',
  domain_id: props.filters.domain_id || '',
});

// Méthodes
const applyFilters = () => {
  router.get(route('student.sessions.index'), {
    search: filters.value.search,
    domain_id: filters.value.domain_id,
  }, {
    preserveState: true,
    replace: true,
  });
};

const resetFilters = () => {
  filters.value = {
    search: '',
    domain_id: '',
  };
  applyFilters();
};

const formatDate = (dateString) => {
  if (!dateString) return 'Date non définie';
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  });
};

const formatPrice = (price) => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR',
  }).format(price);
};

const getStatusClass = (session) => {
  const now = new Date();
  const startDate = new Date(session.start_date);
  const endDate = new Date(session.end_date);

  if (now < startDate) {
    return 'bg-yellow-100 text-yellow-800'; // À venir
  } else if (now >= startDate && now <= endDate) {
    return 'bg-green-100 text-green-800'; // En cours
  } else {
    return 'bg-gray-100 text-gray-800'; // Terminée
  }
};

const getStatusLabel = (session) => {
  const now = new Date();
  const startDate = new Date(session.start_date);
  const endDate = new Date(session.end_date);

  if (now < startDate) {
    return 'À venir';
  } else if (now >= startDate && now <= endDate) {
    return 'En cours';
  } else {
    return 'Terminée';
  }
};

const handleImageError = (event, sessionId) => {
  console.error(`Erreur de chargement de l'image pour la session ${sessionId}`);
  event.target.src = '/images/default-session.jpg';
};
</script>
