<template>
  <Head title="Sessions de formation" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Sessions de formation disponibles
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Navigation par départements -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4">Filtrer par département</h3>
            <div class="flex flex-wrap gap-3">
              <button
                @click="selectedDepartment = ''"
                :class="[
                  'px-4 py-2 rounded-lg font-medium transition-colors duration-200',
                  selectedDepartment === ''
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                ]"
              >
                Tous les départements
              </button>
              <button
                @click="selectedDepartment = 'Secourisme'"
                :class="[
                  'px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2',
                  selectedDepartment === 'Secourisme'
                    ? 'bg-red-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                ]"
              >
                <HeartIcon class="w-4 h-4" />
                Secourisme
              </button>
              <button
                @click="selectedDepartment = 'Langue'"
                :class="[
                  'px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2',
                  selectedDepartment === 'Langue'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                ]"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                </svg>
                Langue
              </button>
              <button
                @click="selectedDepartment = 'Formation à la carte'"
                :class="[
                  'px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2',
                  selectedDepartment === 'Formation à la carte'
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                ]"
              >
                <AcademicCapIcon class="w-4 h-4" />
                Formation à la carte
              </button>
            </div>

            <!-- Recherche -->
            <div class="mt-4 max-w-md">
              <InputLabel for="search" value="Recherche" />
              <TextInput
                id="search"
                v-model="searchQuery"
                type="text"
                class="mt-1 block w-full"
                placeholder="Rechercher par titre ou description"
              />
            </div>
          </div>
        </div>

        <!-- Sessions organisées par département -->
        <div v-if="filteredSessions.length > 0">
          <!-- Affichage par département si aucun département spécifique sélectionné -->
          <div v-if="selectedDepartment === ''" class="space-y-8">
            <div v-for="department in departments" :key="department" class="bg-white rounded-xl shadow-lg overflow-hidden">
              <div class="p-6" :class="getDepartmentHeaderClass(department)">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mr-4">
                      <HeartIcon v-if="department === 'Secourisme'" class="w-6 h-6 text-white" />
                      <svg v-else-if="department === 'Langue'" class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                      </svg>
                      <AcademicCapIcon v-else class="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 class="text-xl font-bold text-white">{{ department }}</h3>
                      <p class="text-white/80">{{ getDepartmentSessionCount(department) }} sessions disponibles</p>
                    </div>
                  </div>
                  <Link
                    :href="route('student.departments.show', department)"
                    class="inline-flex items-center gap-2 bg-white/20 text-white px-4 py-2 rounded-lg hover:bg-white/30 transition-colors duration-200"
                  >
                    Voir tout
                    <ArrowRightIcon class="w-4 h-4" />
                  </Link>
                </div>
              </div>

              <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div v-for="session in getDepartmentSessions(department).slice(0, 3)" :key="session.id" class="bg-gray-50 rounded-lg shadow-sm overflow-hidden border border-gray-200 hover:shadow-md transition-shadow duration-300">
                    <!-- Image de la session -->
                    <div class="h-40 bg-gray-200 relative">
                      <img
                        v-if="session.image"
                        :src="'/storage/' + session.image"
                        :alt="session.title"
                        class="w-full h-full object-cover"
                        @error="handleImageError($event, session.id)"
                      >
                      <div v-else class="w-full h-full flex items-center justify-center bg-gray-100">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div class="absolute top-2 right-2">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full" :class="getStatusClass(session)">
                          {{ getStatusLabel(session) }}
                        </span>
                      </div>
                    </div>

                    <div class="p-4">
                      <div class="flex items-center justify-between mb-2">
                        <span v-if="session.level" class="px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800 rounded-full">
                          {{ session.level }}
                        </span>
                        <span class="text-xs text-gray-500">
                          {{ formatDate(session.start_date) }}
                        </span>
                      </div>

                      <h4 class="text-sm font-bold text-gray-900 mb-2 line-clamp-2">{{ session.title }}</h4>
                      <p class="text-xs text-gray-600 mb-3 line-clamp-2">{{ session.description }}</p>

                      <div class="flex justify-between items-center">
                        <div class="text-xs">
                          <span class="font-semibold">{{ session.price ? formatPrice(session.price) : 'Gratuit' }}</span>
                        </div>
                        <Link
                          :href="route('student.sessions.show', session.id)"
                          class="px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700"
                        >
                          Détails
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>

                <div v-if="getDepartmentSessions(department).length > 3" class="text-center mt-4">
                  <Link
                    :href="route('student.departments.show', department)"
                    class="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Voir toutes les sessions ({{ getDepartmentSessions(department).length }})
                    <ArrowRightIcon class="w-4 h-4" />
                  </Link>
                </div>
              </div>
            </div>
          </div>

          <!-- Affichage pour un département spécifique -->
          <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div v-for="session in filteredSessions" :key="session.id" class="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl transition-shadow duration-300">
              <!-- Image de la session -->
              <div class="h-48 bg-gray-200 relative">
                <img
                  v-if="session.image"
                  :src="'/storage/' + session.image"
                  :alt="session.title"
                  class="w-full h-full object-cover"
                  @error="handleImageError($event, session.id)"
                >
                <div v-else class="w-full h-full flex items-center justify-center bg-gray-100">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <div class="absolute top-2 right-2">
                  <span class="px-2 py-1 text-xs font-semibold rounded-full" :class="getStatusClass(session)">
                    {{ getStatusLabel(session) }}
                  </span>
                </div>
              </div>

              <div class="p-6">
                <div class="flex items-center justify-between mb-2">
                  <span v-if="session.level" class="px-3 py-1 text-xs font-semibold text-blue-600 bg-blue-100 rounded-full">
                    {{ session.level }}
                  </span>
                  <span class="text-sm text-gray-500">
                    {{ formatDate(session.start_date) }}
                  </span>
                </div>

                <h3 class="text-xl font-bold text-gray-900 mb-2 line-clamp-2">{{ session.title }}</h3>
                <p class="text-gray-600 mb-4 line-clamp-3">{{ session.description }}</p>

                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    {{ session.trainer?.name || 'Non assigné' }}
                  </div>
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    {{ session.enrollments_count || 0 }} / {{ session.max_participants || 'Illimité' }}
                  </div>
                </div>

                <div class="flex justify-between items-center">
                  <div class="text-sm">
                    <span class="font-semibold">Prix:</span>
                    {{ session.price ? formatPrice(session.price) : 'Gratuit' }}
                  </div>
                  <Link
                    :href="route('student.sessions.show', session.id)"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                  >
                    Voir les détails
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900 text-center">
            <p class="text-lg font-medium">Aucune session de formation disponible.</p>
            <p class="mt-2">Veuillez réessayer plus tard ou modifier vos critères de recherche.</p>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import {
  HeartIcon,
  AcademicCapIcon,
  ArrowRightIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  sessions: Object,
  domains: Array,
  filters: Object,
});

// État local
const selectedDepartment = ref('');
const searchQuery = ref('');

// Computed properties
const departments = computed(() => ['Secourisme', 'Langue', 'Formation à la carte']);

const filteredSessions = computed(() => {
  let result = props.sessions.data || [];

  // Filtrer par département
  if (selectedDepartment.value) {
    result = result.filter(session => session.department === selectedDepartment.value);
  }

  // Filtrer par recherche
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(session =>
      session.title.toLowerCase().includes(query) ||
      session.description.toLowerCase().includes(query)
    );
  }

  return result;
});

// Méthodes
const getDepartmentSessions = (department) => {
  return (props.sessions.data || []).filter(session => session.department === department);
};

const getDepartmentSessionCount = (department) => {
  return getDepartmentSessions(department).length;
};

const getDepartmentIcon = (department) => {
  const icons = {
    'Secourisme': HeartIcon,
    'Langue': 'language',
    'Formation à la carte': AcademicCapIcon
  };

  if (icons[department] === 'language') {
    return 'svg'; // Nous utiliserons un SVG inline dans le template
  }

  return icons[department] || AcademicCapIcon;
};

const getDepartmentHeaderClass = (department) => {
  const classes = {
    'Secourisme': 'bg-gradient-to-r from-red-500 to-red-700',
    'Langue': 'bg-gradient-to-r from-blue-500 to-blue-700',
    'Formation à la carte': 'bg-gradient-to-r from-green-500 to-green-700'
  };

  return classes[department] || 'bg-gradient-to-r from-gray-500 to-gray-700';
};



const formatDate = (dateString) => {
  if (!dateString) return 'Date non définie';
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  });
};

const formatPrice = (price) => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR',
  }).format(price);
};

const getStatusClass = (session) => {
  const now = new Date();
  const startDate = new Date(session.start_date);
  const endDate = new Date(session.end_date);

  if (now < startDate) {
    return 'bg-yellow-100 text-yellow-800'; // À venir
  } else if (now >= startDate && now <= endDate) {
    return 'bg-green-100 text-green-800'; // En cours
  } else {
    return 'bg-gray-100 text-gray-800'; // Terminée
  }
};

const getStatusLabel = (session) => {
  const now = new Date();
  const startDate = new Date(session.start_date);
  const endDate = new Date(session.end_date);

  if (now < startDate) {
    return 'À venir';
  } else if (now >= startDate && now <= endDate) {
    return 'En cours';
  } else {
    return 'Terminée';
  }
};

const handleImageError = (event, sessionId) => {
  console.error(`Erreur de chargement de l'image pour la session ${sessionId}`);
  event.target.src = '/images/default-session.jpg';
};
</script>
