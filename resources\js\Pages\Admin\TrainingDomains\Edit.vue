<template>
  <Head title="Modifier un domaine de formation" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Modifier un domaine de formation
        </h2>
        <Link :href="route('admin.training-domains.index')" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
          Retour à la liste
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <form @submit.prevent="submit" class="space-y-6">
              <!-- Nom -->
              <div>
                <InputLabel for="name" value="Nom" />
                <TextInput
                  id="name"
                  type="text"
                  class="mt-1 block w-full"
                  v-model="form.name"
                  required
                  autofocus
                />
                <InputError class="mt-2" :message="form.errors.name" />
              </div>

              <!-- Description -->
              <div>
                <InputLabel for="description" value="Description" />
                <textarea
                  id="description"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  v-model="form.description"
                  rows="4"
                ></textarea>
                <InputError class="mt-2" :message="form.errors.description" />
              </div>

              <!-- Image actuelle -->
              <div v-if="domain.image">
                <p class="text-sm text-gray-600 mb-2">Image actuelle :</p>
                <img :src="'/storage/' + domain.image" alt="Image du domaine" class="h-32 w-auto object-cover rounded-md" />
              </div>

              <!-- Nouvelle image -->
              <div>
                <InputLabel for="image" value="Nouvelle image (optionnelle)" />
                <input
                  id="image"
                  type="file"
                  class="mt-1 block w-full"
                  @input="form.image = $event.target.files[0]"
                  accept="image/*"
                />
                <InputError class="mt-2" :message="form.errors.image" />
              </div>

              <!-- Statut -->
              <div class="flex items-center">
                <input
                  id="active"
                  type="checkbox"
                  class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                  v-model="form.active"
                />
                <InputLabel for="active" value="Actif" class="ml-2" />
                <InputError class="mt-2" :message="form.errors.active" />
              </div>

              <!-- Boutons -->
              <div class="flex items-center justify-end">
                <PrimaryButton class="ml-4" :disabled="form.processing">
                  Mettre à jour
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  domain: Object,
});

// Formulaire
const form = useForm({
  name: props.domain.name,
  description: props.domain.description || '',
  image: null,
  active: props.domain.active,
  _method: 'PUT',
});

// Méthodes
const submit = () => {
  form.post(route('admin.training-domains.update', props.domain.id), {
    preserveScroll: true,
  });
};
</script>
