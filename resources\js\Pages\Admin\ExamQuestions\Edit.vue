<template>
  <Head title="Modifier une question" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Modifier une question de l'examen: {{ exam.title }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link :href="route('admin.exam-questions.index', { exam_id: exam.id })" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour aux questions
              </Link>
            </div>

            <!-- Formulaire de modification de question -->
            <form @submit.prevent="submit">
              <div class="grid grid-cols-1 gap-6">
                <!-- Type de question -->
                <div>
                  <InputLabel for="question_type" value="Type de question" />
                  <select
                    id="question_type"
                    v-model="form.question_type"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    required
                  >
                    <option value="multiple_choice">Choix multiple (QCM)</option>
                    <option value="text">Texte libre</option>
                    <option value="file">Soumission de fichier</option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.question_type" />
                </div>

                <!-- Texte de la question -->
                <div>
                  <InputLabel for="question_text" value="Texte de la question" />
                  <textarea
                    id="question_text"
                    v-model="form.question_text"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    rows="3"
                    required
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.question_text" />
                </div>

                <!-- Points -->
                <div>
                  <InputLabel for="points" value="Points" />
                  <TextInput
                    id="points"
                    type="number"
                    class="mt-1 block w-full"
                    v-model="form.points"
                    min="1"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.points" />
                </div>

                <!-- Ordre -->
                <div>
                  <InputLabel for="order" value="Ordre" />
                  <TextInput
                    id="order"
                    type="number"
                    class="mt-1 block w-full"
                    v-model="form.order"
                    min="1"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.order" />
                </div>

                <!-- Options pour QCM -->
                <div v-if="form.question_type === 'multiple_choice'">
                  <h3 class="font-semibold text-lg mb-3">Options de réponse</h3>

                  <!-- Option pour permettre plusieurs réponses correctes -->
                  <div class="mb-4">
                    <div class="flex items-center">
                      <input
                        id="multiple_answers_allowed"
                        type="checkbox"
                        v-model="form.multiple_answers_allowed"
                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                      />
                      <label for="multiple_answers_allowed" class="ml-2 text-sm text-gray-700">
                        Permettre plusieurs réponses correctes
                      </label>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">
                      Si activé, l'apprenant pourra sélectionner plusieurs réponses. Vous devez sélectionner au moins une réponse correcte.
                    </p>
                  </div>

                  <div v-for="(option, index) in options" :key="index" class="flex items-center mb-3">
                    <div class="flex-grow mr-2">
                      <div class="flex items-center">
                        <input
                          v-if="!form.multiple_answers_allowed"
                          :id="`option_correct_${index}`"
                          type="radio"
                          :value="String.fromCharCode(65 + index)"
                          v-model="form.correct_answer"
                          class="mr-2"
                          required
                        />
                        <input
                          v-else
                          :id="`option_correct_${index}`"
                          type="checkbox"
                          :value="String.fromCharCode(65 + index)"
                          v-model="correctAnswers"
                          class="rounded mr-2"
                        />
                        <label :for="`option_${index}`" class="mr-2">Option {{ String.fromCharCode(65 + index) }}:</label>
                        <input
                          :id="`option_${index}`"
                          type="text"
                          v-model="options[index]"
                          class="flex-grow border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                          required
                        />
                      </div>
                    </div>
                    <button
                      type="button"
                      @click="removeOption(index)"
                      class="text-red-600 hover:text-red-800"
                      :disabled="options.length <= 2"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>

                  <button
                    type="button"
                    @click="addOption"
                    class="mt-2 inline-flex items-center px-3 py-1 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300"
                    :disabled="options.length >= 6"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    Ajouter une option
                  </button>
                </div>

                <!-- Instructions pour les questions de type texte -->
                <div v-if="form.question_type === 'text'" class="bg-blue-50 p-4 rounded-lg">
                  <p class="text-blue-700">
                    <strong>Note:</strong> Pour les questions à réponse textuelle, l'apprenant devra saisir sa réponse dans un champ de texte.
                    Vous pourrez évaluer manuellement les réponses après la soumission de l'examen.
                  </p>
                </div>

                <!-- Instructions pour les questions de type fichier -->
                <div v-if="form.question_type === 'file'" class="bg-purple-50 p-4 rounded-lg">
                  <p class="text-purple-700">
                    <strong>Note:</strong> Pour les questions avec soumission de fichier, l'apprenant devra téléverser un document.
                    Vous pourrez télécharger et évaluer les fichiers soumis après la soumission de l'examen.
                  </p>
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="flex items-center justify-end mt-6">
                <PrimaryButton class="ml-4" :disabled="form.processing">
                  Mettre à jour la question
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref, onMounted, watch } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  question: Object,
  exam: Object,
});

// Options pour les QCM
const options = ref([]);

// Tableau pour stocker les réponses correctes multiples
const correctAnswers = ref([]);

// Formulaire
const form = useForm({
  question_text: props.question.question_text,
  question_type: props.question.question_type,
  options: {},
  correct_answer: '',
  correct_answers: [],
  multiple_answers_allowed: false,
  points: props.question.points,
  order: props.question.order,
});

// Initialiser les réponses correctes à partir de correct_options
if (props.question.correct_options) {
  const correctOptions = Array.isArray(props.question.correct_options)
    ? props.question.correct_options
    : (typeof props.question.correct_options === 'string'
      ? JSON.parse(props.question.correct_options)
      : []);

  if (correctOptions.length === 1) {
    // Une seule réponse correcte
    form.correct_answer = correctOptions[0];
    form.multiple_answers_allowed = false;
  } else if (correctOptions.length > 1) {
    // Plusieurs réponses correctes
    form.correct_answers = correctOptions;
    form.multiple_answers_allowed = true;
  }
}

// Initialiser les options à partir des données existantes
onMounted(() => {
  if (props.question.question_type === 'multiple_choice' && props.question.options) {
    const optionsObj = typeof props.question.options === 'string'
      ? JSON.parse(props.question.options)
      : props.question.options;

    // Convertir l'objet d'options en tableau
    const optionsArray = [];
    Object.keys(optionsObj).sort().forEach(key => {
      optionsArray.push(optionsObj[key]);
    });

    options.value = optionsArray.length > 0 ? optionsArray : ['', ''];

    // Initialiser les réponses correctes multiples si nécessaire
    if (form.multiple_answers_allowed) {
      correctAnswers.value = form.correct_answers;
    } else if (form.correct_answer) {
      // Si une seule réponse est correcte
      correctAnswers.value = [form.correct_answer];
    }
  } else {
    options.value = ['', '']; // Au moins 2 options par défaut
  }
});

// Méthodes
const addOption = () => {
  if (options.value.length < 6) { // Limiter à 6 options maximum
    options.value.push('');
  }
};

const removeOption = (index) => {
  if (options.value.length > 2) { // Garder au moins 2 options
    options.value.splice(index, 1);

    // Réinitialiser la réponse correcte si elle était sur l'option supprimée
    if (form.correct_answer === String.fromCharCode(65 + index)) {
      form.correct_answer = '';
    }
    // Ajuster la réponse correcte si elle était sur une option après celle supprimée
    else if (form.correct_answer > String.fromCharCode(65 + index)) {
      form.correct_answer = String.fromCharCode(form.correct_answer.charCodeAt(0) - 1);
    }
  }
};

// Mettre à jour les options dans le formulaire avant la soumission
const submit = () => {
  if (form.question_type === 'multiple_choice') {
    // Convertir le tableau d'options en objet avec des clés A, B, C, etc.
    const optionsObj = {};
    options.value.forEach((option, index) => {
      optionsObj[String.fromCharCode(65 + index)] = option;
    });
    form.options = optionsObj;

    // Gérer les réponses correctes multiples
    if (form.multiple_answers_allowed) {
      // Vérifier qu'au moins une réponse est sélectionnée
      if (correctAnswers.value.length === 0) {
        alert('Veuillez sélectionner au moins une réponse correcte.');
        return;
      }
      form.correct_answers = correctAnswers.value;
      form.correct_answer = null; // Ne pas utiliser correct_answer
    } else {
      // Vérifier qu'une réponse est sélectionnée
      if (!form.correct_answer) {
        alert('Veuillez sélectionner la réponse correcte.');
        return;
      }
      form.correct_answers = null;
    }
  }

  form.put(route('admin.exam-questions.update', props.question.id));
};

// Réinitialiser les réponses correctes quand le type de question change
watch(() => form.question_type, (newType) => {
  if (newType !== 'multiple_choice') {
    form.correct_answer = '';
    correctAnswers.value = [];
    form.multiple_answers_allowed = false;
  }
});

// Réinitialiser les réponses correctes quand le mode de réponse change
watch(() => form.multiple_answers_allowed, (isMultiple) => {
  if (isMultiple) {
    // Convertir la réponse unique en tableau si elle existe
    if (form.correct_answer) {
      correctAnswers.value = [form.correct_answer];
    } else {
      correctAnswers.value = [];
    }
    form.correct_answer = '';
  } else {
    // Prendre la première réponse du tableau si elle existe
    form.correct_answer = correctAnswers.value.length > 0 ? correctAnswers.value[0] : '';
    correctAnswers.value = [];
  }
});
</script>
