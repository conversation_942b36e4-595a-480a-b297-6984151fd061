<template>
    <div class="space-y-4">
        <!-- File Input -->
        <div>
            <InputLabel :for="inputId" :value="label" />
            <input
                :id="inputId"
                type="file"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                @change="handleFileChange"
                :accept="accept"
                :multiple="multiple"
            />
            <InputError class="mt-2" :message="error" />
            
            <!-- File Info -->
            <div v-if="fileInfo" class="mt-2 text-sm text-gray-600">
                <p>{{ fileInfo.name }} ({{ fileInfo.size }})</p>
            </div>
        </div>

        <!-- Preview -->
        <div v-if="preview" class="space-y-4">
            <div class="border rounded-lg p-4 bg-gray-50">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Aperçu</h4>
                
                <!-- Image Preview -->
                <div v-if="fileType === 'image'" class="space-y-2">
                    <img :src="preview" :alt="fileName" class="max-w-full h-64 object-cover rounded-lg shadow-md">
                    <div v-if="imageDimensions" class="text-xs text-gray-500">
                        Dimensions: {{ imageDimensions.width }}x{{ imageDimensions.height }}px
                    </div>
                </div>
                
                <!-- Video Preview -->
                <div v-else-if="fileType === 'video'" class="space-y-2">
                    <video :src="preview" class="max-w-full h-64 object-cover rounded-lg shadow-md" controls>
                        Votre navigateur ne supporte pas la lecture vidéo.
                    </video>
                </div>
                
                <!-- File Preview -->
                <div v-else class="flex items-center space-x-3 p-3 bg-white rounded border">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">{{ fileName }}</p>
                        <p class="text-sm text-gray-500">{{ fileSize }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Upload Progress -->
            <div v-if="uploading" class="space-y-2">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-600">Upload en cours...</span>
                    <span class="text-gray-600">{{ uploadProgress }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" :style="{ width: uploadProgress + '%' }"></div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="flex justify-between items-center">
                <button
                    @click="clearFile"
                    type="button"
                    class="text-sm text-red-600 hover:text-red-800 transition-colors duration-200"
                >
                    Supprimer
                </button>
                
                <div v-if="showOptimizationInfo" class="text-xs text-gray-500">
                    <span v-if="optimized" class="text-green-600">✓ Optimisé</span>
                    <span v-else class="text-yellow-600">⚠ Non optimisé</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import InputLabel from '@/Components/InputLabel.vue';
import InputError from '@/Components/InputError.vue';

const props = defineProps({
    label: {
        type: String,
        default: 'Fichier'
    },
    accept: {
        type: String,
        default: '*/*'
    },
    multiple: {
        type: Boolean,
        default: false
    },
    maxSize: {
        type: Number,
        default: 10 * 1024 * 1024 // 10MB
    },
    showOptimizationInfo: {
        type: Boolean,
        default: true
    },
    modelValue: {
        default: null
    }
});

const emit = defineEmits(['update:modelValue', 'file-selected', 'file-cleared']);

const inputId = computed(() => 'file-input-' + Math.random().toString(36).substr(2, 9));
const file = ref(null);
const preview = ref(null);
const error = ref('');
const uploading = ref(false);
const uploadProgress = ref(0);
const optimized = ref(false);
const imageDimensions = ref(null);

const fileName = computed(() => file.value?.name || '');
const fileSize = computed(() => {
    if (!file.value) return '';
    const bytes = file.value.size;
    if (bytes >= 1073741824) {
        return (bytes / 1073741824).toFixed(2) + ' GB';
    } else if (bytes >= 1048576) {
        return (bytes / 1048576).toFixed(2) + ' MB';
    } else if (bytes >= 1024) {
        return (bytes / 1024).toFixed(2) + ' KB';
    } else {
        return bytes + ' bytes';
    }
});

const fileInfo = computed(() => {
    if (!file.value) return null;
    return {
        name: fileName.value,
        size: fileSize.value
    };
});

const fileType = computed(() => {
    if (!file.value) return null;
    if (file.value.type.startsWith('image/')) return 'image';
    if (file.value.type.startsWith('video/')) return 'video';
    return 'file';
});

const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];
    if (!selectedFile) {
        clearFile();
        return;
    }
    
    // Validate file size
    if (selectedFile.size > props.maxSize) {
        error.value = `Le fichier est trop volumineux. Taille maximale: ${formatBytes(props.maxSize)}`;
        return;
    }
    
    error.value = '';
    file.value = selectedFile;
    
    // Create preview
    createPreview(selectedFile);
    
    // Emit events
    emit('update:modelValue', selectedFile);
    emit('file-selected', selectedFile);
};

const createPreview = (selectedFile) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
        preview.value = e.target.result;
        
        // Get image dimensions if it's an image
        if (selectedFile.type.startsWith('image/')) {
            const img = new Image();
            img.onload = () => {
                imageDimensions.value = {
                    width: img.width,
                    height: img.height
                };
            };
            img.src = e.target.result;
        }
    };
    
    reader.readAsDataURL(selectedFile);
};

const clearFile = () => {
    file.value = null;
    preview.value = null;
    error.value = '';
    imageDimensions.value = null;
    optimized.value = false;
    
    // Clear input
    const input = document.getElementById(inputId.value);
    if (input) {
        input.value = '';
    }
    
    emit('update:modelValue', null);
    emit('file-cleared');
};

const formatBytes = (bytes) => {
    if (bytes >= 1073741824) {
        return (bytes / 1073741824).toFixed(2) + ' GB';
    } else if (bytes >= 1048576) {
        return (bytes / 1048576).toFixed(2) + ' MB';
    } else if (bytes >= 1024) {
        return (bytes / 1024).toFixed(2) + ' KB';
    } else {
        return bytes + ' bytes';
    }
};

// Simulate optimization process
watch(file, (newFile) => {
    if (newFile) {
        // Simulate upload progress
        uploading.value = true;
        uploadProgress.value = 0;
        
        const interval = setInterval(() => {
            uploadProgress.value += 10;
            if (uploadProgress.value >= 100) {
                clearInterval(interval);
                uploading.value = false;
                optimized.value = true;
            }
        }, 100);
    }
});
</script>
