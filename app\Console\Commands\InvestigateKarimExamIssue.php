<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\ExamResult;
use App\Models\Enrollment;
use App\Models\TrainingSession;
use App\Models\Exam;

class InvestigateKarimExamIssue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exam:investigate-karim';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Investigate exam result display <NAME_EMAIL>';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Investigating exam result <NAME_EMAIL>');
        $this->info('=======================================================');
        
        // Find the student by email
        $student = User::where('email', '<EMAIL>')->first();
        
        if (!$student) {
            $this->error('❌ Student <NAME_EMAIL> not found!');
            return Command::FAILURE;
        }
        
        $this->info("👨‍🎓 Student Found: {$student->name} (ID: {$student->id})");
        $this->line("   Email: {$student->email}");
        $this->line("   Role: {$student->role}");
        $this->line('');
        
        $this->checkExamResults($student);
        $this->checkEnrollments($student);
        $this->checkAvailableExams($student);
        $this->simulateControllerLogic($student);
        $this->provideSolution($student);
        
        return Command::SUCCESS;
    }
    
    private function checkExamResults($student)
    {
        $this->info('📝 Checking Exam Results...');
        
        $examResults = ExamResult::where('user_id', $student->id)
            ->with(['exam', 'exam.trainingSession'])
            ->get();
        
        $this->line("   Total exam results: {$examResults->count()}");
        
        if ($examResults->count() > 0) {
            $this->line("   Exam result details:");
            foreach ($examResults as $result) {
                $status = $result->passed ? 'PASSED' : 'FAILED';
                $sessionTitle = $result->exam->trainingSession->title ?? 'No session';
                $sessionActive = $result->exam->trainingSession->active ? 'Active' : 'Inactive';
                $this->line("   - {$result->exam->title}: {$result->score}% ({$status}) - Session: {$sessionTitle} ({$sessionActive})");
                $this->line("     Result ID: {$result->id}, Exam ID: {$result->exam_id}, Created: {$result->created_at}");
            }
        } else {
            $this->warn("   ❌ No exam results found for this student!");
        }
        $this->line('');
    }
    
    private function checkEnrollments($student)
    {
        $this->info('📚 Checking Enrollments...');
        
        $enrollments = Enrollment::where('user_id', $student->id)
            ->with('trainingSession')
            ->get();
        
        $this->line("   Total enrollments: {$enrollments->count()}");
        
        if ($enrollments->count() > 0) {
            $this->line("   Enrollment details:");
            foreach ($enrollments as $enrollment) {
                $sessionActive = $enrollment->trainingSession->active ? 'Active' : 'Inactive';
                $this->line("   - {$enrollment->trainingSession->title}: {$enrollment->status} ({$sessionActive})");
                $this->line("     Enrollment ID: {$enrollment->id}, Session ID: {$enrollment->training_session_id}");
            }
        } else {
            $this->warn("   ❌ No enrollments found for this student!");
        }
        $this->line('');
    }
    
    private function checkAvailableExams($student)
    {
        $this->info('🎯 Checking Available Exams...');
        
        // Get student's session IDs
        $sessionIds = Enrollment::where('user_id', $student->id)
            ->where('status', 'approved')
            ->pluck('training_session_id')
            ->toArray();
        
        $this->line("   Student's session IDs: " . implode(', ', $sessionIds));
        
        if (empty($sessionIds)) {
            $this->warn("   ❌ No approved enrollments found!");
            return;
        }
        
        // Check active sessions
        $activeSessionIds = TrainingSession::whereIn('id', $sessionIds)
            ->where('active', true)
            ->pluck('id')
            ->toArray();
        
        $this->line("   Active session IDs: " . implode(', ', $activeSessionIds));
        
        // Check sessions with exam results
        $examResultSessionIds = ExamResult::where('user_id', $student->id)
            ->join('exams', 'exam_results.exam_id', '=', 'exams.id')
            ->whereIn('exams.training_session_id', $sessionIds)
            ->distinct()
            ->pluck('exams.training_session_id')
            ->toArray();
        
        $this->line("   Sessions with exam results: " . implode(', ', $examResultSessionIds));
        
        // Combined relevant sessions
        $allRelevantSessionIds = array_unique(array_merge($activeSessionIds, $examResultSessionIds));
        $this->line("   All relevant session IDs: " . implode(', ', $allRelevantSessionIds));
        
        // Check exams in relevant sessions
        $exams = Exam::whereIn('training_session_id', $allRelevantSessionIds)
            ->where('is_published', true)
            ->with(['trainingSession'])
            ->get();
        
        $this->line("   Published exams in relevant sessions: {$exams->count()}");
        
        if ($exams->count() > 0) {
            foreach ($exams as $exam) {
                $sessionActive = $exam->trainingSession->active ? 'Active' : 'Inactive';
                $this->line("   - {$exam->title} (ID: {$exam->id}) - Session: {$exam->trainingSession->title} ({$sessionActive})");
            }
        }
        $this->line('');
    }
    
    private function simulateControllerLogic($student)
    {
        $this->info('🔄 Simulating Student Exam Controller Logic...');
        
        // Exact logic from StudentExamController
        $enrollments = Enrollment::where('user_id', $student->id)
            ->where('status', 'approved')
            ->pluck('id')
            ->toArray();

        $sessionIds = Enrollment::where('user_id', $student->id)
            ->where('status', 'approved')
            ->pluck('training_session_id')
            ->toArray();

        $activeSessionIds = TrainingSession::whereIn('id', $sessionIds)
            ->where('active', true)
            ->pluck('id')
            ->toArray();

        $examResultSessionIds = ExamResult::where('user_id', $student->id)
            ->join('exams', 'exam_results.exam_id', '=', 'exams.id')
            ->whereIn('exams.training_session_id', $sessionIds)
            ->distinct()
            ->pluck('exams.training_session_id')
            ->toArray();

        $allRelevantSessionIds = array_unique(array_merge($activeSessionIds, $examResultSessionIds));

        $exams = Exam::whereIn('training_session_id', $allRelevantSessionIds)
            ->where('is_published', true)
            ->with(['trainingSession', 'questions'])
            ->orderBy('created_at', 'desc')
            ->get();

        $examResults = ExamResult::where('user_id', $student->id)
            ->with('exam')
            ->get()
            ->groupBy('exam_id');

        $this->line("   Controller simulation results:");
        $this->line("   - Approved enrollments: " . count($enrollments));
        $this->line("   - Session IDs: " . implode(', ', $sessionIds));
        $this->line("   - Active sessions: " . implode(', ', $activeSessionIds));
        $this->line("   - Sessions with results: " . implode(', ', $examResultSessionIds));
        $this->line("   - All relevant sessions: " . implode(', ', $allRelevantSessionIds));
        $this->line("   - Found exams: {$exams->count()}");
        $this->line("   - Exam result groups: {$examResults->count()}");
        
        if ($exams->count() === 0) {
            $this->error("   ❌ ISSUE FOUND: No exams retrieved by controller logic!");
        } else {
            $this->info("   ✅ Controller would find {$exams->count()} exam(s)");
        }
        $this->line('');
    }
    
    private function provideSolution($student)
    {
        $this->info('💡 Diagnosis and Solution...');
        
        $examResults = ExamResult::where('user_id', $student->id)->count();
        $approvedEnrollments = Enrollment::where('user_id', $student->id)
            ->where('status', 'approved')
            ->count();
        
        if ($examResults === 0) {
            $this->error("   ❌ ISSUE: Student has no exam results in database");
            $this->line("   SOLUTION: Verify if exam was actually completed and saved");
        } elseif ($approvedEnrollments === 0) {
            $this->error("   ❌ ISSUE: Student has no approved enrollments");
            $this->line("   SOLUTION: Check enrollment status and approve if necessary");
        } else {
            $this->info("   ✅ Student has exam results and approved enrollments");
            $this->line("   SOLUTION: Check if training sessions are active or if exam display logic needs adjustment");
        }
    }
}
