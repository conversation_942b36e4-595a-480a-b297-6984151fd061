<template>
  <Head :title="`${departmentInfo.title} - Formations`" />

  <AuthenticatedLayout>
    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- En-tête du département -->
        <div class="mb-8">
          <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r p-8" :class="departmentInfo.gradient">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="bg-white/20 rounded-lg p-4">
                    <HeartIcon v-if="department === 'Secourisme'" class="w-10 h-10 text-white" />
                    <svg v-else-if="department === 'Langue'" class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                    </svg>
                    <AcademicCapIcon v-else class="w-10 h-10 text-white" />
                  </div>
                  <div class="ml-6">
                    <h1 class="text-3xl font-bold text-white">{{ departmentInfo.title }}</h1>
                    <p class="text-white/90 mt-2">{{ departmentInfo.description }}</p>
                  </div>
                </div>
                <Link 
                  :href="route('student.dashboard')"
                  class="inline-flex items-center px-4 py-2 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors duration-200"
                >
                  <ArrowLeftIcon class="w-4 h-4 mr-2" />
                  Retour au dashboard
                </Link>
              </div>
            </div>
            
            <!-- Statistiques du département -->
            <div class="p-6 bg-gray-50">
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900">{{ stats.total_sessions }}</div>
                  <div class="text-sm text-gray-600">Sessions totales</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600">{{ stats.available_sessions }}</div>
                  <div class="text-sm text-gray-600">Disponibles</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-blue-600">{{ stats.enrolled_sessions }}</div>
                  <div class="text-sm text-gray-600">Inscriptions</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-purple-600">{{ stats.completed_sessions }}</div>
                  <div class="text-sm text-gray-600">Terminées</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Filtres et recherche -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
          <h2 class="text-lg font-semibold mb-4">Filtrer les formations</h2>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Recherche -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Titre de formation..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <!-- Niveau -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Niveau</label>
              <select
                v-model="selectedLevel"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Tous les niveaux</option>
                <option v-for="level in availableLevels" :key="level" :value="level">{{ level }}</option>
              </select>
            </div>

            <!-- Prix -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Prix</label>
              <select
                v-model="priceFilter"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Tous les prix</option>
                <option value="free">Gratuit</option>
                <option value="paid">Payant</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Sessions par niveau -->
        <div class="bg-white rounded-xl shadow-lg p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-2xl font-bold text-gray-900">Formations disponibles</h2>
            <div class="flex items-center gap-2">
              <button
                @click="viewMode = 'grid'"
                :class="[
                  'p-2 rounded-md',
                  viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
                ]"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                </svg>
              </button>
              <button
                @click="viewMode = 'list'"
                :class="[
                  'p-2 rounded-md',
                  viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
                ]"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                </svg>
              </button>
            </div>
          </div>

          <div v-if="filteredSessions.length === 0" class="text-center py-12">
            <div class="text-gray-400 mb-4">
              <HeartIcon v-if="department === 'Secourisme'" class="w-16 h-16 mx-auto" />
              <svg v-else-if="department === 'Langue'" class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
              </svg>
              <AcademicCapIcon v-else class="w-16 h-16 mx-auto" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Aucune formation trouvée</h3>
            <p class="text-gray-600">Essayez de modifier vos critères de recherche.</p>
          </div>

          <!-- Vue grille -->
          <div v-else-if="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div
              v-for="sessionData in filteredSessions"
              :key="sessionData.session.id"
              class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden"
            >
              <!-- Image -->
              <div class="h-48 bg-gray-200 relative">
                <img
                  v-if="sessionData.session.image"
                  :src="`/storage/${sessionData.session.image}`"
                  :alt="sessionData.session.title"
                  class="w-full h-full object-cover"
                  @error="handleImageError"
                />
                <div v-else class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-50 to-blue-100">
                  <div class="text-center">
                    <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2">
                      <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                      </svg>
                    </div>
                    <p class="text-sm text-gray-600">Formation</p>
                  </div>
                </div>

                <!-- Badge statut -->
                <div class="absolute top-2 right-2">
                  <span class="px-2 py-1 text-xs font-semibold rounded-full" :class="getStatusClass(sessionData.state)">
                    {{ getStatusLabel(sessionData.state) }}
                  </span>
                </div>

                <!-- Badge niveau -->
                <div class="absolute top-2 left-2">
                  <span class="px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800 rounded-full">
                    {{ sessionData.session.level }}
                  </span>
                </div>
              </div>

              <!-- Contenu -->
              <div class="p-4">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm text-gray-500">
                    {{ formatDate(sessionData.session.start_date) }}
                  </span>
                  <SessionStateIcon :state="sessionData.state" />
                </div>

                <h3 class="text-lg font-bold text-gray-900 mb-2 line-clamp-2">{{ sessionData.session.title }}</h3>
                <p class="text-gray-600 mb-3 line-clamp-2 text-sm">{{ sessionData.session.description }}</p>

                <!-- Raison/État -->
                <div class="mb-3">
                  <p class="text-xs p-2 rounded" :class="getReasonClass(sessionData.state)" :style="getReasonStyle(sessionData.state)">
                    {{ sessionData.reason }}
                  </p>
                </div>

                <!-- Informations -->
                <div class="space-y-1 mb-4 text-sm text-gray-600">
                  <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    {{ sessionData.session.enrollments_count || 0 }} / {{ sessionData.session.max_participants || 'Illimité' }}
                  </div>
                </div>

                <!-- Prix et action -->
                <div class="flex justify-between items-center">
                  <div class="text-lg font-bold text-gray-900">
                    {{ sessionData.session.price ? formatPrice(sessionData.session.price) : 'Gratuit' }}
                  </div>
                  <Link
                    v-if="sessionData.canEnroll"
                    :href="route('student.sessions.show', sessionData.session.id)"
                    class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200"
                  >
                    S'inscrire
                  </Link>
                  <Link
                    v-else-if="sessionData.state === 'enrolled' || sessionData.state === 'pending' || sessionData.state === 'rejected'"
                    :href="route('student.sessions.show', sessionData.session.id)"
                    class="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors duration-200"
                  >
                    Voir détails
                  </Link>
                  <span
                    v-else-if="sessionData.state === 'completed'"
                    class="px-4 py-2 bg-green-100 text-green-800 text-sm font-medium rounded-lg"
                  >
                    Terminé
                  </span>
                  <span
                    v-else
                    class="px-4 py-2 bg-gray-100 text-gray-600 text-sm font-medium rounded-lg cursor-not-allowed"
                  >
                    {{ getActionLabel(sessionData.state) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Vue liste (ancienne vue par niveaux) -->
          <DepartmentSessions
            v-else
            :sessions="sessionsByLevel"
            :completedLevels="completedLevels"
            :department="department"
          />
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import DepartmentSessions from '@/Components/DepartmentSessions.vue';
import SessionStateIcon from '@/Components/SessionStateIcon.vue';
import {
  ArrowLeftIcon,
  HeartIcon,
  AcademicCapIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  department: String,
  sessionsByLevel: Object,
  completedLevels: Array,
  stats: Object,
  departmentInfo: Object,
});

// État local
const searchQuery = ref('');
const selectedLevel = ref('');
const priceFilter = ref('');
const viewMode = ref('grid');

// Computed
const availableLevels = computed(() => {
  if (!props.sessionsByLevel || typeof props.sessionsByLevel !== 'object') {
    return [];
  }
  const levels = ['Niveau 1', 'Niveau 2', 'Niveau 3', 'Niveau 4', 'Niveau 5', 'Niveau 6', 'Niveau 7'];
  return levels.filter(level => props.sessionsByLevel[level] && props.sessionsByLevel[level].length > 0);
});

const allSessions = computed(() => {
  const sessions = [];
  if (props.sessionsByLevel && typeof props.sessionsByLevel === 'object') {
    Object.values(props.sessionsByLevel).forEach(levelSessions => {
      if (Array.isArray(levelSessions)) {
        sessions.push(...levelSessions);
      }
    });
  }
  return sessions;
});

const filteredSessions = computed(() => {
  let result = allSessions.value;

  // Filtrer par recherche
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(sessionData =>
      sessionData.session.title.toLowerCase().includes(query) ||
      sessionData.session.description.toLowerCase().includes(query)
    );
  }

  // Filtrer par niveau
  if (selectedLevel.value) {
    result = result.filter(sessionData => sessionData.session.level === selectedLevel.value);
  }

  // Filtrer par prix
  if (priceFilter.value === 'free') {
    result = result.filter(sessionData => !sessionData.session.price || sessionData.session.price === 0);
  } else if (priceFilter.value === 'paid') {
    result = result.filter(sessionData => sessionData.session.price && sessionData.session.price > 0);
  }

  return result;
});

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return 'Date non définie';
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  });
};

const formatPrice = (price) => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR',
  }).format(price);
};

const getStatusClass = (state) => {
  const classes = {
    'available': 'bg-green-100 text-green-800',
    'enrolled': 'bg-blue-100 text-blue-800',
    'pending': 'bg-yellow-100 text-yellow-800',
    'completed': 'bg-purple-100 text-purple-800',
    'rejected': 'bg-red-100 text-red-800',
    'locked': 'bg-gray-100 text-gray-600',
    'full': 'bg-orange-100 text-orange-800',
    'started': 'bg-gray-100 text-gray-600'
  };
  return classes[state] || 'bg-gray-100 text-gray-600';
};

const getStatusLabel = (state) => {
  const labels = {
    'available': 'Disponible',
    'enrolled': 'Inscrit',
    'pending': 'En attente',
    'completed': 'Terminé',
    'rejected': 'Refusé',
    'locked': 'Verrouillé',
    'full': 'Complet',
    'started': 'Commencé'
  };
  return labels[state] || 'Non disponible';
};

const getReasonClass = (state) => {
  const classes = {
    'available': 'text-green-700',
    'enrolled': 'text-blue-700',
    'pending': 'text-yellow-700',
    'completed': 'text-purple-700',
    'rejected': 'text-red-700',
    'locked': 'text-gray-500',
    'full': 'text-orange-700',
    'started': 'text-gray-600'
  };
  return classes[state] || 'text-gray-600';
};

const getReasonStyle = (state) => {
  const styles = {
    'available': 'background-color: #f0fdf4; color: #166534;',
    'enrolled': 'background-color: #eff6ff; color: #1e40af;',
    'pending': 'background-color: #fefce8; color: #a16207;',
    'completed': 'background-color: #faf5ff; color: #7c2d12;',
    'rejected': 'background-color: #fef2f2; color: #dc2626;',
    'locked': 'background-color: #f9fafb; color: #6b7280;',
    'full': 'background-color: #fff7ed; color: #c2410c;',
    'started': 'background-color: #f9fafb; color: #6b7280;'
  };
  return styles[state] || 'background-color: #f9fafb; color: #6b7280;';
};

const getActionLabel = (state) => {
  const labels = {
    'locked': 'Verrouillé',
    'full': 'Complet',
    'started': 'Commencé',
    'completed': 'Terminé',
    'rejected': 'Refusé'
  };
  return labels[state] || 'Non disponible';
};

const handleImageError = (event) => {
  event.target.src = '/images/default-session.png';
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
