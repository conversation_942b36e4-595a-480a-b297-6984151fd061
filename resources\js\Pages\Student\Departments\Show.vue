<template>
  <Head :title="`${departmentInfo.title} - Formations`" />

  <AuthenticatedLayout>
    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- En-tête du département -->
        <div class="mb-8">
          <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r p-8" :class="departmentInfo.gradient">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="bg-white/20 rounded-lg p-4">
                    <HeartIcon v-if="department === 'Secourisme'" class="w-10 h-10 text-white" />
                    <svg v-else-if="department === 'Langue'" class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                    </svg>
                    <AcademicCapIcon v-else class="w-10 h-10 text-white" />
                  </div>
                  <div class="ml-6">
                    <h1 class="text-3xl font-bold text-white">{{ departmentInfo.title }}</h1>
                    <p class="text-white/90 mt-2">{{ departmentInfo.description }}</p>
                  </div>
                </div>
                <Link 
                  :href="route('student.dashboard')"
                  class="inline-flex items-center px-4 py-2 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors duration-200"
                >
                  <ArrowLeftIcon class="w-4 h-4 mr-2" />
                  Retour au dashboard
                </Link>
              </div>
            </div>
            
            <!-- Statistiques du département -->
            <div class="p-6 bg-gray-50">
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900">{{ stats.total_sessions }}</div>
                  <div class="text-sm text-gray-600">Sessions totales</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600">{{ stats.available_sessions }}</div>
                  <div class="text-sm text-gray-600">Disponibles</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-blue-600">{{ stats.enrolled_sessions }}</div>
                  <div class="text-sm text-gray-600">Inscriptions</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-purple-600">{{ stats.completed_sessions }}</div>
                  <div class="text-sm text-gray-600">Terminées</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sessions par niveau -->
        <div class="bg-white rounded-xl shadow-lg p-6">
          <h2 class="text-2xl font-bold text-gray-900 mb-6">Sessions par niveau</h2>
          
          <div v-if="Object.keys(sessionsByLevel).length === 0" class="text-center py-12">
            <div class="text-gray-400 mb-4">
              <HeartIcon v-if="department === 'Secourisme'" class="w-16 h-16 mx-auto" />
              <svg v-else-if="department === 'Langue'" class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
              </svg>
              <AcademicCapIcon v-else class="w-16 h-16 mx-auto" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Aucune session disponible</h3>
            <p class="text-gray-600">Il n'y a actuellement aucune session disponible pour ce département.</p>
          </div>

          <DepartmentSessions
            v-else
            :sessions="sessionsByLevel"
            :completedLevels="completedLevels"
            :department="department"
          />
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import DepartmentSessions from '@/Components/DepartmentSessions.vue';
import {
  ArrowLeftIcon,
  HeartIcon,
  AcademicCapIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  department: String,
  sessionsByLevel: Object,
  completedLevels: Array,
  stats: Object,
  departmentInfo: Object,
});


</script>
