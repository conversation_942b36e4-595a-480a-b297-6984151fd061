<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Certificate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'enrollment_id',
        'user_id',
        'exam_result_id',
        'training_session_id',
        'certificate_number',
        'pdf_path',
        'signature_image',
        'qr_code',
        'issued_at',
        'issue_date',
        'expiry_date',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'issued_at' => 'datetime:Y-m-d H:i:s',
        'issue_date' => 'datetime:Y-m-d H:i:s',
        'expiry_date' => 'datetime:Y-m-d H:i:s',
        'status' => 'string',
    ];

    /**
     * Certificate status constants
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_ISSUED = 'issued';
    const STATUS_ACTIVE = 'active';
    const STATUS_REVOKED = 'revoked';
    const STATUS_EXPIRED = 'expired';

    /**
     * Get all available status values
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_DRAFT => 'Brouillon',
            self::STATUS_ISSUED => 'Émis',
            self::STATUS_ACTIVE => 'Actif',
            self::STATUS_REVOKED => 'Révoqué',
            self::STATUS_EXPIRED => 'Expiré',
        ];
    }

    /**
     * Check if certificate is visible to students
     */
    public function isVisibleToStudent()
    {
        return in_array($this->status, [self::STATUS_ISSUED, self::STATUS_ACTIVE]);
    }

    /**
     * Check if certificate is downloadable
     */
    public function isDownloadable()
    {
        return in_array($this->status, [self::STATUS_ISSUED, self::STATUS_ACTIVE]);
    }

    /**
     * Scope to filter certificates visible to students
     */
    public function scopeVisibleToStudents($query)
    {
        return $query->whereIn('status', [self::STATUS_ISSUED, self::STATUS_ACTIVE]);
    }

    /**
     * Relation avec l'inscription
     */
    public function enrollment()
    {
        return $this->belongsTo(Enrollment::class);
    }

    /**
     * Relation avec l'utilisateur
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relation avec la session de formation (via l'inscription)
     */
    public function trainingSession()
    {
        return $this->hasOneThrough(TrainingSession::class, Enrollment::class, 'id', 'id', 'enrollment_id', 'training_session_id');
    }

    /**
     * Accesseur pour la date d'expiration au format Y-m-d
     */
    public function getFormattedExpiryDateAttribute()
    {
        return $this->expiry_date ? $this->expiry_date->format('Y-m-d') : null;
    }

    /**
     * Accesseur pour la date d'émission au format Y-m-d
     */
    public function getFormattedIssuedAtAttribute()
    {
        return $this->issued_at ? $this->issued_at->format('Y-m-d') : null;
    }

    /**
     * Accesseur pour la date d'obtention au format Y-m-d
     */
    public function getFormattedIssueDateAttribute()
    {
        return $this->issue_date ? $this->issue_date->format('Y-m-d') : null;
    }

    /**
     * Serialize dates for JSON to avoid timestamp issues
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * Generate a unique certificate number
     */
    public static function generateCertificateNumber($enrollmentId)
    {
        return 'CERT-' . $enrollmentId . '-' . time();
    }

    /**
     * Generate QR code for this certificate
     */
    public function generateQrCode()
    {
        try {
            // Generate verification URL
            $verificationUrl = url('/certificates/verify/' . $this->certificate_number);

            // Use SimpleSoftwareIO QrCode for better compatibility
            $qrCode = \SimpleSoftwareIO\QrCode\Facades\QrCode::format('svg')
                ->size(200)
                ->margin(1)
                ->generate($verificationUrl);

            // Define storage path
            $qrCodePath = 'certificates/qr/' . $this->certificate_number . '.svg';

            // Create directory if it doesn't exist
            $directory = 'certificates/qr';
            if (!\Illuminate\Support\Facades\Storage::disk('public')->exists($directory)) {
                \Illuminate\Support\Facades\Storage::disk('public')->makeDirectory($directory);
            }

            // Save QR code
            \Illuminate\Support\Facades\Storage::disk('public')->put($qrCodePath, $qrCode);

            // Update certificate with QR code path
            $this->update(['qr_code' => $qrCodePath]);

            \Illuminate\Support\Facades\Log::info("QR code generated for certificate {$this->certificate_number}");

            return $qrCodePath;

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Failed to generate QR code for certificate {$this->certificate_number}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Automatically generate QR code when certificate is created
        static::created(function ($certificate) {
            $certificate->generateQrCode();

            // Track certificate creation
            \App\Services\CertificateConsistencyService::trackCertificateChange(
                $certificate,
                'created'
            );
        });

        // Protect certificate number from being changed
        static::updating(function ($certificate) {
            // Ensure certificate number immutability
            if (!\App\Services\CertificateConsistencyService::ensureCertificateNumberImmutability($certificate)) {
                return false; // Prevent the update
            }

            // Track certificate updates
            \App\Services\CertificateConsistencyService::trackCertificateChange(
                $certificate,
                'updating',
                $certificate->getDirty()
            );
        });

        // Track certificate updates
        static::updated(function ($certificate) {
            \App\Services\CertificateConsistencyService::trackCertificateChange(
                $certificate,
                'updated',
                $certificate->getChanges()
            );
        });
    }

    /**
     * Relation avec le résultat d'examen
     */
    public function examResult()
    {
        return $this->belongsTo(ExamResult::class);
    }
}
