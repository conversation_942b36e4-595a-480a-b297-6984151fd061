<?php

namespace App\Http\Controllers;

use App\Models\HomepageContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class MediaImportController extends Controller
{
    public function importPublicMedia()
    {
        $results = [];
        
        // Import VR presentation video
        $vrVideoPath = public_path('presentation vr.mp4');
        if (File::exists($vrVideoPath)) {
            $vrDestination = 'homepage/vr/presentation-vr.mp4';
            
            // Create directory if it doesn't exist
            Storage::disk('public')->makeDirectory('homepage/vr');
            
            // Copy file to storage
            $vrContent = File::get($vrVideoPath);
            Storage::disk('public')->put($vrDestination, $vrContent);
            
            // Create homepage content entry
            HomepageContent::updateOrCreate(
                ['section' => 'vr_training', 'key' => 'presentation_video'],
                [
                    'value' => $vrDestination,
                    'type' => 'video',
                    'metadata' => [
                        'alt' => 'Présentation formation VR',
                        'title' => 'Vidéo de présentation VR',
                        'original_name' => 'presentation vr.mp4'
                    ],
                    'is_active' => true,
                    'sort_order' => 10
                ]
            );
            
            $results['vr_video'] = 'Imported successfully: ' . $vrDestination;
        } else {
            $results['vr_video'] = 'File not found: ' . $vrVideoPath;
        }
        
        // Import first aid instructor image
        $instructorImagePath = public_path('secouriste.jpeg');
        if (File::exists($instructorImagePath)) {
            $imageDestination = 'homepage/hero/secouriste.jpeg';
            
            // Create directory if it doesn't exist
            Storage::disk('public')->makeDirectory('homepage/hero');
            
            // Copy file to storage
            $imageContent = File::get($instructorImagePath);
            Storage::disk('public')->put($imageDestination, $imageContent);
            
            // Get image dimensions
            $imageInfo = getimagesize($instructorImagePath);
            
            // Create homepage content entry
            HomepageContent::updateOrCreate(
                ['section' => 'hero', 'key' => 'instructor_image'],
                [
                    'value' => $imageDestination,
                    'type' => 'image',
                    'metadata' => [
                        'alt' => 'Secouriste professionnel PCMET',
                        'title' => 'Instructeur premiers secours',
                        'width' => $imageInfo[0] ?? null,
                        'height' => $imageInfo[1] ?? null,
                        'original_name' => 'secouriste.jpeg'
                    ],
                    'is_active' => true,
                    'sort_order' => 5
                ]
            );
            
            $results['instructor_image'] = 'Imported successfully: ' . $imageDestination;
        } else {
            $results['instructor_image'] = 'File not found: ' . $instructorImagePath;
        }
        
        return response()->json([
            'message' => 'Media import completed',
            'results' => $results,
            'storage_path' => Storage::disk('public')->path(''),
            'files_created' => [
                'vr_video' => Storage::disk('public')->exists('homepage/vr/presentation-vr.mp4'),
                'instructor_image' => Storage::disk('public')->exists('homepage/hero/secouriste.jpeg')
            ]
        ]);
    }
    
    public function listStorageFiles()
    {
        $homepageFiles = Storage::disk('public')->allFiles('homepage');
        
        return response()->json([
            'homepage_files' => $homepageFiles,
            'homepage_content' => HomepageContent::where('type', 'image')
                ->orWhere('type', 'video')
                ->get()
        ]);
    }
}
