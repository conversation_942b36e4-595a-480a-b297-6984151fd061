<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Exam;
use App\Models\ExamResult;
use App\Models\Enrollment;

class TestExamVisibility extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exams:test-visibility {email : Student email to test}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test exam visibility logic for a specific student';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        // Find the student
        $student = User::where('email', $email)->first();
        if (!$student) {
            $this->error("Student with email {$email} not found.");
            return 1;
        }

        $this->info("🔍 Testing exam visibility for: {$student->name} ({$student->email})");
        $this->line('');

        // Get student's enrollments
        $enrollments = Enrollment::where('user_id', $student->id)
            ->whereIn('status', ['approved', 'completed'])
            ->with('trainingSession')
            ->get();

        if ($enrollments->isEmpty()) {
            $this->warn("No active enrollments found for this student.");
            return 0;
        }

        $this->info("📚 Active Enrollments:");
        foreach ($enrollments as $enrollment) {
            $this->line("- {$enrollment->trainingSession->title} (Status: {$enrollment->status})");
        }
        $this->line('');

        // Get session IDs
        $sessionIds = $enrollments->pluck('training_session_id')->toArray();

        // Get all exams for these sessions
        $allExams = Exam::whereIn('training_session_id', $sessionIds)
            ->orderBy('exam_type')
            ->orderBy('title')
            ->get();

        $this->info("📝 All Exams in Student's Sessions:");
        $headers = ['ID', 'Title', 'Type', 'Published', 'Status', 'Available Until'];
        $rows = [];

        foreach ($allExams as $exam) {
            $rows[] = [
                $exam->id,
                $exam->title,
                $exam->exam_type,
                $exam->is_published ? 'YES' : 'NO',
                $exam->status,
                $exam->available_until ? $exam->available_until->format('Y-m-d H:i') : 'No limit'
            ];
        }
        $this->table($headers, $rows);

        // Get student's exam results
        $examResults = ExamResult::where('user_id', $student->id)
            ->with('exam')
            ->get();

        $this->info("📊 Student's Exam Results:");
        if ($examResults->isEmpty()) {
            $this->line("No exam results found.");
        } else {
            $resultHeaders = ['Exam', 'Type', 'Score', 'Passed', 'Date'];
            $resultRows = [];
            foreach ($examResults as $result) {
                $resultRows[] = [
                    $result->exam->title,
                    $result->exam->exam_type,
                    $result->score . '%',
                    $result->passed ? 'YES' : 'NO',
                    $result->completed_at->format('Y-m-d H:i')
                ];
            }
            $this->table($resultHeaders, $resultRows);
        }

        // Test the visibility logic
        $this->info("👁️ Exam Visibility Test (Published Exams Only):");
        $publishedExams = $allExams->where('is_published', true);
        $examResultsGrouped = $examResults->groupBy('exam_id');

        $visibilityHeaders = ['Exam', 'Type', 'Should Show', 'Reason'];
        $visibilityRows = [];

        foreach ($publishedExams as $exam) {
            $shouldShow = true;
            $reason = 'Normal exam';

            if ($exam->exam_type === 'certification_rattrapage') {
                // Check if student has any results for this retake exam
                $retakeResults = $examResultsGrouped->get($exam->id, collect([]));
                $hasRetakeResults = $retakeResults->isNotEmpty();

                // If student has completed this retake exam, always show it (so they can see results)
                if ($hasRetakeResults) {
                    $shouldShow = true;
                    $reason = 'Student has completed this retake exam (can view results)';
                } else {
                    // For retake exams without results, only show if student has failed the corresponding certification
                    $certificationExam = Exam::where('training_session_id', $exam->training_session_id)
                        ->where('exam_type', 'certification')
                        ->first();

                    if (!$certificationExam) {
                        $shouldShow = false;
                        $reason = 'No certification exam found';
                    } else {
                        $certificationResults = $examResultsGrouped->get($certificationExam->id, collect([]));
                        $hasFailed = $certificationResults->contains('passed', false);
                        $hasPassedCertification = $certificationResults->contains('passed', true);

                        if (!$hasFailed) {
                            $shouldShow = false;
                            $reason = 'Student has not failed certification';
                        } elseif ($hasPassedCertification) {
                            $shouldShow = false;
                            $reason = 'Student has already passed certification';
                        } else {
                            $reason = 'Student failed certification, can retake';
                        }
                    }
                }
            }

            // Check if exam is expired
            if ($shouldShow && $exam->available_until && $exam->available_until < now()) {
                $shouldShow = false;
                $reason = 'Exam has expired';
            }

            $visibilityRows[] = [
                $exam->title,
                $exam->exam_type,
                $shouldShow ? '✅ YES' : '❌ NO',
                $reason
            ];
        }

        $this->table($visibilityHeaders, $visibilityRows);

        // Summary
        $visibleExams = collect($visibilityRows)->where('2', '✅ YES');
        $this->line('');
        $this->info("📋 Summary:");
        $this->line("Total exams in sessions: " . $allExams->count());
        $this->line("Published exams: " . $publishedExams->count());
        $this->line("Visible to student: " . $visibleExams->count());

        return 0;
    }
}
