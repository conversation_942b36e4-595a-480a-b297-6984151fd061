<template>
  <Head title="Modifier un examen" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Modifier un examen
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link :href="route('admin.exams.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à la liste
              </Link>
            </div>

            <!-- Formulaire de modification d'examen -->
            <form @submit.prevent="submit">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Titre -->
                <div>
                  <InputLabel for="title" value="Titre de l'examen" />
                  <TextInput
                    id="title"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.title"
                    required
                    autofocus
                  />
                  <InputError class="mt-2" :message="form.errors.title" />
                </div>

                <!-- Type d'examen -->
                <div>
                  <InputLabel for="exam_type" value="Type d'examen" />
                  <select
                    id="exam_type"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.exam_type"
                    required
                  >
                    <option value="">Sélectionnez un type</option>
                    <option v-for="type in examTypes" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.exam_type" />
                </div>

                <!-- Session de formation -->
                <div>
                  <InputLabel for="training_session_id" value="Session de formation" />
                  <select
                    id="training_session_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.training_session_id"
                    required
                  >
                    <option value="">Sélectionnez une session</option>
                    <option v-for="session in trainingSessions" :key="session.id" :value="session.id">
                      {{ session.title }} ({{ session.training_domain.name }})
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.training_session_id" />
                </div>

                <!-- Formateur créateur -->
                <div>
                  <InputLabel for="creator_id" value="Créé par" />
                  <select
                    id="creator_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.creator_id"
                    required
                  >
                    <option value="">Sélectionnez un formateur</option>
                    <option v-for="trainer in trainers" :key="trainer.id" :value="trainer.id">
                      {{ trainer.name }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.creator_id" />
                </div>

                <!-- Durée -->
                <div>
                  <InputLabel for="duration_minutes" value="Durée (minutes)" />
                  <TextInput
                    id="duration_minutes"
                    type="number"
                    class="mt-1 block w-full"
                    v-model="form.duration_minutes"
                    min="1"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.duration_minutes" />
                </div>

                <!-- Score de réussite -->
                <div>
                  <InputLabel for="passing_score" value="Score de réussite (%)" />
                  <TextInput
                    id="passing_score"
                    type="number"
                    class="mt-1 block w-full"
                    v-model="form.passing_score"
                    min="0"
                    max="100"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.passing_score" />
                </div>

                <!-- Statut de publication -->
                <div>
                  <InputLabel for="is_published" value="Statut" />
                  <div class="mt-2">
                    <label class="inline-flex items-center">
                      <input type="radio" class="form-radio" name="is_published" :value="true" v-model="form.is_published">
                      <span class="ml-2">Publié</span>
                    </label>
                    <label class="inline-flex items-center ml-6">
                      <input type="radio" class="form-radio" name="is_published" :value="false" v-model="form.is_published">
                      <span class="ml-2">Brouillon</span>
                    </label>
                  </div>
                  <InputError class="mt-2" :message="form.errors.is_published" />
                </div>

                <!-- Date de disponibilité -->
                <div>
                  <InputLabel for="available_from" value="Disponible à partir de" />
                  <TextInput
                    id="available_from"
                    type="datetime-local"
                    class="mt-1 block w-full"
                    v-model="form.available_from"
                  />
                  <InputError class="mt-2" :message="form.errors.available_from" />
                </div>

                <!-- Date de fin de disponibilité -->
                <div>
                  <InputLabel for="available_until" value="Disponible jusqu'à" />
                  <TextInput
                    id="available_until"
                    type="datetime-local"
                    class="mt-1 block w-full"
                    v-model="form.available_until"
                  />
                  <InputError class="mt-2" :message="form.errors.available_until" />
                </div>

                <!-- Description -->
                <div class="md:col-span-2">
                  <InputLabel for="description" value="Description" />
                  <textarea
                    id="description"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.description"
                    rows="4"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.description" />
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="flex items-center justify-end mt-6">
                <PrimaryButton class="ml-4" :disabled="form.processing">
                  Mettre à jour l'examen
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  exam: Object,
  trainingSessions: Array,
  trainers: Array,
  examTypes: Array,
});

// Formatage des dates pour les champs datetime-local
const formatDateForInput = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toISOString().slice(0, 16);
};

// Formulaire
const form = useForm({
  title: props.exam.title,
  exam_type: props.exam.exam_type || '',
  description: props.exam.description || '',
  training_session_id: props.exam.training_session_id,
  creator_id: props.exam.creator_id,
  duration_minutes: props.exam.duration_minutes,
  passing_score: props.exam.passing_score,
  is_published: props.exam.is_published,
  available_from: formatDateForInput(props.exam.available_from),
  available_until: formatDateForInput(props.exam.available_until),
});

// Méthodes
const submit = () => {
  form.put(route('admin.exams.update', props.exam.id));
};
</script>
