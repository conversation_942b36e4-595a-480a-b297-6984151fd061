<?php

namespace App\Observers;

use App\Models\ExamResult;
use App\Models\Certificate;
use App\Models\Enrollment;
use App\Services\NotificationService;
use Illuminate\Support\Facades\Log;

class ExamResultObserver
{
    /**
     * Handle the ExamResult "created" event.
     */
    public function created(ExamResult $examResult): void
    {
        $this->checkAndActivateCertificate($examResult);

        // Send notification about exam result
        try {
            $notificationService = app(NotificationService::class);
            $notificationService->notifyExamResult($examResult);
        } catch (\Exception $e) {
            Log::error('Failed to send exam result notification: ' . $e->getMessage());
        }
    }

    /**
     * Handle the ExamResult "updated" event.
     */
    public function updated(ExamResult $examResult): void
    {
        // Check if the passed status changed to true
        if ($examResult->isDirty('passed') && $examResult->passed) {
            $this->checkAndActivateCertificate($examResult);
        }

        // Send notification if exam result was updated significantly
        if ($examResult->isDirty(['passed', 'score'])) {
            try {
                $notificationService = app(NotificationService::class);
                $notificationService->notifyExamResult($examResult);
            } catch (\Exception $e) {
                Log::error('Failed to send exam result update notification: ' . $e->getMessage());
            }
        }
    }

    /**
     * Handle the ExamResult "deleted" event.
     */
    public function deleted(ExamResult $examResult): void
    {
        //
    }

    /**
     * Handle the ExamResult "restored" event.
     */
    public function restored(ExamResult $examResult): void
    {
        //
    }

    /**
     * Handle the ExamResult "force deleted" event.
     */
    public function forceDeleted(ExamResult $examResult): void
    {
        //
    }

    /**
     * Check if certification requirements are met and activate certificate
     */
    private function checkAndActivateCertificate(ExamResult $examResult): void
    {
        try {
            if (!$examResult->passed) {
                return;
            }

            $enrollment = $examResult->enrollment;
            if (!$enrollment) {
                return;
            }

            // Get the exam that was just passed
            $passedExam = $examResult->exam;
            if (!$passedExam) {
                return;
            }

            // Get all exam results for this enrollment
            $allExamResults = $enrollment->examResults;

            // Determine if certificate should be issued based on exam type
            $shouldIssueCertificate = false;

            if ($passedExam->exam_type === 'certification') {
                // For regular certification exams, check if all certification exams are passed
                // Only consider certification and certification_rattrapage exams for certificate eligibility
                $certificationExamResults = $allExamResults->filter(function ($result) {
                    return in_array($result->exam->exam_type, ['certification', 'certification_rattrapage']);
                });

                $shouldIssueCertificate = $certificationExamResults->count() > 0 &&
                    $certificationExamResults->where('passed', true)->count() === $certificationExamResults->count();
            } elseif ($passedExam->exam_type === 'certification_rattrapage') {
                // For retake certification exams, issue certificate immediately when passed
                // This handles the case where student failed initial certification but passed retake
                $shouldIssueCertificate = true;
            } else {
                // For other exam types (evaluation, practice, quiz), do NOT issue certificates
                // These are assessment/training exams and should not result in formal certification
                $shouldIssueCertificate = false;
                Log::info("No certificate issued for exam type '{$passedExam->exam_type}' - certificates only awarded for certification exams");
            }

            if ($shouldIssueCertificate) {
                // Find draft certificate for this enrollment
                $draftCertificate = Certificate::where('enrollment_id', $enrollment->id)
                    ->where('status', 'draft')
                    ->first();

                if ($draftCertificate) {
                    // Issue the certificate (preserve certificate number and QR code)
                    $draftCertificate->update([
                        'status' => 'issued',
                        'issued_at' => now(),
                        'issue_date' => now(),
                        'exam_result_id' => $examResult->id,
                        // Note: certificate_number and qr_code remain unchanged
                    ]);

                    // Update enrollment status to completed
                    $enrollment->update(['status' => 'completed']);

                    Log::info("Certificate issued for enrollment {$enrollment->id} with certificate number {$draftCertificate->certificate_number} (exam type: {$passedExam->exam_type}) - CERTIFICATION EXAM PASSED");
                } else {
                    // If no draft certificate exists, create an issued one directly
                    $certificateNumber = Certificate::generateCertificateNumber($enrollment->id);

                    Certificate::create([
                        'enrollment_id' => $enrollment->id,
                        'user_id' => $enrollment->user_id,
                        'training_session_id' => $enrollment->training_session_id,
                        'exam_result_id' => $examResult->id,
                        'certificate_number' => $certificateNumber,
                        'status' => 'issued',
                        'issued_at' => now(),
                        'issue_date' => now(),
                    ]);
                    // Note: QR code will be automatically generated by the Certificate model's boot method

                    // Update enrollment status to completed
                    $enrollment->update(['status' => 'completed']);

                    Log::info("New issued certificate created for enrollment {$enrollment->id} (exam type: {$passedExam->exam_type}) - CERTIFICATION EXAM PASSED");
                }
            }
        } catch (\Exception $e) {
            Log::error("Failed to issue certificate for exam result {$examResult->id}: " . $e->getMessage());
        }
    }
}
