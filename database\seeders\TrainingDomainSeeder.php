<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\TrainingDomain;

class TrainingDomainSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer des domaines de formation
        TrainingDomain::create([
            'name' => 'Développement Web',
            'description' => 'Formations sur les technologies de développement web modernes incluant HTML, CSS, JavaScript, PHP, et les frameworks populaires.',
            'active' => true,
        ]);

        TrainingDomain::create([
            'name' => 'Cybersecurité',
            'description' => 'Formations sur la sécurité informatique, la protection des données, et les meilleures pratiques pour sécuriser les systèmes.',
            'active' => true,
        ]);

        TrainingDomain::create([
            'name' => 'Intelligence Artificielle',
            'description' => 'Formations sur l\'intelligence artificielle, le machine learning, et le deep learning.',
            'active' => true,
        ]);

        TrainingDomain::create([
            'name' => 'Cloud Computing',
            'description' => 'Formations sur les technologies cloud comme AWS, Azure, et Google Cloud Platform.',
            'active' => true,
        ]);

        TrainingDomain::create([
            'name' => 'Gestion de Projet IT',
            'description' => 'Formations sur les méthodologies de gestion de projet comme Agile, Scrum, et DevOps.',
            'active' => true,
        ]);
    }
}
