<?php

namespace App\Http\Controllers;

use App\Models\HomepageContent;
use Illuminate\Http\Request;

class TestController extends Controller
{
    public function testHomepageContent()
    {
        // Test the data structure
        $homepageContent = [
            'hero' => HomepageContent::getBySection('hero'),
            'vr_training' => HomepageContent::getBySection('vr_training'),
            'about' => HomepageContent::getBySection('about'),
            'contact' => HomepageContent::getBySection('contact'),
        ];

        return response()->json([
            'raw_data' => HomepageContent::all(),
            'structured_data' => $homepageContent,
            'hero_title' => HomepageContent::getValue('hero', 'title', 'Default Title'),
            'vr_title' => HomepageContent::getValue('vr_training', 'title', 'Default VR Title'),
        ]);
    }

    public function createTestData()
    {
        // Create some test data
        HomepageContent::setValue('hero', 'title', 'Test Hero Title from Admin', 'text');
        HomepageContent::setValue('hero', 'description', 'Test Hero Description from Admin', 'text');
        HomepageContent::setValue('vr_training', 'title', 'Test VR Training Title', 'text');
        HomepageContent::setValue('vr_training', 'description', 'Test VR Training Description', 'text');

        return response()->json(['message' => 'Test data created successfully']);
    }
}
