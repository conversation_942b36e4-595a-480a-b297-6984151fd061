<?php

namespace App\Http\Controllers\Trainer;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\ExamQuestion;
use App\Models\TrainingSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ExamQuestionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Vérifier si l'ID de l'examen est fourni
        if (!$request->has('exam_id')) {
            return redirect()->route('trainer.exams.index')
                ->with('error', 'ID d\'examen non spécifié.');
        }

        $examId = $request->exam_id;

        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $sessionIds = TrainingSession::where('trainer_id', $trainer->id)
            ->pluck('id')
            ->toArray();

        // Récupérer l'examen avec ses relations
        $exam = Exam::with(['trainingSession'])
            ->whereIn('training_session_id', $sessionIds)
            ->findOrFail($examId);

        // Récupérer les questions de l'examen
        $questions = ExamQuestion::where('exam_id', $examId)
            ->orderBy('order', 'asc')
            ->get();

        // Retourner la vue avec l'examen et les questions
        return Inertia::render('Trainer/ExamQuestions/Index', [
            'exam' => $exam,
            'questions' => $questions,
            'questionTypes' => [
                ['value' => 'multiple_choice', 'label' => 'Choix multiple'],
                ['value' => 'single_choice', 'label' => 'Choix unique'],
                ['value' => 'text', 'label' => 'Texte libre'],
                ['value' => 'file', 'label' => 'Fichier à soumettre'],
            ],
        ]);
    }

    /**
     * Les formateurs n'ont pas l'autorisation de créer des questions
     */
    public function create(Request $request)
    {
        return redirect()->route('trainer.exams.index')
            ->with('error', 'Vous n\'avez pas l\'autorisation de créer des questions.');
    }

    /**
     * Les formateurs n'ont pas l'autorisation de créer des questions
     */
    public function store(Request $request)
    {
        return redirect()->route('trainer.exams.index')
            ->with('error', 'Vous n\'avez pas l\'autorisation de créer des questions.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Récupérer la question
        $question = ExamQuestion::findOrFail($id);

        // Récupérer l'examen
        $exam = $question->exam;

        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $sessionIds = TrainingSession::where('trainer_id', $trainer->id)
            ->pluck('id')
            ->toArray();

        // Vérifier que l'examen appartient au formateur
        if (!in_array($exam->training_session_id, $sessionIds)) {
            abort(403, 'Non autorisé.');
        }

        // Retourner la vue avec la question
        return Inertia::render('Trainer/ExamQuestions/Show', [
            'question' => $question,
            'exam' => $exam,
        ]);
    }

    /**
     * Les formateurs n'ont pas l'autorisation de modifier des questions
     */
    public function edit(string $id)
    {
        return redirect()->route('trainer.exams.index')
            ->with('error', 'Vous n\'avez pas l\'autorisation de modifier des questions.');
    }

    /**
     * Les formateurs n'ont pas l'autorisation de modifier des questions
     */
    public function update(Request $request, string $id)
    {
        return redirect()->route('trainer.exams.index')
            ->with('error', 'Vous n\'avez pas l\'autorisation de modifier des questions.');
    }

    /**
     * Les formateurs n'ont pas l'autorisation de supprimer des questions
     */
    public function destroy(string $id)
    {
        return redirect()->route('trainer.exams.index')
            ->with('error', 'Vous n\'avez pas l\'autorisation de supprimer des questions.');
    }

    /**
     * Les formateurs n'ont pas l'autorisation de réorganiser des questions
     */
    public function reorder(Request $request)
    {
        return redirect()->route('trainer.exams.index')
            ->with('error', 'Vous n\'avez pas l\'autorisation de réorganiser des questions.');
    }
}
