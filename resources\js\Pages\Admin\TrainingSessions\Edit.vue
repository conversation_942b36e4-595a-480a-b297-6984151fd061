<template>
  <Head title="Modifier une session de formation" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Modifier une session de formation
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link :href="route('admin.training-sessions.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à la liste
              </Link>
            </div>

            <!-- Formulaire de modification de session -->
            <form @submit.prevent="submit" enctype="multipart/form-data">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Titre -->
                <div>
                  <InputLabel for="title" value="Titre" />
                  <TextInput
                    id="title"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.title"
                    required
                    autofocus
                  />
                  <InputError class="mt-2" :message="form.errors.title" />
                </div>

                <!-- Domaine de formation -->
                <div>
                  <InputLabel for="training_domain_id" value="Domaine de formation" />
                  <select
                    id="training_domain_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.training_domain_id"
                    required
                  >
                    <option value="">Sélectionnez un domaine</option>
                    <option v-for="domain in domains" :key="domain.id" :value="domain.id">
                      {{ domain.name }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.training_domain_id" />
                </div>

                <!-- Formateur -->
                <div>
                  <InputLabel for="trainer_id" value="Formateur" />
                  <select
                    id="trainer_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.trainer_id"
                    required
                  >
                    <option value="">Sélectionnez un formateur</option>
                    <option v-for="trainer in trainers" :key="trainer.id" :value="trainer.id">
                      {{ trainer.name }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.trainer_id" />
                </div>

                <!-- Date de début -->
                <div>
                  <InputLabel for="start_date" value="Date de début" />
                  <TextInput
                    id="start_date"
                    type="date"
                    class="mt-1 block w-full"
                    v-model="form.start_date"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.start_date" />
                </div>

                <!-- Date de fin -->
                <div>
                  <InputLabel for="end_date" value="Date de fin" />
                  <TextInput
                    id="end_date"
                    type="date"
                    class="mt-1 block w-full"
                    v-model="form.end_date"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.end_date" />
                </div>

                <!-- Lieu -->
                <div>
                  <InputLabel for="location" value="Lieu" />
                  <TextInput
                    id="location"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.location"
                  />
                  <InputError class="mt-2" :message="form.errors.location" />
                </div>

                <!-- Nombre maximum de participants -->
                <div>
                  <InputLabel for="max_participants" value="Nombre maximum de participants" />
                  <TextInput
                    id="max_participants"
                    type="number"
                    class="mt-1 block w-full"
                    v-model="form.max_participants"
                    min="1"
                  />
                  <InputError class="mt-2" :message="form.errors.max_participants" />
                </div>

                <!-- Prix -->
                <div>
                  <InputLabel for="price" value="Prix (DT)" />
                  <TextInput
                    id="price"
                    type="number"
                    step="0.01"
                    class="mt-1 block w-full"
                    v-model="form.price"
                    min="0"
                  />
                  <InputError class="mt-2" :message="form.errors.price" />
                </div>

                <!-- Image actuelle et nouvelle image -->
                <div>
                  <InputLabel for="image" value="Image" />
                  <div v-if="session.image" class="mt-2 mb-2">
                    <p class="text-sm text-gray-600 mb-1">Image actuelle :</p>
                    <img :src="'/storage/' + session.image" alt="Image de la session" class="h-32 w-auto object-cover rounded">
                  </div>
                  <input
                    id="image"
                    type="file"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
                    @input="form.image = $event.target.files[0]"
                  />
                  <p class="text-sm text-gray-500 mt-1">Laissez vide pour conserver l'image actuelle</p>
                  <InputError class="mt-2" :message="form.errors.image" />
                </div>

                <!-- Statut -->
                <div>
                  <InputLabel for="active" value="Statut" />
                  <div class="mt-2">
                    <label class="inline-flex items-center">
                      <input type="radio" class="form-radio" name="active" :value="true" v-model="form.active">
                      <span class="ml-2">Active</span>
                    </label>
                    <label class="inline-flex items-center ml-6">
                      <input type="radio" class="form-radio" name="active" :value="false" v-model="form.active">
                      <span class="ml-2">Inactive</span>
                    </label>
                  </div>
                  <InputError class="mt-2" :message="form.errors.active" />
                </div>

                <!-- Nom du certificat -->
                <div>
                  <InputLabel for="certificate_name" value="Nom du certificat" />
                  <TextInput
                    id="certificate_name"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.certificate_name"
                    placeholder="Nom qui apparaîtra sur le certificat"
                  />
                  <InputError class="mt-2" :message="form.errors.certificate_name" />
                </div>

                <!-- Objectifs de formation -->
                <div>
                  <InputLabel for="training_objectives" value="Objectifs de formation" />
                  <textarea
                    id="training_objectives"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.training_objectives"
                    rows="3"
                    placeholder="Objectifs qui apparaîtront sur le certificat"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.training_objectives" />
                </div>

                <!-- Description -->
                <div class="md:col-span-2">
                  <InputLabel for="description" value="Description" />
                  <textarea
                    id="description"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.description"
                    rows="4"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.description" />
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="flex items-center justify-end mt-6">
                <PrimaryButton class="ml-4" :disabled="form.processing">
                  Mettre à jour la session
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  session: Object,
  domains: Array,
  trainers: Array,
});

// Formatage des dates pour les champs date
const formatDateForInput = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toISOString().split('T')[0];
};

// Formulaire
const form = useForm({
  title: props.session.title,
  description: props.session.description || '',
  certificate_name: props.session.certificate_name || '',
  training_objectives: props.session.training_objectives || '',
  training_domain_id: props.session.training_domain_id,
  trainer_id: props.session.trainer_id,
  start_date: formatDateForInput(props.session.start_date),
  end_date: formatDateForInput(props.session.end_date),
  location: props.session.location || '',
  max_participants: props.session.max_participants || '',
  price: props.session.price || '',
  image: null,
  active: props.session.active,
  _method: 'PUT',
});

// Méthodes
const submit = () => {
  form.post(route('admin.training-sessions.update', props.session.id));
};
</script>
