<template>
  <Head title="Gestion des examens" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Gestion des examens
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- En-tête avec bouton d'ajout -->
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">Liste des examens</h3>
              <Link :href="route('admin.exams.create')" class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Créer un examen
              </Link>
            </div>

            <!-- Message de succès -->
            <div v-if="$page.props.flash.success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
              <span class="block sm:inline">{{ $page.props.flash.success }}</span>
            </div>

            <!-- Message d'erreur -->
            <div v-if="$page.props.flash.error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
              <span class="block sm:inline">{{ $page.props.flash.error }}</span>
            </div>

            <!-- Formulaire de recherche et filtrage -->
            <div class="bg-gray-100 p-4 rounded-lg mb-6 border border-gray-200">
              <h3 class="text-lg font-medium mb-4">Recherche et filtrage</h3>
              <form @submit.prevent="applyFilters">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                  <!-- Barre de recherche -->
                  <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
                    <input
                      type="text"
                      id="search"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="Rechercher par titre..."
                      v-model="searchQuery"
                    />
                  </div>

                  <!-- Filtre par session de formation -->
                  <div>
                    <label for="training_session" class="block text-sm font-medium text-gray-700 mb-1">Session de formation</label>
                    <select
                      id="training_session"
                      v-model="sessionFilter"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Toutes les sessions</option>
                      <option v-for="session in trainingSessions" :key="session.id" :value="session.id">
                        {{ session.title }}
                      </option>
                    </select>
                  </div>

                  <!-- Filtre par domaine de formation -->
                  <div>
                    <label for="domain" class="block text-sm font-medium text-gray-700 mb-1">Domaine de formation</label>
                    <select
                      id="domain"
                      v-model="domainFilter"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Tous les domaines</option>
                      <option v-for="domain in domains" :key="domain.id" :value="domain.id">
                        {{ domain.name }}
                      </option>
                    </select>
                  </div>

                  <!-- Filtre par créateur (formateur) -->
                  <div>
                    <label for="creator" class="block text-sm font-medium text-gray-700 mb-1">Créé par</label>
                    <select
                      id="creator"
                      v-model="creatorFilter"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Tous les formateurs</option>
                      <option v-for="trainer in trainers" :key="trainer.id" :value="trainer.id">
                        {{ trainer.name }}
                      </option>
                    </select>
                  </div>

                  <!-- Filtre par statut -->
                  <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                    <select
                      id="status"
                      v-model="statusFilter"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Tous les statuts</option>
                      <option value="published">Publié</option>
                      <option value="draft">Brouillon</option>
                      <option value="closed">Fermé</option>
                    </select>
                  </div>

                  <!-- Filtre par disponibilité -->
                  <div>
                    <label for="availability" class="block text-sm font-medium text-gray-700 mb-1">Disponibilité</label>
                    <select
                      id="availability"
                      v-model="availabilityFilter"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Toutes les disponibilités</option>
                      <option value="current">Disponible actuellement</option>
                      <option value="upcoming">À venir</option>
                      <option value="expired">Terminé</option>
                    </select>
                  </div>
                </div>

                <!-- Boutons -->
                <div class="flex justify-end space-x-2">
                  <button
                    type="submit"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Filtrer
                  </button>
                  <button
                    type="button"
                    class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                    @click="resetFilters"
                  >
                    Réinitialiser
                  </button>
                </div>
              </form>
            </div>

            <!-- Tableau des examens -->
            <div class="bg-white rounded-xl shadow-lg overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50 sticky top-0 z-10">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Titre</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Formation</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Créé par</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="exam in exams.data" :key="exam.id" class="hover:bg-blue-50 transition">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">{{ exam.title }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ getExamTypeLabel(exam.exam_type) }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ exam.training_session.title }}</div>
                      <div class="text-xs text-gray-500">{{ exam.training_session.training_domain.name }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500">{{ exam.creator.name }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="{
                        'px-2 py-1 text-xs rounded-full': true,
                        'bg-green-100 text-green-800': exam.status === 'published',
                        'bg-yellow-100 text-yellow-800': exam.status === 'draft',
                        'bg-red-100 text-red-800': exam.status === 'closed'
                      }">
                        {{ exam.status === 'published' ? 'Publié' : exam.status === 'draft' ? 'Brouillon' : 'Fermé' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div class="flex space-x-2">
                        <Link :href="route('admin.exams.show', exam.id)" class="text-indigo-600 hover:text-indigo-900">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </Link>
                        <Link :href="route('admin.exam-questions.index', { exam_id: exam.id })" class="flex items-center text-green-600 hover:text-green-900 bg-green-100 px-2 py-1 rounded">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                          <span class="text-xs">Questions</span>
                        </Link>
                        <Link :href="route('admin.exams.edit', exam.id)" class="text-blue-600 hover:text-blue-900">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 0L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </Link>
                        <button @click="togglePublishExam(exam)" :class="{
                          'text-yellow-600 hover:text-yellow-900': exam.is_published,
                          'text-green-600 hover:text-green-900': !exam.is_published,
                        }">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" v-if="!exam.is_published" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" v-if="exam.is_published" />
                          </svg>
                        </button>
                        <button @click="confirmDelete(exam)" class="text-red-600 hover:text-red-900">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr v-if="exams.data.length === 0">
                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                      Aucun examen trouvé.
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4">
              <Pagination :links="exams.links" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmation de suppression -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg overflow-hidden shadow-xl max-w-md w-full">
        <div class="p-6">
          <h3 class="text-lg font-semibold mb-4">Confirmer la suppression</h3>
          <p class="mb-4">Êtes-vous sûr de vouloir supprimer l'examen <span class="font-semibold">{{ examToDelete?.title }}</span> ?</p>
          <div class="flex justify-end space-x-3">
            <button @click="showDeleteModal = false" class="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400">
              Annuler
            </button>
            <button @click="deleteExam" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
              Supprimer
            </button>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, useForm, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Pagination from '@/Components/Pagination.vue';

// Props
const props = defineProps({
  exams: Object,
  trainingSessions: Array,
  domains: Array,
  trainers: Array,
  filters: Object,
});

// État pour la modal de suppression
const showDeleteModal = ref(false);
const examToDelete = ref(null);

// État pour les filtres
const searchQuery = ref(props.filters?.search || '');
const sessionFilter = ref(props.filters?.training_session_id || '');
const domainFilter = ref(props.filters?.domain_id || '');
const creatorFilter = ref(props.filters?.creator_id || '');
const statusFilter = ref(props.filters?.status || '');
const availabilityFilter = ref(props.filters?.availability || '');

// Formulaire pour la suppression
const deleteForm = useForm({});

// Formulaire pour la publication
const publishForm = useForm({});

// Méthodes pour les filtres
const applyFilters = () => {
  const params = {};

  // Ajouter la recherche si elle existe
  if (searchQuery.value) {
    params.search = searchQuery.value;
  }

  // Ajouter le filtre de session si il existe
  if (sessionFilter.value) {
    params.training_session_id = sessionFilter.value;
  }

  // Ajouter le filtre de domaine s'il existe
  if (domainFilter.value) {
    params.domain_id = domainFilter.value;
  }

  // Ajouter le filtre de créateur s'il existe
  if (creatorFilter.value) {
    params.creator_id = creatorFilter.value;
  }

  // Ajouter le filtre de statut s'il existe
  if (statusFilter.value !== '') {
    params.status = statusFilter.value;
  }

  // Ajouter le filtre de disponibilité s'il existe
  if (availabilityFilter.value) {
    params.availability = availabilityFilter.value;
  }

  // Naviguer vers l'URL avec les paramètres
  router.get(route('admin.exams.index'), params, {
    preserveState: true,
    replace: true,
    preserveScroll: true
  });
};

const resetFilters = () => {
  searchQuery.value = '';
  sessionFilter.value = '';
  domainFilter.value = '';
  creatorFilter.value = '';
  statusFilter.value = '';
  availabilityFilter.value = '';

  // Naviguer vers l'URL sans paramètres
  router.get(route('admin.exams.index'), {}, {
    preserveState: true,
    replace: true,
    preserveScroll: true
  });
};

// Méthode pour obtenir le libellé du type d'examen
const getExamTypeLabel = (type) => {
  switch (type) {
    case 'certification':
      return 'Certification';
    case 'certification_rattrapage':
      return 'Certification de rattrapage';
    case 'evaluation':
      return 'Évaluation';
    case 'practice':
      return 'Entraînement';
    case 'quiz':
      return 'Quiz';
    default:
      return type || 'Non défini';
  }
};

// Méthodes pour la suppression
const confirmDelete = (exam) => {
  examToDelete.value = exam;
  showDeleteModal.value = true;
};

const deleteExam = () => {
  deleteForm.delete(route('admin.exams.destroy', examToDelete.value.id), {
    onSuccess: () => {
      showDeleteModal.value = false;
      examToDelete.value = null;
    },
  });
};

// Méthode pour publier/dépublier un examen
const togglePublishExam = (exam) => {
  publishForm.post(route('admin.exams.toggle-publish', exam.id), {
    onSuccess: () => {
      // Le succès est géré par la redirection dans le contrôleur
    },
    onError: (errors) => {
      console.error('Erreur lors de la publication/dépublication de l\'examen:', errors);
    }
  });
};
</script>
