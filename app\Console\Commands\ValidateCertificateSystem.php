<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Certificate;
use App\Models\Enrollment;

class ValidateCertificateSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'certificates:validate-system';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Validate certificate system consistency and fix any issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Validating certificate system consistency...');

        $this->checkDuplicateCertificates();
        $this->checkCertificateNumberFormat();
        $this->checkQrCodeConsistency();
        $this->checkOrphanedCertificates();

        $this->info('Certificate system validation completed.');
        
        return Command::SUCCESS;
    }

    /**
     * Check for duplicate certificates for the same enrollment
     */
    private function checkDuplicateCertificates()
    {
        $this->info('Checking for duplicate certificates...');

        $duplicates = Certificate::selectRaw('enrollment_id, COUNT(*) as count')
            ->groupBy('enrollment_id')
            ->having('count', '>', 1)
            ->get();

        if ($duplicates->count() > 0) {
            $this->warn("Found {$duplicates->count()} enrollments with duplicate certificates:");
            
            foreach ($duplicates as $duplicate) {
                $certificates = Certificate::where('enrollment_id', $duplicate->enrollment_id)
                    ->orderBy('created_at')
                    ->get();
                
                $this->line("Enrollment {$duplicate->enrollment_id} has {$duplicate->count} certificates:");
                
                foreach ($certificates as $cert) {
                    $this->line("  - ID: {$cert->id}, Number: {$cert->certificate_number}, Status: {$cert->status}, Created: {$cert->created_at}");
                }

                // Keep the first certificate (oldest) and mark others for deletion
                $toKeep = $certificates->first();
                $toDelete = $certificates->skip(1);

                $this->line("  → Keeping certificate ID {$toKeep->id} (oldest)");
                
                foreach ($toDelete as $cert) {
                    if ($this->confirm("Delete duplicate certificate ID {$cert->id}?")) {
                        $cert->delete();
                        $this->line("  ✓ Deleted certificate ID {$cert->id}");
                    }
                }
            }
        } else {
            $this->line('✓ No duplicate certificates found.');
        }
    }

    /**
     * Check certificate number format consistency
     */
    private function checkCertificateNumberFormat()
    {
        $this->info('Checking certificate number format consistency...');

        $certificates = Certificate::whereNotNull('certificate_number')
            ->whereNotNull('enrollment_id')
            ->get();

        $inconsistent = 0;

        foreach ($certificates as $certificate) {
            $expectedPattern = '/^CERT-' . $certificate->enrollment_id . '-\d+$/';
            
            if (!preg_match($expectedPattern, $certificate->certificate_number)) {
                $this->warn("Certificate ID {$certificate->id} has inconsistent number: {$certificate->certificate_number}");
                
                if ($this->confirm("Fix certificate number for ID {$certificate->id}?")) {
                    $oldNumber = $certificate->certificate_number;
                    $newNumber = Certificate::generateCertificateNumber($certificate->enrollment_id);
                    
                    $certificate->update(['certificate_number' => $newNumber]);
                    $certificate->generateQrCode();
                    
                    $this->line("  ✓ Fixed: {$oldNumber} → {$newNumber}");
                }
                
                $inconsistent++;
            }
        }

        if ($inconsistent === 0) {
            $this->line('✓ All certificate numbers follow the correct format.');
        } else {
            $this->line("Found {$inconsistent} certificates with inconsistent numbers.");
        }
    }

    /**
     * Check QR code consistency
     */
    private function checkQrCodeConsistency()
    {
        $this->info('Checking QR code consistency...');

        $certificatesWithoutQr = Certificate::whereNull('qr_code')
            ->orWhere('qr_code', '')
            ->get();

        if ($certificatesWithoutQr->count() > 0) {
            $this->warn("Found {$certificatesWithoutQr->count()} certificates without QR codes.");
            
            if ($this->confirm('Generate missing QR codes?')) {
                foreach ($certificatesWithoutQr as $certificate) {
                    $certificate->generateQrCode();
                    $this->line("  ✓ Generated QR code for certificate {$certificate->certificate_number}");
                }
            }
        } else {
            $this->line('✓ All certificates have QR codes.');
        }
    }

    /**
     * Check for orphaned certificates
     */
    private function checkOrphanedCertificates()
    {
        $this->info('Checking for orphaned certificates...');

        $orphaned = Certificate::whereNotExists(function ($query) {
            $query->select('id')
                ->from('enrollments')
                ->whereColumn('enrollments.id', 'certificates.enrollment_id');
        })->get();

        if ($orphaned->count() > 0) {
            $this->warn("Found {$orphaned->count()} orphaned certificates (enrollment doesn't exist):");
            
            foreach ($orphaned as $cert) {
                $this->line("  - Certificate ID {$cert->id}, Number: {$cert->certificate_number}, Enrollment ID: {$cert->enrollment_id}");
                
                if ($this->confirm("Delete orphaned certificate ID {$cert->id}?")) {
                    $cert->delete();
                    $this->line("  ✓ Deleted orphaned certificate ID {$cert->id}");
                }
            }
        } else {
            $this->line('✓ No orphaned certificates found.');
        }
    }
}
