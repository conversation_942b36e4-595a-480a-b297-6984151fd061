<template>
  <Head title="Gestion des conseils en premiers secours" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Gestion des conseils en premiers secours
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- En-tête avec bouton d'ajout -->
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">Liste des conseils en premiers secours</h3>
              <Link :href="route('admin.first-aid-tips.create')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                Ajouter un conseil
              </Link>
            </div>

            <!-- Statistiques -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div class="bg-blue-50 p-4 rounded-lg">
                <div class="text-2xl font-bold text-blue-600">{{ stats.total }}</div>
                <div class="text-sm text-gray-600">Total</div>
              </div>
              <div class="bg-green-50 p-4 rounded-lg">
                <div class="text-2xl font-bold text-green-600">{{ stats.active }}</div>
                <div class="text-sm text-gray-600">Actifs</div>
              </div>
              <div class="bg-red-50 p-4 rounded-lg">
                <div class="text-2xl font-bold text-red-600">{{ stats.inactive }}</div>
                <div class="text-sm text-gray-600">Inactifs</div>
              </div>
              <div class="bg-purple-50 p-4 rounded-lg">
                <div class="text-2xl font-bold text-purple-600">{{ stats.with_materials }}</div>
                <div class="text-sm text-gray-600">Avec matériels</div>
              </div>
            </div>

            <!-- Filtres -->
            <div class="flex flex-wrap gap-4 mb-6">
              <div class="flex-1 min-w-64">
                <input
                  v-model="form.search"
                  type="text"
                  placeholder="Rechercher par titre..."
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  @input="search"
                />
              </div>
              <div>
                <select
                  v-model="form.active"
                  class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  @change="search"
                >
                  <option value="">Tous les statuts</option>
                  <option value="true">Actifs</option>
                  <option value="false">Inactifs</option>
                </select>
              </div>
            </div>

            <!-- Tableau -->
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Titre
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Matériels
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="tip in firstAidTips.data" :key="tip.id">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">{{ tip.title }}</div>
                    </td>
                    <td class="px-6 py-4">
                      <div class="text-sm text-gray-500 max-w-xs truncate">
                        {{ tip.description || 'Aucune description' }}
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ tip.materials_count || 0 }} matériel(s)</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        tip.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      ]">
                        {{ tip.active ? 'Actif' : 'Inactif' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <Link
                        :href="route('admin.first-aid-tips.show', tip.id)"
                        class="text-blue-600 hover:text-blue-900"
                      >
                        Voir
                      </Link>
                      <Link
                        :href="route('admin.first-aid-tips.edit', tip.id)"
                        class="text-indigo-600 hover:text-indigo-900"
                      >
                        Modifier
                      </Link>
                      <button
                        @click="deleteTip(tip.id)"
                        class="text-red-600 hover:text-red-900"
                      >
                        Supprimer
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            <div class="mt-6" v-if="firstAidTips.links">
              <nav class="flex justify-center">
                <div class="flex space-x-1">
                  <Link
                    v-for="link in firstAidTips.links"
                    :key="link.label"
                    :href="link.url"
                    :class="[
                      'px-3 py-2 text-sm rounded-md',
                      link.active
                        ? 'bg-blue-600 text-white'
                        : link.url
                        ? 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    ]"
                    v-html="link.label"
                  />
                </div>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3';
import { ref, reactive } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  firstAidTips: Object,
  stats: Object,
  filters: Object,
});

// Formulaire de recherche
const form = reactive({
  search: props.filters.search || '',
  active: props.filters.active || '',
});

// Méthodes
const search = () => {
  router.get(route('admin.first-aid-tips.index'), form, {
    preserveState: true,
    replace: true,
  });
};

const deleteTip = (id) => {
  if (confirm('Êtes-vous sûr de vouloir supprimer ce conseil ?')) {
    router.delete(route('admin.first-aid-tips.destroy', id));
  }
};
</script>
