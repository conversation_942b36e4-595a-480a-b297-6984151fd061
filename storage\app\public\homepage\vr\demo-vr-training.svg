<svg width="1920" height="1080" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="vr-bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
            <stop offset="50%" style="stop-color:#2A69E1;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#1E4FBF;stop-opacity:1" />
        </linearGradient>
        <linearGradient id="vr-accent" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
            <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
        </linearGradient>
        <radialGradient id="vr-glow" cx="50%" cy="50%" r="50%">
            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
            <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
        </radialGradient>
    </defs>
    
    <!-- Background -->
    <rect width="100%" height="100%" fill="url(#vr-bg-gradient)"/>
    
    <!-- Glow effects -->
    <circle cx="480" cy="270" r="200" fill="url(#vr-glow)"/>
    <circle cx="1440" cy="810" r="150" fill="url(#vr-glow)"/>
    
    <!-- VR Headset illustration -->
    <g transform="translate(860, 440)">
        <!-- Headset body -->
        <ellipse cx="100" cy="80" rx="120" ry="60" fill="white" opacity="0.9"/>
        <ellipse cx="100" cy="80" rx="100" ry="45" fill="#2A69E1"/>
        
        <!-- Lenses -->
        <circle cx="70" cy="80" r="25" fill="white" opacity="0.8"/>
        <circle cx="130" cy="80" r="25" fill="white" opacity="0.8"/>
        <circle cx="70" cy="80" r="20" fill="#1E4FBF"/>
        <circle cx="130" cy="80" r="20" fill="#1E4FBF"/>
        
        <!-- Strap -->
        <rect x="0" y="70" width="40" height="20" fill="white" opacity="0.7" rx="10"/>
        <rect x="160" y="70" width="40" height="20" fill="white" opacity="0.7" rx="10"/>
        
        <!-- Tech details -->
        <rect x="85" y="50" width="30" height="8" fill="white" opacity="0.6" rx="4"/>
        <circle cx="100" cy="110" r="3" fill="white" opacity="0.8"/>
    </g>
    
    <!-- Medical cross with VR elements -->
    <g transform="translate(400, 300)">
        <circle cx="60" cy="60" r="70" fill="white" opacity="0.1"/>
        <circle cx="60" cy="60" r="50" fill="white" opacity="0.9"/>
        <rect x="50" y="35" width="20" height="50" fill="#8B5CF6" rx="2"/>
        <rect x="35" y="50" width="50" height="20" fill="#8B5CF6" rx="2"/>
        
        <!-- VR overlay effect -->
        <circle cx="60" cy="60" r="45" fill="none" stroke="white" stroke-width="2" opacity="0.6" stroke-dasharray="5,5"/>
    </g>
    
    <!-- Play button overlay -->
    <g transform="translate(910, 490)">
        <circle cx="50" cy="50" r="40" fill="white" opacity="0.9"/>
        <polygon points="40,35 40,65 70,50" fill="#2A69E1"/>
    </g>
    
    <!-- Title -->
    <text x="960" y="200" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="64" font-weight="bold">
        Formation VR Immersive
    </text>
    
    <!-- Subtitle -->
    <text x="960" y="260" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="32" opacity="0.9">
        Réalité Virtuelle pour Premiers Secours
    </text>
    
    <!-- Features -->
    <g transform="translate(200, 700)">
        <rect x="0" y="0" width="300" height="80" fill="white" opacity="0.1" rx="10"/>
        <text x="150" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">
            360° Immersion Complète
        </text>
        <text x="150" y="55" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" opacity="0.8">
            Environnement réaliste et interactif
        </text>
    </g>
    
    <g transform="translate(550, 700)">
        <rect x="0" y="0" width="300" height="80" fill="white" opacity="0.1" rx="10"/>
        <text x="150" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">
            Scénarios Réalistes
        </text>
        <text x="150" y="55" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" opacity="0.8">
            Plus de 100 situations d'urgence
        </text>
    </g>
    
    <g transform="translate(900, 700)">
        <rect x="0" y="0" width="300" height="80" fill="white" opacity="0.1" rx="10"/>
        <text x="150" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">
            Apprentissage Sécurisé
        </text>
        <text x="150" y="55" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" opacity="0.8">
            Erreurs sans conséquences réelles
        </text>
    </g>
    
    <g transform="translate(1250, 700)">
        <rect x="0" y="0" width="300" height="80" fill="white" opacity="0.1" rx="10"/>
        <text x="150" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">
            Suivi des Progrès
        </text>
        <text x="150" y="55" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" opacity="0.8">
            Analytics et évaluation en temps réel
        </text>
    </g>
    
    <!-- Tech specs -->
    <g transform="translate(100, 100)">
        <rect x="0" y="0" width="200" height="120" fill="white" opacity="0.1" rx="10"/>
        <text x="100" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
            Spécifications VR
        </text>
        <text x="20" y="50" fill="white" font-family="Arial, sans-serif" font-size="12" opacity="0.9">
            • Résolution 4K par œil
        </text>
        <text x="20" y="70" fill="white" font-family="Arial, sans-serif" font-size="12" opacity="0.9">
            • Tracking 6DOF précis
        </text>
        <text x="20" y="90" fill="white" font-family="Arial, sans-serif" font-size="12" opacity="0.9">
            • Audio spatial 3D
        </text>
        <text x="20" y="110" fill="white" font-family="Arial, sans-serif" font-size="12" opacity="0.9">
            • Retour haptique
        </text>
    </g>
    
    <!-- Bottom branding -->
    <text x="960" y="1050" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" opacity="0.8">
        PCMET - Innovation Pédagogique en Réalité Virtuelle
    </text>
</svg>
