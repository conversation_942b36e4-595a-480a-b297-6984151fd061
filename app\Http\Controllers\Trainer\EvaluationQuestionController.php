<?php

namespace App\Http\Controllers\Trainer;

use App\Http\Controllers\Controller;
use App\Models\Evaluation;
use App\Models\EvaluationQuestion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class EvaluationQuestionController extends Controller
{
    /**
     * Affiche la liste des questions d'une évaluation
     */
    public function index(string $evaluationId)
    {
        $evaluation = Evaluation::with(['trainingSession', 'course'])->findOrFail($evaluationId);
        
        // Vérifier que l'évaluation appartient bien au formateur
        if ($evaluation->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.evaluations.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à cette évaluation.');
        }
        
        // Récupérer les questions
        $questions = EvaluationQuestion::where('evaluation_id', $evaluationId)
            ->orderBy('order')
            ->get();
            
        return Inertia::render('Trainer/Evaluations/Questions/Index', [
            'evaluation' => $evaluation,
            'questions' => $questions,
        ]);
    }

    /**
     * Affiche le formulaire de création d'une question
     */
    public function create(string $evaluationId)
    {
        $evaluation = Evaluation::with(['trainingSession', 'course'])->findOrFail($evaluationId);
        
        // Vérifier que l'évaluation appartient bien au formateur
        if ($evaluation->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.evaluations.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à cette évaluation.');
        }
        
        // Déterminer l'ordre de la prochaine question
        $nextOrder = EvaluationQuestion::where('evaluation_id', $evaluationId)->max('order') + 1;
        
        return Inertia::render('Trainer/Evaluations/Questions/Create', [
            'evaluation' => $evaluation,
            'nextOrder' => $nextOrder,
            'questionTypes' => [
                ['value' => 'text', 'label' => 'Texte libre'],
                ['value' => 'multiple_choice', 'label' => 'Choix multiple'],
                ['value' => 'rating', 'label' => 'Notation (0-10)'],
                ['value' => 'yes_no', 'label' => 'Oui/Non'],
            ],
        ]);
    }

    /**
     * Enregistre une nouvelle question
     */
    public function store(Request $request, string $evaluationId)
    {
        $evaluation = Evaluation::findOrFail($evaluationId);
        
        // Vérifier que l'évaluation appartient bien au formateur
        if ($evaluation->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.evaluations.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à cette évaluation.');
        }
        
        // Valider les données
        $validated = $request->validate([
            'question_text' => 'required|string',
            'question_type' => 'required|in:text,multiple_choice,rating,yes_no',
            'options' => 'nullable|array',
            'options.*' => 'nullable|string',
            'required' => 'boolean',
            'order' => 'required|integer|min:1',
        ]);
        
        // Ajouter l'ID de l'évaluation
        $validated['evaluation_id'] = $evaluationId;
        
        // Créer la question
        EvaluationQuestion::create($validated);
        
        return redirect()->route('trainer.evaluations.questions.index', $evaluationId)
            ->with('success', 'Question ajoutée avec succès.');
    }

    /**
     * Affiche le formulaire de modification d'une question
     */
    public function edit(string $evaluationId, string $questionId)
    {
        $evaluation = Evaluation::with(['trainingSession', 'course'])->findOrFail($evaluationId);
        
        // Vérifier que l'évaluation appartient bien au formateur
        if ($evaluation->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.evaluations.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à cette évaluation.');
        }
        
        // Récupérer la question
        $question = EvaluationQuestion::findOrFail($questionId);
        
        // Vérifier que la question appartient bien à l'évaluation
        if ($question->evaluation_id !== (int)$evaluationId) {
            return redirect()->route('trainer.evaluations.questions.index', $evaluationId)
                ->with('error', 'Cette question n\'appartient pas à cette évaluation.');
        }
        
        return Inertia::render('Trainer/Evaluations/Questions/Edit', [
            'evaluation' => $evaluation,
            'question' => $question,
            'questionTypes' => [
                ['value' => 'text', 'label' => 'Texte libre'],
                ['value' => 'multiple_choice', 'label' => 'Choix multiple'],
                ['value' => 'rating', 'label' => 'Notation (0-10)'],
                ['value' => 'yes_no', 'label' => 'Oui/Non'],
            ],
        ]);
    }

    /**
     * Met à jour une question
     */
    public function update(Request $request, string $evaluationId, string $questionId)
    {
        $evaluation = Evaluation::findOrFail($evaluationId);
        
        // Vérifier que l'évaluation appartient bien au formateur
        if ($evaluation->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.evaluations.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à cette évaluation.');
        }
        
        // Récupérer la question
        $question = EvaluationQuestion::findOrFail($questionId);
        
        // Vérifier que la question appartient bien à l'évaluation
        if ($question->evaluation_id !== (int)$evaluationId) {
            return redirect()->route('trainer.evaluations.questions.index', $evaluationId)
                ->with('error', 'Cette question n\'appartient pas à cette évaluation.');
        }
        
        // Valider les données
        $validated = $request->validate([
            'question_text' => 'required|string',
            'question_type' => 'required|in:text,multiple_choice,rating,yes_no',
            'options' => 'nullable|array',
            'options.*' => 'nullable|string',
            'required' => 'boolean',
            'order' => 'required|integer|min:1',
        ]);
        
        // Mettre à jour la question
        $question->update($validated);
        
        return redirect()->route('trainer.evaluations.questions.index', $evaluationId)
            ->with('success', 'Question mise à jour avec succès.');
    }

    /**
     * Supprime une question
     */
    public function destroy(string $evaluationId, string $questionId)
    {
        $evaluation = Evaluation::findOrFail($evaluationId);
        
        // Vérifier que l'évaluation appartient bien au formateur
        if ($evaluation->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.evaluations.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à cette évaluation.');
        }
        
        // Récupérer la question
        $question = EvaluationQuestion::findOrFail($questionId);
        
        // Vérifier que la question appartient bien à l'évaluation
        if ($question->evaluation_id !== (int)$evaluationId) {
            return redirect()->route('trainer.evaluations.questions.index', $evaluationId)
                ->with('error', 'Cette question n\'appartient pas à cette évaluation.');
        }
        
        // Supprimer la question
        $question->delete();
        
        return redirect()->route('trainer.evaluations.questions.index', $evaluationId)
            ->with('success', 'Question supprimée avec succès.');
    }
    
    /**
     * Réorganise les questions
     */
    public function reorder(Request $request, string $evaluationId)
    {
        $evaluation = Evaluation::findOrFail($evaluationId);
        
        // Vérifier que l'évaluation appartient bien au formateur
        if ($evaluation->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.evaluations.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à cette évaluation.');
        }
        
        // Valider les données
        $validated = $request->validate([
            'questions' => 'required|array',
            'questions.*.id' => 'required|exists:evaluation_questions,id',
            'questions.*.order' => 'required|integer|min:1',
        ]);
        
        // Mettre à jour l'ordre des questions
        foreach ($validated['questions'] as $questionData) {
            $question = EvaluationQuestion::findOrFail($questionData['id']);
            
            // Vérifier que la question appartient bien à l'évaluation
            if ($question->evaluation_id !== (int)$evaluationId) {
                continue;
            }
            
            $question->update(['order' => $questionData['order']]);
        }
        
        return redirect()->route('trainer.evaluations.questions.index', $evaluationId)
            ->with('success', 'Ordre des questions mis à jour avec succès.');
    }
}
