<template>
  <Head title="Évaluations et feedback" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Évaluations et feedback
        </h2>
        <Link :href="route('trainer.evaluations.create')" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
          Créer une évaluation
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Filtres -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <h4 class="font-medium mb-3">Filtres</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Recherche -->
                <div>
                  <InputLabel for="search" value="Recherche" />
                  <TextInput
                    id="search"
                    v-model="searchQuery"
                    type="text"
                    class="mt-1 block w-full"
                    placeholder="Rechercher..."
                  />
                </div>

                <!-- Session de formation -->
                <div>
                  <InputLabel for="training_session_id" value="Session de formation" />
                  <select
                    id="training_session_id"
                    v-model="sessionFilter"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  >
                    <option value="">Toutes les sessions</option>
                    <option v-for="session in trainingSessions" :key="session.id" :value="session.id">
                      {{ session.title }}
                    </option>
                  </select>
                </div>

                <!-- Cours -->
                <div>
                  <InputLabel for="course_id" value="Cours" />
                  <select
                    id="course_id"
                    v-model="courseFilter"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  >
                    <option value="">Tous les cours</option>
                    <option v-for="course in courses" :key="course.id" :value="course.id">
                      {{ course.title }}
                    </option>
                  </select>
                </div>

                <!-- Type d'évaluation -->
                <div>
                  <InputLabel for="type" value="Type d'évaluation" />
                  <select
                    id="type"
                    v-model="typeFilter"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  >
                    <option value="">Tous les types</option>
                    <option v-for="type in evaluationTypes" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </option>
                  </select>
                </div>

                <!-- Statut -->
                <div>
                  <InputLabel for="status" value="Statut" />
                  <select
                    id="status"
                    v-model="statusFilter"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  >
                    <option value="">Tous les statuts</option>
                    <option v-for="status in statusTypes" :key="status.value" :value="status.value">
                      {{ status.label }}
                    </option>
                  </select>
                </div>
              </div>

              <!-- Boutons de filtrage -->
              <div class="flex justify-end mt-4">
                <SecondaryButton @click="resetFilters" class="mr-2">
                  Réinitialiser
                </SecondaryButton>
                <PrimaryButton @click="applyFilters">
                  Appliquer les filtres
                </PrimaryButton>
              </div>
            </div>

            <!-- Liste des évaluations -->
            <div v-if="evaluations.data.length > 0">
              <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200">
                  <thead>
                    <tr>
                      <th class="py-2 px-4 border-b text-left">Titre</th>
                      <th class="py-2 px-4 border-b text-left">Type</th>
                      <th class="py-2 px-4 border-b text-left">Session</th>
                      <th class="py-2 px-4 border-b text-left">Cours</th>
                      <th class="py-2 px-4 border-b text-left">Statut</th>
                      <th class="py-2 px-4 border-b text-left">Réponses</th>
                      <th class="py-2 px-4 border-b text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="evaluation in evaluations.data" :key="evaluation.id" class="hover:bg-gray-50">
                      <td class="py-2 px-4 border-b">{{ evaluation.title }}</td>
                      <td class="py-2 px-4 border-b">
                        <span v-if="evaluation.type === 'feedback'" class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Feedback</span>
                        <span v-else-if="evaluation.type === 'survey'" class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Sondage</span>
                        <span v-else-if="evaluation.type === 'rating'" class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">Évaluation</span>
                      </td>
                      <td class="py-2 px-4 border-b">{{ evaluation.training_session.title }}</td>
                      <td class="py-2 px-4 border-b">{{ evaluation.course ? evaluation.course.title : 'N/A' }}</td>
                      <td class="py-2 px-4 border-b">
                        <span v-if="evaluation.status === 'draft'" class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">Brouillon</span>
                        <span v-else-if="evaluation.status === 'active'" class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Active</span>
                        <span v-else-if="evaluation.status === 'closed'" class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Fermée</span>
                      </td>
                      <td class="py-2 px-4 border-b">{{ evaluation.responses_count }} / {{ evaluation.questions_count }} questions</td>
                      <td class="py-2 px-4 border-b text-center">
                        <div class="flex justify-center space-x-2">
                          <Link :href="route('trainer.evaluations.show', evaluation.id)" class="text-blue-600 hover:text-blue-900">
                            Voir
                          </Link>
                          <Link :href="route('trainer.evaluations.questions.index', evaluation.id)" class="text-green-600 hover:text-green-900">
                            Questions
                          </Link>
                          <Link :href="route('trainer.evaluations.results', evaluation.id)" class="text-purple-600 hover:text-purple-900">
                            Résultats
                          </Link>
                          <Link :href="route('trainer.evaluations.edit', evaluation.id)" class="text-indigo-600 hover:text-indigo-900">
                            Modifier
                          </Link>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Pagination -->
              <div class="mt-6">
                <Pagination :links="evaluations.links" />
              </div>
            </div>
            <div v-else class="text-center py-8">
              <p class="text-gray-500">Aucune évaluation trouvée.</p>
              <Link :href="route('trainer.evaluations.create')" class="mt-4 inline-block px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                Créer une évaluation
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import Pagination from '@/Components/Pagination.vue';

// Props
const props = defineProps({
  evaluations: Object,
  trainingSessions: Array,
  courses: Array,
  filters: Object,
  evaluationTypes: Array,
  statusTypes: Array,
});

// État pour les filtres
const searchQuery = ref(props.filters?.search || '');
const sessionFilter = ref(props.filters?.training_session_id || '');
const courseFilter = ref(props.filters?.course_id || '');
const typeFilter = ref(props.filters?.type || '');
const statusFilter = ref(props.filters?.status || '');

// Méthodes pour les filtres
const applyFilters = () => {
  const params = {};

  // Ajouter les filtres s'ils existent
  if (searchQuery.value) {
    params.search = searchQuery.value;
  }

  if (sessionFilter.value) {
    params.training_session_id = sessionFilter.value;
  }

  if (courseFilter.value) {
    params.course_id = courseFilter.value;
  }

  if (typeFilter.value) {
    params.type = typeFilter.value;
  }

  if (statusFilter.value) {
    params.status = statusFilter.value;
  }

  // Naviguer vers l'URL avec les paramètres
  router.get(route('trainer.evaluations.index'), params, {
    preserveState: true,
    replace: true,
    preserveScroll: true
  });
};

const resetFilters = () => {
  searchQuery.value = '';
  sessionFilter.value = '';
  courseFilter.value = '';
  typeFilter.value = '';
  statusFilter.value = '';

  // Naviguer vers l'URL sans paramètres
  router.get(route('trainer.evaluations.index'), {}, {
    preserveState: true,
    replace: true,
    preserveScroll: true
  });
};
</script>
