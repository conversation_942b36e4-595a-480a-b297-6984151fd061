<template>
  <Head :title="`Modifier ${material.title}`" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Modifier {{ material.title }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Fil d'Ariane -->
            <div class="mb-6">
              <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                  <li class="inline-flex items-center">
                    <Link :href="route('trainer.dashboard')" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                      <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                      </svg>
                      Tableau de bord
                    </Link>
                  </li>
                  <li>
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <Link :href="route('trainer.courses.index')" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                        Mes cours
                      </Link>
                    </div>
                  </li>
                  <li v-if="module">
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <Link :href="route('trainer.modules.index', { course_id: course.id })" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                        Modules du cours
                      </Link>
                    </div>
                  </li>
                  <li>
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <Link :href="route('trainer.course-materials.index', module ? { module_id: module.id } : { course_id: course.id })" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                        Matériels pédagogiques
                      </Link>
                    </div>
                  </li>
                  <li aria-current="page">
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">
                        Modifier {{ material.title }}
                      </span>
                    </div>
                  </li>
                </ol>
              </nav>
            </div>

            <!-- Formulaire de modification -->
            <form @submit.prevent="submit" enctype="multipart/form-data">
              <!-- Informations de base -->
              <div class="mb-6">
                <InputLabel for="title" value="Titre" />
                <TextInput
                  id="title"
                  type="text"
                  class="mt-1 block w-full"
                  v-model="form.title"
                  required
                  autofocus
                />
                <InputError class="mt-2" :message="form.errors.title" />
              </div>

              <div class="mb-6">
                <InputLabel for="description" value="Description (optionnelle)" />
                <textarea
                  id="description"
                  v-model="form.description"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  rows="3"
                ></textarea>
                <InputError class="mt-2" :message="form.errors.description" />
              </div>

              <!-- Module (si applicable) -->
              <div v-if="modules && modules.length > 0" class="mb-6">
                <InputLabel for="module_id" value="Module (optionnel)" />
                <select
                  id="module_id"
                  v-model="form.module_id"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                >
                  <option :value="null">Aucun module (matériel général du cours)</option>
                  <option v-for="mod in modules" :key="mod.id" :value="mod.id">
                    {{ mod.title }}
                  </option>
                </select>
                <InputError class="mt-2" :message="form.errors.module_id" />
              </div>

              <!-- Type de matériel (lecture seule) -->
              <div class="mb-6">
                <InputLabel for="type" value="Type de matériel" />
                <div class="mt-1 p-2 bg-gray-100 border border-gray-300 rounded-md">
                  {{ getMaterialTypeLabel(material.type) }}
                </div>
                <p class="text-sm text-gray-500 mt-1">
                  Le type de matériel ne peut pas être modifié. Créez un nouveau matériel si vous souhaitez changer de type.
                </p>
              </div>

              <!-- Contenu spécifique au type -->
              <!-- Texte -->
              <div v-if="material.type === 'text'" class="mb-6">
                <InputLabel for="content" value="Contenu textuel" />
                <textarea
                  id="content"
                  v-model="form.content"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  rows="10"
                  required
                ></textarea>
                <InputError class="mt-2" :message="form.errors.content" />
              </div>

              <!-- Vidéo externe -->
              <div v-if="material.type === 'embed_video'" class="mb-6">
                <InputLabel for="video_url" value="URL de la vidéo (YouTube, Dailymotion, Vimeo)" />
                <TextInput
                  id="video_url"
                  type="url"
                  class="mt-1 block w-full"
                  v-model="form.video_url"
                  placeholder="https://www.youtube.com/watch?v=..."
                  required
                />
                <p class="text-sm text-gray-500 mt-1">
                  Collez simplement l'URL de la vidéo, pas le code d'intégration.
                </p>
                <InputError class="mt-2" :message="form.errors.video_url" />
              </div>

              <!-- Fichier existant -->
              <div v-if="['pdf', 'video', 'audio', 'image', 'archive'].includes(material.type) && material.file_path" class="mb-6">
                <InputLabel value="Fichier actuel" />
                <div class="mt-1 p-2 bg-gray-100 border border-gray-300 rounded-md flex justify-between items-center">
                  <span>{{ getFileName(material.file_path) }} ({{ formatFileSize(material.file_size) }})</span>
                  <Link v-if="material.allow_download" :href="route('trainer.course-materials.download', material.id)" class="text-blue-600 hover:underline">
                    Télécharger
                  </Link>
                </div>
              </div>

              <!-- Remplacer le fichier -->
              <div v-if="['pdf', 'video', 'audio', 'image', 'archive'].includes(material.type)" class="mb-6">
                <InputLabel for="file" :value="`Remplacer le fichier ${material.type} (optionnel)`" />
                <input
                  id="file"
                  type="file"
                  class="mt-1 block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none"
                  @change="handleFileChange"
                />
                <p class="mt-1 text-sm text-gray-500">
                  {{ getFileTypeHelp(material.type) }}
                </p>
                <InputError class="mt-2" :message="form.errors.file" />
              </div>

              <!-- Galerie d'images existante -->
              <div v-if="material.type === 'gallery' && material.metadata" class="mb-6">
                <InputLabel value="Images actuelles de la galerie" />
                <div class="mt-1 grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div v-for="(path, index) in getGalleryPaths(material.metadata)" :key="index" class="relative">
                    <img :src="'/storage/' + path" :alt="`Image ${index + 1}`" class="w-full h-32 object-cover rounded-lg" />
                    <button 
                      type="button" 
                      @click="removeGalleryImage(index)" 
                      class="absolute top-1 right-1 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-700"
                    >
                      ×
                    </button>
                  </div>
                </div>
              </div>

              <!-- Ajouter des images à la galerie -->
              <div v-if="material.type === 'gallery'" class="mb-6">
                <InputLabel for="gallery_files" value="Ajouter des images à la galerie (optionnel)" />
                <input
                  id="gallery_files"
                  type="file"
                  multiple
                  accept="image/*"
                  class="mt-1 block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none"
                  @change="handleGalleryFilesChange"
                />
                <p class="mt-1 text-sm text-gray-500">
                  Sélectionnez plusieurs images pour ajouter à la galerie (JPG, PNG, GIF).
                </p>
                <InputError class="mt-2" :message="form.errors.gallery_files" />
              </div>

              <!-- Ordre -->
              <div class="mb-6">
                <InputLabel for="order" value="Ordre d'affichage" />
                <TextInput
                  id="order"
                  type="number"
                  class="mt-1 block w-full"
                  v-model="form.order"
                  min="1"
                />
                <InputError class="mt-2" :message="form.errors.order" />
              </div>

              <!-- Options -->
              <div class="mb-6">
                <div class="flex items-center">
                  <input
                    id="active"
                    type="checkbox"
                    v-model="form.active"
                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                  />
                  <label for="active" class="ml-2 text-sm text-gray-600">
                    Actif (visible pour les apprenants)
                  </label>
                </div>
                <InputError class="mt-2" :message="form.errors.active" />
              </div>

              <div v-if="['pdf', 'video', 'audio', 'image', 'archive'].includes(material.type)" class="mb-6">
                <div class="flex items-center">
                  <input
                    id="allow_download"
                    type="checkbox"
                    v-model="form.allow_download"
                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                  />
                  <label for="allow_download" class="ml-2 text-sm text-gray-600">
                    Autoriser le téléchargement
                  </label>
                </div>
                <InputError class="mt-2" :message="form.errors.allow_download" />
              </div>

              <div v-if="['pdf', 'video', 'audio', 'image'].includes(material.type)" class="mb-6">
                <div class="flex items-center">
                  <input
                    id="allow_online_viewing"
                    type="checkbox"
                    v-model="form.allow_online_viewing"
                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                  />
                  <label for="allow_online_viewing" class="ml-2 text-sm text-gray-600">
                    Autoriser la visualisation en ligne
                  </label>
                </div>
                <InputError class="mt-2" :message="form.errors.allow_online_viewing" />
              </div>

              <!-- Boutons -->
              <div class="flex justify-end">
                <Link
                  :href="route('trainer.course-materials.index', module ? { module_id: module.id } : { course_id: course.id })"
                  class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150 mr-2"
                >
                  Annuler
                </Link>
                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                  Mettre à jour
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  material: Object,
  course: Object,
  module: Object,
  modules: Array,
  materialTypes: Array,
});

// Formulaire
const form = useForm({
  title: props.material.title,
  description: props.material.description || '',
  module_id: props.material.module_id,
  type: props.material.type,
  content: props.material.content || '',
  video_url: props.material.type === 'embed_video' ? props.material.content : '',
  file: null,
  gallery_files: [],
  order: props.material.order,
  active: props.material.active,
  allow_download: props.material.allow_download,
  allow_online_viewing: props.material.allow_online_viewing,
  _method: 'PUT',
  removed_gallery_images: [],
});

// Méthodes
const handleFileChange = (e) => {
  if (e.target.files.length > 0) {
    form.file = e.target.files[0];
  } else {
    form.file = null;
  }
};

const handleGalleryFilesChange = (e) => {
  if (e.target.files.length > 0) {
    form.gallery_files = e.target.files;
  } else {
    form.gallery_files = [];
  }
};

const getFileTypeHelp = (type) => {
  const helps = {
    'pdf': 'Formats acceptés: PDF',
    'video': 'Formats acceptés: MP4, WebM, OGG',
    'audio': 'Formats acceptés: MP3, WAV, OGG',
    'image': 'Formats acceptés: JPG, PNG, GIF, SVG',
    'archive': 'Formats acceptés: ZIP, RAR, 7Z',
  };

  return helps[type] || '';
};

// Méthode pour obtenir le libellé du type de matériel
const getMaterialTypeLabel = (type) => {
  const types = {
    'text': 'Texte',
    'pdf': 'Document PDF',
    'video': 'Vidéo',
    'audio': 'Audio (podcast)',
    'image': 'Image',
    'archive': 'Archive',
    'gallery': 'Galerie d\'images',
    'embed_video': 'Vidéo externe'
  };

  return types[type] || type;
};

// Méthode pour extraire le nom du fichier à partir du chemin
const getFileName = (path) => {
  if (!path) return '';
  return path.split('/').pop();
};

// Méthode pour formater la taille du fichier
const formatFileSize = (sizeInKB) => {
  if (!sizeInKB) return '0 KB';
  
  if (sizeInKB < 1024) {
    return `${Math.round(sizeInKB)} KB`;
  } else {
    return `${(sizeInKB / 1024).toFixed(2)} MB`;
  }
};

// Méthode pour extraire les chemins de la galerie
const getGalleryPaths = (metadata) => {
  if (!metadata) return [];
  
  try {
    const parsed = typeof metadata === 'string' ? JSON.parse(metadata) : metadata;
    return parsed.gallery_paths || [];
  } catch (e) {
    console.error('Erreur lors de l\'analyse des métadonnées de la galerie:', e);
    return [];
  }
};

// Méthode pour supprimer une image de la galerie
const removeGalleryImage = (index) => {
  const paths = getGalleryPaths(props.material.metadata);
  if (index >= 0 && index < paths.length) {
    form.removed_gallery_images.push(paths[index]);
  }
};

const submit = () => {
  form.post(route('trainer.course-materials.update', props.material.id), {
    forceFormData: true,
  });
};
</script>
