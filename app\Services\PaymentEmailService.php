<?php

namespace App\Services;

use App\Models\Enrollment;
use App\Models\User;
use App\Mail\PaymentReceiptMail;
use App\Mail\InvoiceMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class PaymentEmailService
{
    /**
     * Send payment receipt to admin when payment is confirmed
     */
    public function sendPaymentReceiptToAdmin(Enrollment $enrollment): void
    {
        try {
            // Get all admin users
            $admins = User::where('role', 'admin')->get();
            
            foreach ($admins as $admin) {
                Mail::to($admin->email)->send(new PaymentReceiptMail($enrollment));
            }
            
            Log::info("Payment receipt sent to admins for enrollment ID: {$enrollment->id}");
        } catch (\Exception $e) {
            Log::error("Failed to send payment receipt to admins: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Send payment receipt notification when student uploads payment proof
     */
    public function notifyAdminsOfPaymentProof(Enrollment $enrollment): void
    {
        try {
            // Get all admin users
            $admins = User::where('role', 'admin')->get();
            
            foreach ($admins as $admin) {
                Mail::to($admin->email)->send(new PaymentReceiptMail($enrollment, true));
            }
            
            Log::info("Payment proof notification sent to admins for enrollment ID: {$enrollment->id}");
        } catch (\Exception $e) {
            Log::error("Failed to send payment proof notification to admins: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Generate and send invoice to student after enrollment approval and payment confirmation
     */
    public function sendInvoiceToStudent(Enrollment $enrollment): void
    {
        try {
            // Verify both conditions are met
            if ($enrollment->status !== 'approved' || $enrollment->payment_status !== 'paid') {
                Log::warning("Invoice not sent - conditions not met. Status: {$enrollment->status}, Payment: {$enrollment->payment_status}");
                return;
            }

            // Generate invoice data
            $invoiceData = $this->generateInvoiceData($enrollment);
            
            // Send invoice email to student
            Mail::to($enrollment->user->email)->send(new InvoiceMail($enrollment, $invoiceData));
            
            Log::info("Invoice sent to student for enrollment ID: {$enrollment->id}");
        } catch (\Exception $e) {
            Log::error("Failed to send invoice to student: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Generate invoice data
     */
    private function generateInvoiceData(Enrollment $enrollment): array
    {
        $invoiceNumber = 'INV-' . date('Y') . '-' . str_pad($enrollment->id, 6, '0', STR_PAD_LEFT);
        
        return [
            'invoice_number' => $invoiceNumber,
            'invoice_date' => now(),
            'due_date' => $enrollment->payment_due_date ?? now()->addDays(30),
            'student_name' => $enrollment->user->name,
            'student_email' => $enrollment->user->email,
            'training_title' => $enrollment->trainingSession->title,
            'training_domain' => $enrollment->trainingSession->trainingDomain->name,
            'amount' => $enrollment->payment_amount,
            'payment_method' => $enrollment->payment_method,
            'payment_date' => $enrollment->payment_date,
            'enrollment_date' => $enrollment->enrollment_date,
        ];
    }
}
