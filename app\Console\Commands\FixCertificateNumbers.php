<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Certificate;

class FixCertificateNumbers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'certificates:fix-numbers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix inconsistent certificate numbers to use the standard format';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fixing inconsistent certificate numbers...');

        // Get all certificates that don't follow the standard format CERT-{enrollment_id}-{timestamp}
        $certificates = Certificate::whereNotNull('certificate_number')
            ->whereNotNull('enrollment_id')
            ->get();

        $this->info("Found {$certificates->count()} certificates to check.");

        $fixed = 0;
        $skipped = 0;

        foreach ($certificates as $certificate) {
            try {
                $currentNumber = $certificate->certificate_number;
                
                // Check if the certificate number follows the expected format
                $expectedPattern = '/^CERT-' . $certificate->enrollment_id . '-\d+$/';
                
                if (!preg_match($expectedPattern, $currentNumber)) {
                    // Generate a new consistent certificate number
                    $newNumber = Certificate::generateCertificateNumber($certificate->enrollment_id);
                    
                    // Update the certificate
                    $certificate->update(['certificate_number' => $newNumber]);
                    
                    // Regenerate QR code with new number
                    $certificate->generateQrCode();
                    
                    $this->line("✓ Fixed certificate {$certificate->id}: {$currentNumber} → {$newNumber}");
                    $fixed++;
                } else {
                    $this->line("- Certificate {$certificate->id} already has correct format: {$currentNumber}");
                    $skipped++;
                }

            } catch (\Exception $e) {
                $this->error("✗ Failed to fix certificate {$certificate->id}: " . $e->getMessage());
            }
        }

        $this->info("Successfully fixed {$fixed} certificate numbers, skipped {$skipped} correct ones.");
        
        return Command::SUCCESS;
    }
}
