<template>
  <Head title="Tableau de bord apprenant" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Tableau de bord apprenant
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Statistiques modernisées -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 mb-6">
          <StatCard :value="stats.enrollments_count" label="Inscriptions totales" :icon="CalendarIcon" bgGradient="bg-gradient-to-r from-blue-500 to-blue-700" />
          <StatCard :value="stats.active_enrollments_count" label="Sessions en cours" :icon="AcademicCapIcon" bgGradient="bg-gradient-to-r from-green-500 to-green-700" />
          <StatCard :value="stats.pending_enrollments_count" label="Sessions en attente" :icon="ClockIcon" bgGradient="bg-gradient-to-r from-yellow-400 to-yellow-600" />
          <StatCard :value="stats.completed_enrollments_count" label="Formations terminées" :icon="CheckBadgeIcon" bgGradient="bg-gradient-to-r from-purple-500 to-purple-700" />
          <StatCard :value="stats.certificates_count" label="Certificats obtenus" :icon="DocumentCheckIcon" bgGradient="bg-gradient-to-r from-indigo-500 to-indigo-700" />
          <StatCard :value="stats.passed_exams_count" label="Examens réussis" :icon="PencilSquareIcon" bgGradient="bg-gradient-to-r from-pink-500 to-pink-700" />
        </div>

        <!-- Actions rapides -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Actions rapides</h3>
            <div class="flex flex-wrap gap-4">
              <Link :href="route('student.enrollments.index')" class="flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
                Mes inscriptions
              </Link>
              <Link :href="route('student.enrollments.payments')" class="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                  <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd" />
                </svg>
                Mes paiements
              </Link>
              <Link :href="route('student.exams.index')" class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                  <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
                </svg>
                Mes examens
              </Link>
              <Link :href="route('student.certificates')" class="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
                </svg>
                Mes certificats
              </Link>
            </div>
          </div>
        </div>

        <!-- Dernières annonces -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Dernières annonces</h3>

            <div v-if="announcements && announcements.length > 0" class="space-y-4">
              <div v-for="announcement in announcements" :key="announcement.id"
                   :class="{'border-l-4 border-red-500': announcement.is_important, 'border-l-4 border-gray-200': !announcement.is_important}"
                   class="p-4 bg-gray-50 rounded-lg">
                <div class="flex justify-between items-start">
                  <h4 class="font-medium text-gray-900">{{ announcement.title }}</h4>
                  <span class="text-xs text-gray-500">{{ formatDate(announcement.publish_date) }}</span>
                </div>
                <p class="mt-2 text-gray-600 text-sm">{{ announcement.content }}</p>
                <div v-if="announcement.training_session" class="mt-2 text-xs text-gray-500">
                  Session: {{ announcement.training_session.title }}
                </div>
              </div>
            </div>

            <div v-else class="text-center py-4 text-gray-500">
              Aucune annonce pour le moment.
            </div>
          </div>
        </div>

        <!-- Prochaine session -->
        <div v-if="nextSession" class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Prochaine session à venir</h3>

            <div class="border rounded-lg p-4 bg-indigo-50">
              <h4 class="font-semibold text-lg text-indigo-900">{{ nextSession.title }}</h4>
              <p class="text-sm text-indigo-700 mb-2">{{ nextSession.training_domain.name }}</p>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span class="text-sm">Du {{ formatDate(nextSession.start_date) }} au {{ formatDate(nextSession.end_date) }}</span>
                </div>

                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <span class="text-sm">Formateur: {{ nextSession.trainer.name }}</span>
                </div>
              </div>

              <div class="flex justify-end">
                <Link :href="route('student.courses.show', nextSession.id)" class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                  Voir les détails
                  <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Formations actives -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
              <h3 class="text-lg font-semibold mb-4">Mes formations actives</h3>
              <div v-if="activeSessions.length > 0">
                <div class="space-y-4">
                  <div v-for="session in activeSessions" :key="session.id" class="border rounded-lg p-4 hover:bg-gray-50">
                    <h4 class="font-semibold text-lg">{{ session.title }}</h4>
                    <p class="text-sm text-gray-600 mb-2">{{ session.training_domain.name }}</p>
                    <div class="flex justify-between items-center">
                      <div class="text-sm">
                        <span class="text-gray-600">Formateur:</span> {{ session.trainer.name }}
                      </div>
                      <Link :href="route('student.courses.show', session.id)" class="text-blue-600 hover:text-blue-800 text-sm">
                        Accéder aux cours
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="text-gray-500 italic">
                Vous n'êtes inscrit à aucune formation active.
              </div>
            </div>
          </div>

          <!-- Derniers résultats d'examen modernisé -->
          <div class="bg-white rounded-xl shadow-lg overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Examen</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="result in recentExamResults" :key="result.id" class="hover:bg-blue-50 transition">
                  <td class="px-6 py-4 whitespace-nowrap">{{ result.exam.title }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">{{ result.score }}/{{ result.total_points }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="{
                      'px-2 py-1 text-xs rounded-full': true,
                      'bg-green-100 text-green-800': result.passed,
                      'bg-red-100 text-red-800': !result.passed
                    }">
                      {{ result.passed ? 'Réussi' : 'Échoué' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">{{ formatDate(result.completed_at) }}</td>
                </tr>
                <tr v-if="recentExamResults.length === 0">
                  <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                    Vous n'avez pas encore passé d'examen.
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Mes certificats -->
        <div class="mt-6 bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Mes certificats</h3>
            <div v-if="certificates.length > 0">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div v-for="certificate in certificates" :key="certificate.id" class="border rounded-lg p-4 hover:bg-gray-50">
                  <h4 class="font-semibold">{{ certificate.training_session.title }}</h4>
                  <p class="text-sm text-gray-600 mb-2">{{ certificate.training_session.training_domain.name }}</p>
                  <p class="text-sm mb-2">
                    <span class="text-gray-600">Date d'obtention:</span> {{ formatDate(certificate.issue_date) }}
                  </p>
                  <div class="flex justify-between items-center">
                    <a :href="route('student.certificates.view', certificate.id)" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                      </svg>
                      Voir
                    </a>
                    <Link :href="route('student.certificates.download', certificate.id)" class="text-green-600 hover:text-green-800 text-sm flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                      </svg>
                      Télécharger
                    </Link>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-gray-500 italic">
              Vous n'avez pas encore obtenu de certificat.
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import StatCard from '@/Components/StatCard.vue';
import { AcademicCapIcon, CalendarIcon, ClockIcon, CheckBadgeIcon, DocumentCheckIcon, PencilSquareIcon } from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  stats: Object,
  activeSessions: Array,
  recentExamResults: Array,
  certificates: Array,
  announcements: Array,
  nextSession: Object,
});

// Méthodes
const formatDate = (dateString) => {
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};
</script>

<style scoped>
.btn-action-view {
  @apply text-indigo-600 hover:text-indigo-900 transition;
}
.btn-action-edit {
  @apply text-blue-600 hover:text-blue-900 transition;
}
.btn-action-delete {
  @apply text-red-600 hover:text-red-900 transition;
}
</style>
