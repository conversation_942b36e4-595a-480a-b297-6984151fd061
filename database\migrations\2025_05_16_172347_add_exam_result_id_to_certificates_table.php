<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('certificates', function (Blueprint $table) {
            $table->foreignId('exam_result_id')->nullable()->after('user_id')->constrained('exam_results')->onDelete('set null');
            $table->foreignId('training_session_id')->nullable()->after('enrollment_id')->constrained('training_sessions')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('certificates', function (Blueprint $table) {
            $table->dropForeign(['exam_result_id']);
            $table->dropForeign(['training_session_id']);
            $table->dropColumn(['exam_result_id', 'training_session_id']);
        });
    }
};
