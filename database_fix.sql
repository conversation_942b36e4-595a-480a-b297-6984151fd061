-- <PERSON><PERSON>t pour corriger les problèmes de base de données

-- 1. <PERSON><PERSON> la migration announcements comme exécutée si la table existe
INSERT IGNORE INTO migrations (migration, batch) 
VALUES ('2023_06_15_000001_create_announcements_table', 1);

-- 2. <PERSON><PERSON><PERSON> la table training_domains si elle n'existe pas
CREATE TABLE IF NOT EXISTS `training_domains` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. C<PERSON>er la table training_sessions si elle n'existe pas
CREATE TABLE IF NOT EXISTS `training_sessions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text,
  `certificate_name` varchar(255) DEFAULT NULL,
  `training_objectives` text,
  `training_domain_id` bigint unsigned NOT NULL,
  `department` enum('Secourisme','Langue','Formation à la carte') DEFAULT NULL,
  `level` enum('Niveau 1','Niveau 2','Niveau 3','Niveau 4','Niveau 5','Niveau 6','Niveau 7') DEFAULT NULL,
  `trainer_id` bigint unsigned DEFAULT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `max_participants` int unsigned DEFAULT NULL,
  `price` decimal(8,2) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `prerequisites` text,
  `image` varchar(255) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `training_sessions_training_domain_id_foreign` (`training_domain_id`),
  KEY `training_sessions_trainer_id_foreign` (`trainer_id`),
  CONSTRAINT `training_sessions_training_domain_id_foreign` FOREIGN KEY (`training_domain_id`) REFERENCES `training_domains` (`id`) ON DELETE CASCADE,
  CONSTRAINT `training_sessions_trainer_id_foreign` FOREIGN KEY (`trainer_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Créer la table enrollments si elle n'existe pas
CREATE TABLE IF NOT EXISTS `enrollments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `training_session_id` bigint unsigned NOT NULL,
  `time_slot_id` bigint unsigned DEFAULT NULL,
  `status` enum('pending','approved','rejected','completed') NOT NULL DEFAULT 'pending',
  `enrollment_date` date NOT NULL,
  `notes` text,
  `payment_amount` decimal(10,2) DEFAULT NULL,
  `payment_confirmed` tinyint(1) NOT NULL DEFAULT '0',
  `payment_status` enum('pending','paid','failed','refunded') DEFAULT 'pending',
  `payment_method` varchar(255) DEFAULT NULL,
  `payment_proof` varchar(255) DEFAULT NULL,
  `payment_date` timestamp NULL DEFAULT NULL,
  `payment_due_date` timestamp NULL DEFAULT NULL,
  `payment_notes` text,
  `payment_instructions` text,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `enrollments_user_id_training_session_id_unique` (`user_id`,`training_session_id`),
  KEY `enrollments_training_session_id_foreign` (`training_session_id`),
  KEY `enrollments_time_slot_id_foreign` (`time_slot_id`),
  CONSTRAINT `enrollments_training_session_id_foreign` FOREIGN KEY (`training_session_id`) REFERENCES `training_sessions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `enrollments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. Insérer des données de test
INSERT IGNORE INTO training_domains (name, description, active) VALUES
('Premiers Secours', 'Formation aux premiers secours et techniques de sauvetage', 1),
('Langues', 'Formation linguistique et communication internationale', 1),
('Formation Personnalisée', 'Formations sur mesure selon vos besoins', 1);

-- 6. Insérer des sessions de test
INSERT IGNORE INTO training_sessions (title, description, training_domain_id, department, level, start_date, end_date, price, location, active) VALUES
('Secourisme Niveau 1 - Gestes de base', 'Apprenez les gestes de premiers secours essentiels', 1, 'Secourisme', 'Niveau 1', '2025-02-01', '2025-02-02', 150.00, 'Centre PCMET', 1),
('Secourisme Niveau 2 - Techniques avancées', 'Perfectionnez vos techniques de secourisme', 1, 'Secourisme', 'Niveau 2', '2025-02-15', '2025-02-16', 200.00, 'Centre PCMET', 1),
('Secourisme Niveau 3 - Formation instructeur', 'Devenez instructeur en premiers secours', 1, 'Secourisme', 'Niveau 3', '2025-03-01', '2025-03-02', 300.00, 'Centre PCMET', 1),
('Langue Niveau 1 - Bases de communication', 'Apprenez les bases de la communication', 2, 'Langue', 'Niveau 1', '2025-02-01', '2025-02-02', 100.00, 'Centre PCMET', 1),
('Langue Niveau 2 - Communication avancée', 'Perfectionnez votre communication', 2, 'Langue', 'Niveau 2', '2025-02-15', '2025-02-16', 150.00, 'Centre PCMET', 1),
('Formation à la carte Niveau 1 - Sur mesure', 'Formation personnalisée selon vos besoins', 3, 'Formation à la carte', 'Niveau 1', '2025-02-01', '2025-02-02', NULL, 'Centre PCMET', 1);

-- 7. Créer un utilisateur étudiant de test
INSERT IGNORE INTO users (name, email, password, role, phone, address, date_of_birth, email_verified_at, created_at, updated_at) VALUES
('Étudiant Test', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', '0123456789', 'Adresse test', '1990-01-01', NOW(), NOW(), NOW());

-- 8. Marquer les migrations importantes comme exécutées
INSERT IGNORE INTO migrations (migration, batch) VALUES
('2025_04_22_134958_create_training_domains_table', 2),
('2025_04_22_135013_create_training_sessions_table', 2),
('2025_04_22_135532_create_enrollments_table', 2),
('2025_07_04_235232_add_department_and_level_to_training_sessions_table', 2);
