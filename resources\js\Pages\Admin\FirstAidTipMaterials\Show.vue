<template>
  <Head :title="`Matériel: ${material.title}`" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Matériel: {{ material.title }}
        </h2>
        <div class="flex space-x-2">
          <Link :href="route('admin.first-aid-tip-materials.index', { first_aid_tip_id: material.first_aid_tip.id })" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
            Retour à la liste
          </Link>
          <Link :href="route('admin.first-aid-tip-materials.edit', material.id)" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
            Modifier
          </Link>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
        <!-- Informations du conseil -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="flex items-center">
            <HeartIcon class="h-6 w-6 text-red-500 mr-3" />
            <div>
              <h3 class="text-lg font-semibold text-blue-900">{{ material.first_aid_tip.title }}</h3>
              <p class="text-sm text-blue-700" v-if="material.first_aid_tip.description">{{ material.first_aid_tip.description }}</p>
            </div>
          </div>
        </div>

        <!-- Détails du matériel -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-6">Détails du matériel</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700">Titre</label>
                <p class="mt-1 text-sm text-gray-900">{{ material.title }}</p>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">Type</label>
                <span :class="[
                  'mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                  getTypeColor(material.type)
                ]">
                  {{ getTypeLabel(material.type) }}
                </span>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">Statut</label>
                <span :class="[
                  'mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                  material.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                ]">
                  {{ material.active ? 'Actif' : 'Inactif' }}
                </span>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">Ordre d'affichage</label>
                <p class="mt-1 text-sm text-gray-900">{{ material.order }}</p>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">Téléchargement autorisé</label>
                <p class="mt-1 text-sm text-gray-900">{{ material.allow_download ? 'Oui' : 'Non' }}</p>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">Visualisation en ligne autorisée</label>
                <p class="mt-1 text-sm text-gray-900">{{ material.allow_online_viewing ? 'Oui' : 'Non' }}</p>
              </div>
            </div>
            
            <div class="mt-6" v-if="material.description">
              <label class="block text-sm font-medium text-gray-700">Description</label>
              <p class="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{{ material.description }}</p>
            </div>

            <!-- Contenu spécifique par type -->
            <div class="mt-6">
              <label class="block text-sm font-medium text-gray-700 mb-3">Contenu</label>
              
              <!-- Texte -->
              <div v-if="material.type === 'text' && material.content" class="bg-gray-50 p-4 rounded-lg">
                <div class="prose prose-sm max-w-none whitespace-pre-wrap">{{ material.content }}</div>
              </div>

              <!-- Vidéo intégrée -->
              <div v-else-if="material.type === 'embed_video' && material.embed_code" class="aspect-video">
                <div v-html="material.embed_code" class="w-full h-full"></div>
              </div>

              <!-- Fichier -->
              <div v-else-if="material.file_path" class="flex items-center space-x-4">
                <div class="flex items-center">
                  <component :is="getTypeIcon(material.type)" class="h-8 w-8 mr-3" :class="getTypeIconColor(material.type)" />
                  <div>
                    <p class="font-medium">{{ material.title }}</p>
                    <p class="text-sm text-gray-500">
                      {{ material.mime_type }} 
                      <span v-if="material.file_size">({{ formatFileSize(material.file_size * 1024) }})</span>
                    </p>
                  </div>
                </div>
                
                <div class="flex space-x-2">
                  <button
                    v-if="material.allow_online_viewing && ['pdf', 'image'].includes(material.type)"
                    @click="viewMaterial(material)"
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <EyeIcon class="h-4 w-4 mr-1" />
                    Visualiser
                  </button>
                  
                  <button
                    v-if="material.allow_download"
                    @click="downloadMaterial(material)"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <ArrowDownTrayIcon class="h-4 w-4 mr-1" />
                    Télécharger
                  </button>
                </div>
              </div>

              <div v-else class="text-gray-500 italic">
                Aucun contenu disponible pour ce matériel.
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
              <Link
                :href="route('admin.first-aid-tip-materials.edit', material.id)"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Modifier ce matériel
              </Link>
              <button
                @click="deleteMaterial"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
              >
                Supprimer ce matériel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { 
  HeartIcon,
  EyeIcon, 
  ArrowDownTrayIcon,
  DocumentIcon,
  DocumentTextIcon,
  VideoCameraIcon,
  PhotoIcon,
  SpeakerWaveIcon,
  ArchiveBoxIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  material: Object,
});

// Méthodes
const getTypeLabel = (type) => {
  const labels = {
    text: 'Texte',
    pdf: 'PDF',
    video: 'Vidéo',
    audio: 'Audio',
    image: 'Image',
    archive: 'Archive',
    gallery: 'Galerie',
    embed_video: 'Vidéo externe'
  };
  return labels[type] || type;
};

const getTypeColor = (type) => {
  const colors = {
    text: 'bg-gray-100 text-gray-800',
    pdf: 'bg-red-100 text-red-800',
    video: 'bg-blue-100 text-blue-800',
    audio: 'bg-green-100 text-green-800',
    image: 'bg-yellow-100 text-yellow-800',
    archive: 'bg-purple-100 text-purple-800',
    gallery: 'bg-pink-100 text-pink-800',
    embed_video: 'bg-indigo-100 text-indigo-800'
  };
  return colors[type] || 'bg-gray-100 text-gray-800';
};

const getTypeIcon = (type) => {
  const icons = {
    text: DocumentTextIcon,
    pdf: DocumentIcon,
    video: VideoCameraIcon,
    audio: SpeakerWaveIcon,
    image: PhotoIcon,
    archive: ArchiveBoxIcon,
    gallery: PhotoIcon,
    embed_video: VideoCameraIcon
  };
  return icons[type] || DocumentIcon;
};

const getTypeIconColor = (type) => {
  const colors = {
    text: 'text-gray-500',
    pdf: 'text-red-500',
    video: 'text-blue-500',
    audio: 'text-green-500',
    image: 'text-yellow-500',
    archive: 'text-purple-500',
    gallery: 'text-pink-500',
    embed_video: 'text-indigo-500'
  };
  return colors[type] || 'text-gray-500';
};

const formatFileSize = (bytes) => {
  if (bytes < 1024) return bytes + ' octets';
  else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' Ko';
  else return (bytes / 1048576).toFixed(1) + ' Mo';
};

const viewMaterial = (material) => {
  window.open(route('admin.first-aid-tip-materials.view', material.id), '_blank');
};

const downloadMaterial = (material) => {
  window.location.href = route('admin.first-aid-tip-materials.download', material.id);
};

const deleteMaterial = () => {
  if (confirm('Êtes-vous sûr de vouloir supprimer ce matériel ?')) {
    router.delete(route('admin.first-aid-tip-materials.destroy', props.material.id));
  }
};
</script>
