<template>
  <Head title="Questions d'examen" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Questions pour l'examen: {{ exam.title }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- En-tête avec boutons d'action -->
            <div class="flex justify-between items-center mb-6">
              <div>
                <Link :href="route('admin.exams.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  Retour aux examens
                </Link>
              </div>
              <div>
                <Link :href="route('admin.exam-questions.create', { exam_id: exam.id })" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  Ajouter une question
                </Link>
              </div>
            </div>

            <!-- Informations sur l'examen -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 class="text-lg font-semibold mb-2">Informations sur l'examen</h3>
                  <p><span class="font-medium">Session de formation:</span> {{ exam.training_session.title }}</p>
                  <p><span class="font-medium">Durée:</span> {{ exam.duration_minutes }} minutes</p>
                  <p><span class="font-medium">Score de réussite:</span> {{ exam.passing_score }}%</p>
                </div>
              </div>
            </div>

            <!-- Liste des questions -->
            <div v-if="questions.length > 0">
              <h3 class="text-lg font-semibold mb-4">Questions ({{ questions.length }})</h3>
              <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200">
                  <thead>
                    <tr>
                      <th class="py-2 px-4 border-b text-left">Ordre</th>
                      <th class="py-2 px-4 border-b text-left">Question</th>
                      <th class="py-2 px-4 border-b text-left">Type</th>
                      <th class="py-2 px-4 border-b text-left">Points</th>
                      <th class="py-2 px-4 border-b text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="question in questions" :key="question.id" class="hover:bg-gray-50">
                      <td class="py-2 px-4 border-b">{{ question.order }}</td>
                      <td class="py-2 px-4 border-b">
                        <div class="truncate max-w-md" :title="question.question_text">
                          {{ question.question_text }}
                        </div>
                      </td>
                      <td class="py-2 px-4 border-b">
                        <span v-if="question.question_type === 'multiple_choice'" class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">QCM</span>
                        <span v-else-if="question.question_type === 'text'" class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Texte</span>
                        <span v-else-if="question.question_type === 'file'" class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">Fichier</span>
                      </td>
                      <td class="py-2 px-4 border-b">{{ question.points }}</td>
                      <td class="py-2 px-4 border-b text-center">
                        <div class="flex justify-center space-x-2">
                          <Link :href="route('admin.exam-questions.edit', question.id)" class="text-indigo-600 hover:text-indigo-900">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </Link>
                          <button @click="confirmDelete(question)" class="text-red-600 hover:text-red-900">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div v-else class="bg-yellow-50 p-4 rounded-lg">
              <p class="text-yellow-700">Aucune question n'a encore été ajoutée à cet examen.</p>
              <p class="text-yellow-700 mt-2">Cliquez sur "Ajouter une question" pour commencer à créer des questions.</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmation de suppression -->
    <Modal :show="confirmingDeletion" @close="closeModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">
          Êtes-vous sûr de vouloir supprimer cette question ?
        </h2>
        <p class="mt-1 text-sm text-gray-600">
          Cette action est irréversible.
        </p>
        <div class="mt-6 flex justify-end">
          <SecondaryButton @click="closeModal" class="mr-2">
            Annuler
          </SecondaryButton>
          <DangerButton @click="deleteQuestion" :class="{ 'opacity-25': processing }" :disabled="processing">
            Supprimer
          </DangerButton>
        </div>
      </div>
    </Modal>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3';
import { ref } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';

// Props
const props = defineProps({
  exam: Object,
  questions: Array,
});

// État pour la suppression
const confirmingDeletion = ref(false);
const processing = ref(false);
const questionToDelete = ref(null);

// Méthodes
const confirmDelete = (question) => {
  questionToDelete.value = question;
  confirmingDeletion.value = true;
};

const closeModal = () => {
  confirmingDeletion.value = false;
  processing.value = false;
  setTimeout(() => {
    questionToDelete.value = null;
  }, 200);
};

const deleteQuestion = () => {
  if (!questionToDelete.value) return;
  
  processing.value = true;
  
  router.delete(route('admin.exam-questions.destroy', questionToDelete.value.id), {
    onSuccess: () => closeModal(),
    onError: () => processing.value = false,
  });
};
</script>
