<template>
  <Head title="Questions d'examen" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Questions pour l'examen: {{ exam.title }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Informations sur l'examen -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-lg font-semibold text-gray-800">{{ exam.title }}</h3>
                <p class="text-sm text-gray-600 mt-1">{{ exam.description }}</p>
                <div class="mt-2 flex flex-wrap gap-2">
                  <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {{ exam.duration_minutes }} minutes
                  </span>
                  <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                    Score de réussite: {{ exam.passing_score }}%
                  </span>
                </div>
              </div>
              <div class="flex space-x-2">
                <Link :href="route('admin.exams.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  Retour aux examens
                </Link>
                <Link :href="route('admin.exam-questions.create', { exam_id: exam.id })" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  Ajouter une question
                </Link>
              </div>
            </div>
          </div>
        </div>

        <!-- Liste des questions -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
              </svg>
              Questions ({{ exam.questions.length }})
            </h3>

            <div v-if="exam.questions.length > 0" class="space-y-6">
              <div
                v-for="(question, index) in exam.questions"
                :key="question.id"
                class="border rounded-lg overflow-hidden transition-all duration-200 hover:shadow-md"
              >
                <!-- En-tête de la question -->
                <div class="bg-gray-50 p-4 border-b flex justify-between items-center">
                  <div class="flex items-center">
                    <div class="bg-indigo-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 font-semibold">
                      {{ index + 1 }}
                    </div>
                    <div>
                      <div class="font-medium">Question {{ index + 1 }}</div>
                      <div class="flex items-center mt-1 space-x-2">
                        <span
                          :class="{
                            'px-2 py-1 rounded-full text-xs': true,
                            'bg-purple-100 text-purple-800': question.question_type === 'multiple_choice',
                            'bg-green-100 text-green-800': question.question_type === 'text',
                            'bg-blue-100 text-blue-800': question.question_type === 'file'
                          }"
                        >
                          {{ getQuestionTypeLabel(question.question_type) }}
                        </span>
                        <span class="text-sm text-gray-500">{{ question.points }} point{{ question.points > 1 ? 's' : '' }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Corps de la question -->
                <div class="p-4">
                  <div class="text-gray-800 mb-4">{{ question.question_text }}</div>

                  <!-- Affichage des options pour les QCM -->
                  <div v-if="question.question_type === 'multiple_choice'" class="mt-3">
                    <div class="text-sm font-medium text-gray-700 mb-2">Options:</div>

                    <div class="space-y-2">
                      <div
                        v-for="(option, optionKey) in getOptions(question.options)"
                        :key="optionKey"
                        :class="{
                          'p-3 rounded-md flex items-start': true,
                          'bg-green-50 border border-green-200': isCorrectOption(question.correct_options, optionKey),
                          'bg-gray-50 border border-gray-200': !isCorrectOption(question.correct_options, optionKey)
                        }"
                      >
                        <div
                          :class="{
                            'w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0': true,
                            'bg-green-500 text-white': isCorrectOption(question.correct_options, optionKey),
                            'bg-gray-300 text-gray-700': !isCorrectOption(question.correct_options, optionKey)
                          }"
                        >
                          {{ optionKey }}
                        </div>
                        <div class="flex-grow">{{ option }}</div>
                        <div v-if="isCorrectOption(question.correct_options, optionKey)" class="text-green-600 flex items-center ml-2">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                          </svg>
                          <span class="text-sm font-medium">Correcte</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Affichage de la réponse pour les questions à texte libre -->
                  <div v-if="question.question_type === 'text'" class="mt-3">
                    <div class="text-sm font-medium text-gray-700 mb-2">Réponse attendue:</div>
                    <div class="p-3 bg-green-50 border border-green-200 rounded-md text-gray-800">
                      {{ question.correct_answer || 'Aucune réponse spécifique attendue' }}
                    </div>
                  </div>

                  <!-- Affichage des informations pour les questions de type fichier -->
                  <div v-if="question.question_type === 'file'" class="mt-3">
                    <div class="text-sm font-medium text-gray-700 mb-2">Type de fichier attendu:</div>
                    <div class="p-3 bg-blue-50 border border-blue-200 rounded-md text-gray-800 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clip-rule="evenodd" />
                      </svg>
                      Tous types de fichiers
                    </div>
                  </div>

                  <!-- Explication (si disponible) -->
                  <div v-if="question.explanation" class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <div class="text-sm font-medium text-gray-700 mb-1 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                      </svg>
                      Explication:
                    </div>
                    <div class="text-gray-800">{{ question.explanation }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="bg-gray-50 p-8 text-center rounded-lg border border-dashed border-gray-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              <p class="text-gray-500 text-lg">Aucune question n'a encore été ajoutée à cet examen.</p>
              <Link :href="route('admin.exam-questions.create', { exam_id: exam.id })" class="mt-4 inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Ajouter une question
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  exam: Object,
});

// Corriger les données JSON si nécessaire
if (props.exam && props.exam.questions) {
  props.exam.questions.forEach(question => {
    if (question.question_type === 'multiple_choice' || question.question_type === 'single_choice') {
      // Corriger les options
      if (typeof question.options === 'string' && question.options !== '[object Object]') {
        try {
          question.options = JSON.parse(question.options);
        } catch (e) {
          console.error('Erreur lors du parsing des options:', e);
          question.options = {
            'A': 'Option A',
            'B': 'Option B',
            'C': 'Option C',
            'D': 'Option D'
          };
        }
      } else if (!question.options || question.options === '[object Object]') {
        question.options = {
          'A': 'Option A',
          'B': 'Option B',
          'C': 'Option C',
          'D': 'Option D'
        };
      }

      // Corriger les options correctes
      if (typeof question.correct_options === 'string' && question.correct_options !== '[object Object]') {
        try {
          question.correct_options = JSON.parse(question.correct_options);
          console.log('Parsed correct_options:', question.correct_options);
        } catch (e) {
          console.error('Erreur lors du parsing des options correctes:', e);
          // Ne pas définir d'option par défaut, utiliser un tableau vide
          question.correct_options = [];
        }
      } else if (!question.correct_options || question.correct_options === '[object Object]') {
        // Ne pas définir d'option par défaut, utiliser un tableau vide
        question.correct_options = [];
      }
    }
  });
}

// Méthodes utilitaires
const getQuestionTypeLabel = (type) => {
  switch (type) {
    case 'multiple_choice':
      return 'Choix multiple';
    case 'single_choice':
      return 'Choix unique';
    case 'text':
      return 'Texte libre';
    case 'file':
      return 'Fichier';
    default:
      return type;
  }
};

const getOptions = (options) => {
  // Si aucune option n'est fournie, créer des options par défaut
  if (!options) {
    return {
      'A': 'Option A',
      'B': 'Option B',
      'C': 'Option C',
      'D': 'Option D'
    };
  }

  // Si les options sont une chaîne, essayer de les parser en JSON
  if (typeof options === 'string') {
    try {
      return JSON.parse(options);
    } catch (e) {
      console.error('Erreur de parsing des options:', e);
      return {
        'A': 'Option A',
        'B': 'Option B',
        'C': 'Option C',
        'D': 'Option D'
      };
    }
  }

  // Si les options sont déjà un objet, les retourner directement
  if (typeof options === 'object' && !Array.isArray(options)) {
    // Vérifier si l'objet est vide
    if (Object.keys(options).length === 0) {
      return {
        'A': 'Option A',
        'B': 'Option B',
        'C': 'Option C',
        'D': 'Option D'
      };
    }
    return options;
  }

  // Si les options sont un tableau, les convertir en objet
  if (Array.isArray(options)) {
    const optionsObj = {};
    options.forEach((option, index) => {
      const key = String.fromCharCode(65 + index); // A, B, C, ...
      optionsObj[key] = option;
    });
    return optionsObj;
  }

  // Par défaut, retourner des options par défaut
  return {
    'A': 'Option A',
    'B': 'Option B',
    'C': 'Option C',
    'D': 'Option D'
  };
};

const isCorrectOption = (correctOptions, optionKey) => {
  console.log('Checking if option', optionKey, 'is correct among', correctOptions);

  // Si aucune option correcte n'est fournie, retourner false
  if (!correctOptions) return false;

  let parsedCorrectOptions = correctOptions;

  // Si les options correctes sont une chaîne, essayer de les parser en JSON
  if (typeof correctOptions === 'string') {
    try {
      parsedCorrectOptions = JSON.parse(correctOptions);
      console.log('Parsed correctOptions from string:', parsedCorrectOptions);
    } catch (e) {
      console.error('Erreur de parsing des options correctes:', e);
      // Ne pas définir d'option par défaut, retourner false
      return false;
    }
  }

  // Si les options correctes sont un tableau, vérifier si l'option est incluse
  if (Array.isArray(parsedCorrectOptions)) {
    const result = parsedCorrectOptions.includes(optionKey) || parsedCorrectOptions.includes(optionKey.toString());
    console.log('Array check result:', result);
    return result;
  }

  // Si les options correctes sont un objet, vérifier si l'option est une clé avec valeur true
  if (typeof parsedCorrectOptions === 'object' && !Array.isArray(parsedCorrectOptions)) {
    const result = parsedCorrectOptions[optionKey] === true || parsedCorrectOptions[optionKey.toString()] === true;
    console.log('Object check result:', result);
    return result;
  }

  // Si les options correctes sont une valeur simple, vérifier l'égalité
  const result = parsedCorrectOptions === optionKey || parsedCorrectOptions === optionKey.toString();
  console.log('Simple value check result:', result);
  return result;
};
</script>
