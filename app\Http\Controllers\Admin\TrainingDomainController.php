<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TrainingDomain;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class TrainingDomainController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Initialiser la requête
        $query = TrainingDomain::query();

        // Appliquer la recherche si elle existe
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                  ->orWhere('description', 'like', $searchTerm);
            });
        }

        // Appliquer le filtre de statut si il existe
        if ($request->has('status') && !empty($request->status)) {
            $query->where('active', $request->status === 'active');
        }

        // Récupérer les domaines de formation
        $domains = $query->orderBy('name')->get();

        // Retourner la vue avec les domaines de formation et les filtres
        return Inertia::render('Admin/TrainingDomains/Index', [
            'domains' => $domains,
            'filters' => [
                'search' => $request->search ?? '',
                'status' => $request->status ?? '',
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Retourner la vue pour créer un nouveau domaine de formation
        return Inertia::render('Admin/TrainingDomains/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'active' => 'boolean',
        ]);

        // Gérer l'upload de l'image si elle existe
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('training-domains', 'public');
            $validated['image'] = $imagePath;
        }

        // Créer le domaine de formation
        TrainingDomain::create($validated);

        // Rediriger vers la liste des domaines de formation avec un message de succès
        return redirect()->route('admin.training-domains.index')
            ->with('success', 'Domaine de formation créé avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Récupérer le domaine de formation
        $domain = TrainingDomain::findOrFail($id);

        // Récupérer les sessions de formation associées
        $sessions = $domain->trainingSessions;

        // Retourner la vue avec le domaine de formation et ses sessions
        return Inertia::render('Admin/TrainingDomains/Show', [
            'domain' => $domain,
            'sessions' => $sessions
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Récupérer le domaine de formation
        $domain = TrainingDomain::findOrFail($id);

        // Retourner la vue pour éditer le domaine de formation
        return Inertia::render('Admin/TrainingDomains/Edit', [
            'domain' => $domain
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Récupérer le domaine de formation
        $domain = TrainingDomain::findOrFail($id);

        // Valider les données du formulaire
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'active' => 'boolean',
        ]);

        // Gérer l'upload de l'image si elle existe
        if ($request->hasFile('image')) {
            // Supprimer l'ancienne image si elle existe
            if ($domain->image) {
                Storage::disk('public')->delete($domain->image);
            }

            $imagePath = $request->file('image')->store('training-domains', 'public');
            $validated['image'] = $imagePath;
        }

        // Mettre à jour le domaine de formation
        $domain->update($validated);

        // Rediriger vers la liste des domaines de formation avec un message de succès
        return redirect()->route('admin.training-domains.index')
            ->with('success', 'Domaine de formation mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Récupérer le domaine de formation
        $domain = TrainingDomain::findOrFail($id);

        // Vérifier s'il y a des sessions de formation associées
        if ($domain->trainingSessions()->count() > 0) {
            return redirect()->route('admin.training-domains.index')
                ->with('error', 'Impossible de supprimer ce domaine car il contient des sessions de formation.');
        }

        // Supprimer l'image si elle existe
        if ($domain->image) {
            Storage::disk('public')->delete($domain->image);
        }

        // Supprimer le domaine de formation
        $domain->delete();

        // Rediriger vers la liste des domaines de formation avec un message de succès
        return redirect()->route('admin.training-domains.index')
            ->with('success', 'Domaine de formation supprimé avec succès.');
    }
}
