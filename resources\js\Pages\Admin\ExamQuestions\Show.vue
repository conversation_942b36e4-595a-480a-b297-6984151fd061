<template>
  <Head title="Détails de la question" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Détails de la question
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link :href="route('admin.exam-questions.index', { exam_id: question.exam_id })" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour aux questions
              </Link>
            </div>

            <!-- Détails de la question -->
            <div class="bg-gray-50 p-6 rounded-lg mb-6">
              <h3 class="text-lg font-semibold mb-4">Informations sur la question</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <p class="text-sm text-gray-600">Type de question</p>
                  <p class="font-medium">
                    <span v-if="question.question_type === 'multiple_choice'" class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">QCM</span>
                    <span v-else-if="question.question_type === 'text'" class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Texte</span>
                    <span v-else-if="question.question_type === 'file'" class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">Fichier</span>
                  </p>
                </div>
                
                <div>
                  <p class="text-sm text-gray-600">Points</p>
                  <p class="font-medium">{{ question.points }}</p>
                </div>
                
                <div>
                  <p class="text-sm text-gray-600">Ordre</p>
                  <p class="font-medium">{{ question.order }}</p>
                </div>
              </div>
              
              <div class="mb-6">
                <p class="text-sm text-gray-600 mb-1">Question</p>
                <div class="p-3 bg-white rounded border">
                  {{ question.question_text }}
                </div>
              </div>
              
              <!-- Options pour QCM -->
              <div v-if="question.question_type === 'multiple_choice' && options">
                <p class="text-sm text-gray-600 mb-2">Options de réponse</p>
                <div class="space-y-2">
                  <div v-for="(value, key) in options" :key="key" class="flex items-center p-2 rounded" :class="{'bg-green-100': key === question.correct_answer, 'bg-white border': key !== question.correct_answer}">
                    <div class="w-8 h-8 flex items-center justify-center rounded-full mr-3" :class="{'bg-green-500 text-white': key === question.correct_answer, 'bg-gray-200': key !== question.correct_answer}">
                      {{ key }}
                    </div>
                    <div>{{ value }}</div>
                    <div v-if="key === question.correct_answer" class="ml-auto text-green-600 font-medium text-sm">
                      Réponse correcte
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Boutons d'action -->
            <div class="flex justify-end space-x-3">
              <Link :href="route('admin.exam-questions.edit', question.id)" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 0L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Modifier
              </Link>
              <button @click="confirmDelete" class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Supprimer
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmation de suppression -->
    <Modal :show="confirmingDeletion" @close="closeModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">
          Êtes-vous sûr de vouloir supprimer cette question ?
        </h2>
        <p class="mt-1 text-sm text-gray-600">
          Cette action est irréversible.
        </p>
        <div class="mt-6 flex justify-end">
          <SecondaryButton @click="closeModal" class="mr-2">
            Annuler
          </SecondaryButton>
          <DangerButton @click="deleteQuestion" :class="{ 'opacity-25': processing }" :disabled="processing">
            Supprimer
          </DangerButton>
        </div>
      </div>
    </Modal>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';

// Props
const props = defineProps({
  question: Object,
  exam: Object,
});

// État pour la suppression
const confirmingDeletion = ref(false);
const processing = ref(false);

// Computed
const options = computed(() => {
  if (props.question.question_type === 'multiple_choice' && props.question.options) {
    return typeof props.question.options === 'string' 
      ? JSON.parse(props.question.options) 
      : props.question.options;
  }
  return null;
});

// Méthodes
const confirmDelete = () => {
  confirmingDeletion.value = true;
};

const closeModal = () => {
  confirmingDeletion.value = false;
  processing.value = false;
};

const deleteQuestion = () => {
  processing.value = true;
  
  router.delete(route('admin.exam-questions.destroy', props.question.id), {
    onSuccess: () => {
      router.visit(route('admin.exam-questions.index', { exam_id: props.question.exam_id }));
    },
    onError: () => {
      processing.value = false;
    },
  });
};
</script>
