<template>
  <Head title="Créer une évaluation" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Créer une nouvelle évaluation
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Liens de navigation -->
            <div class="mb-6">
              <Link :href="route('trainer.evaluations.index')" class="text-indigo-600 hover:text-indigo-900">
                &larr; Retour aux évaluations
              </Link>
            </div>

            <!-- Formulaire de création d'évaluation -->
            <form @submit.prevent="submit">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Titre -->
                <div class="md:col-span-2">
                  <InputLabel for="title" value="Titre de l'évaluation" />
                  <TextInput
                    id="title"
                    v-model="form.title"
                    type="text"
                    class="mt-1 block w-full"
                    required
                    autofocus
                  />
                  <InputError class="mt-2" :message="form.errors.title" />
                </div>

                <!-- Description -->
                <div class="md:col-span-2">
                  <InputLabel for="description" value="Description" />
                  <textarea
                    id="description"
                    v-model="form.description"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    rows="3"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.description" />
                </div>

                <!-- Session de formation -->
                <div>
                  <InputLabel for="training_session_id" value="Session de formation" />
                  <select
                    id="training_session_id"
                    v-model="form.training_session_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    required
                    @change="updateCourseOptions"
                  >
                    <option value="">Sélectionnez une session</option>
                    <option v-for="session in trainingSessions" :key="session.id" :value="session.id">
                      {{ session.title }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.training_session_id" />
                </div>

                <!-- Cours (optionnel) -->
                <div>
                  <InputLabel for="course_id" value="Cours (optionnel)" />
                  <select
                    id="course_id"
                    v-model="form.course_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    :disabled="!form.training_session_id"
                  >
                    <option value="">Aucun cours spécifique</option>
                    <option v-for="course in availableCourses" :key="course.id" :value="course.id">
                      {{ course.title }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.course_id" />
                </div>

                <!-- Type d'évaluation -->
                <div>
                  <InputLabel for="type" value="Type d'évaluation" />
                  <select
                    id="type"
                    v-model="form.type"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    required
                  >
                    <option value="">Sélectionnez un type</option>
                    <option v-for="type in evaluationTypes" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.type" />
                </div>

                <!-- Statut -->
                <div>
                  <InputLabel for="status" value="Statut" />
                  <select
                    id="status"
                    v-model="form.status"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    required
                  >
                    <option value="draft">Brouillon</option>
                    <option value="active">Active</option>
                    <option value="closed">Fermée</option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.status" />
                </div>

                <!-- Date de début -->
                <div>
                  <InputLabel for="start_date" value="Date de début" />
                  <TextInput
                    id="start_date"
                    v-model="form.start_date"
                    type="datetime-local"
                    class="mt-1 block w-full"
                  />
                  <InputError class="mt-2" :message="form.errors.start_date" />
                </div>

                <!-- Date de fin -->
                <div>
                  <InputLabel for="end_date" value="Date de fin" />
                  <TextInput
                    id="end_date"
                    v-model="form.end_date"
                    type="datetime-local"
                    class="mt-1 block w-full"
                  />
                  <InputError class="mt-2" :message="form.errors.end_date" />
                </div>

                <!-- Anonyme -->
                <div class="md:col-span-2">
                  <div class="flex items-center">
                    <input
                      id="is_anonymous"
                      v-model="form.is_anonymous"
                      type="checkbox"
                      class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                    />
                    <label for="is_anonymous" class="ml-2 text-sm text-gray-600">
                      Réponses anonymes
                    </label>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">
                    Si cette option est activée, les réponses des étudiants seront anonymes.
                  </p>
                  <InputError class="mt-2" :message="form.errors.is_anonymous" />
                </div>

                <!-- Boutons de soumission -->
                <div class="md:col-span-2 flex items-center justify-end mt-4">
                  <Link
                    :href="route('trainer.evaluations.index')"
                    class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-4"
                  >
                    Annuler
                  </Link>
                  <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    Créer l'évaluation
                  </PrimaryButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  trainingSessions: Array,
  courses: Array,
  evaluationTypes: Array,
});

// Formulaire
const form = useForm({
  title: '',
  description: '',
  training_session_id: '',
  course_id: '',
  type: '',
  status: 'draft',
  start_date: '',
  end_date: '',
  is_anonymous: false,
});

// Computed
const availableCourses = computed(() => {
  if (!form.training_session_id) {
    return [];
  }
  
  return props.courses.filter(course => course.training_session_id === parseInt(form.training_session_id));
});

// Méthodes
const updateCourseOptions = () => {
  // Réinitialiser le cours sélectionné si la session change
  form.course_id = '';
};

const submit = () => {
  form.post(route('trainer.evaluations.store'));
};
</script>
