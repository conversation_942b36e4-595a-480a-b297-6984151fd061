<template>
  <Head title="Créer un certificat" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Créer un certificat
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link :href="route('admin.certificates.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à la liste
              </Link>
            </div>

            <!-- Formulaire de création de certificat -->
            <form @submit.prevent="submit" enctype="multipart/form-data">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Inscription -->
                <div>
                  <InputLabel for="enrollment_id" value="Apprenant et formation" />
                  <select
                    id="enrollment_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.enrollment_id"
                    required
                  >
                    <option value="">Sélectionnez un apprenant et une formation</option>
                    <option v-for="enrollment in enrollments" :key="enrollment.id" :value="enrollment.id">
                      {{ enrollment.user.name }} - {{ enrollment.training_session.title }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.enrollment_id" />
                </div>

                <!-- Date d'émission -->
                <div>
                  <InputLabel for="issued_at" value="Date d'émission" />
                  <TextInput
                    id="issued_at"
                    type="date"
                    class="mt-1 block w-full"
                    v-model="form.issued_at"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.issued_at" />
                </div>

                <!-- Date d'expiration -->
                <div>
                  <InputLabel for="expiry_date" value="Date d'expiration (optionnelle)" />
                  <TextInput
                    id="expiry_date"
                    type="date"
                    class="mt-1 block w-full"
                    v-model="form.expiry_date"
                  />
                  <InputError class="mt-2" :message="form.errors.expiry_date" />
                </div>

                <!-- Statut -->
                <div>
                  <InputLabel for="status" value="Statut" />
                  <select
                    id="status"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.status"
                    required
                  >
                    <option value="draft">Brouillon</option>
                    <option value="issued">Émis</option>
                    <option value="revoked">Révoqué</option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.status" />
                </div>

                <!-- Image de signature -->
                <div class="md:col-span-2">
                  <InputLabel for="signature_image" value="Image de signature (optionnelle)" />
                  <input
                    id="signature_image"
                    type="file"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
                    @input="form.signature_image = $event.target.files[0]"
                  />
                  <p class="text-sm text-gray-500 mt-1">Format accepté: JPEG, PNG, JPG, GIF. Taille maximale: 2Mo.</p>
                  <InputError class="mt-2" :message="form.errors.signature_image" />
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="flex items-center justify-end mt-6">
                <PrimaryButton class="ml-4" :disabled="form.processing">
                  Créer le certificat
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  enrollments: Array,
});

// Formulaire
const form = useForm({
  enrollment_id: '',
  issued_at: new Date().toISOString().split('T')[0],
  expiry_date: '',
  status: 'issued',
  signature_image: null,
});

// Méthodes
const submit = () => {
  form.post(route('admin.certificates.store'), {
    forceFormData: true,
  });
};
</script>
