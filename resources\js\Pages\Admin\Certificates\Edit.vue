<template>
  <Head title="Modifier un certificat" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Modifier un certificat
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link :href="route('admin.certificates.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à la liste
              </Link>
            </div>

            <!-- Informations sur le certificat -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 class="text-lg font-semibold mb-2">Informations sur le certificat</h3>
                  <p><span class="font-medium">Numéro de certificat:</span> {{ certificate.certificate_number }}</p>
                  <p><span class="font-medium">Apprenant:</span> {{ certificate.enrollment.user.name }}</p>
                  <p><span class="font-medium">Formation:</span> {{ certificate.enrollment.training_session.title }}</p>
                </div>
              </div>
            </div>

            <!-- Formulaire de modification de certificat -->
            <form @submit.prevent="submit" enctype="multipart/form-data">
              <!-- Utilisation du formulaire Inertia qui gère automatiquement le CSRF -->

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Date d'émission -->
                <div>
                  <InputLabel for="issued_at" value="Date d'émission" />
                  <input
                    id="issued_at"
                    type="date"
                    v-model="form.issued_at"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.issued_at" />
                </div>

                <!-- Date d'expiration -->
                <div>
                  <InputLabel for="expiry_date" value="Date d'expiration (optionnelle)" />
                  <input
                    id="expiry_date"
                    type="date"
                    v-model="form.expiry_date"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  />
                  <p class="text-xs text-gray-500 mt-1">Format: AAAA-MM-JJ</p>
                  <InputError class="mt-2" :message="form.errors.expiry_date" />
                </div>

                <!-- Statut -->
                <div>
                  <InputLabel for="status" value="Statut" />
                  <select
                    id="status"
                    v-model="form.status"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    required
                  >
                    <option value="draft">Brouillon</option>
                    <option value="issued">Émis</option>
                    <option value="revoked">Révoqué</option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.status" />
                </div>

                <!-- Image de signature actuelle et nouvelle image -->
                <div>
                  <InputLabel for="signature_image" value="Image de signature" />
                  <div v-if="certificate.signature_image" class="mt-2 mb-2">
                    <p class="text-sm text-gray-600 mb-1">Image de signature actuelle :</p>
                    <img :src="'/storage/' + certificate.signature_image" alt="Signature" class="h-16 w-auto object-contain rounded">
                  </div>
                  <input
                    id="signature_image"
                    type="file"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
                    accept="image/*"
                    @input="form.signature_image = $event.target.files[0]"
                  />
                  <p class="text-sm text-gray-500 mt-1">Laissez vide pour conserver l'image actuelle. Format accepté: JPEG, PNG, JPG, GIF. Taille maximale: 2Mo.</p>
                  <InputError class="mt-2" :message="form.errors.signature_image" />
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="flex items-center justify-end mt-6">
                <PrimaryButton class="ml-4" :disabled="form.processing">
                  Mettre à jour le certificat
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import axios from 'axios';

// Props
const props = defineProps({
  certificate: Object,
});

// Formatage des dates pour les champs date
const formatDateForInput = (dateString) => {
  if (!dateString) return '';

  // Si la date est déjà au format YYYY-MM-DD, la retourner telle quelle
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    return dateString;
  }

  // Sinon, convertir la date
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    return ''; // Retourner une chaîne vide si la date est invalide
  }

  return date.toISOString().split('T')[0]; // Format YYYY-MM-DD pour l'input type="date"
};

// Afficher les valeurs des dates pour le débogage
console.log('Date d\'émission originale:', props.certificate.issued_at);
console.log('Date d\'expiration originale:', props.certificate.expiry_date);
console.log('Date d\'émission formatée:', formatDateForInput(props.certificate.issued_at));
console.log('Date d\'expiration formatée:', formatDateForInput(props.certificate.expiry_date));

// Afficher le type de données pour le débogage
console.log('Type de issued_at:', typeof props.certificate.issued_at);
console.log('Type de expiry_date:', typeof props.certificate.expiry_date);

// Formulaire
const form = useForm({
  issued_at: props.certificate.formatted_issued_at || formatDateForInput(props.certificate.issued_at),
  expiry_date: props.certificate.formatted_expiry_date || formatDateForInput(props.certificate.expiry_date),
  status: props.certificate.status,
  signature_image: null,
});

// Méthodes
const submit = () => {
  // Afficher les valeurs des dates avant soumission
  console.log('Date d\'émission avant soumission:', form.issued_at);
  console.log('Date d\'expiration avant soumission:', form.expiry_date);
  console.log('Statut avant soumission:', form.status);

  // Vérifier si les dates sont valides
  if (form.expiry_date && new Date(form.expiry_date) <= new Date(form.issued_at)) {
    alert('La date d\'expiration doit être postérieure à la date d\'émission.');
    return;
  }

  // Utiliser le formulaire Inertia pour l'envoi
  form.put(route('admin.certificates.update', props.certificate.id), {
    onSuccess: () => {
      console.log('Certificat mis à jour avec succès');
    },
    onError: (errors) => {
      console.error('Erreurs de validation:', errors);
    }
  });
};
</script>
