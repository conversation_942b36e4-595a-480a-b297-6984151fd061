<template>
  <Head title="Ajouter un module" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Ajouter un module au cours: {{ course.title }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Liens de navigation -->
            <div class="mb-6">
              <Link :href="route('admin.modules.index', { course_id: course.id })" class="text-indigo-600 hover:text-indigo-900">
                &larr; Retour aux modules
              </Link>
            </div>

            <!-- Formulaire de création de module -->
            <form @submit.prevent="submit">
              <div class="grid grid-cols-1 gap-6">
                <!-- Titre -->
                <div>
                  <InputLabel for="title" value="Titre du module" />
                  <TextInput
                    id="title"
                    v-model="form.title"
                    type="text"
                    class="mt-1 block w-full"
                    required
                    autofocus
                  />
                  <InputError class="mt-2" :message="form.errors.title" />
                </div>

                <!-- Description -->
                <div>
                  <InputLabel for="description" value="Description" />
                  <textarea
                    id="description"
                    v-model="form.description"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    rows="3"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.description" />
                </div>

                <!-- Ordre -->
                <div>
                  <InputLabel for="order" value="Ordre d'affichage" />
                  <TextInput
                    id="order"
                    v-model="form.order"
                    type="number"
                    class="mt-1 block w-full"
                    min="1"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.order" />
                </div>

                <!-- Publication -->
                <div>
                  <div class="flex items-center">
                    <input
                      id="is_published"
                      v-model="form.is_published"
                      type="checkbox"
                      class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                    />
                    <InputLabel for="is_published" value="Publier le module" class="ml-2" />
                  </div>
                  <InputError class="mt-2" :message="form.errors.is_published" />
                </div>

                <!-- Date de publication -->
                <div>
                  <InputLabel for="publish_date" value="Date de publication" />
                  <TextInput
                    id="publish_date"
                    v-model="form.publish_date"
                    type="date"
                    class="mt-1 block w-full"
                    :disabled="!form.is_published"
                  />
                  <InputError class="mt-2" :message="form.errors.publish_date" />
                </div>

                <!-- Boutons de soumission -->
                <div class="flex items-center justify-end mt-4">
                  <Link
                    :href="route('admin.modules.index', { course_id: course.id })"
                    class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-4"
                  >
                    Annuler
                  </Link>
                  <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    Créer le module
                  </PrimaryButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  course: Object,
  nextOrder: Number,
});

// Formulaire
const form = useForm({
  course_id: props.course.id,
  title: '',
  description: '',
  order: props.nextOrder,
  is_published: false,
  publish_date: '',
});

// Méthodes
const submit = () => {
  form.post(route('admin.modules.store'));
};
</script>
