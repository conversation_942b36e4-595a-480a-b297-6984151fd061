<template>
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Département Secourisme -->
    <Link 
      :href="route('student.departments.show', 'Secourisme')"
      class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
    >
      <div class="bg-gradient-to-r from-red-500 to-red-700 p-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="bg-white/20 rounded-lg p-3">
              <HeartIcon class="w-8 h-8 text-white" />
            </div>
            <div class="ml-4">
              <h3 class="text-xl font-bold text-white">Secourisme</h3>
              <p class="text-red-100 text-sm">Premiers secours</p>
            </div>
          </div>
          <ArrowRightIcon class="w-5 h-5 text-white/70 group-hover:text-white group-hover:translate-x-1 transition-all duration-200" />
        </div>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">{{ getDepartmentStats('Secourisme').total }}</div>
            <div class="text-xs text-gray-600">Sessions</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ getDepartmentStats('Secourisme').available }}</div>
            <div class="text-xs text-gray-600">Disponibles</div>
          </div>
        </div>
        
        <!-- Progression -->
        <div class="mb-3">
          <div class="flex justify-between items-center mb-1">
            <span class="text-sm font-medium text-gray-700">Progression</span>
            <span class="text-sm text-gray-500">{{ getCompletedLevelsCount('Secourisme') }}/7 niveaux</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div 
              class="bg-red-600 h-2 rounded-full transition-all duration-300" 
              :style="{ width: `${(getCompletedLevelsCount('Secourisme') / 7) * 100}%` }"
            ></div>
          </div>
        </div>

        <!-- Niveaux complétés -->
        <div v-if="completedLevels['Secourisme'] && completedLevels['Secourisme'].length > 0" class="flex flex-wrap gap-1">
          <span 
            v-for="level in completedLevels['Secourisme']" 
            :key="level"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
          >
            <CheckIcon class="w-3 h-3 mr-1" />
            {{ level }}
          </span>
        </div>
        <div v-else class="text-sm text-gray-500 italic">Aucun niveau complété</div>
      </div>
    </Link>

    <!-- Département Langue -->
    <Link 
      :href="route('student.departments.show', 'Langue')"
      class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
    >
      <div class="bg-gradient-to-r from-blue-500 to-blue-700 p-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="bg-white/20 rounded-lg p-3">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-xl font-bold text-white">Langue</h3>
              <p class="text-blue-100 text-sm">Formation linguistique</p>
            </div>
          </div>
          <ArrowRightIcon class="w-5 h-5 text-white/70 group-hover:text-white group-hover:translate-x-1 transition-all duration-200" />
        </div>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">{{ getDepartmentStats('Langue').total }}</div>
            <div class="text-xs text-gray-600">Sessions</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ getDepartmentStats('Langue').available }}</div>
            <div class="text-xs text-gray-600">Disponibles</div>
          </div>
        </div>
        
        <!-- Progression -->
        <div class="mb-3">
          <div class="flex justify-between items-center mb-1">
            <span class="text-sm font-medium text-gray-700">Progression</span>
            <span class="text-sm text-gray-500">{{ getCompletedLevelsCount('Langue') }}/7 niveaux</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div 
              class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
              :style="{ width: `${(getCompletedLevelsCount('Langue') / 7) * 100}%` }"
            ></div>
          </div>
        </div>

        <!-- Niveaux complétés -->
        <div v-if="completedLevels['Langue'] && completedLevels['Langue'].length > 0" class="flex flex-wrap gap-1">
          <span 
            v-for="level in completedLevels['Langue']" 
            :key="level"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
          >
            <CheckIcon class="w-3 h-3 mr-1" />
            {{ level }}
          </span>
        </div>
        <div v-else class="text-sm text-gray-500 italic">Aucun niveau complété</div>
      </div>
    </Link>

    <!-- Département Formation à la carte -->
    <Link 
      :href="route('student.departments.show', 'Formation à la carte')"
      class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
    >
      <div class="bg-gradient-to-r from-green-500 to-green-700 p-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="bg-white/20 rounded-lg p-3">
              <AcademicCapIcon class="w-8 h-8 text-white" />
            </div>
            <div class="ml-4">
              <h3 class="text-xl font-bold text-white">Formation à la carte</h3>
              <p class="text-green-100 text-sm">Formations personnalisées</p>
            </div>
          </div>
          <ArrowRightIcon class="w-5 h-5 text-white/70 group-hover:text-white group-hover:translate-x-1 transition-all duration-200" />
        </div>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">{{ getDepartmentStats('Formation à la carte').total }}</div>
            <div class="text-xs text-gray-600">Sessions</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ getDepartmentStats('Formation à la carte').available }}</div>
            <div class="text-xs text-gray-600">Disponibles</div>
          </div>
        </div>
        
        <!-- Progression -->
        <div class="mb-3">
          <div class="flex justify-between items-center mb-1">
            <span class="text-sm font-medium text-gray-700">Progression</span>
            <span class="text-sm text-gray-500">{{ getCompletedLevelsCount('Formation à la carte') }}/7 niveaux</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div 
              class="bg-green-600 h-2 rounded-full transition-all duration-300" 
              :style="{ width: `${(getCompletedLevelsCount('Formation à la carte') / 7) * 100}%` }"
            ></div>
          </div>
        </div>

        <!-- Niveaux complétés -->
        <div v-if="completedLevels['Formation à la carte'] && completedLevels['Formation à la carte'].length > 0" class="flex flex-wrap gap-1">
          <span 
            v-for="level in completedLevels['Formation à la carte']" 
            :key="level"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
          >
            <CheckIcon class="w-3 h-3 mr-1" />
            {{ level }}
          </span>
        </div>
        <div v-else class="text-sm text-gray-500 italic">Aucun niveau complété</div>
      </div>
    </Link>
  </div>
</template>

<script setup>
import { Link } from '@inertiajs/vue3';
import { 
  HeartIcon, 
  AcademicCapIcon,
  ArrowRightIcon,
  CheckIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  sessionsByDepartment: Object,
  completedLevels: Object,
});

// Méthodes
const getDepartmentStats = (department) => {
  const sessions = props.sessionsByDepartment[department] || {};
  let total = 0;
  let available = 0;

  Object.values(sessions).forEach(levelSessions => {
    if (Array.isArray(levelSessions)) {
      levelSessions.forEach(sessionData => {
        total++;
        if (sessionData.state === 'available') {
          available++;
        }
      });
    }
  });

  return { total, available };
};

const getCompletedLevelsCount = (department) => {
  return props.completedLevels[department]?.length || 0;
};
</script>
