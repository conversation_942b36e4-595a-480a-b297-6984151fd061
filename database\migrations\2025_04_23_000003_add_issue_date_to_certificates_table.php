<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\Certificate;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('certificates', function (Blueprint $table) {
            $table->timestamp('issue_date')->nullable()->after('issued_at');
        });

        // Copier les données de issued_at vers issue_date
        $certificates = Certificate::all();
        foreach ($certificates as $certificate) {
            $certificate->issue_date = $certificate->issued_at;
            $certificate->save();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('certificates', function (Blueprint $table) {
            $table->dropColumn('issue_date');
        });
    }
};
