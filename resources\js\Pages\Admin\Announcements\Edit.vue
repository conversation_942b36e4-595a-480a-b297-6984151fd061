<template>
  <Head :title="`Modifier l'annonce - ${announcement.title}`" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Modifier l'annonce
        </h2>
        <Link :href="route('admin.announcements.index')" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
          Retour à la liste
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <form @submit.prevent="submit">
              <!-- Titre -->
              <div class="mb-4">
                <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Titre</label>
                <input
                  id="title"
                  v-model="form.title"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md"
                  :class="{ 'border-red-500': form.errors.title }"
                  required
                />
                <div v-if="form.errors.title" class="text-red-500 text-sm mt-1">{{ form.errors.title }}</div>
              </div>
              
              <!-- Contenu -->
              <div class="mb-4">
                <label for="content" class="block text-sm font-medium text-gray-700 mb-1">Contenu</label>
                <textarea
                  id="content"
                  v-model="form.content"
                  rows="6"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md"
                  :class="{ 'border-red-500': form.errors.content }"
                  required
                ></textarea>
                <div v-if="form.errors.content" class="text-red-500 text-sm mt-1">{{ form.errors.content }}</div>
              </div>
              
              <!-- Visibilité -->
              <div class="mb-4">
                <label for="visible_to" class="block text-sm font-medium text-gray-700 mb-1">Visibilité</label>
                <select
                  id="visible_to"
                  v-model="form.visible_to"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md"
                  :class="{ 'border-red-500': form.errors.visible_to }"
                  required
                >
                  <option value="all">Tous les utilisateurs</option>
                  <option value="admin">Administrateurs uniquement</option>
                  <option value="trainer">Formateurs uniquement</option>
                  <option value="student">Apprenants uniquement</option>
                </select>
                <div v-if="form.errors.visible_to" class="text-red-500 text-sm mt-1">{{ form.errors.visible_to }}</div>
              </div>
              
              <!-- Session de formation -->
              <div class="mb-4">
                <label for="training_session_id" class="block text-sm font-medium text-gray-700 mb-1">Session de formation (optionnel)</label>
                <select
                  id="training_session_id"
                  v-model="form.training_session_id"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md"
                  :class="{ 'border-red-500': form.errors.training_session_id }"
                >
                  <option :value="null">Annonce générale</option>
                  <option v-for="session in trainingSessions" :key="session.id" :value="session.id">
                    {{ session.title }}
                  </option>
                </select>
                <div v-if="form.errors.training_session_id" class="text-red-500 text-sm mt-1">{{ form.errors.training_session_id }}</div>
              </div>
              
              <!-- Dates -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label for="publish_date" class="block text-sm font-medium text-gray-700 mb-1">Date de publication</label>
                  <input
                    id="publish_date"
                    v-model="form.publish_date"
                    type="datetime-local"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    :class="{ 'border-red-500': form.errors.publish_date }"
                    required
                  />
                  <div v-if="form.errors.publish_date" class="text-red-500 text-sm mt-1">{{ form.errors.publish_date }}</div>
                </div>
                
                <div>
                  <label for="expiry_date" class="block text-sm font-medium text-gray-700 mb-1">Date d'expiration (optionnel)</label>
                  <input
                    id="expiry_date"
                    v-model="form.expiry_date"
                    type="datetime-local"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    :class="{ 'border-red-500': form.errors.expiry_date }"
                  />
                  <div v-if="form.errors.expiry_date" class="text-red-500 text-sm mt-1">{{ form.errors.expiry_date }}</div>
                </div>
              </div>
              
              <!-- Importance -->
              <div class="mb-6">
                <div class="flex items-center">
                  <input
                    id="is_important"
                    v-model="form.is_important"
                    type="checkbox"
                    class="h-4 w-4 text-indigo-600 border-gray-300 rounded"
                  />
                  <label for="is_important" class="ml-2 block text-sm text-gray-700">
                    Marquer comme importante
                  </label>
                </div>
                <div v-if="form.errors.is_important" class="text-red-500 text-sm mt-1">{{ form.errors.is_important }}</div>
              </div>
              
              <!-- Boutons -->
              <div class="flex justify-end space-x-3">
                <Link
                  :href="route('admin.announcements.index')"
                  class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                >
                  Annuler
                </Link>
                <button
                  type="submit"
                  class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                  :disabled="form.processing"
                >
                  Mettre à jour
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import { onMounted } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  announcement: Object,
  trainingSessions: Array,
});

// Formulaire
const form = useForm({
  title: props.announcement.title,
  content: props.announcement.content,
  visible_to: props.announcement.visible_to,
  is_important: props.announcement.is_important,
  publish_date: '',
  expiry_date: '',
  training_session_id: props.announcement.training_session_id,
});

// Formater les dates pour les champs datetime-local
onMounted(() => {
  // Formater la date de publication
  if (props.announcement.publish_date) {
    const publishDate = new Date(props.announcement.publish_date);
    form.publish_date = publishDate.toISOString().slice(0, 16);
  }
  
  // Formater la date d'expiration si elle existe
  if (props.announcement.expiry_date) {
    const expiryDate = new Date(props.announcement.expiry_date);
    form.expiry_date = expiryDate.toISOString().slice(0, 16);
  }
});

// Méthodes
const submit = () => {
  form.put(route('admin.announcements.update', props.announcement.id));
};
</script>
