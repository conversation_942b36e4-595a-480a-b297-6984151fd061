<?php

namespace App\Mail;

use App\Models\Enrollment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentReceiptMail extends Mailable
{
    use Queueable, SerializesModels;

    public $enrollment;
    public $isPaymentProofNotification;

    /**
     * Create a new message instance.
     */
    public function __construct(Enrollment $enrollment, bool $isPaymentProofNotification = false)
    {
        $this->enrollment = $enrollment;
        $this->isPaymentProofNotification = $isPaymentProofNotification;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = $this->isPaymentProofNotification 
            ? 'Nouveau justificatif de paiement reçu'
            : 'Confirmation de paiement reçu';

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $view = $this->isPaymentProofNotification 
            ? 'emails.payment-proof-notification'
            : 'emails.payment-receipt';

        return new Content(
            view: $view,
            with: [
                'enrollment' => $this->enrollment,
                'student' => $this->enrollment->user,
                'trainingSession' => $this->enrollment->trainingSession,
                'trainingDomain' => $this->enrollment->trainingSession->trainingDomain,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
