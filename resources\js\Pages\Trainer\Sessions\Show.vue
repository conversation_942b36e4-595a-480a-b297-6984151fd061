<template>
  <Head :title="'Session: ' + session.title" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Session: {{ session.title }}
        </h2>
        <Link
          :href="route('trainer.sessions.index')"
          class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
        >
          Retour aux sessions
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Informations sur la session -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Informations sur la session</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p class="mb-2"><span class="font-semibold">Titre:</span> {{ session.title }}</p>
                <p class="mb-2"><span class="font-semibold">Domaine:</span> {{ session.training_domain.name }}</p>
                <p class="mb-2"><span class="font-semibold">Période:</span> {{ formatDate(session.start_date) }} - {{ formatDate(session.end_date) }}</p>
                <p class="mb-2"><span class="font-semibold">Statut:</span> 
                  <span
                    :class="{
                      'px-2 py-1 text-xs rounded-full ml-2': true,
                      'bg-green-100 text-green-800': isActive(session),
                      'bg-yellow-100 text-yellow-800': isUpcoming(session),
                      'bg-gray-100 text-gray-800': isPast(session)
                    }"
                  >
                    {{ getSessionStatus(session) }}
                  </span>
                </p>
              </div>
              <div>
                <p class="mb-2"><span class="font-semibold">Description:</span></p>
                <p class="text-gray-700">{{ session.description || 'Aucune description disponible.' }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Statistiques de progression -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 text-gray-900">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-semibold">Statistiques de progression</h3>
              <Link
                :href="route('trainer.sessions.progress-report', session.id)"
                class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
              >
                Rapport détaillé
              </Link>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div class="bg-blue-50 p-4 rounded-lg">
                <div class="text-3xl font-bold text-blue-600">{{ progressStats.total_students }}</div>
                <div class="text-sm text-gray-600">Apprenants inscrits</div>
              </div>
              <div class="bg-green-50 p-4 rounded-lg">
                <div class="text-3xl font-bold text-green-600">{{ progressStats.course_completion }}%</div>
                <div class="text-sm text-gray-600">Complétion des cours</div>
              </div>
              <div class="bg-purple-50 p-4 rounded-lg">
                <div class="text-3xl font-bold text-purple-600">{{ progressStats.exam_participation }}%</div>
                <div class="text-sm text-gray-600">Participation aux examens</div>
              </div>
              <div class="bg-yellow-50 p-4 rounded-lg">
                <div class="text-3xl font-bold text-yellow-600">{{ progressStats.exam_success_rate }}%</div>
                <div class="text-sm text-gray-600">Taux de réussite</div>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- Liste des apprenants -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
              <h3 class="text-lg font-semibold mb-4">Apprenants inscrits ({{ enrollments.length }})</h3>
              <div v-if="enrollments.length > 0">
                <div class="overflow-x-auto">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                      <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <tr v-for="enrollment in enrollments" :key="enrollment.id">
                        <td class="px-6 py-4 whitespace-nowrap">{{ enrollment.user.name }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ enrollment.user.email }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <div v-else class="text-gray-500 italic">
                Aucun apprenant inscrit à cette session.
              </div>
            </div>
          </div>

          <!-- Cours et examens -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
              <h3 class="text-lg font-semibold mb-4">Contenu de la formation</h3>
              
              <!-- Cours -->
              <div class="mb-6">
                <h4 class="font-medium text-gray-700 mb-2">Cours ({{ courses.length }})</h4>
                <div v-if="courses.length > 0">
                  <ul class="space-y-2">
                    <li v-for="course in courses" :key="course.id" class="border-l-4 border-indigo-500 pl-4 py-1">
                      <div class="flex justify-between items-center">
                        <span>{{ course.title }}</span>
                        <Link :href="route('trainer.courses.show', course.id)" class="text-indigo-600 hover:text-indigo-900 text-sm">
                          Gérer
                        </Link>
                      </div>
                    </li>
                  </ul>
                </div>
                <div v-else class="text-gray-500 italic">
                  Aucun cours disponible pour cette session.
                </div>
                <div class="mt-3">
                  <Link :href="route('trainer.courses.create', { session_id: session.id })" class="text-indigo-600 hover:text-indigo-900 text-sm">
                    Ajouter un cours
                  </Link>
                </div>
              </div>
              
              <!-- Examens -->
              <div>
                <h4 class="font-medium text-gray-700 mb-2">Examens ({{ exams.length }})</h4>
                <div v-if="exams.length > 0">
                  <ul class="space-y-2">
                    <li v-for="exam in exams" :key="exam.id" class="border-l-4 border-red-500 pl-4 py-1">
                      <div class="flex justify-between items-center">
                        <span>{{ exam.title }}</span>
                        <Link :href="route('trainer.exams.show', exam.id)" class="text-indigo-600 hover:text-indigo-900 text-sm">
                          Gérer
                        </Link>
                      </div>
                    </li>
                  </ul>
                </div>
                <div v-else class="text-gray-500 italic">
                  Aucun examen disponible pour cette session.
                </div>
                <div class="mt-3">
                  <Link :href="route('trainer.exams.create', { session_id: session.id })" class="text-indigo-600 hover:text-indigo-900 text-sm">
                    Ajouter un examen
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Gestion des présences -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Gestion des présences</h3>
            
            <div v-if="enrollments.length > 0">
              <div class="mb-6">
                <div class="flex items-end space-x-4">
                  <div>
                    <label for="attendance-date" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                    <input
                      type="date"
                      id="attendance-date"
                      v-model="attendanceDate"
                      class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <button
                    @click="loadAttendanceForDate"
                    class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    :disabled="!attendanceDate"
                  >
                    Charger
                  </button>
                </div>
              </div>

              <div v-if="attendanceDate">
                <form @submit.prevent="saveAttendance">
                  <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Apprenant</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Présent</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="enrollment in enrollments" :key="enrollment.id">
                          <td class="px-6 py-4 whitespace-nowrap">{{ enrollment.user.name }}</td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <input
                              type="checkbox"
                              :id="'present-' + enrollment.user.id"
                              v-model="currentAttendance[enrollment.user.id].present"
                              class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            />
                          </td>
                          <td class="px-6 py-4">
                            <input
                              type="text"
                              :id="'notes-' + enrollment.user.id"
                              v-model="currentAttendance[enrollment.user.id].notes"
                              class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 w-full"
                              placeholder="Notes (optionnel)"
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div class="mt-6 flex justify-end">
                    <button
                      type="submit"
                      class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                      Enregistrer les présences
                    </button>
                  </div>
                </form>
              </div>
            </div>
            <div v-else class="text-gray-500 italic">
              Aucun apprenant inscrit à cette session. Impossible de gérer les présences.
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  session: Object,
  enrollments: Array,
  courses: Array,
  exams: Array,
  attendances: Object,
  progressStats: Object,
});

// État
const attendanceDate = ref(new Date().toISOString().split('T')[0]); // Format YYYY-MM-DD
const currentAttendance = ref({});

// Initialiser les présences pour tous les apprenants
const initializeAttendance = () => {
  currentAttendance.value = {};
  props.enrollments.forEach(enrollment => {
    currentAttendance.value[enrollment.user.id] = {
      user_id: enrollment.user.id,
      present: true,
      notes: '',
    };
  });
};

// Charger les présences pour une date spécifique
const loadAttendanceForDate = () => {
  initializeAttendance();
  
  // Vérifier si des présences existent déjà pour cette date
  if (props.attendances[attendanceDate.value]) {
    const dateAttendances = props.attendances[attendanceDate.value];
    
    // Mettre à jour l'état avec les présences existantes
    props.enrollments.forEach(enrollment => {
      if (dateAttendances[enrollment.user.id]) {
        currentAttendance.value[enrollment.user.id] = {
          user_id: enrollment.user.id,
          present: dateAttendances[enrollment.user.id].present,
          notes: dateAttendances[enrollment.user.id].notes || '',
        };
      }
    });
  }
};

// Enregistrer les présences
const saveAttendance = () => {
  const attendances = Object.values(currentAttendance.value);
  
  router.post(route('trainer.sessions.attendance.store', props.session.id), {
    date: attendanceDate.value,
    attendances: attendances,
  }, {
    onSuccess: () => {
      // Afficher un message de succès
    },
  });
};

// Méthodes
const formatDate = (dateString) => {
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const isActive = (session) => {
  const now = new Date();
  const startDate = new Date(session.start_date);
  const endDate = new Date(session.end_date);
  return startDate <= now && endDate >= now;
};

const isUpcoming = (session) => {
  const now = new Date();
  const startDate = new Date(session.start_date);
  return startDate > now;
};

const isPast = (session) => {
  const now = new Date();
  const endDate = new Date(session.end_date);
  return endDate < now;
};

const getSessionStatus = (session) => {
  if (isActive(session)) return 'En cours';
  if (isUpcoming(session)) return 'À venir';
  if (isPast(session)) return 'Terminée';
  return 'Inconnu';
};

// Initialiser les présences au chargement de la page
onMounted(() => {
  initializeAttendance();
  loadAttendanceForDate();
});
</script>
