<template>
  <Head title="Ajouter un conseil en premiers secours" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Ajouter un conseil en premiers secours
        </h2>
        <Link :href="route('admin.first-aid-tips.index')" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
          Retour à la liste
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <form @submit.prevent="submit">
              <!-- Titre -->
              <div class="mb-6">
                <InputLabel for="title" value="Titre *" />
                <TextInput
                  id="title"
                  v-model="form.title"
                  type="text"
                  class="mt-1 block w-full"
                  required
                  autofocus
                />
                <InputError class="mt-2" :message="form.errors.title" />
              </div>

              <!-- Description -->
              <div class="mb-6">
                <InputLabel for="description" value="Description" />
                <textarea
                  id="description"
                  v-model="form.description"
                  rows="4"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  placeholder="Description du conseil en premiers secours..."
                ></textarea>
                <InputError class="mt-2" :message="form.errors.description" />
              </div>

              <!-- Ordre -->
              <div class="mb-6">
                <InputLabel for="order" value="Ordre d'affichage" />
                <TextInput
                  id="order"
                  v-model="form.order"
                  type="number"
                  class="mt-1 block w-full"
                  min="0"
                />
                <InputError class="mt-2" :message="form.errors.order" />
                <p class="mt-1 text-sm text-gray-600">
                  Ordre d'affichage (0 = premier). Laissez vide pour ajouter à la fin.
                </p>
              </div>

              <!-- Statut -->
              <div class="mb-6">
                <label class="flex items-center">
                  <input
                    v-model="form.active"
                    type="checkbox"
                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                  />
                  <span class="ml-2 text-sm text-gray-600">Conseil actif</span>
                </label>
                <InputError class="mt-2" :message="form.errors.active" />
              </div>

              <!-- Boutons -->
              <div class="flex items-center justify-end space-x-4">
                <Link
                  :href="route('admin.first-aid-tips.index')"
                  class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Annuler
                </Link>
                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                  Créer le conseil
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Formulaire
const form = useForm({
  title: '',
  description: '',
  order: 0,
  active: true,
});

// Méthodes
const submit = () => {
  form.post(route('admin.first-aid-tips.store'));
};
</script>
