<?php

require_once 'vendor/autoload.php';

use SimpleSoftwareIO\QrCode\Generator;

// Test simple de génération de QR code
try {
    echo "Test de génération de QR code...\n";

    // Créer une instance du générateur
    $qrCode = new Generator;

    // Test 1: Génération basique
    $qrCodeData = $qrCode->format('png')->size(200)->generate('https://example.com');
    echo "QR code généré avec succès (test basique)\n";

    // Test 2: Avec une URL de vérification
    $verificationUrl = 'http://localhost:8000/certificates/verify/TEST123';
    $qrCodeData2 = $qrCode->format('png')->size(200)->generate($verificationUrl);
    echo "QR code généré avec succès (URL de vérification)\n";

    // Test 3: Sauvegarde dans un fichier
    file_put_contents('test_qr.png', $qrCodeData2);
    echo "QR code sauvegardé dans test_qr.png\n";

    echo "Tous les tests ont réussi !\n";

} catch (Exception $e) {
    echo "Erreur lors de la génération du QR code: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
