<template>
  <Head title="Créer un résultat d'examen" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Créer un résultat d'examen
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link :href="route('admin.exam-results.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à la liste
              </Link>
            </div>

            <!-- Formulaire de création de résultat d'examen -->
            <form @submit.prevent="submit">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Examen -->
                <div>
                  <InputLabel for="exam_id" value="Examen" />
                  <select
                    id="exam_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.exam_id"
                    required
                    @change="updateSelectedExam"
                  >
                    <option value="">Sélectionnez un examen</option>
                    <option v-for="exam in exams" :key="exam.id" :value="exam.id">
                      {{ exam.title }} ({{ exam.training_session.title }})
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.exam_id" />
                </div>

                <!-- Inscription -->
                <div>
                  <InputLabel for="enrollment_id" value="Apprenant" />
                  <select
                    id="enrollment_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.enrollment_id"
                    required
                  >
                    <option value="">Sélectionnez un apprenant</option>
                    <option v-for="enrollment in filteredEnrollments" :key="enrollment.id" :value="enrollment.id">
                      {{ enrollment.user.name }} ({{ enrollment.training_session.title }})
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.enrollment_id" />
                </div>

                <!-- Score -->
                <div>
                  <InputLabel for="score" value="Score (%)" />
                  <TextInput
                    id="score"
                    type="number"
                    class="mt-1 block w-full"
                    v-model="form.score"
                    min="0"
                    max="100"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.score" />
                </div>

                <!-- Résultat -->
                <div>
                  <InputLabel for="passed" value="Résultat" />
                  <div class="mt-2">
                    <label class="inline-flex items-center">
                      <input type="radio" class="form-radio" name="passed" :value="true" v-model="form.passed">
                      <span class="ml-2">Réussi</span>
                    </label>
                    <label class="inline-flex items-center ml-6">
                      <input type="radio" class="form-radio" name="passed" :value="false" v-model="form.passed">
                      <span class="ml-2">Échoué</span>
                    </label>
                  </div>
                  <InputError class="mt-2" :message="form.errors.passed" />
                </div>

                <!-- Feedback -->
                <div class="md:col-span-2">
                  <InputLabel for="feedback" value="Feedback" />
                  <textarea
                    id="feedback"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.feedback"
                    rows="4"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.feedback" />
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="flex items-center justify-end mt-6">
                <PrimaryButton class="ml-4" :disabled="form.processing">
                  Créer le résultat
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  exams: Array,
  enrollments: Array,
});

// État
const selectedExamId = ref(null);
const selectedExamSessionId = ref(null);

// Filtrer les inscriptions en fonction de l'examen sélectionné
const filteredEnrollments = computed(() => {
  if (!selectedExamSessionId.value) return [];
  return props.enrollments.filter(enrollment => 
    enrollment.training_session_id === selectedExamSessionId.value
  );
});

// Formulaire
const form = useForm({
  exam_id: '',
  enrollment_id: '',
  score: 0,
  passed: false,
  feedback: '',
  answers: JSON.stringify([]),
});

// Méthodes
const updateSelectedExam = () => {
  form.enrollment_id = '';
  selectedExamId.value = form.exam_id;
  
  if (form.exam_id) {
    const selectedExam = props.exams.find(exam => exam.id == form.exam_id);
    if (selectedExam) {
      selectedExamSessionId.value = selectedExam.training_session_id;
    }
  } else {
    selectedExamSessionId.value = null;
  }
};

const submit = () => {
  form.post(route('admin.exam-results.store'));
};
</script>
