<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Assurez-vous que le type 'archive' est bien inclus dans l'ENUM
        DB::statement("ALTER TABLE course_materials MODIFY COLUMN type ENUM('pdf', 'video', 'text', 'archive', 'image', 'audio')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Ne rien faire dans le down car nous ne voulons pas supprimer le type 'archive'
    }
};
