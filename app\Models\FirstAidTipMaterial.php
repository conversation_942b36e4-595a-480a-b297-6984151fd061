<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FirstAidTipMaterial extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'first_aid_tip_id',
        'type',
        'content',
        'embed_code',
        'file_path',
        'order',
        'active',
        'allow_download',
        'allow_online_viewing',
        'mime_type',
        'file_size',
        'thumbnail_path',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'active' => 'boolean',
        'allow_download' => 'boolean',
        'allow_online_viewing' => 'boolean',
        'file_size' => 'integer',
        'metadata' => 'json',
    ];

    /**
     * Relation avec le conseil en premiers secours
     */
    public function firstAidTip()
    {
        return $this->belongsTo(FirstAidTip::class);
    }

    /**
     * Accessor pour les images de galerie
     */
    public function getGalleryImagesAttribute()
    {
        if ($this->type !== 'gallery') {
            return [];
        }

        $galleryImages = [];

        // Essayer d'abord avec metadata (format JSON)
        $metadata = null;
        if ($this->metadata) {
            $metadata = is_array($this->metadata) ? $this->metadata : json_decode($this->metadata, true);
        }

        if (is_array($metadata)) {
            if (isset($metadata['images']) && is_array($metadata['images'])) {
                // Format utilisé dans l'interface admin
                $galleryImages = $metadata['images'];
            } elseif (isset($metadata['gallery_paths']) && is_array($metadata['gallery_paths'])) {
                // Format utilisé dans l'interface formateur
                foreach ($metadata['gallery_paths'] as $index => $path) {
                    $galleryImages[] = [
                        'path' => $path,
                        'caption' => 'Image ' . ($index + 1)
                    ];
                }
            }
        }

        // Si aucune image n'a été trouvée dans les métadonnées, essayer avec gallery_data
        if (empty($galleryImages) && $this->gallery_data) {
            $galleryData = json_decode($this->gallery_data, true);
            if (isset($galleryData['images']) && is_array($galleryData['images'])) {
                $galleryImages = $galleryData['images'];
            }
        }

        return $galleryImages;
    }
}
