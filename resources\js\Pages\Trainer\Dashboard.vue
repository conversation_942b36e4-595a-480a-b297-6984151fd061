<template>
  <Head title="Tableau de bord formateur" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Tableau de bord formateur
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Statistiques modernisées -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 mb-6">
          <StatCard :value="stats.active_students" label="Apprenants actifs" :icon="UserIcon" bgGradient="bg-gradient-to-r from-blue-500 to-blue-700" />
          <StatCard :value="stats.pass_rate + '%'" label="Taux de réussite" :icon="AcademicCapIcon" bgGradient="bg-gradient-to-r from-green-500 to-green-700" />
          <StatCard :value="stats.active_sessions_count" label="Sessions en cours" :icon="CalendarIcon" bgGradient="bg-gradient-to-r from-purple-500 to-purple-700" />
          <StatCard :value="stats.pending_results_count" label="Résultats en attente" :icon="ClockIcon" bgGradient="bg-gradient-to-r from-yellow-400 to-yellow-600" />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- Calendrier -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
              <h3 class="text-lg font-semibold mb-4">Calendrier des sessions</h3>
              <FullCalendar :options="calendarOptions" />
            </div>
          </div>

          <!-- Notifications -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
              <h3 class="text-lg font-semibold mb-4">Notifications récentes</h3>
              <div v-if="notifications.length > 0" class="space-y-4">
                <div v-for="notification in notifications" :key="notification.date" class="border-b pb-3 last:border-b-0">
                  <div class="flex items-start">
                    <div class="flex-shrink-0 mt-1">
                      <div v-if="notification.type === 'enrollment'" class="bg-green-100 p-2 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                        </svg>
                      </div>
                      <div v-else-if="notification.type === 'exam_result'" class="bg-blue-100 p-2 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                    </div>
                    <div class="ml-3 flex-1">
                      <p class="text-sm text-gray-800">{{ notification.message }}</p>
                      <p class="text-xs text-gray-500 mt-1">{{ formatDate(notification.date) }}</p>
                    </div>
                    <Link :href="notification.url" class="text-indigo-600 hover:text-indigo-800 text-sm">
                      Voir
                    </Link>
                  </div>
                </div>
              </div>
              <div v-else class="text-gray-500 italic">
                Aucune notification récente.
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Sessions en cours -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
              <h3 class="text-lg font-semibold mb-4">Sessions en cours</h3>
              <div v-if="currentSessions.length > 0">
                <div class="space-y-4">
                  <div v-for="session in currentSessions" :key="session.id" class="border rounded-lg p-4 hover:bg-gray-50">
                    <h4 class="font-semibold text-lg">{{ session.title }}</h4>
                    <p class="text-sm text-gray-600 mb-2">{{ session.training_domain.name }}</p>
                    <div class="flex justify-between items-center">
                      <div class="text-sm">
                        <span class="text-gray-600">Période:</span> {{ formatDate(session.start_date) }} - {{ formatDate(session.end_date) }}
                      </div>
                      <div class="text-sm">
                        <span class="text-gray-600">Inscrits:</span> {{ session.enrollments.length }}
                      </div>
                    </div>
                    <div class="mt-3 flex space-x-4">
                      <Link :href="route('trainer.sessions.show', session.id)" class="text-indigo-600 hover:text-indigo-800 text-sm">
                        Détails de la session
                      </Link>
                      <Link :href="route('trainer.courses.index', { session_id: session.id })" class="text-indigo-600 hover:text-indigo-800 text-sm">
                        Gérer les cours
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="text-gray-500 italic">
                Aucune session en cours.
              </div>
              <div class="mt-4 text-right">
                <Link :href="route('trainer.sessions.index')" class="text-indigo-600 hover:text-indigo-800 text-sm">
                  Voir toutes mes sessions
                </Link>
              </div>
            </div>
          </div>

          <!-- Résultats d'examen à évaluer modernisé -->
          <div class="bg-white rounded-xl shadow-lg overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Apprenant</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Examen</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="result in pendingResults" :key="result.id" class="hover:bg-blue-50 transition">
                  <td class="px-6 py-4 whitespace-nowrap">{{ result.user.name }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">{{ result.exam.title }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">{{ formatDate(result.completed_at) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Link :href="route('trainer.exam-results.edit', result.id)" class="btn-action-edit">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 0L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                      Évaluer
                    </Link>
                  </td>
                </tr>
                <tr v-if="pendingResults.length === 0">
                  <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                    Aucun résultat en attente d'évaluation.
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import FullCalendar from '@fullcalendar/vue3';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import StatCard from '@/Components/StatCard.vue';
import { UserIcon, AcademicCapIcon, CalendarIcon, ClockIcon } from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  stats: Object,
  upcomingSessions: Array,
  currentSessions: Array,
  pendingResults: Array,
  calendarEvents: Array,
  notifications: Array,
});

// Configuration du calendrier
const calendarOptions = {
  plugins: [dayGridPlugin, interactionPlugin],
  initialView: 'dayGridMonth',
  headerToolbar: {
    left: 'prev,next today',
    center: 'title',
    right: 'dayGridMonth,dayGridWeek'
  },
  events: props.calendarEvents,
  eventClick: function(info) {
    if (info.event.url) {
      window.location.href = info.event.url;
      info.jsEvent.preventDefault(); // empêche le navigateur de suivre le lien
    }
  },
  eventTimeFormat: {
    hour: '2-digit',
    minute: '2-digit',
    meridiem: false,
    hour12: false
  },
  locale: 'fr',
  firstDay: 1, // Lundi comme premier jour de la semaine
  buttonText: {
    today: "Aujourd'hui",
    month: 'Mois',
    week: 'Semaine'
  },
  dayHeaderFormat: { weekday: 'short', day: 'numeric' },
  height: 'auto'
};

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return '';
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};
</script>

<style>
.fc-event {
  cursor: pointer;
}
.fc-day-today {
  background-color: rgba(79, 70, 229, 0.05) !important;
}
.fc-header-toolbar {
  margin-bottom: 1rem !important;
}
.fc-toolbar-title {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
}
.fc-button-primary {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
}
.fc-button-primary:hover {
  background-color: #4338CA !important;
  border-color: #4338CA !important;
}
.fc-button-active {
  background-color: #3730A3 !important;
  border-color: #3730A3 !important;
}
</style>

<style scoped>
.btn-action-edit {
  @apply text-blue-600 hover:text-blue-900 transition;
}
</style>
