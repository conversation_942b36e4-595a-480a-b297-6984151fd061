<template>
  <Head title="Créer une session de formation" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Créer une session de formation
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link :href="route('admin.training-sessions.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à la liste
              </Link>
            </div>

            <!-- Formulaire de création de session -->
            <form @submit.prevent="submit" enctype="multipart/form-data">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Titre -->
                <div>
                  <InputLabel for="title" value="Titre" />
                  <TextInput
                    id="title"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.title"
                    required
                    autofocus
                  />
                  <InputError class="mt-2" :message="form.errors.title" />
                </div>

                <!-- Domaine de formation -->
                <div>
                  <InputLabel for="training_domain_id" value="Domaine de formation" />
                  <select
                    id="training_domain_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.training_domain_id"
                    required
                  >
                    <option value="">Sélectionnez un domaine</option>
                    <option v-for="domain in domains" :key="domain.id" :value="domain.id">
                      {{ domain.name }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.training_domain_id" />
                </div>

                <!-- Département -->
                <div>
                  <InputLabel for="department" value="Département" />
                  <select
                    id="department"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.department"
                    required
                  >
                    <option value="">Sélectionnez un département</option>
                    <option v-for="department in departments" :key="department" :value="department">
                      {{ department }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.department" />
                </div>

                <!-- Niveau -->
                <div>
                  <InputLabel for="level" value="Niveau" />
                  <select
                    id="level"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.level"
                    required
                  >
                    <option value="">Sélectionnez un niveau</option>
                    <option v-for="level in levels" :key="level" :value="level">
                      {{ level }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.level" />
                </div>

                <!-- Formateur -->
                <div>
                  <InputLabel for="trainer_id" value="Formateur" />
                  <select
                    id="trainer_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.trainer_id"
                    required
                  >
                    <option value="">Sélectionnez un formateur</option>
                    <option v-for="trainer in trainers" :key="trainer.id" :value="trainer.id">
                      {{ trainer.name }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.trainer_id" />
                </div>

                <!-- Date de début -->
                <div>
                  <InputLabel for="start_date" value="Date de début" />
                  <TextInput
                    id="start_date"
                    type="date"
                    class="mt-1 block w-full"
                    v-model="form.start_date"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.start_date" />
                </div>

                <!-- Date de fin -->
                <div>
                  <InputLabel for="end_date" value="Date de fin" />
                  <TextInput
                    id="end_date"
                    type="date"
                    class="mt-1 block w-full"
                    v-model="form.end_date"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.end_date" />
                </div>

                <!-- Lieu -->
                <div>
                  <InputLabel for="location" value="Lieu" />
                  <TextInput
                    id="location"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.location"
                  />
                  <InputError class="mt-2" :message="form.errors.location" />
                </div>

                <!-- Nombre maximum de participants -->
                <div>
                  <InputLabel for="max_participants" value="Nombre maximum de participants" />
                  <TextInput
                    id="max_participants"
                    type="number"
                    class="mt-1 block w-full"
                    v-model="form.max_participants"
                    min="1"
                  />
                  <InputError class="mt-2" :message="form.errors.max_participants" />
                </div>

                <!-- Prix -->
                <div>
                  <InputLabel for="price" value="Prix (DT)" />
                  <TextInput
                    id="price"
                    type="number"
                    step="0.01"
                    class="mt-1 block w-full"
                    v-model="form.price"
                    min="0"
                  />
                  <InputError class="mt-2" :message="form.errors.price" />
                </div>

                <!-- Image -->
                <div>
                  <InputLabel for="image" value="Image" />
                  <input
                    id="image"
                    type="file"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
                    @input="form.image = $event.target.files[0]"
                  />
                  <InputError class="mt-2" :message="form.errors.image" />
                </div>

                <!-- Statut -->
                <div>
                  <InputLabel for="active" value="Statut" />
                  <div class="mt-2">
                    <label class="inline-flex items-center">
                      <input type="radio" class="form-radio" name="active" :value="true" v-model="form.active">
                      <span class="ml-2">Active</span>
                    </label>
                    <label class="inline-flex items-center ml-6">
                      <input type="radio" class="form-radio" name="active" :value="false" v-model="form.active">
                      <span class="ml-2">Inactive</span>
                    </label>
                  </div>
                  <InputError class="mt-2" :message="form.errors.active" />
                </div>

                <!-- Nom du certificat -->
                <div>
                  <InputLabel for="certificate_name" value="Nom du certificat" />
                  <TextInput
                    id="certificate_name"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.certificate_name"
                    placeholder="Nom qui apparaîtra sur le certificat"
                  />
                  <InputError class="mt-2" :message="form.errors.certificate_name" />
                </div>

                <!-- Objectifs de formation -->
                <div>
                  <InputLabel for="training_objectives" value="Objectifs de formation" />
                  <textarea
                    id="training_objectives"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.training_objectives"
                    rows="3"
                    placeholder="Objectifs qui apparaîtront sur le certificat"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.training_objectives" />
                </div>

                <!-- Description -->
                <div class="md:col-span-2">
                  <InputLabel for="description" value="Description" />
                  <textarea
                    id="description"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.description"
                    rows="4"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.description" />
                </div>


              </div>

              <!-- Section des créneaux horaires -->
              <div class="mt-8 border-t pt-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-semibold text-gray-900">Créneaux horaires</h3>
                  <button
                    type="button"
                    @click="addTimeSlot"
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Ajouter un créneau
                  </button>
                </div>

                <div v-if="timeSlots.length === 0" class="text-center py-8 text-gray-500">
                  <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <p>Aucun créneau horaire défini</p>
                  <p class="text-sm">Cliquez sur "Ajouter un créneau" pour commencer</p>
                </div>

                <div v-else class="space-y-4">
                  <div
                    v-for="(slot, index) in timeSlots"
                    :key="index"
                    class="bg-gray-50 p-4 rounded-lg border"
                  >
                    <div class="flex items-center justify-between mb-3">
                      <h4 class="font-medium text-gray-900">Créneau {{ index + 1 }}</h4>
                      <button
                        type="button"
                        @click="removeTimeSlot(index)"
                        class="text-red-600 hover:text-red-800"
                        :disabled="timeSlots.length === 1"
                      >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                      </button>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <!-- Nom du créneau -->
                      <div>
                        <InputLabel :for="`slot_name_${index}`" value="Nom (optionnel)" />
                        <TextInput
                          :id="`slot_name_${index}`"
                          type="text"
                          class="mt-1 block w-full"
                          v-model="slot.name"
                          placeholder="Ex: Matin, Après-midi"
                        />
                      </div>

                      <!-- Heure de début -->
                      <div>
                        <InputLabel :for="`slot_start_${index}`" value="Heure de début" />
                        <TextInput
                          :id="`slot_start_${index}`"
                          type="time"
                          class="mt-1 block w-full"
                          v-model="slot.start_time"
                          required
                        />
                      </div>

                      <!-- Heure de fin -->
                      <div>
                        <InputLabel :for="`slot_end_${index}`" value="Heure de fin" />
                        <TextInput
                          :id="`slot_end_${index}`"
                          type="time"
                          class="mt-1 block w-full"
                          v-model="slot.end_time"
                          required
                        />
                      </div>

                      <!-- Nombre max de participants -->
                      <div>
                        <InputLabel :for="`slot_max_${index}`" value="Max participants" />
                        <TextInput
                          :id="`slot_max_${index}`"
                          type="number"
                          class="mt-1 block w-full"
                          v-model="slot.max_participants"
                          min="1"
                          placeholder="Optionnel"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="flex items-center justify-end mt-6">
                <PrimaryButton class="ml-4" :disabled="form.processing">
                  Créer la session
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  domains: Array,
  trainers: Array,
  departments: Array,
  levels: Array,
});

// Gestion des créneaux horaires
const timeSlots = ref([]);

// Formulaire
const form = useForm({
  title: '',
  description: '',
  certificate_name: '',
  training_objectives: '',
  training_domain_id: '',
  department: '',
  level: '',
  trainer_id: '',
  start_date: '',
  end_date: '',
  location: '',
  max_participants: '',
  price: '',
  image: null,
  active: true,
  time_slots: [],
});

// Méthodes pour les créneaux horaires
const addTimeSlot = () => {
  timeSlots.value.push({
    name: '',
    start_time: '',
    end_time: '',
    max_participants: '',
    active: true
  });
};

const removeTimeSlot = (index) => {
  if (timeSlots.value.length > 1) {
    timeSlots.value.splice(index, 1);
  }
};

// Ajouter un créneau par défaut au chargement
addTimeSlot();

// Méthodes
const submit = () => {
  // Synchroniser les créneaux avec le formulaire
  form.time_slots = timeSlots.value.filter(slot =>
    slot.start_time && slot.end_time
  );

  form.post(route('admin.training-sessions.store'));
};
</script>
