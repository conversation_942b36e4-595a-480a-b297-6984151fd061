<template>
  <Head :title="material.title" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        {{ material.title }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Fil d'Ariane -->
            <div class="mb-6">
              <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                  <li class="inline-flex items-center">
                    <Link :href="route('trainer.dashboard')" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                      <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                      </svg>
                      Tableau de bord
                    </Link>
                  </li>
                  <li>
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <Link :href="route('trainer.courses.index')" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                        Mes cours
                      </Link>
                    </div>
                  </li>
                  <li v-if="module">
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <Link :href="route('trainer.modules.index', { course_id: course.id })" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                        Modules du cours
                      </Link>
                    </div>
                  </li>
                  <li>
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <Link :href="route('trainer.course-materials.index', module ? { module_id: module.id } : { course_id: course.id })" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                        Matériels pédagogiques
                      </Link>
                    </div>
                  </li>
                  <li aria-current="page">
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">
                        {{ material.title }}
                      </span>
                    </div>
                  </li>
                </ol>
              </nav>
            </div>

            <!-- En-tête avec boutons d'action -->
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">
                {{ material.title }}
              </h3>
              <div class="flex space-x-2">
                <Link :href="route('trainer.course-materials.edit', material.id)" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  Modifier
                </Link>
                <Link v-if="material.file_path && material.allow_download" :href="route('trainer.course-materials.download', material.id)" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  Télécharger
                </Link>
                <button @click="confirmDelete" class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  Supprimer
                </button>
              </div>
            </div>

            <!-- Informations du matériel -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 class="font-medium text-gray-700">Type</h4>
                  <p>{{ getMaterialTypeLabel(material.type) }}</p>
                </div>
                <div>
                  <h4 class="font-medium text-gray-700">Statut</h4>
                  <p :class="material.active ? 'text-green-600' : 'text-red-600'">
                    {{ material.active ? 'Actif' : 'Inactif' }}
                  </p>
                </div>
                <div v-if="material.file_path">
                  <h4 class="font-medium text-gray-700">Fichier</h4>
                  <p>{{ getFileName(material.file_path) }}</p>
                </div>
                <div v-if="material.file_size">
                  <h4 class="font-medium text-gray-700">Taille</h4>
                  <p>{{ formatFileSize(material.file_size) }}</p>
                </div>
                <div v-if="material.mime_type">
                  <h4 class="font-medium text-gray-700">Type MIME</h4>
                  <p>{{ material.mime_type }}</p>
                </div>
                <div>
                  <h4 class="font-medium text-gray-700">Ordre</h4>
                  <p>{{ material.order }}</p>
                </div>
                <div v-if="material.file_path">
                  <h4 class="font-medium text-gray-700">Téléchargement</h4>
                  <p>{{ material.allow_download ? 'Autorisé' : 'Non autorisé' }}</p>
                </div>
                <div v-if="material.file_path">
                  <h4 class="font-medium text-gray-700">Visualisation en ligne</h4>
                  <p>{{ material.allow_online_viewing ? 'Autorisée' : 'Non autorisée' }}</p>
                </div>
              </div>
            </div>

            <!-- Description -->
            <div v-if="material.description" class="mb-6">
              <h4 class="font-medium text-gray-700 mb-2">Description</h4>
              <div class="bg-white p-4 border rounded-lg">
                <p>{{ material.description }}</p>
              </div>
            </div>

            <!-- Contenu du matériel -->
            <div class="mb-6">
              <h4 class="font-medium text-gray-700 mb-2">Contenu</h4>

              <!-- Contenu textuel -->
              <div v-if="material.type === 'text'" class="bg-white p-4 border rounded-lg">
                <div v-html="material.content"></div>
              </div>

              <!-- Image -->
              <div v-else-if="material.type === 'image' && material.file_path && material.allow_online_viewing" class="bg-white p-4 border rounded-lg">
                <div class="flex flex-col items-center">
                  <img
                    :src="route('trainer.course-materials.view', material.id)"
                    :alt="material.title"
                    class="max-w-full h-auto mb-4"
                    @error="handleImageError"
                  />
                  <div class="flex justify-center space-x-4">
                    <a
                      :href="route('trainer.course-materials.view', material.id)"
                      target="_blank"
                      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      Ouvrir dans un nouvel onglet
                    </a>
                    <a
                      v-if="material.allow_download"
                      :href="route('trainer.course-materials.download', material.id)"
                      class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                    >
                      Télécharger l'image
                    </a>
                  </div>
                </div>
              </div>

              <!-- PDF -->
              <div v-else-if="material.type === 'pdf' && material.file_path && material.allow_online_viewing" class="bg-white p-4 border rounded-lg">
                <div class="flex flex-col items-center mb-4">
                  <div class="bg-gray-100 p-4 rounded-lg mb-4 w-full text-center">
                    <p class="text-gray-700 mb-2">Le document PDF ne peut pas être affiché directement. Veuillez utiliser l'une des options ci-dessous :</p>
                    <div class="flex justify-center space-x-4">
                      <a
                        :href="route('trainer.course-materials.view', material.id)"
                        target="_blank"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                      >
                        Ouvrir dans un nouvel onglet
                      </a>
                      <a
                        v-if="material.allow_download"
                        :href="route('trainer.course-materials.download', material.id)"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                      >
                        Télécharger le PDF
                      </a>
                    </div>
                  </div>
                </div>
                <div class="border border-gray-300 rounded-lg p-8 flex flex-col items-center justify-center w-full h-64">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-red-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <p class="text-lg font-medium">{{ material.title }}</p>
                  <p class="text-sm text-gray-500">{{ formatFileSize(material.file_size) }}</p>
                </div>
              </div>

              <!-- Vidéo -->
              <div v-else-if="material.type === 'video' && material.file_path && material.allow_online_viewing" class="bg-white p-4 border rounded-lg">
                <video controls width="100%" class="max-w-full">
                  <source :src="route('trainer.course-materials.view', material.id)" :type="material.mime_type">
                  Votre navigateur ne prend pas en charge la lecture de vidéos.
                </video>
                <div class="mt-4 flex justify-center space-x-4">
                  <a
                    :href="route('trainer.course-materials.view', material.id)"
                    target="_blank"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Ouvrir dans un nouvel onglet
                  </a>
                  <a
                    v-if="material.allow_download"
                    :href="route('trainer.course-materials.download', material.id)"
                    class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Télécharger la vidéo
                  </a>
                </div>
              </div>

              <!-- Audio -->
              <div v-else-if="material.type === 'audio' && material.file_path && material.allow_online_viewing" class="bg-white p-4 border rounded-lg">
                <audio controls class="w-full">
                  <source :src="route('trainer.course-materials.view', material.id)" :type="material.mime_type">
                  Votre navigateur ne prend pas en charge la lecture audio.
                </audio>
                <div class="mt-4 flex justify-center space-x-4">
                  <a
                    :href="route('trainer.course-materials.view', material.id)"
                    target="_blank"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Ouvrir dans un nouvel onglet
                  </a>
                  <a
                    v-if="material.allow_download"
                    :href="route('trainer.course-materials.download', material.id)"
                    class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Télécharger l'audio
                  </a>
                </div>
              </div>

              <!-- Vidéo externe -->
              <div v-else-if="material.type === 'embed_video'" class="bg-white p-4 border rounded-lg">
                <!-- Utiliser embed_code s'il existe, sinon essayer de générer à partir de content -->
                <div v-if="material.embed_code" class="aspect-w-16 aspect-h-9" v-html="material.embed_code"></div>
                <div v-else-if="material.content && getEmbedUrl(material.content)" class="aspect-w-16 aspect-h-9">
                  <iframe
                    :src="getEmbedUrl(material.content)"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen
                    class="w-full h-full"
                  ></iframe>
                </div>
                <div v-else class="error-container">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 error-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  <p class="error-message">Aucune vidéo externe disponible ou URL invalide.</p>
                  <p class="text-sm text-gray-500 mt-2">
                    Contenu: {{ material.content }}<br>
                    Code d'intégration: {{ material.embed_code ? 'Présent' : 'Absent' }}
                  </p>
                </div>
              </div>

              <!-- Galerie d'images -->
              <div v-else-if="material.type === 'gallery'" class="bg-white p-4 border rounded-lg">
                <div v-if="material.metadata && getGalleryPaths(material.metadata).length > 0" class="gallery-grid">
                  <div v-for="(path, index) in getGalleryPaths(material.metadata)" :key="index" class="gallery-item">
                    <img
                      :src="route('trainer.course-materials.view', { id: material.id, image_index: index })"
                      :alt="`Image ${index + 1}`"
                      class="gallery-image"
                      @error="(e) => handleGalleryImageError(e, index)"
                    />
                  </div>
                </div>
                <div v-else class="error-container">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 error-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <p class="error-message">Aucune image disponible dans cette galerie.</p>
                  <p class="text-sm text-gray-500 mt-2">Métadonnées: {{ JSON.stringify(material.metadata) }}</p>
                </div>
              </div>

              <!-- Archive ou autre type de fichier -->
              <div v-else-if="material.file_path" class="bg-white p-4 border rounded-lg">
                <p>
                  Ce type de contenu ne peut pas être affiché directement.
                  <Link v-if="material.allow_download" :href="route('trainer.course-materials.download', material.id)" class="text-blue-600 hover:underline">
                    Cliquez ici pour télécharger le fichier.
                  </Link>
                </p>
              </div>

              <!-- Pas de contenu -->
              <div v-else class="bg-white p-4 border rounded-lg">
                <div class="error-container">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 error-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                  <p class="error-message">Aucun contenu disponible pour ce matériel.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmation de suppression -->
    <Modal :show="deleteModalOpen" @close="closeModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">
          Confirmer la suppression
        </h2>

        <p class="mt-1 text-sm text-gray-600">
          Êtes-vous sûr de vouloir supprimer ce matériel pédagogique ? Cette action est irréversible.
        </p>

        <div class="mt-6 flex justify-end">
          <SecondaryButton @click="closeModal" class="mr-2">
            Annuler
          </SecondaryButton>

          <DangerButton @click="deleteCourseMaterial" :class="{ 'opacity-25': processing }" :disabled="processing">
            Supprimer
          </DangerButton>
        </div>
      </div>
    </Modal>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';

// Props
const props = defineProps({
  material: Object,
  course: Object,
  module: Object,
});

// État pour la modal de suppression
const deleteModalOpen = ref(false);
const processing = ref(false);

// État pour la gestion des erreurs d'image
const imageErrors = ref({});

// Méthodes pour la suppression
const confirmDelete = () => {
  deleteModalOpen.value = true;
};

const closeModal = () => {
  deleteModalOpen.value = false;
};

const deleteCourseMaterial = () => {
  processing.value = true;

  router.delete(route('trainer.course-materials.destroy', props.material.id), {
    onSuccess: () => {
      closeModal();
      processing.value = false;
    },
    onError: () => {
      processing.value = false;
    },
  });
};

// Méthode pour obtenir le libellé du type de matériel
const getMaterialTypeLabel = (type) => {
  const types = {
    'text': 'Texte',
    'pdf': 'Document PDF',
    'video': 'Vidéo',
    'audio': 'Audio (podcast)',
    'image': 'Image',
    'archive': 'Archive',
    'gallery': 'Galerie d\'images',
    'embed_video': 'Vidéo externe'
  };

  return types[type] || type;
};

// Méthode pour extraire le nom du fichier à partir du chemin
const getFileName = (path) => {
  if (!path) return '';
  return path.split('/').pop();
};

// Méthode pour formater la taille du fichier
const formatFileSize = (sizeInKB) => {
  if (!sizeInKB) return '0 KB';

  if (sizeInKB < 1024) {
    return `${Math.round(sizeInKB)} KB`;
  } else {
    return `${(sizeInKB / 1024).toFixed(2)} MB`;
  }
};

// Méthode pour obtenir l'URL d'intégration pour les vidéos externes
const getEmbedUrl = (url) => {
  if (!url) return '';

  try {
    // YouTube
    if (url.includes('youtube.com/watch') || url.includes('youtu.be')) {
      let videoId;
      if (url.includes('youtube.com/watch')) {
        const urlObj = new URL(url);
        videoId = urlObj.searchParams.get('v');
      } else {
        videoId = url.split('/').pop().split('?')[0];
      }

      if (!videoId) {
        console.error('Impossible d\'extraire l\'ID de la vidéo YouTube:', url);
        return '';
      }

      return `https://www.youtube.com/embed/${videoId}`;
    }

    // Dailymotion
    if (url.includes('dailymotion.com')) {
      const parts = url.split('/');
      const lastPart = parts.pop() || '';
      const videoId = lastPart.split('_')[0];

      if (!videoId) {
        console.error('Impossible d\'extraire l\'ID de la vidéo Dailymotion:', url);
        return '';
      }

      return `https://www.dailymotion.com/embed/video/${videoId}`;
    }

    // Vimeo
    if (url.includes('vimeo.com')) {
      const parts = url.split('/');
      const videoId = parts.pop();

      if (!videoId || isNaN(Number(videoId))) {
        console.error('Impossible d\'extraire l\'ID de la vidéo Vimeo:', url);
        return '';
      }

      return `https://player.vimeo.com/video/${videoId}`;
    }

    // Si l'URL est déjà une URL d'intégration, la retourner telle quelle
    if (url.includes('/embed/') || url.includes('player.')) {
      return url;
    }

    console.warn('URL de vidéo non reconnue:', url);
    return url;
  } catch (error) {
    console.error('Erreur lors de l\'analyse de l\'URL de la vidéo:', error);
    return '';
  }
};

// Méthode pour extraire les chemins de la galerie
const getGalleryPaths = (metadata) => {
  if (!metadata) return [];

  try {
    const parsed = typeof metadata === 'string' ? JSON.parse(metadata) : metadata;
    console.log('Métadonnées de la galerie:', parsed);

    // Vérifier différentes structures possibles de métadonnées
    if (parsed.gallery_paths && Array.isArray(parsed.gallery_paths)) {
      return parsed.gallery_paths;
    } else if (parsed.images && Array.isArray(parsed.images)) {
      // Format utilisé dans l'interface admin
      return parsed.images.map(img => img.path || img);
    } else if (Array.isArray(parsed)) {
      // Si les métadonnées sont directement un tableau
      return parsed;
    }

    // Aucune structure reconnue
    console.warn('Structure de métadonnées non reconnue:', parsed);
    return [];
  } catch (e) {
    console.error('Erreur lors de l\'analyse des métadonnées de la galerie:', e);
    return [];
  }
};

// Méthodes pour gérer les erreurs d'image
const handleImageError = (event) => {
  console.error('Erreur de chargement de l\'image principale:', event);
  event.target.src = '/images/image-not-found.png'; // Image de remplacement
};

const handleGalleryImageError = (event, index) => {
  console.error(`Erreur de chargement de l'image de galerie ${index}:`, event);
  imageErrors.value[index] = true;
  event.target.src = '/images/image-not-found.png'; // Image de remplacement
};
</script>

<style>
/* Style pour les vidéos responsives */
.aspect-w-16 {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 */
  height: 0;
}
.aspect-w-16 iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Style pour les galeries d'images */
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.gallery-item {
  overflow: hidden;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-5px);
}

.gallery-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-image:hover {
  transform: scale(1.05);
}

/* Style pour les messages d'erreur */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.error-icon {
  color: #ef4444;
  margin-bottom: 1rem;
}

.error-message {
  color: #4b5563;
  font-style: italic;
}
</style>
