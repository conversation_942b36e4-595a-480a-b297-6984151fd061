<?php

namespace App\Http\Controllers;

use App\Models\Certificate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class PublicCertificateController extends Controller
{
    /**
     * Visualise un certificat publiquement (sans authentification)
     * Cette route est utilisée pour les profils publics d'apprenants
     */
    public function view(Certificate $certificate)
    {
        // Vérifier que le certificat est visible publiquement (actif ou émis)
        if (!$certificate->isVisibleToStudent()) {
            abort(403, 'Ce certificat n\'est pas disponible publiquement.');
        }

        // Detect mobile device
        $userAgent = request()->header('User-Agent');
        $isMobile = preg_match('/Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i', $userAgent);

        // For mobile devices, use optimized headers
        if ($isMobile) {
            return $this->viewMobile($certificate);
        }

        try {
            // Vérifier si le certificat a un numéro de certificat
            if (empty($certificate->certificate_number)) {
                throw new \Exception('Le certificat n\'a pas de numéro valide.');
            }

            // Définir le chemin du PDF basé sur le numéro de certificat
            $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';

            // Vérifier si le PDF existe
            if (!Storage::disk('public')->exists($pdfPath)) {
                // Si le PDF n'existe pas, essayer de le générer via l'admin controller
                $adminController = new \App\Http\Controllers\Admin\CertificateController();
                $adminController->generatePdf($certificate);
                
                // Vérifier à nouveau si le PDF a été créé
                if (!Storage::disk('public')->exists($pdfPath)) {
                    throw new \Exception("Le certificat PDF n'est pas disponible.");
                }
            }

            // Retourner le PDF pour affichage dans le navigateur
            $fullPath = Storage::disk('public')->path($pdfPath);
            
            return response()->file($fullPath, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="Certificat_' . $certificate->certificate_number . '.pdf"'
            ]);

        } catch (\Exception $e) {
            // En cas d'erreur, enregistrer l'erreur dans les logs
            \Log::error('Erreur lors de la visualisation publique du certificat: ' . $e->getMessage());
            \Log::error('Trace: ' . $e->getTraceAsString());

            // Retourner une erreur
            abort(500, 'Erreur lors de la visualisation du certificat: ' . $e->getMessage());
        }
    }

    /**
     * Visualise un certificat publiquement optimisé pour mobile
     */
    private function viewMobile(Certificate $certificate)
    {
        try {
            // Vérifier si le certificat a un numéro de certificat
            if (empty($certificate->certificate_number)) {
                throw new \Exception('Le certificat n\'a pas de numéro valide.');
            }

            // Définir le chemin du PDF basé sur le numéro de certificat
            $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';

            // Vérifier si le PDF existe
            if (!Storage::disk('public')->exists($pdfPath)) {
                // Si le PDF n'existe pas, essayer de le générer via l'admin controller
                $adminController = new \App\Http\Controllers\Admin\CertificateController();
                $adminController->generatePdf($certificate);

                // Vérifier à nouveau si le PDF a été créé
                if (!Storage::disk('public')->exists($pdfPath)) {
                    throw new \Exception("Le certificat PDF n'est pas disponible.");
                }
            }

            // Obtenir le chemin physique complet du fichier
            $fullPath = Storage::disk('public')->path($pdfPath);

            // Vérifier que le fichier existe physiquement
            if (!file_exists($fullPath)) {
                throw new \Exception("Le fichier PDF n'existe pas sur le disque: " . $fullPath);
            }

            // Headers optimisés pour mobile
            return response()->file($fullPath, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="Certificat_' . $certificate->certificate_number . '.pdf"',
                'Cache-Control' => 'public, max-age=3600', // Cache for mobile
                'X-Frame-Options' => 'SAMEORIGIN',
                'X-Content-Type-Options' => 'nosniff',
                'X-Mobile-Optimized' => 'true'
            ]);

        } catch (\Exception $e) {
            // En cas d'erreur, retourner une erreur simple
            \Log::error('Erreur lors de la visualisation mobile publique du certificat: ' . $e->getMessage());
            abort(500, 'Erreur lors de la visualisation du certificat sur mobile: ' . $e->getMessage());
        }
    }
}
