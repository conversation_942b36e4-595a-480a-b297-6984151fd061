import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/js/app.js',
                'resources/js/Pages/Admin/Dashboard.vue',
                'resources/js/Pages/Admin/TrainingDomains/Index.vue',
                'resources/js/Pages/Admin/TrainingDomains/Create.vue',
                'resources/js/Pages/Admin/TrainingDomains/Edit.vue',
                'resources/js/Pages/Admin/TrainingDomains/Show.vue',
                'resources/js/Pages/Admin/Trainers/Index.vue',
                'resources/js/Pages/Admin/Trainers/Create.vue',
                'resources/js/Pages/Admin/Trainers/Edit.vue',
                'resources/js/Pages/Admin/Trainers/Show.vue',
                'resources/js/Pages/Admin/Exams/Index.vue',
                'resources/js/Pages/Admin/Exams/Create.vue',
                'resources/js/Pages/Admin/Exams/Edit.vue',
                'resources/js/Pages/Admin/Exams/Show.vue',
                'resources/js/Pages/Admin/TrainingSessions/Index.vue',
                'resources/js/Pages/Admin/TrainingSessions/Create.vue',
                'resources/js/Pages/Admin/TrainingSessions/Edit.vue',
                'resources/js/Pages/Admin/TrainingSessions/Show.vue',
                'resources/js/Pages/Admin/Enrollments/Index.vue',
                'resources/js/Pages/Admin/Enrollments/Create.vue',
                'resources/js/Pages/Admin/Enrollments/Edit.vue',
                'resources/js/Pages/Admin/Enrollments/Show.vue',
                'resources/js/Pages/Admin/ExamResults/Index.vue',
                'resources/js/Pages/Admin/ExamResults/Create.vue',
                'resources/js/Pages/Admin/ExamResults/Edit.vue',
                'resources/js/Pages/Admin/ExamResults/Show.vue',
                'resources/js/Pages/Admin/Certificates/Index.vue',
                'resources/js/Pages/Admin/Certificates/Create.vue',
                'resources/js/Pages/Admin/Certificates/Edit.vue',
                'resources/js/Pages/Admin/Certificates/Show.vue',
                'resources/js/Pages/Admin/ExamQuestions/Index.vue',
                'resources/js/Pages/Admin/ExamQuestions/Create.vue',
                'resources/js/Pages/Admin/ExamQuestions/Edit.vue',
                'resources/js/Pages/Admin/ExamQuestions/Show.vue',
                'resources/js/Pages/Admin/Modules/Index.vue',
                'resources/js/Pages/Admin/Modules/Create.vue',
                'resources/js/Pages/Admin/Modules/Edit.vue',
                'resources/js/Pages/Admin/Modules/Show.vue',
                'resources/js/Pages/Trainer/QuestionBank/Index.vue',
                'resources/js/Pages/Trainer/QuestionBank/Create.vue',
                'resources/js/Pages/Trainer/QuestionBank/Edit.vue',
                'resources/js/Pages/Trainer/Modules/Index.vue',
                'resources/js/Pages/Trainer/Modules/Create.vue',
                'resources/js/Pages/Trainer/Modules/Edit.vue',
                'resources/js/Pages/Trainer/Modules/Show.vue',
                'resources/js/Pages/Trainer/Courses/Index.vue',
                'resources/js/Pages/Trainer/Evaluations/Index.vue',
                'resources/js/Pages/Trainer/Evaluations/Create.vue',
                'resources/js/Pages/Trainer/Evaluations/Edit.vue',
                'resources/js/Pages/Trainer/Evaluations/Show.vue',
                'resources/js/Pages/Trainer/Evaluations/Results.vue',
                'resources/js/Pages/Trainer/Evaluations/Questions/Index.vue',
                'resources/js/Pages/Trainer/Evaluations/Questions/Create.vue',
                'resources/js/Pages/Trainer/Evaluations/Questions/Edit.vue',
                'resources/js/Pages/Trainer/Exams/Index.vue',
                'resources/js/Pages/Trainer/ExamResults/Index.vue',
                'resources/js/Pages/Welcome.vue',
                'resources/js/Pages/Sessions/Show.vue',
                'resources/js/Pages/Sessions/Enroll.vue',
                'resources/js/Pages/Sessions/EnrollmentConfirmation.vue',
                'resources/js/Pages/Student/Enrollments/Index.vue',
                'resources/js/Pages/Student/Enrollments/Show.vue',
                'resources/js/Pages/Student/Enrollments/Payments.vue',
                'resources/js/Pages/Admin/Announcements/Index.vue',
                'resources/js/Pages/Admin/Announcements/Create.vue',
                'resources/js/Pages/Admin/Announcements/Edit.vue',
                'resources/js/Pages/Admin/Announcements/Show.vue',
                'resources/js/Pages/Announcements/Index.vue',
                'resources/js/Pages/Announcements/Show.vue',
            ],
            refresh: true,
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],
});
