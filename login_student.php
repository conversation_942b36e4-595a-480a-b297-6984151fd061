<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;

// Récupérer l'étudiant Sophie Bernard
$student = User::where('email', '<EMAIL>')->first();

if (!$student) {
    echo "Étudiant non trouvé\n";
    exit(1);
}

echo "Étudiant trouvé: {$student->name} ({$student->email})\n";
echo "Mot de passe par défaut: password\n";
echo "\nVous pouvez maintenant vous connecter sur: http://localhost:8000/login\n";
echo "Email: {$student->email}\n";
echo "Mot de passe: password\n";
