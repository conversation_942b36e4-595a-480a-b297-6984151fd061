<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\CourseMaterial;
use App\Models\Module;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class CourseMaterialController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $courseId = $request->query('course_id');
        $moduleId = $request->query('module_id');

        if (!$courseId && !$moduleId) {
            return redirect()->route('admin.courses.index')
                ->with('error', 'Veuillez sélectionner un cours ou un module pour voir ses matériels pédagogiques.');
        }

        if ($moduleId) {
            // Récupérer le module et ses matériels
            $module = Module::with(['course.trainingSession'])->findOrFail($moduleId);
            $course = $module->course;
            $materials = CourseMaterial::where('module_id', $moduleId)
                ->orderBy('order')
                ->paginate(10);

            return Inertia::render('Admin/CourseMaterials/Index', [
                'course' => $course,
                'module' => $module,
                'materials' => $materials
            ]);
        } else {
            // Récupérer le cours et ses matériels (hors modules)
            $course = Course::with('trainingSession')->findOrFail($courseId);
            $materials = CourseMaterial::where('course_id', $courseId)
                ->whereNull('module_id')
                ->orderBy('order')
                ->paginate(10);

            return Inertia::render('Admin/CourseMaterials/Index', [
                'course' => $course,
                'materials' => $materials
            ]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $courseId = $request->query('course_id');
        $moduleId = $request->query('module_id');

        if (!$courseId && !$moduleId) {
            return redirect()->route('admin.courses.index')
                ->with('error', 'Veuillez sélectionner un cours ou un module pour ajouter un matériel pédagogique.');
        }

        if ($moduleId) {
            // Récupérer le module
            $module = Module::with(['course.trainingSession'])->findOrFail($moduleId);
            $course = $module->course;

            return Inertia::render('Admin/CourseMaterials/Create', [
                'course' => $course,
                'module' => $module,
                'materialTypes' => [
                    ['value' => 'text', 'label' => 'Texte'],
                    ['value' => 'pdf', 'label' => 'Document PDF'],
                    ['value' => 'video', 'label' => 'Vidéo'],
                    ['value' => 'audio', 'label' => 'Audio (podcast)'],
                    ['value' => 'image', 'label' => 'Image'],
                    ['value' => 'archive', 'label' => 'Archive (ZIP, RAR, etc.)'],
                    ['value' => 'gallery', 'label' => 'Galerie d\'images'],
                    ['value' => 'embed_video', 'label' => 'Vidéo externe (YouTube, Dailymotion, Vimeo)']
                ]
            ]);
        } else {
            // Récupérer le cours
            $course = Course::with('trainingSession')->findOrFail($courseId);

            return Inertia::render('Admin/CourseMaterials/Create', [
                'course' => $course,
                'materialTypes' => [
                    ['value' => 'text', 'label' => 'Texte'],
                    ['value' => 'pdf', 'label' => 'Document PDF'],
                    ['value' => 'video', 'label' => 'Vidéo'],
                    ['value' => 'audio', 'label' => 'Audio (podcast)'],
                    ['value' => 'image', 'label' => 'Image'],
                    ['value' => 'archive', 'label' => 'Archive (ZIP, RAR, etc.)'],
                    ['value' => 'gallery', 'label' => 'Galerie d\'images'],
                    ['value' => 'embed_video', 'label' => 'Vidéo externe (YouTube, Dailymotion, Vimeo)']
                ]
            ]);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'course_id' => 'required|exists:courses,id',
            'module_id' => 'nullable|exists:modules,id',
            'type' => 'required|in:text,pdf,video,audio,image,archive,gallery,embed_video',
            'content' => 'nullable|string',
            'embed_code' => 'nullable|string',
            'video_url' => 'nullable|url',
            'file' => 'nullable|file', // Taille illimitée
            'gallery_files.*' => 'nullable|file|image', // Pour les fichiers multiples de la galerie
            'order' => 'nullable|integer|min:0',
            'active' => 'boolean',
            'allow_download' => 'boolean',
            'allow_online_viewing' => 'boolean',
        ]);

        // Initialiser les données du matériel
        $materialData = [
            'title' => $validated['title'],
            'description' => $validated['description'] ?? null,
            'course_id' => $validated['course_id'],
            'module_id' => $validated['module_id'] ?? null,
            'type' => $validated['type'],
            'order' => $validated['order'] ?? 0,
            'active' => $validated['active'] ?? true,
            'allow_download' => $validated['allow_download'] ?? true,
            'allow_online_viewing' => $validated['allow_online_viewing'] ?? true,
        ];

        // Traiter le contenu en fonction du type
        if ($validated['type'] === 'text') {
            $materialData['content'] = $validated['content'] ?? null;
        } elseif ($validated['type'] === 'embed_video') {
            // Pour les vidéos externes (YouTube, Dailymotion, Vimeo)
            if (isset($validated['video_url'])) {
                // Convertir l'URL en code d'intégration
                $embedCode = $this->generateEmbedCodeFromUrl($validated['video_url']);
                $materialData['embed_code'] = $embedCode;
            } else if (isset($validated['embed_code'])) {
                $materialData['embed_code'] = $validated['embed_code'];
            }
            $materialData['allow_download'] = false; // On ne peut pas télécharger une vidéo externe
        } elseif ($validated['type'] === 'gallery') {
            // Pour les galeries d'images
            if ($request->hasFile('gallery_files')) {
                $galleryFiles = $request->file('gallery_files');
                $images = [];
                $firstPath = null;

                // Traiter chaque image
                foreach ($galleryFiles as $index => $file) {
                    $path = $file->store('course-materials/gallery', 'public');

                    // Stocker les informations de l'image
                    $images[] = [
                        'path' => $path,
                        'caption' => $validated['title'] . ' - Image ' . ($index + 1),
                        'order' => $index
                    ];

                    // Utiliser la première image comme référence principale
                    if ($index === 0) {
                        $firstPath = $path;
                        $materialData['file_path'] = $path;
                        $materialData['mime_type'] = $file->getMimeType();
                        $materialData['file_size'] = $file->getSize() / 1024; // Convertir en Ko
                        $materialData['thumbnail_path'] = $path;
                    }
                }

                // Stocker les métadonnées de la galerie
                $metadata = [
                    'images' => $images
                ];
                $materialData['metadata'] = json_encode($metadata);

                // Log pour le débogage
                \Log::info('Galerie créée avec ' . count($images) . ' images', [
                    'images' => $images,
                    'metadata' => $metadata,
                    'metadata_json' => $materialData['metadata']
                ]);
            } elseif ($request->hasFile('file')) {
                // Compatibilité avec l'ancien système à fichier unique
                $file = $request->file('file');
                $path = $file->store('course-materials/gallery', 'public');
                $materialData['file_path'] = $path;
                $materialData['mime_type'] = $file->getMimeType();
                $materialData['file_size'] = $file->getSize() / 1024; // Convertir en Ko

                // Stocker les métadonnées de la galerie
                $metadata = [
                    'images' => [
                        [
                            'path' => $path,
                            'caption' => $validated['title'],
                            'order' => 0
                        ]
                    ]
                ];
                $materialData['metadata'] = json_encode($metadata);

                // Utiliser la première image comme miniature
                $materialData['thumbnail_path'] = $path;
            }
        } elseif (in_array($validated['type'], ['pdf', 'video', 'audio', 'image', 'archive'])) {
            if ($request->hasFile('file')) {
                $file = $request->file('file');
                $path = $file->store('course-materials/' . $validated['type'], 'public');
                $materialData['file_path'] = $path;
                $materialData['mime_type'] = $file->getMimeType();
                $materialData['file_size'] = $file->getSize() / 1024; // Convertir en Ko

                // Générer une miniature pour les images
                if ($validated['type'] === 'image') {
                    // Pour les images, on pourrait utiliser une bibliothèque comme Intervention Image
                    // pour créer une miniature, mais pour l'instant on utilise l'image originale
                    $materialData['thumbnail_path'] = $path;
                }

                // Pour les archives, définir le type MIME approprié
                if ($validated['type'] === 'archive') {
                    $extension = strtolower($file->getClientOriginalExtension());
                    $mimeTypes = [
                        'zip' => 'application/zip',
                        'rar' => 'application/x-rar-compressed',
                        '7z' => 'application/x-7z-compressed',
                        'tar' => 'application/x-tar',
                        'gz' => 'application/gzip',
                    ];

                    if (isset($mimeTypes[$extension])) {
                        $materialData['mime_type'] = $mimeTypes[$extension];
                    }

                    // Désactiver la visualisation en ligne pour les archives
                    $materialData['allow_online_viewing'] = false;
                }
            }
        }

        // Créer le matériel de cours
        CourseMaterial::create($materialData);

        // Rediriger vers la liste des matériels du cours ou du module avec un message de succès
        if (isset($validated['module_id'])) {
            return redirect()->route('admin.course-materials.index', ['module_id' => $validated['module_id']])
                ->with('success', 'Matériel pédagogique ajouté avec succès.');
        } else {
            return redirect()->route('admin.course-materials.index', ['course_id' => $validated['course_id']])
                ->with('success', 'Matériel pédagogique ajouté avec succès.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Récupérer le matériel de cours avec ses relations
        $material = CourseMaterial::with(['course.trainingSession'])->findOrFail($id);

        return Inertia::render('Admin/CourseMaterials/Show', [
            'material' => $material
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Récupérer le matériel de cours
        $material = CourseMaterial::with('course')->findOrFail($id);

        return Inertia::render('Admin/CourseMaterials/Edit', [
            'material' => $material,
            'materialTypes' => [
                ['value' => 'text', 'label' => 'Texte'],
                ['value' => 'pdf', 'label' => 'Document PDF'],
                ['value' => 'video', 'label' => 'Vidéo'],
                ['value' => 'audio', 'label' => 'Audio (podcast)'],
                ['value' => 'image', 'label' => 'Image'],
                ['value' => 'archive', 'label' => 'Archive (ZIP, RAR, etc.)'],
                ['value' => 'gallery', 'label' => 'Galerie d\'images'],
                ['value' => 'embed_video', 'label' => 'Vidéo externe (YouTube, Dailymotion, Vimeo)']
            ]
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Récupérer le matériel de cours
        $material = CourseMaterial::findOrFail($id);

        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:text,pdf,video,audio,image,archive,gallery,embed_video',
            'content' => 'nullable|string',
            'embed_code' => 'nullable|string',
            'video_url' => 'nullable|url',
            'file' => 'nullable|file', // Taille illimitée
            'gallery_files.*' => 'nullable|file|image', // Pour les fichiers multiples de la galerie
            'order' => 'nullable|integer|min:0',
            'active' => 'boolean',
            'allow_download' => 'boolean',
            'allow_online_viewing' => 'boolean',
        ]);

        // Initialiser les données du matériel
        $materialData = [
            'title' => $validated['title'],
            'description' => $validated['description'] ?? null,
            'type' => $validated['type'],
            'order' => $validated['order'] ?? 0,
            'active' => $validated['active'] ?? true,
            'allow_download' => $validated['allow_download'] ?? true,
            'allow_online_viewing' => $validated['allow_online_viewing'] ?? true,
        ];

        // Traiter le contenu en fonction du type
        if ($validated['type'] === 'text') {
            $materialData['content'] = $validated['content'] ?? null;
            // Si le type a changé, supprimer l'ancien fichier
            if ($material->type !== 'text' && $material->file_path) {
                Storage::disk('public')->delete($material->file_path);
                $materialData['file_path'] = null;
                $materialData['mime_type'] = null;
                $materialData['file_size'] = null;
                $materialData['thumbnail_path'] = null;
                $materialData['embed_code'] = null;
            }
        } elseif ($validated['type'] === 'embed_video') {
            // Pour les vidéos externes (YouTube, Dailymotion, Vimeo)
            if (isset($validated['video_url'])) {
                // Convertir l'URL en code d'intégration
                $embedCode = $this->generateEmbedCodeFromUrl($validated['video_url']);
                $materialData['embed_code'] = $embedCode;
            } else if (isset($validated['embed_code'])) {
                $materialData['embed_code'] = $validated['embed_code'];
            }

            $materialData['allow_download'] = false; // On ne peut pas télécharger une vidéo externe

            // Si le type a changé, supprimer l'ancien fichier
            if ($material->type !== 'embed_video' && $material->file_path) {
                Storage::disk('public')->delete($material->file_path);
                $materialData['file_path'] = null;
                $materialData['mime_type'] = null;
                $materialData['file_size'] = null;
                $materialData['thumbnail_path'] = null;
            }

            // Vérifier si un code d'intégration ou une URL a été fourni
            if (empty($validated['embed_code']) && empty($validated['video_url']) && empty($material->embed_code)) {
                return redirect()->back()->withErrors([
                    'video_url' => 'Une URL de vidéo est requise pour les vidéos externes.'
                ]);
            }
        } elseif ($validated['type'] === 'gallery') {
            // Pour les galeries d'images
            if ($request->hasFile('gallery_files')) {
                $galleryFiles = $request->file('gallery_files');
                $images = [];
                $firstPath = null;

                // Si c'est une nouvelle galerie (changement de type)
                if ($material->type !== 'gallery') {
                    // Supprimer l'ancien fichier s'il existe
                    if ($material->file_path) {
                        Storage::disk('public')->delete($material->file_path);
                    }

                    // Traiter chaque image
                    foreach ($galleryFiles as $index => $file) {
                        $path = $file->store('course-materials/gallery', 'public');

                        // Stocker les informations de l'image
                        $images[] = [
                            'path' => $path,
                            'caption' => $validated['title'] . ' - Image ' . ($index + 1),
                            'order' => $index
                        ];

                        // Utiliser la première image comme référence principale
                        if ($index === 0) {
                            $firstPath = $path;
                            $materialData['file_path'] = $path;
                            $materialData['mime_type'] = $file->getMimeType();
                            $materialData['file_size'] = $file->getSize() / 1024; // Convertir en Ko
                            $materialData['thumbnail_path'] = $path;
                        }
                    }

                    // Stocker les métadonnées de la galerie
                    $metadata = [
                        'images' => $images
                    ];
                    $materialData['metadata'] = json_encode($metadata);
                } else {
                    // Ajouter des images à une galerie existante
                    $metadata = is_array($material->metadata) ? $material->metadata : (json_decode($material->metadata, true) ?? ['images' => []]);
                    $currentCount = count($metadata['images'] ?? []);

                    // Traiter chaque nouvelle image
                    foreach ($galleryFiles as $index => $file) {
                        $path = $file->store('course-materials/gallery', 'public');

                        // Ajouter l'image aux métadonnées
                        $newImage = [
                            'path' => $path,
                            'caption' => $validated['title'] . ' - Image ' . ($currentCount + $index + 1),
                            'order' => $currentCount + $index
                        ];
                        $metadata['images'][] = $newImage;

                        // Si c'est la première image d'une galerie vide, l'utiliser comme référence principale
                        if ($currentCount === 0 && $index === 0) {
                            $materialData['file_path'] = $path;
                            $materialData['mime_type'] = $file->getMimeType();
                            $materialData['file_size'] = $file->getSize() / 1024;
                            $materialData['thumbnail_path'] = $path;
                        }
                    }

                    $materialData['metadata'] = json_encode($metadata);
                }
            } elseif ($request->hasFile('file')) {
                // Compatibilité avec l'ancien système à fichier unique
                $file = $request->file('file');
                $path = $file->store('course-materials/gallery', 'public');

                // Si c'est une nouvelle galerie (changement de type)
                if ($material->type !== 'gallery') {
                    // Supprimer l'ancien fichier s'il existe
                    if ($material->file_path) {
                        Storage::disk('public')->delete($material->file_path);
                    }

                    // Initialiser les métadonnées de la galerie
                    $metadata = [
                        'images' => [
                            [
                                'path' => $path,
                                'caption' => $validated['title'],
                                'order' => 0
                            ]
                        ]
                    ];

                    $materialData['file_path'] = $path;
                    $materialData['mime_type'] = $file->getMimeType();
                    $materialData['file_size'] = $file->getSize() / 1024; // Convertir en Ko
                    $materialData['metadata'] = json_encode($metadata);
                    $materialData['thumbnail_path'] = $path;
                } else {
                    // Ajouter une image à une galerie existante
                    $metadata = is_array($material->metadata) ? $material->metadata : (json_decode($material->metadata, true) ?? ['images' => []]);
                    $newImage = [
                        'path' => $path,
                        'caption' => $validated['title'],
                        'order' => count($metadata['images'] ?? [])
                    ];
                    $metadata['images'][] = $newImage;

                    $materialData['metadata'] = json_encode($metadata);

                    // Si c'est la première image, l'utiliser comme miniature et fichier principal
                    if (count($metadata['images'] ?? []) === 1) {
                        $materialData['file_path'] = $path;
                        $materialData['mime_type'] = $file->getMimeType();
                        $materialData['file_size'] = $file->getSize() / 1024;
                        $materialData['thumbnail_path'] = $path;
                    }
                }
            } elseif ($material->type !== 'gallery' && !$material->file_path) {
                // Si on change le type en galerie mais qu'aucun fichier n'est fourni
                return redirect()->back()->withErrors([
                    'gallery_files' => 'Au moins une image est requise pour créer une galerie.'
                ]);
            }
        } elseif (in_array($validated['type'], ['pdf', 'video', 'audio', 'image', 'archive'])) {
            if ($request->hasFile('file')) {
                // Supprimer l'ancien fichier s'il existe
                if ($material->file_path) {
                    Storage::disk('public')->delete($material->file_path);
                }

                $file = $request->file('file');
                $path = $file->store('course-materials/' . $validated['type'], 'public');
                $materialData['file_path'] = $path;
                $materialData['mime_type'] = $file->getMimeType();
                $materialData['file_size'] = $file->getSize() / 1024; // Convertir en Ko
                $materialData['embed_code'] = null; // Réinitialiser le code d'intégration

                // Générer une miniature pour les images
                if ($validated['type'] === 'image') {
                    // Pour les images, on pourrait utiliser une bibliothèque comme Intervention Image
                    // pour créer une miniature, mais pour l'instant on utilise l'image originale
                    $materialData['thumbnail_path'] = $path;
                }

                // Pour les archives, définir le type MIME approprié
                if ($validated['type'] === 'archive') {
                    $extension = strtolower($file->getClientOriginalExtension());
                    $mimeTypes = [
                        'zip' => 'application/zip',
                        'rar' => 'application/x-rar-compressed',
                        '7z' => 'application/x-7z-compressed',
                        'tar' => 'application/x-tar',
                        'gz' => 'application/gzip',
                    ];

                    if (isset($mimeTypes[$extension])) {
                        $materialData['mime_type'] = $mimeTypes[$extension];
                    }

                    // Désactiver la visualisation en ligne pour les archives
                    $materialData['allow_online_viewing'] = false;
                }
            } elseif ($material->type !== $validated['type']) {
                // Si le type a changé mais qu'aucun nouveau fichier n'a été fourni
                return redirect()->back()->withErrors([
                    'file' => 'Un fichier est requis pour ce type de matériel.'
                ]);
            }
        }

        // Mettre à jour le matériel de cours
        $material->update($materialData);

        // Rediriger vers la liste des matériels du cours ou du module avec un message de succès
        if ($material->module_id) {
            return redirect()->route('admin.course-materials.index', ['module_id' => $material->module_id])
                ->with('success', 'Matériel pédagogique mis à jour avec succès.');
        } else {
            return redirect()->route('admin.course-materials.index', ['course_id' => $material->course_id])
                ->with('success', 'Matériel pédagogique mis à jour avec succès.');
        }
    }

    /**
     * Générer un code d'intégration à partir d'une URL de vidéo
     */
    private function generateEmbedCodeFromUrl($url)
    {
        // YouTube
        if (preg_match('/youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/', $url, $matches) ||
            preg_match('/youtu\.be\/([a-zA-Z0-9_-]+)/', $url, $matches)) {
            $videoId = $matches[1];
            return '<iframe width="100%" height="400" src="https://www.youtube.com/embed/' . $videoId . '" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>';
        }

        // Dailymotion
        if (preg_match('/dailymotion\.com\/video\/([a-zA-Z0-9_-]+)/', $url, $matches)) {
            $videoId = $matches[1];
            return '<iframe width="100%" height="400" src="https://www.dailymotion.com/embed/video/' . $videoId . '" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>';
        }

        // Vimeo
        if (preg_match('/vimeo\.com\/([0-9]+)/', $url, $matches)) {
            $videoId = $matches[1];
            return '<iframe width="100%" height="400" src="https://player.vimeo.com/video/' . $videoId . '" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>';
        }

        // Si aucun format reconnu, retourner l'URL comme texte
        return '<p>Vidéo non reconnue : ' . htmlspecialchars($url) . '</p>';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Récupérer le matériel de cours
        $material = CourseMaterial::findOrFail($id);
        $courseId = $material->course_id;
        $moduleId = $material->module_id;

        // Supprimer le fichier associé s'il existe
        if ($material->file_path) {
            Storage::disk('public')->delete($material->file_path);
        }

        // Supprimer la miniature s'il en existe une
        if ($material->thumbnail_path && $material->thumbnail_path !== $material->file_path) {
            Storage::disk('public')->delete($material->thumbnail_path);
        }

        // Supprimer le matériel de cours
        $material->delete();

        // Rediriger vers la liste des matériels du cours ou du module avec un message de succès
        if ($moduleId) {
            return redirect()->route('admin.course-materials.index', ['module_id' => $moduleId])
                ->with('success', 'Matériel pédagogique supprimé avec succès.');
        } else {
            return redirect()->route('admin.course-materials.index', ['course_id' => $courseId])
                ->with('success', 'Matériel pédagogique supprimé avec succès.');
        }
    }

    /**
     * Télécharger le fichier associé au matériel de cours
     */
    public function download(string $id)
    {
        // Récupérer le matériel de cours
        $material = CourseMaterial::findOrFail($id);

        // Vérifier si le téléchargement est autorisé
        if (!$material->allow_download) {
            return redirect()->back()->with('error', 'Le téléchargement de ce matériel n\'est pas autorisé.');
        }

        // Vérifier si un fichier est associé
        if (!$material->file_path) {
            return redirect()->back()->with('error', 'Aucun fichier n\'est associé à ce matériel.');
        }

        // Obtenir le chemin complet du fichier
        $filePath = Storage::disk('public')->path($material->file_path);

        // Vérifier si le fichier existe
        if (!file_exists($filePath)) {
            return redirect()->back()->with('error', 'Le fichier n\'existe pas sur le serveur.');
        }

        // Déterminer l'extension du fichier
        $extension = pathinfo($material->file_path, PATHINFO_EXTENSION);

        // Créer un nom de fichier pour le téléchargement
        $downloadName = $material->title . '.' . $extension;

        // Journaliser les informations pour le débogage
        \Log::info('Téléchargement de fichier', [
            'material_id' => $material->id,
            'file_path' => $material->file_path,
            'full_path' => $filePath,
            'download_name' => $downloadName,
            'exists' => file_exists($filePath)
        ]);

        // Télécharger le fichier avec le bon type MIME
        return response()->download($filePath, $downloadName, [
            'Content-Type' => $material->mime_type ?? 'application/octet-stream',
        ]);
    }

    /**
     * Afficher le fichier associé au matériel de cours
     */
    public function view(Request $request, string $id)
    {
        // Récupérer le matériel de cours
        $material = CourseMaterial::findOrFail($id);

        // Vérifier si la visualisation en ligne est autorisée
        if (!$material->allow_online_viewing) {
            return redirect()->back()->with('error', 'La visualisation en ligne de ce matériel n\'est pas autorisée.');
        }

        // Vérifier si un fichier est associé
        if (!$material->file_path && $material->type !== 'gallery') {
            return redirect()->back()->with('error', 'Aucun fichier n\'est associé à ce matériel.');
        }

        // Traitement spécial pour les galeries d'images
        if ($material->type === 'gallery') {
            $imageIndex = $request->query('image_index', 0);
            $metadata = is_array($material->metadata) ? $material->metadata : json_decode($material->metadata, true);

            // Journaliser les métadonnées pour le débogage
            \Log::info('Métadonnées de la galerie (admin)', [
                'material_id' => $material->id,
                'metadata' => $metadata
            ]);

            // Extraire les chemins d'images en fonction de la structure des métadonnées
            $galleryPaths = [];

            if (is_array($metadata)) {
                if (isset($metadata['gallery_paths']) && is_array($metadata['gallery_paths'])) {
                    // Format utilisé dans l'interface formateur
                    $galleryPaths = array_values($metadata['gallery_paths']);
                } elseif (isset($metadata['images']) && is_array($metadata['images'])) {
                    // Format utilisé dans l'interface admin
                    $galleryPaths = array_map(function($img) {
                        return is_array($img) ? ($img['path'] ?? '') : $img;
                    }, $metadata['images']);
                } elseif (is_array($metadata) && !isset($metadata['gallery_paths']) && !isset($metadata['images'])) {
                    // Si les métadonnées sont directement un tableau de chemins
                    $galleryPaths = array_values($metadata);
                }
            }

            // Filtrer les chemins vides
            $galleryPaths = array_filter($galleryPaths);

            if (empty($galleryPaths)) {
                \Log::warning('Aucune image trouvée dans la galerie (admin)', [
                    'material_id' => $material->id,
                    'metadata' => $metadata
                ]);
                return response()->json(['error' => 'Aucune image trouvée dans la galerie'], 404);
            }

            // Vérifier si l'index demandé existe
            if (!isset($galleryPaths[$imageIndex])) {
                \Log::warning('Image non trouvée à cet index (admin)', [
                    'material_id' => $material->id,
                    'image_index' => $imageIndex,
                    'available_indices' => array_keys($galleryPaths)
                ]);
                return response()->json(['error' => 'Image non trouvée à cet index'], 404);
            }

            $imagePath = $galleryPaths[$imageIndex];
            $filePath = Storage::disk('public')->path($imagePath);

            // Vérifier si le fichier existe
            if (!file_exists($filePath)) {
                \Log::error('Image de galerie non trouvée', [
                    'material_id' => $material->id,
                    'image_index' => $imageIndex,
                    'image_path' => $imagePath,
                    'full_path' => $filePath
                ]);
                return response()->json(['error' => 'Image non trouvée'], 404);
            }

            // Déterminer le type MIME
            $mimeType = mime_content_type($filePath) ?: 'image/jpeg';

            // Journaliser les informations pour le débogage
            \Log::info('Visualisation d\'image de galerie', [
                'material_id' => $material->id,
                'image_index' => $imageIndex,
                'image_path' => $imagePath,
                'full_path' => $filePath,
                'mime_type' => $mimeType
            ]);

            return response()->file($filePath, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => 'inline; filename="' . basename($imagePath) . '"',
            ]);
        }

        // Pour les autres types de matériels
        $filePath = Storage::disk('public')->path($material->file_path);

        // Vérifier si le fichier existe
        if (!file_exists($filePath)) {
            \Log::error('Fichier non trouvé', [
                'material_id' => $material->id,
                'file_path' => $material->file_path,
                'full_path' => $filePath
            ]);
            return response()->json(['error' => 'Fichier non trouvé'], 404);
        }

        // Journaliser les informations pour le débogage
        \Log::info('Visualisation de fichier', [
            'material_id' => $material->id,
            'file_path' => $material->file_path,
            'full_path' => $filePath,
            'mime_type' => $material->mime_type,
            'exists' => file_exists($filePath)
        ]);

        // Afficher le fichier avec le bon type MIME
        return response()->file($filePath, [
            'Content-Type' => $material->mime_type ?? $this->getMimeTypeFromExtension($filePath),
            'Content-Disposition' => 'inline; filename="' . basename($material->file_path) . '"',
        ]);
    }

    /**
     * Détermine le type MIME à partir de l'extension du fichier
     */
    private function getMimeTypeFromExtension($filePath)
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        $mimeTypes = [
            'pdf' => 'application/pdf',
            'mp4' => 'video/mp4',
            'webm' => 'video/webm',
            'ogg' => 'video/ogg',
            'mp3' => 'audio/mpeg',
            'wav' => 'audio/wav',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'zip' => 'application/zip',
            'rar' => 'application/x-rar-compressed',
            '7z' => 'application/x-7z-compressed',
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
}
