<?php

namespace App\Http\Controllers\Trainer;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Evaluation;
use App\Models\EvaluationQuestion;
use App\Models\EvaluationResponse;
use App\Models\TrainingSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class EvaluationController extends Controller
{
    /**
     * Affiche la liste des évaluations du formateur
     */
    public function index(Request $request)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();
        
        // Récupérer les sessions de formation du formateur
        $trainingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->with('trainingDomain')
            ->get();
            
        $sessionIds = $trainingSessions->pluck('id')->toArray();
        
        // Initialiser la requête pour les évaluations
        $query = Evaluation::where('trainer_id', $trainer->id)
            ->with(['trainingSession', 'course']);
            
        // Appliquer les filtres
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function($q) use ($searchTerm) {
                $q->where('title', 'like', $searchTerm)
                  ->orWhere('description', 'like', $searchTerm);
            });
        }
        
        if ($request->has('training_session_id') && !empty($request->training_session_id)) {
            $query->where('training_session_id', $request->training_session_id);
        }
        
        if ($request->has('course_id') && !empty($request->course_id)) {
            $query->where('course_id', $request->course_id);
        }
        
        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }
        
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }
        
        // Ajouter les compteurs
        $query->withCount(['questions', 'responses']);
        
        // Récupérer les évaluations avec pagination
        $evaluations = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();
            
        // Récupérer les cours pour le filtre
        $courses = Course::whereIn('training_session_id', $sessionIds)
            ->orderBy('title')
            ->get();
            
        // Retourner la vue avec les évaluations et les filtres
        return Inertia::render('Trainer/Evaluations/Index', [
            'evaluations' => $evaluations,
            'trainingSessions' => $trainingSessions,
            'courses' => $courses,
            'filters' => [
                'search' => $request->search ?? '',
                'training_session_id' => $request->training_session_id ?? '',
                'course_id' => $request->course_id ?? '',
                'type' => $request->type ?? '',
                'status' => $request->status ?? '',
            ],
            'evaluationTypes' => [
                ['value' => 'feedback', 'label' => 'Feedback'],
                ['value' => 'survey', 'label' => 'Sondage'],
                ['value' => 'rating', 'label' => 'Évaluation'],
            ],
            'statusTypes' => [
                ['value' => 'draft', 'label' => 'Brouillon'],
                ['value' => 'active', 'label' => 'Active'],
                ['value' => 'closed', 'label' => 'Fermée'],
            ],
        ]);
    }

    /**
     * Affiche le formulaire de création d'une évaluation
     */
    public function create()
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();
        
        // Récupérer les sessions de formation du formateur
        $trainingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->with('trainingDomain')
            ->get();
            
        $sessionIds = $trainingSessions->pluck('id')->toArray();
        
        // Récupérer les cours pour le sélecteur
        $courses = Course::whereIn('training_session_id', $sessionIds)
            ->orderBy('title')
            ->get();
            
        return Inertia::render('Trainer/Evaluations/Create', [
            'trainingSessions' => $trainingSessions,
            'courses' => $courses,
            'evaluationTypes' => [
                ['value' => 'feedback', 'label' => 'Feedback'],
                ['value' => 'survey', 'label' => 'Sondage'],
                ['value' => 'rating', 'label' => 'Évaluation'],
            ],
        ]);
    }

    /**
     * Enregistre une nouvelle évaluation
     */
    public function store(Request $request)
    {
        // Valider les données
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'training_session_id' => 'required|exists:training_sessions,id',
            'course_id' => 'nullable|exists:courses,id',
            'type' => 'required|in:feedback,survey,rating',
            'status' => 'required|in:draft,active,closed',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_anonymous' => 'boolean',
        ]);
        
        // Vérifier que la session de formation appartient bien au formateur
        $trainingSession = TrainingSession::findOrFail($validated['training_session_id']);
        
        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->back()->withErrors([
                'training_session_id' => 'Vous n\'êtes pas autorisé à créer une évaluation pour cette session de formation.'
            ]);
        }
        
        // Si un cours est spécifié, vérifier qu'il appartient bien à la session de formation
        if (isset($validated['course_id'])) {
            $course = Course::findOrFail($validated['course_id']);
            if ($course->training_session_id !== $validated['training_session_id']) {
                return redirect()->back()->withErrors([
                    'course_id' => 'Le cours sélectionné n\'appartient pas à cette session de formation.'
                ]);
            }
        }
        
        // Ajouter l'ID du formateur
        $validated['trainer_id'] = Auth::id();
        
        // Créer l'évaluation
        $evaluation = Evaluation::create($validated);
        
        return redirect()->route('trainer.evaluations.questions.create', $evaluation->id)
            ->with('success', 'Évaluation créée avec succès. Ajoutez maintenant des questions.');
    }

    /**
     * Affiche une évaluation
     */
    public function show(string $id)
    {
        $evaluation = Evaluation::with(['trainingSession', 'course', 'questions', 'responses.student'])
            ->withCount(['questions', 'responses'])
            ->findOrFail($id);
            
        // Vérifier que l'évaluation appartient bien au formateur
        if ($evaluation->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.evaluations.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à cette évaluation.');
        }
        
        // Calculer les statistiques
        $completedResponses = $evaluation->responses->where('status', 'completed')->count();
        $completionRate = $evaluation->responses_count > 0 
            ? round(($completedResponses / $evaluation->responses_count) * 100, 2) 
            : 0;
            
        $averageRating = $evaluation->responses->where('status', 'completed')
            ->avg('overall_rating');
            
        return Inertia::render('Trainer/Evaluations/Show', [
            'evaluation' => $evaluation,
            'questions' => $evaluation->questions()->orderBy('order')->get(),
            'responses' => $evaluation->responses()->with('student')->get(),
            'stats' => [
                'completion_rate' => $completionRate,
                'average_rating' => $averageRating,
                'completed_responses' => $completedResponses,
            ],
        ]);
    }

    /**
     * Affiche le formulaire de modification d'une évaluation
     */
    public function edit(string $id)
    {
        $evaluation = Evaluation::with(['trainingSession', 'course'])->findOrFail($id);
        
        // Vérifier que l'évaluation appartient bien au formateur
        if ($evaluation->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.evaluations.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à cette évaluation.');
        }
        
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();
        
        // Récupérer les sessions de formation du formateur
        $trainingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->with('trainingDomain')
            ->get();
            
        $sessionIds = $trainingSessions->pluck('id')->toArray();
        
        // Récupérer les cours pour le sélecteur
        $courses = Course::whereIn('training_session_id', $sessionIds)
            ->orderBy('title')
            ->get();
            
        return Inertia::render('Trainer/Evaluations/Edit', [
            'evaluation' => $evaluation,
            'trainingSessions' => $trainingSessions,
            'courses' => $courses,
            'evaluationTypes' => [
                ['value' => 'feedback', 'label' => 'Feedback'],
                ['value' => 'survey', 'label' => 'Sondage'],
                ['value' => 'rating', 'label' => 'Évaluation'],
            ],
            'statusTypes' => [
                ['value' => 'draft', 'label' => 'Brouillon'],
                ['value' => 'active', 'label' => 'Active'],
                ['value' => 'closed', 'label' => 'Fermée'],
            ],
        ]);
    }

    /**
     * Met à jour une évaluation
     */
    public function update(Request $request, string $id)
    {
        $evaluation = Evaluation::findOrFail($id);
        
        // Vérifier que l'évaluation appartient bien au formateur
        if ($evaluation->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.evaluations.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à cette évaluation.');
        }
        
        // Valider les données
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'training_session_id' => 'required|exists:training_sessions,id',
            'course_id' => 'nullable|exists:courses,id',
            'type' => 'required|in:feedback,survey,rating',
            'status' => 'required|in:draft,active,closed',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_anonymous' => 'boolean',
        ]);
        
        // Vérifier que la session de formation appartient bien au formateur
        $trainingSession = TrainingSession::findOrFail($validated['training_session_id']);
        
        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->back()->withErrors([
                'training_session_id' => 'Vous n\'êtes pas autorisé à créer une évaluation pour cette session de formation.'
            ]);
        }
        
        // Si un cours est spécifié, vérifier qu'il appartient bien à la session de formation
        if (isset($validated['course_id'])) {
            $course = Course::findOrFail($validated['course_id']);
            if ($course->training_session_id !== $validated['training_session_id']) {
                return redirect()->back()->withErrors([
                    'course_id' => 'Le cours sélectionné n\'appartient pas à cette session de formation.'
                ]);
            }
        }
        
        // Mettre à jour l'évaluation
        $evaluation->update($validated);
        
        return redirect()->route('trainer.evaluations.show', $evaluation->id)
            ->with('success', 'Évaluation mise à jour avec succès.');
    }

    /**
     * Supprime une évaluation
     */
    public function destroy(string $id)
    {
        $evaluation = Evaluation::findOrFail($id);
        
        // Vérifier que l'évaluation appartient bien au formateur
        if ($evaluation->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.evaluations.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à cette évaluation.');
        }
        
        // Supprimer l'évaluation
        $evaluation->delete();
        
        return redirect()->route('trainer.evaluations.index')
            ->with('success', 'Évaluation supprimée avec succès.');
    }
    
    /**
     * Affiche les résultats d'une évaluation
     */
    public function results(string $id)
    {
        $evaluation = Evaluation::with(['trainingSession', 'course', 'questions', 'responses.student', 'responses.answers'])
            ->findOrFail($id);
            
        // Vérifier que l'évaluation appartient bien au formateur
        if ($evaluation->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.evaluations.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à cette évaluation.');
        }
        
        // Récupérer les réponses complétées
        $completedResponses = $evaluation->responses->where('status', 'completed');
        
        // Préparer les données pour les graphiques
        $chartData = [];
        
        // Pour chaque question, préparer les données
        foreach ($evaluation->questions as $question) {
            $questionData = [
                'id' => $question->id,
                'text' => $question->question_text,
                'type' => $question->question_type,
            ];
            
            if ($question->question_type === 'multiple_choice') {
                // Pour les questions à choix multiples, compter les occurrences de chaque option
                $optionCounts = [];
                foreach ($question->options as $option) {
                    $optionCounts[$option] = 0;
                }
                
                foreach ($completedResponses as $response) {
                    $answer = $response->answers->where('evaluation_question_id', $question->id)->first();
                    if ($answer && $answer->answer_option) {
                        if (isset($optionCounts[$answer->answer_option])) {
                            $optionCounts[$answer->answer_option]++;
                        }
                    }
                }
                
                $questionData['data'] = [
                    'labels' => array_keys($optionCounts),
                    'values' => array_values($optionCounts),
                ];
            } elseif ($question->question_type === 'rating') {
                // Pour les questions de notation, calculer la distribution des notes
                $ratingCounts = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]; // 0-10
                
                foreach ($completedResponses as $response) {
                    $answer = $response->answers->where('evaluation_question_id', $question->id)->first();
                    if ($answer && $answer->rating_value !== null) {
                        $ratingCounts[$answer->rating_value]++;
                    }
                }
                
                $questionData['data'] = [
                    'labels' => range(0, 10),
                    'values' => $ratingCounts,
                ];
            } elseif ($question->question_type === 'yes_no') {
                // Pour les questions oui/non, compter les oui et les non
                $yesCounts = 0;
                $noCounts = 0;
                
                foreach ($completedResponses as $response) {
                    $answer = $response->answers->where('evaluation_question_id', $question->id)->first();
                    if ($answer && $answer->answer_option) {
                        if ($answer->answer_option === 'yes') {
                            $yesCounts++;
                        } elseif ($answer->answer_option === 'no') {
                            $noCounts++;
                        }
                    }
                }
                
                $questionData['data'] = [
                    'labels' => ['Oui', 'Non'],
                    'values' => [$yesCounts, $noCounts],
                ];
            } else {
                // Pour les questions textuelles, récupérer toutes les réponses
                $textAnswers = [];
                
                foreach ($completedResponses as $response) {
                    $answer = $response->answers->where('evaluation_question_id', $question->id)->first();
                    if ($answer && $answer->answer_text) {
                        $textAnswers[] = [
                            'student' => $evaluation->is_anonymous ? 'Anonyme' : $response->student->name,
                            'text' => $answer->answer_text,
                        ];
                    }
                }
                
                $questionData['data'] = $textAnswers;
            }
            
            $chartData[] = $questionData;
        }
        
        // Calculer les statistiques générales
        $completionRate = $evaluation->responses->count() > 0 
            ? round(($completedResponses->count() / $evaluation->responses->count()) * 100, 2) 
            : 0;
            
        $averageRating = $completedResponses->avg('overall_rating');
        
        return Inertia::render('Trainer/Evaluations/Results', [
            'evaluation' => $evaluation,
            'chartData' => $chartData,
            'stats' => [
                'completion_rate' => $completionRate,
                'average_rating' => $averageRating,
                'completed_responses' => $completedResponses->count(),
                'total_responses' => $evaluation->responses->count(),
            ],
        ]);
    }
    
    /**
     * Exporte les résultats d'une évaluation au format CSV
     */
    public function export(string $id)
    {
        $evaluation = Evaluation::with(['trainingSession', 'course', 'questions', 'responses.student', 'responses.answers'])
            ->findOrFail($id);
            
        // Vérifier que l'évaluation appartient bien au formateur
        if ($evaluation->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.evaluations.index')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à cette évaluation.');
        }
        
        // Récupérer les réponses complétées
        $completedResponses = $evaluation->responses->where('status', 'completed');
        
        // Préparer les en-têtes du CSV
        $headers = [
            'ID',
            'Date de soumission',
            'Étudiant',
            'Note globale',
            'Commentaires',
        ];
        
        // Ajouter les questions aux en-têtes
        foreach ($evaluation->questions as $question) {
            $headers[] = $question->question_text;
        }
        
        // Préparer les données
        $data = [];
        
        foreach ($completedResponses as $response) {
            $row = [
                $response->id,
                $response->submitted_at ? $response->submitted_at->format('d/m/Y H:i') : '',
                $evaluation->is_anonymous ? 'Anonyme' : $response->student->name,
                $response->overall_rating,
                $response->comments,
            ];
            
            // Ajouter les réponses aux questions
            foreach ($evaluation->questions as $question) {
                $answer = $response->answers->where('evaluation_question_id', $question->id)->first();
                
                if ($answer) {
                    if ($question->question_type === 'rating') {
                        $row[] = $answer->rating_value;
                    } elseif ($question->question_type === 'multiple_choice' || $question->question_type === 'yes_no') {
                        $row[] = $answer->answer_option;
                    } else {
                        $row[] = $answer->answer_text;
                    }
                } else {
                    $row[] = '';
                }
            }
            
            $data[] = $row;
        }
        
        // Générer le CSV
        $callback = function() use ($headers, $data) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $headers);
            
            foreach ($data as $row) {
                fputcsv($file, $row);
            }
            
            fclose($file);
        };
        
        $filename = 'evaluation_' . $evaluation->id . '_results.csv';
        
        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }
}
