<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TrainingSession extends Model
{
    use HasFactory;

    /**
     * Départements disponibles
     */
    public const DEPARTMENTS = [
        'Secourisme',
        'Langue',
        'Formation à la carte'
    ];

    /**
     * Niveaux disponibles
     */
    public const LEVELS = [
        'Niveau 1',
        'Niveau 2',
        'Niveau 3',
        'Niveau 4',
        'Niveau 5',
        'Niveau 6',
        'Niveau 7'
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'certificate_name',
        'training_objectives',
        'training_domain_id',
        'department',
        'level',
        'trainer_id',
        'start_date',
        'end_date',
        'max_students',
        'price',
        'location',
        'prerequisites',
        'image',
        'active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'price' => 'decimal:2',
        'active' => 'boolean',
    ];

    /**
     * Relation avec le domaine de formation
     */
    public function training_domain()
    {
        return $this->belongsTo(TrainingDomain::class);
    }

    /**
     * Alias pour la compatibilité
     */
    public function trainingDomain()
    {
        return $this->training_domain();
    }

    /**
     * Relation avec le formateur
     */
    public function trainer()
    {
        return $this->belongsTo(User::class, 'trainer_id');
    }

    /**
     * Relation avec les cours
     */
    public function courses()
    {
        return $this->hasMany(Course::class);
    }

    /**
     * Relation avec les examens
     */
    public function exams()
    {
        return $this->hasMany(Exam::class);
    }

    /**
     * Relation avec les inscriptions
     */
    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    /**
     * Relation avec les apprenants inscrits
     */
    public function students()
    {
        return $this->belongsToMany(User::class, 'enrollments')
            ->withPivot(['status', 'enrollment_date', 'payment_confirmed'])
            ->withTimestamps();
    }

    /**
     * Relation avec les certificats
     */
    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }
}
