<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TrainingSession extends Model
{
    use HasFactory;

    /**
     * Départements disponibles
     */
    public const DEPARTMENTS = [
        'Secourisme',
        'Langue',
        'Formation à la carte'
    ];

    /**
     * Niveaux disponibles
     */
    public const LEVELS = [
        'Niveau 1',
        'Niveau 2',
        'Niveau 3',
        'Niveau 4',
        'Niveau 5',
        'Niveau 6',
        'Niveau 7'
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'certificate_name',
        'training_objectives',
        'training_domain_id',
        'department',
        'level',
        'trainer_id',
        'start_date',
        'end_date',
        'max_participants',
        'price',
        'location',
        'prerequisites',
        'image',
        'active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'price' => 'decimal:2',
        'active' => 'boolean',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'active_time_slots'
    ];

    /**
     * Relation avec le domaine de formation
     */
    public function training_domain()
    {
        return $this->belongsTo(TrainingDomain::class);
    }

    /**
     * Alias pour la compatibilité
     */
    public function trainingDomain()
    {
        return $this->training_domain();
    }

    /**
     * Relation avec le formateur
     */
    public function trainer()
    {
        return $this->belongsTo(User::class, 'trainer_id');
    }

    /**
     * Relation avec les cours
     */
    public function courses()
    {
        return $this->hasMany(Course::class);
    }

    /**
     * Relation avec les examens
     */
    public function exams()
    {
        return $this->hasMany(Exam::class);
    }

    /**
     * Relation avec les inscriptions
     */
    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    /**
     * Relation avec les apprenants inscrits
     */
    public function students()
    {
        return $this->belongsToMany(User::class, 'enrollments')
            ->withPivot(['status', 'enrollment_date', 'payment_confirmed'])
            ->withTimestamps();
    }

    /**
     * Relation avec les certificats
     */
    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }

    /**
     * Relation avec les créneaux horaires
     */
    public function timeSlots()
    {
        return $this->hasMany(TimeSlot::class);
    }

    /**
     * Relation avec les créneaux horaires actifs
     */
    public function activeTimeSlots()
    {
        return $this->hasMany(TimeSlot::class)->where('active', true);
    }

    /**
     * Vérifier si la session a des créneaux horaires
     */
    public function hasTimeSlots()
    {
        return $this->timeSlots()->exists();
    }

    /**
     * Obtenir les créneaux horaires disponibles
     */
    public function availableTimeSlots()
    {
        return $this->timeSlots()->available();
    }

    /**
     * Accesseur pour les créneaux horaires actifs (pour l'API JSON)
     */
    public function getActiveTimeSlotsAttribute()
    {
        return $this->activeTimeSlots()->get()->map(function ($timeSlot) {
            return [
                'id' => $timeSlot->id,
                'name' => $timeSlot->name,
                'start_time' => $timeSlot->start_time,
                'end_time' => $timeSlot->end_time,
                'formatted_start_time' => $timeSlot->formatted_start_time,
                'formatted_end_time' => $timeSlot->formatted_end_time,
                'time_range' => $timeSlot->time_range,
                'full_name' => $timeSlot->full_name,
                'max_participants' => $timeSlot->max_participants,
                'enrollments_count' => $timeSlot->enrollments()->count(),
                'available_spots' => $timeSlot->available_spots,
                'is_full' => $timeSlot->isFull(),
                'active' => $timeSlot->active,
            ];
        });
    }
}
