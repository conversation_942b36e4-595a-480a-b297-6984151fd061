<template>
  <div :class="[
    'flex flex-col items-center justify-center p-6 rounded-xl shadow-lg transition-transform duration-200 hover:scale-105',
    bgGradient
  ]">
    <div class="flex items-center gap-2">
      <component :is="icon" class="w-8 h-8 text-white" v-if="icon" />
      <span class="text-white text-4xl font-extrabold">{{ value }}</span>
    </div>
    <div class="text-white/80 text-sm mt-2 text-center">{{ label }}</div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';
const props = defineProps({
  value: [String, Number],
  label: String,
  icon: Object, // Un composant d'icône Vue
  bgGradient: {
    type: String,
    default: 'bg-gradient-to-r from-blue-500 to-blue-700'
  }
});
</script> 