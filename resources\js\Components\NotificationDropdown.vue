<template>
  <div class="relative">
    <!-- Notification Bell Icon -->
    <button
      @click="toggleDropdown"
      class="relative p-2 text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md transition-colors duration-200"
      :class="{ 'text-blue-600': hasUnreadNotifications }"
    >
      <BellIcon class="h-6 w-6" />
      
      <!-- Unread Count Badge -->
      <span
        v-if="unreadCount > 0"
        class="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full min-w-[20px] h-5"
      >
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </span>
    </button>

    <!-- Dropdown Panel -->
    <transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <div
        v-if="isOpen"
        class="absolute right-0 z-50 mt-2 w-96 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
        @click.stop
      >
        <!-- Header -->
        <div class="px-4 py-3 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Notifications</h3>
            <div class="flex items-center space-x-2">
              <button
                v-if="unreadCount > 0"
                @click="markAllAsRead"
                class="text-sm text-blue-600 hover:text-blue-800 font-medium"
                :disabled="markingAllAsRead"
              >
                {{ markingAllAsRead ? 'Marquage...' : 'Tout marquer comme lu' }}
              </button>
              <button
                @click="refreshNotifications"
                class="p-1 text-gray-400 hover:text-gray-600 rounded"
                :disabled="loading"
              >
                <ArrowPathIcon class="h-4 w-4" :class="{ 'animate-spin': loading }" />
              </button>
            </div>
          </div>
        </div>

        <!-- Notifications List -->
        <div class="max-h-96 overflow-y-auto">
          <div v-if="loading && notifications.length === 0" class="px-4 py-8 text-center text-gray-500">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            Chargement des notifications...
          </div>

          <div v-else-if="notifications.length === 0" class="px-4 py-8 text-center text-gray-500">
            <BellIcon class="h-12 w-12 mx-auto mb-2 text-gray-300" />
            <p class="text-sm">Aucune notification</p>
          </div>

          <div v-else class="divide-y divide-gray-100">
            <div
              v-for="notification in notifications"
              :key="notification.id"
              class="px-4 py-3 hover:bg-gray-50 transition-colors duration-150 cursor-pointer"
              :class="{ 'bg-blue-50': !notification.is_read }"
              @click="handleNotificationClick(notification)"
            >
              <div class="flex items-start space-x-3">
                <!-- Icon -->
                <div class="flex-shrink-0">
                  <div
                    class="w-8 h-8 rounded-full flex items-center justify-center"
                    :class="getNotificationIconClass(notification)"
                  >
                    <component :is="getNotificationIcon(notification)" class="h-4 w-4" />
                  </div>
                </div>

                <!-- Content -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <p class="text-sm font-medium text-gray-900 truncate">
                        {{ notification.title }}
                      </p>
                      <p class="text-sm text-gray-600 mt-1 line-clamp-2">
                        {{ notification.message }}
                      </p>
                      <p class="text-xs text-gray-400 mt-1">
                        {{ notification.time_ago }}
                      </p>
                    </div>
                    
                    <!-- Unread indicator -->
                    <div v-if="!notification.is_read" class="flex-shrink-0 ml-2">
                      <div class="w-2 h-2 bg-blue-600 rounded-full"></div>
                    </div>
                  </div>
                </div>

                <!-- Actions -->
                <div class="flex-shrink-0 ml-2">
                  <button
                    @click.stop="markAsRead(notification)"
                    v-if="!notification.is_read"
                    class="text-xs text-blue-600 hover:text-blue-800"
                  >
                    Marquer comme lu
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="px-4 py-3 border-t border-gray-200 bg-gray-50 rounded-b-lg">
          <Link
            :href="route('notifications.index')"
            class="block text-center text-sm text-blue-600 hover:text-blue-800 font-medium"
            @click="closeDropdown"
          >
            Voir toutes les notifications
          </Link>
        </div>
      </div>
    </transition>

    <!-- Backdrop -->
    <div
      v-if="isOpen"
      class="fixed inset-0 z-40"
      @click="closeDropdown"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Link } from '@inertiajs/vue3'
import {
  BellIcon,
  ArrowPathIcon,
  MegaphoneIcon,
  UserPlusIcon,
  ClipboardDocumentCheckIcon,
  TrophyIcon,
  CalendarIcon,
} from '@heroicons/vue/24/outline'
import axios from 'axios'

// Reactive state
const isOpen = ref(false)
const notifications = ref([])
const unreadCount = ref(0)
const loading = ref(false)
const markingAllAsRead = ref(false)

// Computed properties
const hasUnreadNotifications = computed(() => unreadCount.value > 0)

// Methods
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
  if (isOpen.value) {
    fetchNotifications()
  }
}

const closeDropdown = () => {
  isOpen.value = false
}

const fetchNotifications = async () => {
  try {
    loading.value = true
    const response = await axios.get('/notifications/recent')
    notifications.value = response.data.notifications
    unreadCount.value = response.data.unread_count
  } catch (error) {
    console.error('Error fetching notifications:', error)
  } finally {
    loading.value = false
  }
}

const refreshNotifications = () => {
  fetchNotifications()
}

const markAsRead = async (notification) => {
  if (notification.is_read) return

  try {
    await axios.post(`/notifications/${notification.id}/mark-as-read`)
    notification.is_read = true
    unreadCount.value = Math.max(0, unreadCount.value - 1)
  } catch (error) {
    console.error('Error marking notification as read:', error)
  }
}

const markAllAsRead = async () => {
  if (unreadCount.value === 0) return

  try {
    markingAllAsRead.value = true
    await axios.post('/notifications/mark-all-as-read')
    
    // Update local state
    notifications.value.forEach(notification => {
      notification.is_read = true
    })
    unreadCount.value = 0
  } catch (error) {
    console.error('Error marking all notifications as read:', error)
  } finally {
    markingAllAsRead.value = false
  }
}

const handleNotificationClick = (notification) => {
  // Mark as read if unread
  if (!notification.is_read) {
    markAsRead(notification)
  }

  // Navigate to notification URL if available
  if (notification.data && notification.data.url) {
    window.location.href = notification.data.url
  }

  closeDropdown()
}

const getNotificationIcon = (notification) => {
  switch (notification.type) {
    case 'announcement':
      return MegaphoneIcon
    case 'enrollment':
      return UserPlusIcon
    case 'exam_result':
      return ClipboardDocumentCheckIcon
    case 'certificate':
      return TrophyIcon
    case 'training_session':
      return CalendarIcon
    default:
      return BellIcon
  }
}

const getNotificationIconClass = (notification) => {
  const baseClass = 'text-white'
  
  switch (notification.color) {
    case 'blue':
      return `${baseClass} bg-blue-500`
    case 'green':
      return `${baseClass} bg-green-500`
    case 'purple':
      return `${baseClass} bg-purple-500`
    case 'yellow':
      return `${baseClass} bg-yellow-500`
    case 'indigo':
      return `${baseClass} bg-indigo-500`
    default:
      return `${baseClass} bg-gray-500`
  }
}

// Fetch initial unread count
const fetchUnreadCount = async () => {
  try {
    const response = await axios.get('/notifications/unread-count')
    unreadCount.value = response.data.count
  } catch (error) {
    console.error('Error fetching unread count:', error)
  }
}

// Close dropdown when clicking outside
const handleClickOutside = (event) => {
  if (isOpen.value && !event.target.closest('.relative')) {
    closeDropdown()
  }
}

// Lifecycle hooks
onMounted(() => {
  fetchUnreadCount()

  // Set up periodic refresh (every 30 seconds)
  const interval = setInterval(fetchUnreadCount, 30000)

  // Set up click outside event listener
  document.addEventListener('click', handleClickOutside)

  onUnmounted(() => {
    clearInterval(interval)
    document.removeEventListener('click', handleClickOutside)
  })
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
