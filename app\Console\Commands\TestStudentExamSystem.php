<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ExamResult;
use App\Models\Certificate;
use App\Models\User;
use App\Models\Exam;

class TestStudentExamSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exam:test-student-system';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test student exam system functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing student exam system...');
        
        $this->testExamResults();
        $this->testCertificates();
        $this->testStudentData();
        
        return Command::SUCCESS;
    }
    
    /**
     * Test exam results
     */
    private function testExamResults(): void
    {
        $this->info('📝 Testing exam results...');
        
        $examResults = ExamResult::with(['exam', 'user'])->get();
        $this->line("   Total exam results: {$examResults->count()}");
        
        if ($examResults->count() > 0) {
            $this->line("   Recent exam results:");
            $examResults->take(5)->each(function($result) {
                $status = $result->passed ? '✅ PASSED' : '❌ FAILED';
                $this->line("   - {$result->user->name}: {$result->exam->title} - {$result->score}% {$status}");
            });
            
            // Test exam result statuses
            $statusCounts = $examResults->groupBy('status')->map->count();
            $this->line("   Exam result statuses:");
            foreach ($statusCounts as $status => $count) {
                $this->line("   - {$status}: {$count}");
            }
        }
    }
    
    /**
     * Test certificates
     */
    private function testCertificates(): void
    {
        $this->info('🏆 Testing certificates...');
        
        $certificates = Certificate::with(['user', 'examResult'])->get();
        $this->line("   Total certificates: {$certificates->count()}");
        
        if ($certificates->count() > 0) {
            $this->line("   Certificate statuses:");
            $statusCounts = $certificates->groupBy('status')->map->count();
            foreach ($statusCounts as $status => $count) {
                $this->line("   - {$status}: {$count}");
            }
            
            $this->line("   Recent certificates:");
            $certificates->take(5)->each(function($cert) {
                $examInfo = $cert->examResult ? "Exam Score: {$cert->examResult->score}%" : "No exam result";
                $this->line("   - {$cert->user->name}: {$cert->certificate_number} ({$cert->status}) - {$examInfo}");
            });
        }
    }
    
    /**
     * Test student data for a specific user
     */
    private function testStudentData(): void
    {
        $this->info('👨‍🎓 Testing student data...');
        
        // Find a student with exam results
        $studentWithResults = User::whereHas('examResults')->with(['examResults.exam', 'certificates'])->first();
        
        if ($studentWithResults) {
            $this->line("   Testing data for student: {$studentWithResults->name}");
            $this->line("   - Exam results: {$studentWithResults->examResults->count()}");
            $this->line("   - Certificates: {$studentWithResults->certificates->count()}");
            
            if ($studentWithResults->examResults->count() > 0) {
                $this->line("   Exam results details:");
                $studentWithResults->examResults->each(function($result) {
                    $status = $result->passed ? 'PASSED' : 'FAILED';
                    $this->line("     - {$result->exam->title}: {$result->score}% ({$status})");
                });
            }
            
            if ($studentWithResults->certificates->count() > 0) {
                $this->line("   Certificate details:");
                $studentWithResults->certificates->each(function($cert) {
                    $this->line("     - {$cert->certificate_number}: {$cert->status}");
                });
            }
        } else {
            $this->warn("   No students with exam results found");
        }
    }
}
