<?php

namespace App\Http\Controllers\Trainer;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\TrainingSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ExamController extends Controller
{
    /**
     * Affiche la liste des examens associés aux sessions de formation du formateur
     */
    public function index(Request $request)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $trainingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->where('active', true)
            ->with('trainingDomain')
            ->get();

        $sessionIds = $trainingSessions->pluck('id')->toArray();

        // Initialiser la requête pour les examens
        // Afficher tous les examens associés aux sessions de formation du formateur, même ceux qu'il n'a pas créés
        $query = Exam::whereIn('training_session_id', $sessionIds)
            ->with(['trainingSession', 'creator']);

        // Appliquer les filtres
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function($q) use ($searchTerm) {
                $q->where('title', 'like', $searchTerm)
                  ->orWhere('description', 'like', $searchTerm);
            });
        }

        if ($request->has('training_session_id') && !empty($request->training_session_id)) {
            $query->where('training_session_id', $request->training_session_id);
        }

        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Ajouter les compteurs
        $query->withCount(['questions']);

        // Récupérer les examens avec pagination
        $exams = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Retourner la vue avec les examens et les filtres
        return Inertia::render('Trainer/Exams/Index', [
            'exams' => $exams,
            'trainingSessions' => $trainingSessions,
            'filters' => [
                'search' => $request->search ?? '',
                'training_session_id' => $request->training_session_id ?? '',
                'status' => $request->status ?? '',
            ],
            'statusOptions' => [
                ['value' => 'draft', 'label' => 'Brouillon'],
                ['value' => 'published', 'label' => 'Publié'],
                ['value' => 'closed', 'label' => 'Fermé'],
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $trainingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->where('active', true)
            ->with('trainingDomain')
            ->get();

        // Retourner la vue de création d'examen
        return Inertia::render('Trainer/Exams/Create', [
            'trainingSessions' => $trainingSessions,
            'statusOptions' => [
                ['value' => 'draft', 'label' => 'Brouillon'],
                ['value' => 'published', 'label' => 'Publié'],
                ['value' => 'closed', 'label' => 'Fermé'],
            ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'training_session_id' => 'required|exists:training_sessions,id',
            'duration_minutes' => 'required|integer|min:1',
            'passing_score' => 'required|integer|min:0|max:100',
            'status' => 'required|in:draft,published,closed',
            'instructions' => 'nullable|string',
        ]);

        // Synchroniser is_published avec status
        $isPublished = $validated['status'] === 'published';

        // Créer l'examen avec les données validées et is_published
        $exam = Exam::create(array_merge($validated, [
            'is_published' => $isPublished,
            'creator_id' => Auth::id(),
        ]));

        // Rediriger vers la page de détails de l'examen
        return redirect()->route('trainer.exams.show', $exam->id)
            ->with('success', 'Examen créé avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $sessionIds = TrainingSession::where('trainer_id', $trainer->id)
            ->where('active', true)
            ->pluck('id')
            ->toArray();

        // Récupérer l'examen avec ses relations
        $exam = Exam::with(['trainingSession', 'questions', 'creator'])
            ->whereIn('training_session_id', $sessionIds)
            ->findOrFail($id);

        // Récupérer les résultats d'examen
        $examResults = $exam->examResults()
            ->with('student')
            ->get();

        // Calculer les statistiques
        $totalStudents = $examResults->count();
        $passedStudents = $examResults->where('passed', true)->count();
        $failedStudents = $examResults->where('passed', false)->count();
        $pendingStudents = $examResults->whereNull('completed_at')->count();

        $passRate = $totalStudents > 0 ? round(($passedStudents / $totalStudents) * 100) : 0;

        // Retourner la vue avec l'examen et les statistiques
        return Inertia::render('Trainer/Exams/Show', [
            'exam' => $exam,
            'stats' => [
                'totalStudents' => $totalStudents,
                'passedStudents' => $passedStudents,
                'failedStudents' => $failedStudents,
                'pendingStudents' => $pendingStudents,
                'passRate' => $passRate,
            ],
            'examResults' => $examResults,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $sessionIds = TrainingSession::where('trainer_id', $trainer->id)
            ->where('active', true)
            ->pluck('id')
            ->toArray();

        // Récupérer l'examen
        $exam = Exam::whereIn('training_session_id', $sessionIds)
            ->findOrFail($id);

        // Récupérer toutes les sessions de formation du formateur
        $trainingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->where('active', true)
            ->with('trainingDomain')
            ->get();

        // Retourner la vue d'édition d'examen
        return Inertia::render('Trainer/Exams/Edit', [
            'exam' => $exam,
            'trainingSessions' => $trainingSessions,
            'statusOptions' => [
                ['value' => 'draft', 'label' => 'Brouillon'],
                ['value' => 'published', 'label' => 'Publié'],
                ['value' => 'closed', 'label' => 'Fermé'],
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $sessionIds = TrainingSession::where('trainer_id', $trainer->id)
            ->where('active', true)
            ->pluck('id')
            ->toArray();

        // Récupérer l'examen
        $exam = Exam::whereIn('training_session_id', $sessionIds)
            ->findOrFail($id);

        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'training_session_id' => 'required|exists:training_sessions,id',
            'duration_minutes' => 'required|integer|min:1',
            'passing_score' => 'required|integer|min:0|max:100',
            'status' => 'required|in:draft,published,closed',
            'instructions' => 'nullable|string',
        ]);

        // Synchroniser is_published avec status
        $isPublished = $validated['status'] === 'published';

        // Mettre à jour l'examen avec les données validées et is_published
        $exam->update(array_merge($validated, [
            'is_published' => $isPublished,
        ]));

        // Rediriger vers la page de détails de l'examen
        return redirect()->route('trainer.exams.show', $exam->id)
            ->with('success', 'Examen mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $sessionIds = TrainingSession::where('trainer_id', $trainer->id)
            ->where('active', true)
            ->pluck('id')
            ->toArray();

        // Récupérer l'examen
        $exam = Exam::whereIn('training_session_id', $sessionIds)
            ->findOrFail($id);

        // Supprimer l'examen
        $exam->delete();

        // Rediriger vers la liste des examens
        return redirect()->route('trainer.exams.index')
            ->with('success', 'Examen supprimé avec succès.');
    }
}
