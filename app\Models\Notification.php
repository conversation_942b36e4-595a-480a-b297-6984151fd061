<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Notification extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'type',
        'title',
        'message',
        'data',
        'read_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Notification types constants
     */
    const TYPE_ANNOUNCEMENT = 'announcement';
    const TYPE_ENROLLMENT = 'enrollment';
    const TYPE_EXAM_RESULT = 'exam_result';
    const TYPE_CERTIFICATE = 'certificate';
    const TYPE_TRAINING_SESSION = 'training_session';

    /**
     * Get all available notification types
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_ANNOUNCEMENT,
            self::TYPE_ENROLLMENT,
            self::TYPE_EXAM_RESULT,
            self::TYPE_CERTIFICATE,
            self::TYPE_TRAINING_SESSION,
        ];
    }

    /**
     * Relation with the user
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for unread notifications
     */
    public function scopeUnread(Builder $query): Builder
    {
        return $query->whereNull('read_at');
    }

    /**
     * Scope for read notifications
     */
    public function scopeRead(Builder $query): Builder
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * Scope for specific type
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for recent notifications (last 30 days)
     */
    public function scopeRecent(Builder $query): Builder
    {
        return $query->where('created_at', '>=', now()->subDays(30));
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(): bool
    {
        if ($this->read_at) {
            return true; // Already read
        }

        return $this->update(['read_at' => now()]);
    }

    /**
     * Mark notification as unread
     */
    public function markAsUnread(): bool
    {
        return $this->update(['read_at' => null]);
    }

    /**
     * Check if notification is read
     */
    public function isRead(): bool
    {
        return !is_null($this->read_at);
    }

    /**
     * Check if notification is unread
     */
    public function isUnread(): bool
    {
        return is_null($this->read_at);
    }

    /**
     * Get formatted time ago
     */
    public function getTimeAgoAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get notification icon based on type
     */
    public function getIconAttribute(): string
    {
        return match($this->type) {
            self::TYPE_ANNOUNCEMENT => 'megaphone',
            self::TYPE_ENROLLMENT => 'user-plus',
            self::TYPE_EXAM_RESULT => 'clipboard-check',
            self::TYPE_CERTIFICATE => 'award',
            self::TYPE_TRAINING_SESSION => 'calendar',
            default => 'bell',
        };
    }

    /**
     * Get notification color based on type
     */
    public function getColorAttribute(): string
    {
        return match($this->type) {
            self::TYPE_ANNOUNCEMENT => 'blue',
            self::TYPE_ENROLLMENT => 'green',
            self::TYPE_EXAM_RESULT => 'purple',
            self::TYPE_CERTIFICATE => 'yellow',
            self::TYPE_TRAINING_SESSION => 'indigo',
            default => 'gray',
        };
    }
}
