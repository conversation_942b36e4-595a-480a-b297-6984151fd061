<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Enrollment;
use App\Models\Certificate;

class GenerateDraftCertificates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'certificates:generate-drafts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate draft certificates for all approved enrollments that don\'t have certificates yet';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating draft certificates for approved enrollments...');

        // Get all approved enrollments that don't have certificates
        $enrollments = Enrollment::where('status', 'approved')
            ->whereDoesntHave('certificate')
            ->with(['user', 'trainingSession'])
            ->get();

        $this->info("Found {$enrollments->count()} approved enrollments without certificates.");

        $created = 0;

        foreach ($enrollments as $enrollment) {
            try {
                // Generate final certificate number (no DRAFT suffix)
                $certificateNumber = Certificate::generateCertificateNumber($enrollment->id);

                // Create draft certificate with final certificate number and QR code
                Certificate::create([
                    'enrollment_id' => $enrollment->id,
                    'user_id' => $enrollment->user_id,
                    'training_session_id' => $enrollment->training_session_id,
                    'certificate_number' => $certificateNumber,
                    'status' => 'draft',
                    'issued_at' => null,
                    'issue_date' => null,
                ]);
                // Note: QR code will be automatically generated by the Certificate model's boot method

                $this->line("✓ Created draft certificate for {$enrollment->user->name} - {$enrollment->trainingSession->title} (Certificate: {$certificateNumber})");
                $created++;

            } catch (\Exception $e) {
                $this->error("✗ Failed to create certificate for enrollment {$enrollment->id}: " . $e->getMessage());
            }
        }

        $this->info("Successfully created {$created} draft certificates.");
        
        return Command::SUCCESS;
    }
}
