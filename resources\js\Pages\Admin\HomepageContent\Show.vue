<template>
    <Head title="Détails du contenu" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    Détails du contenu: {{ content.key }}
                </h2>
                <div class="flex space-x-2">
                    <Link :href="route('admin.homepage-content.edit', content.id)" 
                          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Modifier
                    </Link>
                    <Link :href="route('admin.homepage-content.index')" 
                          class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                        Retour
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Basic Information -->
                            <div class="space-y-4">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Informations générales</h3>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Section</label>
                                    <p class="mt-1 text-sm text-gray-900 bg-gray-50 p-2 rounded">
                                        {{ formatSectionName(content.section) }}
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Clé</label>
                                    <p class="mt-1 text-sm text-gray-900 bg-gray-50 p-2 rounded">
                                        {{ content.key }}
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Type</label>
                                    <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                          :class="getTypeClass(content.type)">
                                        {{ content.type }}
                                    </span>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Ordre d'affichage</label>
                                    <p class="mt-1 text-sm text-gray-900 bg-gray-50 p-2 rounded">
                                        {{ content.sort_order }}
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Statut</label>
                                    <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                          :class="content.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                                        {{ content.is_active ? 'Actif' : 'Inactif' }}
                                    </span>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Créé le</label>
                                    <p class="mt-1 text-sm text-gray-900 bg-gray-50 p-2 rounded">
                                        {{ formatDate(content.created_at) }}
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Modifié le</label>
                                    <p class="mt-1 text-sm text-gray-900 bg-gray-50 p-2 rounded">
                                        {{ formatDate(content.updated_at) }}
                                    </p>
                                </div>
                            </div>

                            <!-- Content Display -->
                            <div class="space-y-4">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Contenu</h3>
                                </div>

                                <!-- Image Content -->
                                <div v-if="content.type === 'image'" class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">Image</label>
                                    <div class="border rounded-lg p-4">
                                        <img :src="'/storage/' + content.value" 
                                             :alt="content.key" 
                                             class="max-w-full h-auto rounded-lg shadow-md">
                                        <p class="mt-2 text-xs text-gray-500">
                                            Chemin: {{ content.value }}
                                        </p>
                                    </div>
                                </div>

                                <!-- Video Content -->
                                <div v-else-if="content.type === 'video'" class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">Vidéo</label>
                                    <div class="border rounded-lg p-4">
                                        <video :src="'/storage/' + content.value" 
                                               class="max-w-full h-auto rounded-lg shadow-md" 
                                               controls>
                                            Votre navigateur ne supporte pas la lecture vidéo.
                                        </video>
                                        <p class="mt-2 text-xs text-gray-500">
                                            Chemin: {{ content.value }}
                                        </p>
                                    </div>
                                </div>

                                <!-- Text/JSON Content -->
                                <div v-else class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">
                                        {{ content.type === 'json' ? 'Contenu JSON' : 'Contenu texte' }}
                                    </label>
                                    <div class="border rounded-lg p-4 bg-gray-50">
                                        <pre v-if="content.type === 'json'" 
                                             class="text-sm text-gray-900 whitespace-pre-wrap">{{ formatJSON(content.value) }}</pre>
                                        <p v-else class="text-sm text-gray-900 whitespace-pre-wrap">{{ content.value }}</p>
                                    </div>
                                </div>

                                <!-- Metadata -->
                                <div v-if="content.metadata" class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">Métadonnées</label>
                                    <div class="border rounded-lg p-4 bg-gray-50">
                                        <pre class="text-sm text-gray-900 whitespace-pre-wrap">{{ formatJSON(content.metadata) }}</pre>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="mt-8 flex justify-end space-x-4">
                            <Link :href="route('admin.homepage-content.edit', content.id)" 
                                  class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Modifier ce contenu
                            </Link>
                            <button @click="deleteContent" 
                                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                Supprimer ce contenu
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { router } from '@inertiajs/vue3';

const props = defineProps({
    content: Object
});

const formatSectionName = (section) => {
    const names = {
        'hero': 'Section Héro',
        'vr_training': 'Formation VR',
        'about': 'À propos',
        'contact': 'Contact'
    };
    return names[section] || section;
};

const getTypeClass = (type) => {
    const classes = {
        'text': 'bg-gray-100 text-gray-800',
        'image': 'bg-green-100 text-green-800',
        'video': 'bg-blue-100 text-blue-800',
        'json': 'bg-purple-100 text-purple-800'
    };
    return classes[type] || 'bg-gray-100 text-gray-800';
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const formatJSON = (data) => {
    if (typeof data === 'string') {
        try {
            return JSON.stringify(JSON.parse(data), null, 2);
        } catch (e) {
            return data;
        }
    }
    return JSON.stringify(data, null, 2);
};

const deleteContent = () => {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce contenu ? Cette action est irréversible.')) {
        router.delete(route('admin.homepage-content.destroy', props.content.id));
    }
};
</script>
