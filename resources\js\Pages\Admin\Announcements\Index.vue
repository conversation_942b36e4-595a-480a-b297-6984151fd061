<template>
  <Head title="Gestion des annonces" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Gestion des annonces
        </h2>
        <Link :href="route('admin.announcements.create')" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
          Créer une annonce
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Filtres -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Filtrer les annonces</h3>
            <form @submit.prevent="applyFilters">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <!-- Recherche -->
                <div>
                  <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
                  <input
                    type="text"
                    id="search"
                    v-model="searchQuery"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Rechercher par titre ou contenu..."
                  />
                </div>

                <!-- Visibilité -->
                <div>
                  <label for="visible_to" class="block text-sm font-medium text-gray-700 mb-1">Visibilité</label>
                  <select
                    id="visible_to"
                    v-model="visibleToFilter"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="">Toutes les visibilités</option>
                    <option value="all">Tous les utilisateurs</option>
                    <option value="admin">Administrateurs</option>
                    <option value="trainer">Formateurs</option>
                    <option value="student">Apprenants</option>
                  </select>
                </div>

                <!-- Importance -->
                <div>
                  <label for="importance" class="block text-sm font-medium text-gray-700 mb-1">Importance</label>
                  <select
                    id="importance"
                    v-model="importanceFilter"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="">Toutes les annonces</option>
                    <option value="true">Importantes</option>
                    <option value="false">Normales</option>
                  </select>
                </div>

                <!-- Session de formation -->
                <div>
                  <label for="training-session" class="block text-sm font-medium text-gray-700 mb-1">Session de formation</label>
                  <select
                    id="training-session"
                    v-model="sessionFilter"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="">Toutes les sessions</option>
                    <option value="null">Annonces générales</option>
                    <option v-for="session in trainingSessions" :key="session.id" :value="session.id">
                      {{ session.title }}
                    </option>
                  </select>
                </div>
              </div>

              <div class="flex justify-end">
                <button
                  type="button"
                  @click="resetFilters"
                  class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 mr-2"
                >
                  Réinitialiser
                </button>
                <button
                  type="submit"
                  class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                >
                  Filtrer
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Liste des annonces -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Liste des annonces</h3>

            <div v-if="announcements.data.length > 0" class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Titre</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visibilité</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Session</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Publication</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="announcement in announcements.data" :key="announcement.id" :class="{ 'bg-yellow-50': announcement.is_important }">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div v-if="announcement.is_important" class="mr-2 flex-shrink-0">
                          <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-red-100">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-red-600" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                          </span>
                        </div>
                        <div class="text-sm font-medium text-gray-900">{{ announcement.title }}</div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="getVisibilityClass(announcement.visible_to)" class="px-2 py-1 text-xs rounded-full">
                        {{ getVisibilityLabel(announcement.visible_to) }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div v-if="announcement.training_session" class="text-sm text-gray-900">
                        {{ announcement.training_session.title }}
                      </div>
                      <div v-else class="text-sm text-gray-500">
                        Générale
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ formatDate(announcement.publish_date) }}</div>
                      <div v-if="announcement.expiry_date" class="text-xs text-gray-500">
                        Expire le {{ formatDate(announcement.expiry_date) }}
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                      <div class="flex space-x-2">
                        <Link :href="route('admin.announcements.show', announcement.id)" class="text-indigo-600 hover:text-indigo-900">
                          Voir
                        </Link>
                        <span class="text-gray-300">|</span>
                        <Link :href="route('admin.announcements.edit', announcement.id)" class="text-blue-600 hover:text-blue-900">
                          Modifier
                        </Link>
                        <span class="text-gray-300">|</span>
                        <button @click="confirmDelete(announcement)" class="text-red-600 hover:text-red-900">
                          Supprimer
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>

              <!-- Pagination -->
              <div class="mt-4">
                <Pagination :links="announcements.links" />
              </div>
            </div>

            <div v-else class="text-center py-8 text-gray-500">
              Aucune annonce trouvée.
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmation de suppression -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Confirmer la suppression</h3>
        <p class="text-gray-600 mb-6">
          Êtes-vous sûr de vouloir supprimer l'annonce "{{ announcementToDelete?.title }}" ?
        </p>
        <div class="flex justify-end space-x-3">
          <button
            @click="showDeleteModal = false"
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
          >
            Annuler
          </button>
          <button
            @click="deleteAnnouncement"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Supprimer
          </button>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3';
import { ref } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Pagination from '@/Components/Pagination.vue';

// Props
const props = defineProps({
  announcements: Object,
  trainingSessions: Array,
  filters: Object,
});

// État local
const searchQuery = ref(props.filters?.search || '');
const visibleToFilter = ref(props.filters?.visible_to || '');
const importanceFilter = ref(props.filters?.is_important || '');
const sessionFilter = ref(props.filters?.training_session_id || '');
const showDeleteModal = ref(false);
const announcementToDelete = ref(null);

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return '';
  const options = { day: 'numeric', month: 'long', year: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const getVisibilityLabel = (visibility) => {
  const labels = {
    'all': 'Tous',
    'admin': 'Administrateurs',
    'trainer': 'Formateurs',
    'student': 'Apprenants'
  };
  return labels[visibility] || visibility;
};

const getVisibilityClass = (visibility) => {
  const classes = {
    'all': 'bg-purple-100 text-purple-800',
    'admin': 'bg-red-100 text-red-800',
    'trainer': 'bg-blue-100 text-blue-800',
    'student': 'bg-green-100 text-green-800'
  };
  return classes[visibility] || 'bg-gray-100 text-gray-800';
};

const applyFilters = () => {
  router.get(route('admin.announcements.index'), {
    search: searchQuery.value,
    visible_to: visibleToFilter.value,
    is_important: importanceFilter.value,
    training_session_id: sessionFilter.value,
  }, {
    preserveState: true,
    replace: true,
  });
};

const resetFilters = () => {
  searchQuery.value = '';
  visibleToFilter.value = '';
  importanceFilter.value = '';
  sessionFilter.value = '';
  applyFilters();
};

const confirmDelete = (announcement) => {
  announcementToDelete.value = announcement;
  showDeleteModal.value = true;
};

const deleteAnnouncement = () => {
  router.delete(route('admin.announcements.destroy', announcementToDelete.value.id), {
    onSuccess: () => {
      showDeleteModal.value = false;
      announcementToDelete.value = null;
    },
  });
};
</script>
