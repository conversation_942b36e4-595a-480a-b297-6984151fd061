<?php

// Script pour réparer les questions d'examen

// Charger l'environnement Laravel
require __DIR__.'/vendor/autoload.php';
$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "Début de la réparation des questions d'examen...\n";

// Récupérer toutes les questions à choix multiple
$questions = DB::table('exam_questions')
    ->where('question_type', 'multiple_choice')
    ->get();

echo "Nombre de questions à choix multiple trouvées : " . count($questions) . "\n";

$updated = 0;

foreach ($questions as $question) {
    echo "Traitement de la question ID: " . $question->id . "\n";
    
    // Options par défaut
    $defaultOptions = json_encode([
        'A' => 'Option A',
        'B' => 'Option B',
        'C' => 'Option C',
        'D' => 'Option D'
    ]);
    
    // Réponses correctes par défaut
    $defaultCorrectOptions = json_encode(['A']);
    
    // Vérifier les options actuelles
    $options = $question->options;
    $correctOptions = $question->correct_options;
    
    echo "Options actuelles: " . ($options ?: "NULL") . "\n";
    echo "Options correctes actuelles: " . ($correctOptions ?: "NULL") . "\n";
    
    // Déterminer si une mise à jour est nécessaire
    $needsUpdate = false;
    
    // Vérifier si les options sont vides ou invalides
    if (empty($options) || $options === "null" || $options === "[]" || $options === "{}" || $options === "[object Object]") {
        $options = $defaultOptions;
        $needsUpdate = true;
        echo "Options mises à jour avec les valeurs par défaut\n";
    } else {
        // Essayer de parser les options pour vérifier si elles sont valides
        try {
            $decodedOptions = json_decode($options);
            if (json_last_error() !== JSON_ERROR_NONE || empty($decodedOptions) || !is_object($decodedOptions)) {
                $options = $defaultOptions;
                $needsUpdate = true;
                echo "Options invalides, mises à jour avec les valeurs par défaut\n";
            }
        } catch (Exception $e) {
            $options = $defaultOptions;
            $needsUpdate = true;
            echo "Erreur lors du parsing des options, mises à jour avec les valeurs par défaut\n";
        }
    }
    
    // Vérifier si les options correctes sont vides ou invalides
    if (empty($correctOptions) || $correctOptions === "null" || $correctOptions === "[]" || $correctOptions === "{}" || $correctOptions === "[object Object]") {
        $correctOptions = $defaultCorrectOptions;
        $needsUpdate = true;
        echo "Options correctes mises à jour avec les valeurs par défaut\n";
    } else {
        // Essayer de parser les options correctes pour vérifier si elles sont valides
        try {
            $decodedCorrectOptions = json_decode($correctOptions);
            if (json_last_error() !== JSON_ERROR_NONE || empty($decodedCorrectOptions)) {
                $correctOptions = $defaultCorrectOptions;
                $needsUpdate = true;
                echo "Options correctes invalides, mises à jour avec les valeurs par défaut\n";
            }
        } catch (Exception $e) {
            $correctOptions = $defaultCorrectOptions;
            $needsUpdate = true;
            echo "Erreur lors du parsing des options correctes, mises à jour avec les valeurs par défaut\n";
        }
    }
    
    // Mettre à jour la question si nécessaire
    if ($needsUpdate) {
        DB::table('exam_questions')
            ->where('id', $question->id)
            ->update([
                'options' => $options,
                'correct_options' => $correctOptions
            ]);
        
        $updated++;
        echo "Question ID: " . $question->id . " mise à jour avec succès\n";
    } else {
        echo "Question ID: " . $question->id . " n'a pas besoin de mise à jour\n";
    }
    
    echo "-----------------------------------\n";
}

echo "Réparation terminée. $updated questions ont été mises à jour.\n";
