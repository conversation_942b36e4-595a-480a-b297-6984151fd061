<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('first_aid_tip_materials', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->foreignId('first_aid_tip_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['pdf', 'video', 'text', 'archive', 'image', 'audio', 'gallery', 'embed_video']);
            $table->text('content')->nullable();
            $table->text('embed_code')->nullable();
            $table->string('file_path')->nullable();
            $table->integer('order')->default(0);
            $table->boolean('active')->default(true);
            $table->boolean('allow_download')->default(true);
            $table->boolean('allow_online_viewing')->default(true);
            $table->string('mime_type')->nullable();
            $table->integer('file_size')->nullable(); // in KB
            $table->string('thumbnail_path')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('first_aid_tip_materials');
    }
};
