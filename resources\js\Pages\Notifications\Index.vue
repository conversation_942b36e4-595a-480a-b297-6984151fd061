<template>
  <Head title="Notifications" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Notifications
        </h2>
        <div class="flex items-center space-x-4">
          <button
            v-if="stats.unread > 0"
            @click="markAllAsRead"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            :disabled="markingAllAsRead"
          >
            {{ markingAllAsRead ? 'Marquage...' : `Marquer tout comme lu (${stats.unread})` }}
          </button>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <BellIcon class="h-8 w-8 text-gray-400" />
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Total</p>
                  <p class="text-2xl font-semibold text-gray-900">{{ stats.total }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <ExclamationCircleIcon class="h-8 w-8 text-red-500" />
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Non lues</p>
                  <p class="text-2xl font-semibold text-red-600">{{ stats.unread }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <CheckCircleIcon class="h-8 w-8 text-green-500" />
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Lues</p>
                  <p class="text-2xl font-semibold text-green-600">{{ stats.read }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <ClockIcon class="h-8 w-8 text-blue-500" />
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Récentes</p>
                  <p class="text-2xl font-semibold text-blue-600">{{ stats.recent }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Filters -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                <select
                  id="type"
                  v-model="filters.type"
                  @change="applyFilters"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Tous les types</option>
                  <option value="announcement">Annonces</option>
                  <option value="enrollment">Inscriptions</option>
                  <option value="exam_result">Résultats d'examen</option>
                  <option value="certificate">Certificats</option>
                  <option value="training_session">Sessions de formation</option>
                </select>
              </div>

              <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                <select
                  id="status"
                  v-model="filters.status"
                  @change="applyFilters"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Toutes</option>
                  <option value="unread">Non lues</option>
                  <option value="read">Lues</option>
                </select>
              </div>

              <div class="flex items-end">
                <button
                  @click="clearFilters"
                  class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
                >
                  Effacer les filtres
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Notifications List -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="divide-y divide-gray-200">
            <div v-if="notifications.data.length === 0" class="p-8 text-center text-gray-500">
              <BellIcon class="h-16 w-16 mx-auto mb-4 text-gray-300" />
              <p class="text-lg font-medium mb-2">Aucune notification</p>
              <p class="text-sm">Vous n'avez aucune notification correspondant aux filtres sélectionnés.</p>
            </div>

            <div
              v-for="notification in notifications.data"
              :key="notification.id"
              class="p-6 hover:bg-gray-50 transition-colors cursor-pointer"
              :class="{ 'bg-blue-50': !notification.read_at }"
              @click="handleNotificationClick(notification)"
            >
              <div class="flex items-start space-x-4">
                <!-- Icon -->
                <div class="flex-shrink-0">
                  <div
                    class="w-10 h-10 rounded-full flex items-center justify-center"
                    :class="getNotificationIconClass(notification)"
                  >
                    <component :is="getNotificationIcon(notification)" class="h-5 w-5 text-white" />
                  </div>
                </div>

                <!-- Content -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <p class="text-lg font-medium text-gray-900">
                        {{ notification.title }}
                      </p>
                      <p class="text-gray-600 mt-1">
                        {{ notification.message }}
                      </p>
                      <div class="flex items-center mt-2 space-x-4">
                        <p class="text-sm text-gray-400">
                          {{ formatDate(notification.created_at) }}
                        </p>
                        <span
                          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                          :class="getTypeClass(notification.type)"
                        >
                          {{ getTypeLabel(notification.type) }}
                        </span>
                      </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="flex items-center space-x-2 ml-4">
                      <button
                        v-if="!notification.read_at"
                        @click.stop="markAsRead(notification)"
                        class="text-sm text-blue-600 hover:text-blue-800 font-medium"
                      >
                        Marquer comme lu
                      </button>
                      <button
                        @click.stop="deleteNotification(notification)"
                        class="text-sm text-red-600 hover:text-red-800 font-medium"
                      >
                        Supprimer
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Unread indicator -->
                <div v-if="!notification.read_at" class="flex-shrink-0">
                  <div class="w-3 h-3 bg-blue-600 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Pagination -->
          <div v-if="notifications.links.length > 3" class="px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
              <div class="text-sm text-gray-700">
                Affichage de {{ notifications.from }} à {{ notifications.to }} sur {{ notifications.total }} notifications
              </div>
              <div class="flex space-x-1">
                <Link
                  v-for="link in notifications.links"
                  :key="link.label"
                  :href="link.url"
                  :class="[
                    'px-3 py-2 text-sm rounded-md',
                    link.active
                      ? 'bg-blue-600 text-white'
                      : link.url
                      ? 'text-gray-700 hover:bg-gray-100'
                      : 'text-gray-400 cursor-not-allowed'
                  ]"
                  v-html="link.label"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Head, Link, router } from '@inertiajs/vue3'
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue'
import {
  BellIcon,
  ExclamationCircleIcon,
  CheckCircleIcon,
  ClockIcon,
  MegaphoneIcon,
  UserPlusIcon,
  ClipboardDocumentCheckIcon,
  TrophyIcon,
  CalendarIcon,
} from '@heroicons/vue/24/outline'
import axios from 'axios'

// Props
const props = defineProps({
  notifications: Object,
  stats: Object,
  filters: Object,
  types: Array,
})

// Reactive state
const markingAllAsRead = ref(false)
const filters = reactive({
  type: props.filters.type,
  status: props.filters.status,
})

// Methods
const applyFilters = () => {
  router.get(route('notifications.index'), filters, {
    preserveState: true,
    preserveScroll: true,
  })
}

const clearFilters = () => {
  filters.type = ''
  filters.status = ''
  applyFilters()
}

const markAsRead = async (notification) => {
  if (notification.read_at) return

  try {
    await axios.post(`/notifications/${notification.id}/mark-as-read`)
    notification.read_at = new Date().toISOString()
    // Refresh page to update stats
    router.reload({ only: ['notifications', 'stats'] })
  } catch (error) {
    console.error('Error marking notification as read:', error)
  }
}

const markAllAsRead = async () => {
  if (props.stats.unread === 0) return

  try {
    markingAllAsRead.value = true
    await axios.post('/notifications/mark-all-as-read')
    // Refresh page
    router.reload()
  } catch (error) {
    console.error('Error marking all notifications as read:', error)
  } finally {
    markingAllAsRead.value = false
  }
}

const deleteNotification = async (notification) => {
  if (!confirm('Êtes-vous sûr de vouloir supprimer cette notification ?')) {
    return
  }

  try {
    await axios.delete(`/notifications/${notification.id}`)
    // Refresh page
    router.reload()
  } catch (error) {
    console.error('Error deleting notification:', error)
  }
}

const handleNotificationClick = (notification) => {
  // Mark as read if unread
  if (!notification.read_at) {
    markAsRead(notification)
  }

  // Navigate to notification URL if available
  if (notification.data && notification.data.url) {
    window.location.href = notification.data.url
  }
}

const getNotificationIcon = (notification) => {
  switch (notification.type) {
    case 'announcement':
      return MegaphoneIcon
    case 'enrollment':
      return UserPlusIcon
    case 'exam_result':
      return ClipboardDocumentCheckIcon
    case 'certificate':
      return TrophyIcon
    case 'training_session':
      return CalendarIcon
    default:
      return BellIcon
  }
}

const getNotificationIconClass = (notification) => {
  switch (notification.type) {
    case 'announcement':
      return 'bg-blue-500'
    case 'enrollment':
      return 'bg-green-500'
    case 'exam_result':
      return 'bg-purple-500'
    case 'certificate':
      return 'bg-yellow-500'
    case 'training_session':
      return 'bg-indigo-500'
    default:
      return 'bg-gray-500'
  }
}

const getTypeClass = (type) => {
  switch (type) {
    case 'announcement':
      return 'bg-blue-100 text-blue-800'
    case 'enrollment':
      return 'bg-green-100 text-green-800'
    case 'exam_result':
      return 'bg-purple-100 text-purple-800'
    case 'certificate':
      return 'bg-yellow-100 text-yellow-800'
    case 'training_session':
      return 'bg-indigo-100 text-indigo-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getTypeLabel = (type) => {
  switch (type) {
    case 'announcement':
      return 'Annonce'
    case 'enrollment':
      return 'Inscription'
    case 'exam_result':
      return 'Résultat'
    case 'certificate':
      return 'Certificat'
    case 'training_session':
      return 'Session'
    default:
      return 'Notification'
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
