<?php

namespace Database\Seeders;

use App\Models\HomepageContent;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class HomepageContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Hero Section Content
        HomepageContent::updateOrCreate(
            ['section' => 'hero', 'key' => 'title'],
            [
                'value' => 'Formation Premiers Secours Professionnelle',
                'type' => 'text',
                'sort_order' => 1,
                'is_active' => true
            ]
        );

        HomepageContent::updateOrCreate(
            ['section' => 'hero', 'key' => 'subtitle'],
            [
                'value' => 'Maîtrisez les gestes qui sauvent avec nos formations certifiées',
                'type' => 'text',
                'sort_order' => 2,
                'is_active' => true
            ]
        );

        HomepageContent::updateOrCreate(
            ['section' => 'hero', 'key' => 'description'],
            [
                'value' => 'Apprenez les techniques de premiers secours avec des experts qualifiés et une technologie VR innovante.',
                'type' => 'text',
                'sort_order' => 3,
                'is_active' => true
            ]
        );

        // VR Training Section Content
        HomepageContent::updateOrCreate(
            ['section' => 'vr_training', 'key' => 'title'],
            [
                'value' => 'Formation VR Réalité Virtuelle',
                'type' => 'text',
                'sort_order' => 1,
                'is_active' => true
            ]
        );

        HomepageContent::updateOrCreate(
            ['section' => 'vr_training', 'key' => 'description'],
            [
                'value' => 'Découvrez notre approche révolutionnaire de la formation aux premiers secours grâce à la réalité virtuelle. Une expérience immersive pour un apprentissage optimal.',
                'type' => 'text',
                'sort_order' => 2,
                'is_active' => true
            ]
        );

        // About Section Content
        HomepageContent::updateOrCreate(
            ['section' => 'about', 'key' => 'badge'],
            [
                'value' => 'À Propos de PCMET',
                'type' => 'text',
                'sort_order' => 1,
                'is_active' => true
            ]
        );

        HomepageContent::updateOrCreate(
            ['section' => 'about', 'key' => 'title'],
            [
                'value' => 'Experts en Formation Premiers Secours',
                'type' => 'text',
                'sort_order' => 2,
                'is_active' => true
            ]
        );

        HomepageContent::updateOrCreate(
            ['section' => 'about', 'key' => 'description'],
            [
                'value' => 'PCMET Horizon Qualité est un centre de formation spécialisé dans l\'enseignement des premiers secours. Nous combinons expertise traditionnelle et technologies innovantes pour offrir la meilleure formation possible.',
                'type' => 'text',
                'sort_order' => 3,
                'is_active' => true
            ]
        );

        HomepageContent::updateOrCreate(
            ['section' => 'about', 'key' => 'stats'],
            [
                'value' => json_encode([
                    'students_trained' => '500+',
                    'years_experience' => '15+',
                    'success_rate' => '98%',
                    'support_availability' => '24/7'
                ]),
                'type' => 'json',
                'sort_order' => 4,
                'is_active' => true
            ]
        );

        // Contact Section Content
        HomepageContent::updateOrCreate(
            ['section' => 'contact', 'key' => 'title'],
            [
                'value' => 'Contactez-nous',
                'type' => 'text',
                'sort_order' => 1,
                'is_active' => true
            ]
        );

        HomepageContent::updateOrCreate(
            ['section' => 'contact', 'key' => 'subtitle'],
            [
                'value' => 'Prêt à commencer votre formation ? Nous sommes là pour vous aider.',
                'type' => 'text',
                'sort_order' => 2,
                'is_active' => true
            ]
        );

        HomepageContent::updateOrCreate(
            ['section' => 'contact', 'key' => 'address'],
            [
                'value' => '58 Rue Des Jacinthes<br>2000, Tunisie',
                'type' => 'text',
                'sort_order' => 3,
                'is_active' => true
            ]
        );

        HomepageContent::updateOrCreate(
            ['section' => 'contact', 'key' => 'phone'],
            [
                'value' => '55 000 511',
                'type' => 'text',
                'sort_order' => 4,
                'is_active' => true
            ]
        );

        HomepageContent::updateOrCreate(
            ['section' => 'contact', 'key' => 'email'],
            [
                'value' => '<EMAIL>',
                'type' => 'text',
                'sort_order' => 5,
                'is_active' => true
            ]
        );

        // Media Content Examples
        HomepageContent::updateOrCreate(
            ['section' => 'hero', 'key' => 'main_image'],
            [
                'value' => 'homepage/hero/formation-premiers-secours.jpg',
                'type' => 'image',
                'metadata' => [
                    'alt' => 'Formation premiers secours PCMET',
                    'title' => 'Formation professionnelle premiers secours',
                    'width' => 800,
                    'height' => 600
                ],
                'sort_order' => 4,
                'is_active' => true
            ]
        );

        HomepageContent::updateOrCreate(
            ['section' => 'vr_training', 'key' => 'demo_video'],
            [
                'value' => 'homepage/vr/demo-vr-training.mp4',
                'type' => 'video',
                'metadata' => [
                    'alt' => 'Démonstration formation VR',
                    'title' => 'Vidéo de démonstration VR',
                    'duration' => '2:30'
                ],
                'sort_order' => 3,
                'is_active' => true
            ]
        );

        HomepageContent::updateOrCreate(
            ['section' => 'about', 'key' => 'facility_images'],
            [
                'value' => json_encode([
                    'homepage/about/facility-1.jpg',
                    'homepage/about/facility-2.jpg',
                    'homepage/about/facility-3.jpg'
                ]),
                'type' => 'json',
                'metadata' => [
                    'type' => 'image_gallery',
                    'title' => 'Nos installations'
                ],
                'sort_order' => 5,
                'is_active' => true
            ]
        );
    }
}
