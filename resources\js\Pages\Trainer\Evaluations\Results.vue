<template>
  <Head :title="`Résultats - ${evaluation.title}`" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Résultats: {{ evaluation.title }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Liens de navigation -->
            <div class="mb-6 flex justify-between items-center">
              <Link :href="route('trainer.evaluations.show', evaluation.id)" class="text-indigo-600 hover:text-indigo-900">
                &larr; Retour à l'évaluation
              </Link>
              <Link :href="route('trainer.evaluations.export', evaluation.id)" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                Exporter en CSV
              </Link>
            </div>

            <!-- Statistiques -->
            <div class="mb-6">
              <h3 class="text-lg font-semibold mb-4">Statistiques</h3>
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <div class="text-2xl font-bold text-indigo-600">{{ stats.total_responses }}</div>
                  <div class="text-sm text-gray-500">Réponses totales</div>
                </div>
                <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <div class="text-2xl font-bold text-green-600">{{ stats.completed_responses }}</div>
                  <div class="text-sm text-gray-500">Réponses complétées</div>
                </div>
                <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <div class="text-2xl font-bold text-blue-600">{{ stats.completion_rate }}%</div>
                  <div class="text-sm text-gray-500">Taux de complétion</div>
                </div>
                <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <div class="text-2xl font-bold text-purple-600">{{ stats.average_rating ? stats.average_rating.toFixed(1) : '-' }}/10</div>
                  <div class="text-sm text-gray-500">Note moyenne</div>
                </div>
              </div>
            </div>

            <!-- Résultats par question -->
            <div>
              <h3 class="text-lg font-semibold mb-4">Résultats par question</h3>
              <div v-if="chartData.length > 0" class="space-y-8">
                <div v-for="(question, index) in chartData" :key="index" class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <h4 class="font-medium mb-2">{{ index + 1 }}. {{ question.text }}</h4>
                  
                  <!-- Graphique pour les questions à choix multiples -->
                  <div v-if="question.type === 'multiple_choice'" class="mt-4">
                    <div class="h-64">
                      <canvas :id="`chart-${question.id}`" ref="chartCanvas"></canvas>
                    </div>
                  </div>
                  
                  <!-- Graphique pour les questions de notation -->
                  <div v-else-if="question.type === 'rating'" class="mt-4">
                    <div class="h-64">
                      <canvas :id="`chart-${question.id}`" ref="chartCanvas"></canvas>
                    </div>
                  </div>
                  
                  <!-- Graphique pour les questions oui/non -->
                  <div v-else-if="question.type === 'yes_no'" class="mt-4">
                    <div class="h-64">
                      <canvas :id="`chart-${question.id}`" ref="chartCanvas"></canvas>
                    </div>
                  </div>
                  
                  <!-- Réponses textuelles -->
                  <div v-else-if="question.type === 'text'" class="mt-4">
                    <div v-if="question.data.length > 0" class="space-y-2">
                      <div v-for="(answer, answerIndex) in question.data" :key="answerIndex" class="p-3 bg-gray-50 rounded-md">
                        <p class="text-sm text-gray-500 mb-1">{{ answer.student }}</p>
                        <p>{{ answer.text }}</p>
                      </div>
                    </div>
                    <div v-else class="text-center py-4">
                      <p class="text-gray-500">Aucune réponse pour cette question.</p>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="text-center py-8">
                <p class="text-gray-500">Aucune donnée disponible pour cette évaluation.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import { onMounted } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  evaluation: Object,
  chartData: Array,
  stats: Object,
});

// Méthodes pour créer les graphiques
onMounted(() => {
  // Simuler la création de graphiques
  // Dans une implémentation réelle, vous utiliseriez Chart.js ou une autre bibliothèque
  console.log('Graphiques initialisés');
});
</script>
