<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use App\Models\Announcement;
use App\Models\Enrollment;
use App\Models\ExamResult;
use App\Models\Certificate;
use App\Models\TrainingSession;
use Illuminate\Support\Collection;

class NotificationService
{
    /**
     * Create a notification for a specific user
     */
    public function createNotification(
        User $user,
        string $type,
        string $title,
        string $message,
        array $data = []
    ): Notification {
        return Notification::create([
            'user_id' => $user->id,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
        ]);
    }

    /**
     * Create notifications for multiple users
     */
    public function createNotificationsForUsers(
        Collection $users,
        string $type,
        string $title,
        string $message,
        array $data = []
    ): int {
        $notifications = [];
        $now = now();

        foreach ($users as $user) {
            $notifications[] = [
                'user_id' => $user->id,
                'type' => $type,
                'title' => $title,
                'message' => $message,
                'data' => json_encode($data),
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        Notification::insert($notifications);
        return count($notifications);
    }

    /**
     * Notify about new announcement
     */
    public function notifyNewAnnouncement(Announcement $announcement): int
    {
        $users = $this->getUsersForAnnouncementNotification($announcement);
        
        $title = $announcement->is_important 
            ? "🔥 Annonce importante: {$announcement->title}"
            : "📢 Nouvelle annonce: {$announcement->title}";

        $message = $announcement->training_session_id
            ? "Nouvelle annonce pour la session {$announcement->trainingSession->title}"
            : "Nouvelle annonce générale publiée";

        $data = [
            'announcement_id' => $announcement->id,
            'url' => route('announcements.show', $announcement->id),
            'is_important' => $announcement->is_important,
        ];

        return $this->createNotificationsForUsers(
            $users,
            Notification::TYPE_ANNOUNCEMENT,
            $title,
            $message,
            $data
        );
    }

    /**
     * Notify about enrollment status change
     */
    public function notifyEnrollmentStatusChange(Enrollment $enrollment): void
    {
        $user = $enrollment->user;
        $session = $enrollment->trainingSession;

        $title = match($enrollment->status) {
            'approved' => "✅ Inscription approuvée",
            'rejected' => "❌ Inscription refusée",
            'pending' => "⏳ Inscription en attente",
            default => "📝 Statut d'inscription mis à jour",
        };

        $message = match($enrollment->status) {
            'approved' => "Votre inscription à '{$session->title}' a été approuvée. Vous pouvez maintenant accéder aux cours.",
            'rejected' => "Votre inscription à '{$session->title}' a été refusée. Contactez l'administration pour plus d'informations.",
            'pending' => "Votre inscription à '{$session->title}' est en cours de traitement.",
            default => "Le statut de votre inscription à '{$session->title}' a été mis à jour.",
        };

        $data = [
            'enrollment_id' => $enrollment->id,
            'training_session_id' => $session->id,
            'status' => $enrollment->status,
            'url' => route('student.enrollments.index'),
        ];

        $this->createNotification(
            $user,
            Notification::TYPE_ENROLLMENT,
            $title,
            $message,
            $data
        );
    }

    /**
     * Notify about exam result
     */
    public function notifyExamResult(ExamResult $examResult): void
    {
        $user = $examResult->user;
        $exam = $examResult->exam;

        $title = $examResult->passed 
            ? "🎉 Examen réussi!"
            : "📊 Résultat d'examen disponible";

        $message = $examResult->passed
            ? "Félicitations! Vous avez réussi l'examen '{$exam->title}' avec {$examResult->score}%."
            : "Votre résultat pour l'examen '{$exam->title}' est disponible: {$examResult->score}%.";

        $data = [
            'exam_result_id' => $examResult->id,
            'exam_id' => $exam->id,
            'score' => $examResult->score,
            'passed' => $examResult->passed,
            'url' => route('student.exam-results.show', $examResult->id),
        ];

        $this->createNotification(
            $user,
            Notification::TYPE_EXAM_RESULT,
            $title,
            $message,
            $data
        );
    }

    /**
     * Notify about certificate generation
     */
    public function notifyCertificateGenerated(Certificate $certificate): void
    {
        $user = $certificate->user;
        $session = $certificate->trainingSession;

        $title = match($certificate->status) {
            'active' => "🏆 Certificat disponible!",
            'issued' => "📜 Certificat délivré!",
            default => "📋 Certificat mis à jour",
        };

        $message = match($certificate->status) {
            'active' => "Votre certificat pour '{$session->title}' est maintenant disponible au téléchargement.",
            'issued' => "Votre certificat pour '{$session->title}' a été officiellement délivré.",
            default => "Le statut de votre certificat pour '{$session->title}' a été mis à jour.",
        };

        $data = [
            'certificate_id' => $certificate->id,
            'training_session_id' => $session->id,
            'status' => $certificate->status,
            'url' => route('student.certificates.show', $certificate->id),
        ];

        $this->createNotification(
            $user,
            Notification::TYPE_CERTIFICATE,
            $title,
            $message,
            $data
        );
    }

    /**
     * Notify about training session updates
     */
    public function notifyTrainingSessionUpdate(TrainingSession $session, string $updateType = 'updated'): int
    {
        $enrolledUsers = $session->students()->wherePivot('status', 'approved')->get();

        if ($enrolledUsers->isEmpty()) {
            return 0;
        }

        $title = match($updateType) {
            'schedule_changed' => "📅 Horaire modifié",
            'location_changed' => "📍 Lieu modifié",
            'content_updated' => "📚 Contenu mis à jour",
            default => "🔄 Session mise à jour",
        };

        $message = "La session de formation '{$session->title}' a été mise à jour. Consultez les détails pour plus d'informations.";

        $data = [
            'training_session_id' => $session->id,
            'update_type' => $updateType,
            'url' => route('student.sessions.show', $session->id),
        ];

        return $this->createNotificationsForUsers(
            $enrolledUsers,
            Notification::TYPE_TRAINING_SESSION,
            $title,
            $message,
            $data
        );
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(int $notificationId, User $user): bool
    {
        $notification = $user->notifications()->find($notificationId);
        
        if (!$notification) {
            return false;
        }

        return $notification->markAsRead();
    }

    /**
     * Mark all notifications as read for a user
     */
    public function markAllAsRead(User $user): int
    {
        return $user->notifications()->unread()->update(['read_at' => now()]);
    }

    /**
     * Get users who should receive announcement notifications
     */
    private function getUsersForAnnouncementNotification(Announcement $announcement): Collection
    {
        $query = User::where('active', true);

        // Filter by visibility
        if ($announcement->visible_to !== 'all') {
            $query->where('role', $announcement->visible_to);
        }

        // Filter by training session if specified
        if ($announcement->training_session_id) {
            $query->whereHas('enrollments', function ($q) use ($announcement) {
                $q->where('training_session_id', $announcement->training_session_id)
                  ->where('status', 'approved');
            });
        }

        return $query->get();
    }

    /**
     * Get notification statistics for a user
     */
    public function getNotificationStats(User $user): array
    {
        // Use base query for all counts to ensure consistency
        $baseQuery = Notification::where('user_id', $user->id);

        // Get basic counts
        $total = $baseQuery->count();
        $unread = $baseQuery->whereNull('read_at')->count();
        $read = $baseQuery->whereNotNull('read_at')->count();
        $recent = $baseQuery->where('created_at', '>=', now()->subDays(30))->count();

        // Get type statistics with a safer approach
        $typeStats = [];
        try {
            $typeResults = $baseQuery->select('type')
                ->selectRaw('COUNT(*) as count')
                ->groupBy('type')
                ->get();

            foreach ($typeResults as $result) {
                $typeStats[$result->type] = $result->count;
            }
        } catch (\Exception $e) {
            // Fallback: calculate type stats manually if GROUP BY fails
            $allNotifications = $baseQuery->select('type')->get();
            $typeStats = $allNotifications->groupBy('type')->map->count()->toArray();
        }

        return [
            'total' => $total,
            'unread' => $unread,
            'read' => $read,
            'recent' => $recent,
            'by_type' => $typeStats,
        ];
    }

    /**
     * Clean old notifications (older than 90 days)
     */
    public function cleanOldNotifications(): int
    {
        return Notification::where('created_at', '<', now()->subDays(90))->delete();
    }
}
