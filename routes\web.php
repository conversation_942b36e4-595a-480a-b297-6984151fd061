<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\SessionEnrollmentController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\TrainingDomainController;
use App\Http\Controllers\Admin\TrainingSessionController;
use App\Http\Controllers\Admin\EnrollmentController;
use App\Http\Controllers\Admin\CertificateController;
use App\Http\Controllers\Admin\TrainerController;
use App\Http\Controllers\Admin\ExamController as AdminExamController;
use App\Http\Controllers\Admin\ExamQuestionController as AdminExamQuestionController;
use App\Http\Controllers\Admin\ExamResultController as AdminExamResultController;
use App\Http\Controllers\Admin\FixQuestionsController;
use App\Http\Controllers\Admin\CourseController as AdminCourseController;
use App\Http\Controllers\Admin\CourseMaterialController as AdminCourseMaterialController;
use App\Http\Controllers\Admin\FirstAidTipController;
use App\Http\Controllers\Admin\FirstAidTipMaterialController;
use App\Http\Controllers\Admin\HomepageContentController;
use App\Http\Controllers\Admin\StudentController as AdminStudentController;
use App\Http\Controllers\Trainer\DashboardController as TrainerDashboardController;
use App\Http\Controllers\Trainer\CourseController;
use App\Http\Controllers\Trainer\CourseMaterialController;
use App\Http\Controllers\Trainer\ModuleController;
use App\Http\Controllers\Trainer\ExamController;
use App\Http\Controllers\Trainer\ExamQuestionController;
use App\Http\Controllers\Trainer\ExamResultController;
use App\Http\Controllers\Trainer\QuestionBankController;
use App\Http\Controllers\Trainer\EvaluationController;
use App\Http\Controllers\Trainer\EvaluationQuestionController;
use App\Http\Controllers\Student\DashboardController as StudentDashboardController;
use App\Http\Controllers\Student\CourseProgressController;
use App\Http\Controllers\Student\FirstAidTipAccessController;
use App\Http\Controllers\Student\ExamController as StudentExamController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', [HomeController::class, 'index'])->name('welcome');
Route::get('/sessions', [HomeController::class, 'listSessions'])->name('sessions.index');
Route::get('/sessions/{id}', [HomeController::class, 'showSession'])->name('sessions.show');

// Routes pour l'inscription aux sessions
Route::get('/sessions/{id}/enroll', [SessionEnrollmentController::class, 'create'])->name('session.enrollment.create');
Route::post('/sessions/{id}/enroll/existing', [SessionEnrollmentController::class, 'storeExistingUser'])->name('session.enrollment.store-existing')->middleware('auth');
Route::post('/sessions/{id}/enroll/new', [SessionEnrollmentController::class, 'storeNewUser'])->name('session.enrollment.store-new');
Route::get('/enrollment/confirmation/{id}', [SessionEnrollmentController::class, 'confirmation'])->name('session.enrollment.confirmation')->middleware('auth');

// Route spéciale pour le téléchargement direct des certificats (sans middleware Inertia)
Route::get('/direct-download/certificate/{id}', [\App\Http\Controllers\CertificateController::class, 'directDownload'])
    ->name('certificates.direct-download')
    ->middleware('auth');

// Route publique pour tester le certificat
Route::get('/test-certificate', function () {
    try {
        // Créer des données fictives pour le test
        $user = (object) [
            'name' => 'Ahmed Ben Ali'
        ];

        $trainingSession = (object) [
            'title' => 'Formation Premiers Secours',
            'certificate_name' => 'Premiers Secours',
            'training_objectives' => 'Cette formation a permis au participant de maîtriser les gestes essentiels de premiers secours, d\'assurer la sécurité de la victime et de transmettre une alerte efficace. Les compétences ont été validées par des mises en situation pratiques.'
        ];

        $trainingDomain = (object) [
            'name' => 'Sécurité et Premiers Secours'
        ];

        $certificate = (object) [
            'certificate_number' => 'TEST-2025-001',
            'issued_at' => now()
        ];

        // Préparer les données pour la vue
        $data = [
            'certificate' => $certificate,
            'user' => $user,
            'trainingSession' => $trainingSession,
            'trainingDomain' => $trainingDomain
        ];

        // Générer le PDF
        $pdf = app('dompdf.wrapper');
        $pdf->loadView('certificates.pdf', $data);
        $pdf->setPaper('a4', 'portrait');
        $pdf->setOptions([
            'defaultFont' => 'sans-serif',
            'isHtml5ParserEnabled' => true,
            'isRemoteEnabled' => false
        ]);

        // Retourner le PDF pour affichage dans le navigateur
        return $pdf->stream('certificat_test.pdf');

    } catch (\Exception $e) {
        return response('Erreur lors de la génération du certificat de test: ' . $e->getMessage(), 500);
    }
});

// Route publique pour vérifier l'authenticité d'un certificat
Route::get('/certificates/verify/{number}', function ($number) {
    $certificate = \App\Models\Certificate::where('certificate_number', $number)
        ->with(['user', 'enrollment.trainingSession.trainingDomain'])
        ->first();

    if (!$certificate) {
        return Inertia::render('Certificates/Verify', [
            'valid' => false,
            'message' => 'Ce certificat n\'est pas valide ou n\'existe pas.',
        ]);
    }

    return Inertia::render('Certificates/Verify', [
        'valid' => true,
        'certificate' => [
            'number' => $certificate->certificate_number,
            'user' => $certificate->user->name,
            'training' => $certificate->enrollment->trainingSession->title,
            'domain' => $certificate->enrollment->trainingSession->trainingDomain->name,
            'issue_date' => $certificate->issue_date ? $certificate->issue_date->format('d/m/Y') : ($certificate->issued_at ? $certificate->issued_at->format('d/m/Y') : 'Non définie'),
            'expiry_date' => $certificate->expiry_date ? $certificate->expiry_date->format('d/m/Y') : null,
        ],
    ]);
})->name('certificates.verify');

// Route publique pour les profils d'apprenants (accessible via QR code)
Route::get('/students/{id}/profile', [AdminStudentController::class, 'publicProfile'])->name('students.public-profile');

// Redirection vers le tableau de bord approprié en fonction du rôle de l'utilisateur
Route::get('/dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified', 'redirect.role'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::post('/profile', [ProfileController::class, 'update'])->name('profile.update.post');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Notification routes
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [\App\Http\Controllers\NotificationController::class, 'index'])->name('index');
        Route::get('/recent', [\App\Http\Controllers\NotificationController::class, 'recent'])->name('recent');
        Route::get('/unread-count', [\App\Http\Controllers\NotificationController::class, 'unreadCount'])->name('unread-count');
        Route::post('/{id}/mark-as-read', [\App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('mark-as-read');
        Route::post('/mark-all-as-read', [\App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('mark-all-as-read');
        Route::delete('/{id}', [\App\Http\Controllers\NotificationController::class, 'destroy'])->name('destroy');
    });

    // Public announcement routes (for all authenticated users)
    Route::prefix('announcements')->name('announcements.')->group(function () {
        Route::get('/', [\App\Http\Controllers\AnnouncementController::class, 'index'])->name('index');
        Route::get('/{id}', [\App\Http\Controllers\AnnouncementController::class, 'show'])->name('show');
    });

    // Routes pour les administrateurs
    Route::middleware(['role:admin', 'debug'])->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
        Route::resource('training-domains', TrainingDomainController::class);
        Route::resource('training-sessions', TrainingSessionController::class);
        Route::resource('trainers', TrainerController::class);
        Route::resource('enrollments', EnrollmentController::class);
        Route::put('enrollments/{id}/update-payment', [EnrollmentController::class, 'updatePayment'])->name('enrollments.update-payment');
        Route::resource('announcements', \App\Http\Controllers\Admin\AnnouncementController::class);
        Route::resource('exams', AdminExamController::class);
        Route::get('exams/{exam}/questions', [AdminExamController::class, 'showQuestions'])->name('exams.questions');
        Route::post('exams/{exam}/toggle-publish', [AdminExamController::class, 'togglePublish'])->name('exams.toggle-publish');
        Route::get('fix-questions', [FixQuestionsController::class, 'fixQuestions'])->name('fix-questions');
        Route::resource('exam-questions', AdminExamQuestionController::class);
        Route::resource('exam-results', AdminExamResultController::class);
        Route::post('exam-results/{id}/validate', [AdminExamResultController::class, 'validateResult'])->name('exam-results.validate');
        Route::resource('certificates', CertificateController::class);
        Route::get('certificates/{id}/download', [CertificateController::class, 'download'])->name('certificates.download');
        Route::get('certificates/{id}/view', [CertificateController::class, 'view'])->name('certificates.view');
        Route::get('certificates/{id}/pdf-url', [CertificateController::class, 'getPdfUrl'])->name('certificates.pdf-url');
        Route::post('certificates/{id}/regenerate', [CertificateController::class, 'regenerate'])->name('certificates.regenerate');
        Route::get('certificates/test-pdf', [CertificateController::class, 'testPdf'])->name('certificates.test-pdf');
        Route::get('certificates/create-test-certificate', [CertificateController::class, 'createTestCertificate'])->name('certificates.create-test');
        Route::resource('courses', AdminCourseController::class);
        Route::resource('modules', \App\Http\Controllers\Admin\ModuleController::class);
        Route::resource('course-materials', AdminCourseMaterialController::class);
        Route::get('course-materials/{id}/download', [AdminCourseMaterialController::class, 'download'])->name('course-materials.download');
        Route::get('course-materials/{id}/view', [AdminCourseMaterialController::class, 'view'])->name('course-materials.view');

        // Routes pour les conseils en premiers secours
        Route::resource('first-aid-tips', FirstAidTipController::class);
        Route::resource('first-aid-tip-materials', FirstAidTipMaterialController::class);
        Route::get('first-aid-tip-materials/{id}/download', [FirstAidTipMaterialController::class, 'download'])->name('first-aid-tip-materials.download');
        Route::get('first-aid-tip-materials/{id}/view', [FirstAidTipMaterialController::class, 'view'])->name('first-aid-tip-materials.view');

        // Routes pour la gestion du contenu de la page d'accueil
        Route::resource('homepage-content', HomepageContentController::class);
        Route::post('homepage-content/upload', [HomepageContentController::class, 'uploadFile'])->name('homepage-content.upload');
        Route::get('homepage-content-media', [HomepageContentController::class, 'media'])->name('homepage-content.media');

        // Routes pour la gestion des apprenants
        Route::resource('students', \App\Http\Controllers\Admin\StudentManagementController::class)->only(['index', 'show']);
        Route::get('students/{id}/qr-code', [\App\Http\Controllers\Admin\StudentManagementController::class, 'downloadQrCode'])->name('students.qr-code');
        Route::get('students/{id}/card', [\App\Http\Controllers\Admin\StudentManagementController::class, 'generateStudentCard'])->name('students.card');
        Route::get('students/{id}/card/download', [\App\Http\Controllers\Admin\StudentManagementController::class, 'downloadStudentCardPng'])->name('students.card.download');
        Route::get('students/{id}/sheet', [\App\Http\Controllers\Admin\StudentManagementController::class, 'generateStudentSheet'])->name('students.sheet');
        Route::get('students/{id}/sheet/view', [\App\Http\Controllers\Admin\StudentManagementController::class, 'viewStudentSheet'])->name('students.sheet.view');
        Route::get('students/export/pdf', [\App\Http\Controllers\Admin\StudentManagementController::class, 'exportPdf'])->name('students.export-pdf');
        Route::post('students/bulk-action', [\App\Http\Controllers\Admin\StudentManagementController::class, 'bulkAction'])->name('students.bulk-action');
    });

    // Routes pour les formateurs
    Route::middleware('role:trainer')->prefix('trainer')->name('trainer.')->group(function () {
        Route::get('/dashboard', [TrainerDashboardController::class, 'index'])->name('dashboard');

        // Routes pour les sessions de formation
        Route::resource('sessions', \App\Http\Controllers\Trainer\TrainingSessionController::class)->only(['index', 'show']);
        Route::post('sessions/{id}/attendance', [\App\Http\Controllers\Trainer\TrainingSessionController::class, 'storeAttendance'])->name('sessions.attendance.store');
        Route::get('sessions/{id}/progress-report', [\App\Http\Controllers\Trainer\TrainingSessionController::class, 'generateProgressReport'])->name('sessions.progress-report');

        // Routes existantes
        Route::resource('courses', CourseController::class);
        Route::resource('course-materials', CourseMaterialController::class);
        Route::get('course-materials/{id}/download', [CourseMaterialController::class, 'download'])->name('course-materials.download');
        Route::get('course-materials/{id}/view', [CourseMaterialController::class, 'view'])->name('course-materials.view');
        Route::resource('modules', ModuleController::class);
        Route::resource('exams', ExamController::class)->except(['create', 'store']);
        Route::resource('exam-questions', ExamQuestionController::class)->only(['index', 'show']);
        // Route::post('exam-questions/reorder', [ExamQuestionController::class, 'reorder'])->name('exam-questions.reorder');
        Route::resource('exam-results', ExamResultController::class);
        Route::post('exam-results/{id}/validate', [ExamResultController::class, 'validateResult'])->name('exam-results.validate');

        // Routes pour la banque de questions
        Route::resource('question-bank', QuestionBankController::class);
        Route::post('question-bank/{id}/duplicate', [QuestionBankController::class, 'duplicate'])->name('question-bank.duplicate');

        // Routes pour les évaluations
        Route::resource('evaluations', EvaluationController::class);
        Route::get('evaluations/{id}/results', [EvaluationController::class, 'results'])->name('evaluations.results');
        Route::get('evaluations/{id}/export', [EvaluationController::class, 'export'])->name('evaluations.export');

        // Routes pour les questions d'évaluation
        Route::get('evaluations/{evaluationId}/questions', [EvaluationQuestionController::class, 'index'])->name('evaluations.questions.index');
        Route::get('evaluations/{evaluationId}/questions/create', [EvaluationQuestionController::class, 'create'])->name('evaluations.questions.create');
        Route::post('evaluations/{evaluationId}/questions', [EvaluationQuestionController::class, 'store'])->name('evaluations.questions.store');
        Route::get('evaluations/{evaluationId}/questions/{questionId}/edit', [EvaluationQuestionController::class, 'edit'])->name('evaluations.questions.edit');
        Route::put('evaluations/{evaluationId}/questions/{questionId}', [EvaluationQuestionController::class, 'update'])->name('evaluations.questions.update');
        Route::delete('evaluations/{evaluationId}/questions/{questionId}', [EvaluationQuestionController::class, 'destroy'])->name('evaluations.questions.destroy');
        Route::post('evaluations/{evaluationId}/questions/reorder', [EvaluationQuestionController::class, 'reorder'])->name('evaluations.questions.reorder');
    });

    // Routes pour les apprenants
    Route::middleware('role:student')->prefix('student')->name('student.')->group(function () {
        Route::get('/dashboard', [StudentDashboardController::class, 'index'])->name('dashboard');
        Route::resource('course-progress', CourseProgressController::class);
        Route::get('/courses/{course}', [CourseProgressController::class, 'show'])->name('courses.show');
        Route::get('/courses/{course}/materials', [CourseProgressController::class, 'materials'])->name('courses.materials');
        Route::get('/course-materials/{id}/download', [CourseProgressController::class, 'downloadMaterial'])->name('course-materials.download');
        Route::get('/course-materials/{id}/view', [CourseProgressController::class, 'viewMaterial'])->name('course-materials.view');

        // Routes pour les conseils en premiers secours
        Route::get('/first-aid-tips', [FirstAidTipAccessController::class, 'index'])->name('first-aid-tips.index');
        Route::get('/first-aid-tips/{id}', [FirstAidTipAccessController::class, 'show'])->name('first-aid-tips.show');
        Route::get('/first-aid-tip-materials/{id}/download', [FirstAidTipAccessController::class, 'downloadMaterial'])->name('first-aid-tip-materials.download');
        Route::get('/first-aid-tip-materials/{id}/view', [FirstAidTipAccessController::class, 'viewMaterial'])->name('first-aid-tip-materials.view');

        // Routes pour les inscriptions
        Route::get('/enrollments', [\App\Http\Controllers\Student\EnrollmentController::class, 'index'])->name('enrollments.index');
        Route::get('/enrollments/{id}', [\App\Http\Controllers\Student\EnrollmentController::class, 'show'])->name('enrollments.show');
        Route::get('/enrollments/{id}/cancel', [\App\Http\Controllers\Student\EnrollmentController::class, 'cancel'])->name('enrollments.cancel');
        Route::post('/enrollments/{id}/upload-payment-proof', [\App\Http\Controllers\Student\EnrollmentController::class, 'uploadPaymentProof'])->name('enrollments.upload-payment-proof');
        Route::get('/payments', [\App\Http\Controllers\Student\EnrollmentController::class, 'payments'])->name('enrollments.payments');

        // Routes pour les examens
        Route::get('/exams', [StudentExamController::class, 'index'])->name('exams.index');
        Route::get('/exams/{id}', [StudentExamController::class, 'show'])->name('exams.show');
        Route::post('/exams/{id}/submit', [StudentExamController::class, 'submit'])->name('exams.submit');
        Route::get('/exam-results/{id}', [StudentExamController::class, 'results'])->name('exams.results');

        // Routes pour les certificats
        Route::get('/certificates', [\App\Http\Controllers\Student\CertificateController::class, 'index'])->name('certificates');
        Route::get('/certificates/{certificate}/view', [\App\Http\Controllers\Student\CertificateController::class, 'view'])->name('certificates.view');
        Route::get('/certificates/{certificate}/download', [\App\Http\Controllers\Student\CertificateController::class, 'download'])->name('certificates.download');

        // Routes pour les sessions de formation
        Route::get('/sessions', [\App\Http\Controllers\Student\SessionController::class, 'index'])->name('sessions.index');
        Route::get('/sessions/{id}', [\App\Http\Controllers\Student\SessionController::class, 'show'])->name('sessions.show');
    });
});

// Route publique pour les profils d'apprenants (accessible via QR code)
Route::get('/students/{id}/profile', [\App\Http\Controllers\Admin\StudentManagementController::class, 'publicProfile'])->name('students.public-profile');

// Route publique pour visualiser les certificats (accessible sans authentification)
Route::get('/public/certificates/{certificate}/view', [\App\Http\Controllers\PublicCertificateController::class, 'view'])->name('public.certificates.view');

// Test route for debugging exam display issue
Route::get('/test-exam-data/{userId}', function($userId) {
    $student = \App\Models\User::findOrFail($userId);
    \Auth::login($student);

    $controller = new \App\Http\Controllers\Student\ExamController();
    $request = new \Illuminate\Http\Request();

    return $controller->index($request);
})->name('test.exam.data');

require __DIR__.'/auth.php';
