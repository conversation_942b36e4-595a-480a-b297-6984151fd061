<template>
  <Head :title="`Conseil: ${firstAidTip.title}`" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          {{ firstAidTip.title }}
        </h2>
        <Link :href="route('student.first-aid-tips.index')" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
          Retour à la liste
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
        <!-- Informations du conseil -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <div class="flex items-center mb-4">
              <HeartIcon class="h-8 w-8 text-red-500 mr-3" />
              <h3 class="text-lg font-semibold">{{ firstAidTip.title }}</h3>
            </div>
            
            <div v-if="firstAidTip.description" class="mb-6">
              <p class="text-gray-700 whitespace-pre-wrap">{{ firstAidTip.description }}</p>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div class="flex items-center">
                <InformationCircleIcon class="h-5 w-5 text-blue-500 mr-2" />
                <p class="text-sm text-blue-700">
                  Ce contenu est accessible gratuitement à tous les apprenants inscrits.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Matériels disponibles -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-6">Matériels disponibles</h3>

            <div v-if="firstAidTip.materials && firstAidTip.materials.length > 0" class="space-y-4">
              <div
                v-for="material in firstAidTip.materials"
                :key="material.id"
                class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
              >
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <div class="flex items-center mb-2">
                      <component :is="getTypeIcon(material.type)" class="h-6 w-6 mr-2" :class="getTypeIconColor(material.type)" />
                      <h4 class="font-medium text-gray-900">{{ material.title }}</h4>
                    </div>
                    
                    <p class="text-sm text-gray-600 mb-3" v-if="material.description">
                      {{ material.description }}
                    </p>
                    
                    <div class="flex items-center space-x-4 mb-4">
                      <span :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        getTypeColor(material.type)
                      ]">
                        {{ getTypeLabel(material.type) }}
                      </span>
                      
                      <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <span v-if="material.allow_online_viewing" class="flex items-center">
                          <EyeIcon class="h-4 w-4 mr-1" />
                          Visualisation
                        </span>
                        <span v-if="material.allow_download" class="flex items-center">
                          <ArrowDownTrayIcon class="h-4 w-4 mr-1" />
                          Téléchargement
                        </span>
                      </div>
                    </div>

                    <!-- Contenu spécifique par type -->
                    <div class="mt-4">
                      <!-- Texte -->
                      <div v-if="material.type === 'text' && material.content" class="bg-gray-50 p-4 rounded-lg">
                        <div class="prose prose-sm max-w-none" v-html="material.content"></div>
                      </div>

                      <!-- Vidéo intégrée -->
                      <div v-else-if="material.type === 'embed_video' && material.embed_code" class="aspect-video">
                        <div v-html="material.embed_code" class="w-full h-full"></div>
                      </div>

                      <!-- Galerie d'images -->
                      <div v-else-if="material.type === 'gallery' && material.gallery_images && material.gallery_images.length > 0">
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                          <div
                            v-for="(image, index) in material.gallery_images"
                            :key="index"
                            class="aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:opacity-75 transition-opacity"
                            @click="openImageModal(material, index)"
                          >
                            <img
                              :src="`/storage/${image.path}`"
                              :alt="image.caption || `Image ${index + 1}`"
                              class="w-full h-full object-cover"
                              loading="lazy"
                            />
                          </div>
                        </div>
                      </div>

                      <!-- Autres types de fichiers -->
                      <div v-else-if="material.file_path" class="flex items-center space-x-4">
                        <button
                          v-if="material.allow_online_viewing && ['pdf', 'image'].includes(material.type)"
                          @click="viewMaterial(material)"
                          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        >
                          <EyeIcon class="h-4 w-4 mr-2" />
                          Visualiser
                        </button>
                        
                        <button
                          v-if="material.allow_download"
                          @click="downloadMaterial(material)"
                          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                        >
                          <ArrowDownTrayIcon class="h-4 w-4 mr-2" />
                          Télécharger
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="text-center py-12 text-gray-500">
              <DocumentIcon class="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p>Aucun matériel disponible pour ce conseil.</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal pour les images -->
    <div v-if="imageModal.show" class="fixed inset-0 z-50 overflow-y-auto" @click="closeImageModal">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full" @click.stop>
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-medium text-gray-900">
                {{ imageModal.caption }}
              </h3>
              <button @click="closeImageModal" class="text-gray-400 hover:text-gray-600">
                <XMarkIcon class="h-6 w-6" />
              </button>
            </div>
            <div class="text-center">
              <img
                :src="imageModal.src"
                :alt="imageModal.caption"
                class="max-w-full max-h-96 mx-auto rounded-lg"
              />
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="closeImageModal"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-gray-600 text-base font-medium text-white hover:bg-gray-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm"
            >
              Fermer
            </button>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import { ref } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { 
  HeartIcon, 
  InformationCircleIcon, 
  EyeIcon, 
  ArrowDownTrayIcon, 
  DocumentIcon,
  DocumentTextIcon,
  VideoCameraIcon,
  PhotoIcon,
  SpeakerWaveIcon,
  ArchiveBoxIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  firstAidTip: Object,
});

// État pour le modal d'image
const imageModal = ref({
  show: false,
  src: '',
  caption: ''
});

// Méthodes
const getTypeLabel = (type) => {
  const labels = {
    text: 'Texte',
    pdf: 'PDF',
    video: 'Vidéo',
    audio: 'Audio',
    image: 'Image',
    archive: 'Archive',
    gallery: 'Galerie',
    embed_video: 'Vidéo externe'
  };
  return labels[type] || type;
};

const getTypeColor = (type) => {
  const colors = {
    text: 'bg-gray-100 text-gray-800',
    pdf: 'bg-red-100 text-red-800',
    video: 'bg-blue-100 text-blue-800',
    audio: 'bg-green-100 text-green-800',
    image: 'bg-yellow-100 text-yellow-800',
    archive: 'bg-purple-100 text-purple-800',
    gallery: 'bg-pink-100 text-pink-800',
    embed_video: 'bg-indigo-100 text-indigo-800'
  };
  return colors[type] || 'bg-gray-100 text-gray-800';
};

const getTypeIcon = (type) => {
  const icons = {
    text: DocumentTextIcon,
    pdf: DocumentIcon,
    video: VideoCameraIcon,
    audio: SpeakerWaveIcon,
    image: PhotoIcon,
    archive: ArchiveBoxIcon,
    gallery: PhotoIcon,
    embed_video: VideoCameraIcon
  };
  return icons[type] || DocumentIcon;
};

const getTypeIconColor = (type) => {
  const colors = {
    text: 'text-gray-500',
    pdf: 'text-red-500',
    video: 'text-blue-500',
    audio: 'text-green-500',
    image: 'text-yellow-500',
    archive: 'text-purple-500',
    gallery: 'text-pink-500',
    embed_video: 'text-indigo-500'
  };
  return colors[type] || 'text-gray-500';
};

const viewMaterial = (material) => {
  window.open(route('student.first-aid-tip-materials.view', material.id), '_blank');
};

const downloadMaterial = (material) => {
  window.location.href = route('student.first-aid-tip-materials.download', material.id);
};

const openImageModal = (material, index) => {
  const image = material.gallery_images[index];
  imageModal.value = {
    show: true,
    src: `/storage/${image.path}`,
    caption: image.caption || `Image ${index + 1}`
  };
};

const closeImageModal = () => {
  imageModal.value.show = false;
};
</script>
