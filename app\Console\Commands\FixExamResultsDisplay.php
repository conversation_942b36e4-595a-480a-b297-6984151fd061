<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Enrollment;
use App\Models\ExamResult;

class FixExamResultsDisplay extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exam:fix-results-display {--dry-run : Show what would be fixed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix exam results display issues by correcting enrollment statuses for students with exam results';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Checking for students with exam results display issues...');
        
        // Find students with exam results but enrollment status != 'approved'
        $problematicEnrollments = Enrollment::whereHas('user', function($query) {
            $query->where('role', 'student');
        })
        ->whereHas('examResults')
        ->where('status', '!=', 'approved')
        ->with(['user', 'examResults'])
        ->get();

        if ($problematicEnrollments->isEmpty()) {
            $this->info('✅ No issues found. All students with exam results have proper enrollment status.');
            return 0;
        }

        $this->warn("Found {$problematicEnrollments->count()} enrollment(s) with potential display issues:");
        
        $headers = ['Student Name', 'Student ID', 'Email', 'Enrollment Status', 'Exam Results Count', 'Action'];
        $rows = [];

        foreach ($problematicEnrollments as $enrollment) {
            $user = $enrollment->user;
            $examResultsCount = $enrollment->examResults->count();
            
            $rows[] = [
                $user->name,
                $user->id,
                $user->email,
                $enrollment->status,
                $examResultsCount,
                $this->option('dry-run') ? 'Would fix' : 'Will fix'
            ];
        }

        $this->table($headers, $rows);

        if ($this->option('dry-run')) {
            $this->info('🔍 Dry run completed. Use --no-dry-run to apply fixes.');
            return 0;
        }

        if (!$this->confirm('Do you want to fix these enrollment statuses?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        $fixedCount = 0;
        foreach ($problematicEnrollments as $enrollment) {
            $oldStatus = $enrollment->status;
            $enrollment->status = 'approved';
            $enrollment->save();
            
            $this->info("✅ Fixed {$enrollment->user->name}: {$oldStatus} → approved");
            $fixedCount++;
        }

        $this->info("🎉 Successfully fixed {$fixedCount} enrollment status(es)!");
        $this->info('Students should now be able to see their exam results properly.');
        
        return 0;
    }
}
