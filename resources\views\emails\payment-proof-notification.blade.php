<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nouveau Justificatif de Paiement</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f59e0b;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8fafc;
            padding: 30px;
            border: 1px solid #e2e8f0;
        }
        .footer {
            background-color: #64748b;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 14px;
        }
        .info-box {
            background-color: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f3f4f6;
        }
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .label {
            font-weight: bold;
            color: #374151;
        }
        .value {
            color: #6b7280;
        }
        .status-pending {
            background-color: #f59e0b;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .alert {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .alert-icon {
            font-size: 20px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📄 Nouveau Justificatif de Paiement</h1>
        <p>Centre de Formation PCMET</p>
    </div>

    <div class="content">
        <h2>Bonjour,</h2>
        
        <div class="alert">
            <span class="alert-icon">⚠️</span>
            <strong>Action requise :</strong> Un apprenant a téléversé un justificatif de paiement qui nécessite votre validation.
        </div>

        <div class="info-box">
            <h3>Informations de l'Apprenant</h3>
            <div class="info-row">
                <span class="label">Nom :</span>
                <span class="value">{{ $student->name }}</span>
            </div>
            <div class="info-row">
                <span class="label">Email :</span>
                <span class="value">{{ $student->email }}</span>
            </div>
            <div class="info-row">
                <span class="label">Formation :</span>
                <span class="value">{{ $trainingSession->title }}</span>
            </div>
            <div class="info-row">
                <span class="label">Domaine :</span>
                <span class="value">{{ $trainingDomain->name }}</span>
            </div>
            <div class="info-row">
                <span class="label">Date d'inscription :</span>
                <span class="value">{{ $enrollment->enrollment_date->format('d/m/Y') }}</span>
            </div>
        </div>

        <div class="info-box">
            <h3>Détails du Paiement</h3>
            <div class="info-row">
                <span class="label">Montant attendu :</span>
                <span class="value">{{ number_format($enrollment->payment_amount, 2) }} DT</span>
            </div>
            <div class="info-row">
                <span class="label">Statut actuel :</span>
                <span class="value"><span class="status-pending">EN ATTENTE</span></span>
            </div>
            <div class="info-row">
                <span class="label">Date de téléversement :</span>
                <span class="value">{{ $enrollment->payment_date ? $enrollment->payment_date->format('d/m/Y H:i') : 'Maintenant' }}</span>
            </div>
            @if($enrollment->payment_proof)
            <div class="info-row">
                <span class="label">Justificatif :</span>
                <span class="value">
                    <a href="{{ asset('storage/' . $enrollment->payment_proof) }}" target="_blank" 
                       style="color: #2563eb; text-decoration: none;">
                        📎 Voir le justificatif
                    </a>
                </span>
            </div>
            @endif
        </div>

        <p><strong>Prochaines étapes :</strong></p>
        <ol>
            <li>Vérifiez le justificatif de paiement téléversé</li>
            <li>Validez ou rejetez le paiement</li>
            <li>Mettez à jour le statut de l'inscription si nécessaire</li>
        </ol>
        
        <p>
            <a href="{{ route('admin.enrollments.show', $enrollment->id) }}" 
               style="background-color: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Examiner l'inscription
            </a>
        </p>
    </div>

    <div class="footer">
        <p>Centre de Formation PCMET - Système de Gestion des Formations</p>
        <p>Cet email a été généré automatiquement, merci de ne pas y répondre.</p>
    </div>
</body>
</html>
