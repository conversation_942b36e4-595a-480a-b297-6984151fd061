<template>
  <div class="bg-white p-4 rounded-lg shadow-sm mb-6 border border-gray-200">
    <h3 class="text-lg font-medium mb-4">Recherche et filtrage</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Barre de recherche -->
      <div>
        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
        <input
          type="text"
          name="search"
          id="search"
          class="w-full px-3 py-2 border border-gray-300 rounded-md"
          placeholder="Rechercher..."
          v-model="searchQuery"
          @input="debounceSearch"
        />
      </div>

      <!-- Filtres -->
      <div v-for="(filter, index) in filters" :key="index">
        <label :for="'filter-' + index" class="block text-sm font-medium text-gray-700 mb-1">{{ filter.label }}</label>
        <select
          :id="'filter-' + index"
          v-model="selectedFilters[filter.name]"
          class="w-full px-3 py-2 border border-gray-300 rounded-md"
          @change="applyFilters"
        >
          <option value="">Tous</option>
          <option v-for="option in filter.options" :key="option.value" :value="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>

      <!-- Bouton de réinitialisation -->
      <div class="flex items-end">
        <button
          type="button"
          class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
          @click="resetFilters"
        >
          Réinitialiser les filtres
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { router } from '@inertiajs/vue3';
import { debounce } from 'lodash';

const props = defineProps({
  // Filtres disponibles
  filters: {
    type: Array,
    default: () => [],
    // Exemple de format:
    // [
    //   {
    //     name: 'status',
    //     label: 'Statut',
    //     options: [
    //       { value: 'active', label: 'Actif' },
    //       { value: 'inactive', label: 'Inactif' }
    //     ]
    //   }
    // ]
  },
  // URL de base pour la recherche et le filtrage
  baseUrl: {
    type: String,
    required: true
  },
  // Paramètres initiaux (pour conserver l'état lors de la navigation)
  initialParams: {
    type: Object,
    default: () => ({})
  }
});

// État local
const searchQuery = ref(props.initialParams.search || '');
const selectedFilters = reactive({});

// Initialiser les filtres sélectionnés à partir des paramètres initiaux
onMounted(() => {
  props.filters.forEach(filter => {
    selectedFilters[filter.name] = props.initialParams[filter.name] || '';
  });
});

// Fonction pour appliquer les filtres avec debounce pour la recherche
const debounceSearch = debounce(() => {
  applyFilters();
}, 500);

// Fonction pour appliquer les filtres
const applyFilters = () => {
  const params = {};

  // Ajouter la recherche si elle existe
  if (searchQuery.value) {
    params.search = searchQuery.value;
  }

  // Ajouter les filtres sélectionnés
  for (const [key, value] of Object.entries(selectedFilters)) {
    if (value) {
      params[key] = value;
    }
  }

  // Naviguer vers l'URL avec les paramètres
  router.get(props.baseUrl, params, {
    preserveState: true,
    replace: true,
    preserveScroll: true
  });
};

// Fonction pour réinitialiser les filtres
const resetFilters = () => {
  searchQuery.value = '';

  // Réinitialiser tous les filtres
  props.filters.forEach(filter => {
    selectedFilters[filter.name] = '';
  });

  // Appliquer les filtres réinitialisés
  router.get(props.baseUrl, {}, {
    preserveState: true,
    replace: true,
    preserveScroll: true
  });
};
</script>
