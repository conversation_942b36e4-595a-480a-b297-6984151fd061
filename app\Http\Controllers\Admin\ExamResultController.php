<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\ExamResult;
use App\Models\Enrollment;
use App\Models\Certificate;
use App\Models\TrainingSession;
use App\Models\TrainingDomain;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ExamResultController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Initialiser la requête
        $query = ExamResult::with(['exam', 'enrollment', 'enrollment.user', 'enrollment.trainingSession', 'enrollment.trainingSession.trainingDomain']);

        // Appliquer la recherche si elle existe
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->whereHas('enrollment.user', function($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                  ->orWhere('email', 'like', $searchTerm);
            });
        }

        // Filtrer par examen
        if ($request->has('exam_id') && !empty($request->exam_id)) {
            $query->where('exam_id', $request->exam_id);
        }

        // Filtrer par session de formation
        if ($request->has('training_session_id') && !empty($request->training_session_id)) {
            $query->whereHas('enrollment', function($q) use ($request) {
                $q->where('training_session_id', $request->training_session_id);
            });
        }

        // Filtrer par domaine de formation
        if ($request->has('domain_id') && !empty($request->domain_id)) {
            $query->whereHas('enrollment.trainingSession', function($q) use ($request) {
                $q->where('training_domain_id', $request->domain_id);
            });
        }

        // Filtrer par résultat (réussi ou échoué)
        if ($request->has('passed') && $request->passed !== '') {
            $query->where('passed', $request->passed === 'true');
        }

        // Filtrer par date de début
        if ($request->has('start_date') && !empty($request->start_date)) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }

        // Filtrer par date de fin
        if ($request->has('end_date') && !empty($request->end_date)) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // Récupérer les résultats d'examen avec pagination
        $examResults = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Récupérer tous les examens pour le filtre
        $exams = Exam::orderBy('title')->get();

        // Récupérer toutes les sessions de formation pour le filtre
        $trainingSessions = TrainingSession::orderBy('title')->get();

        // Récupérer tous les domaines de formation pour le filtre
        $domains = TrainingDomain::orderBy('name')->get();

        // Retourner la vue avec les résultats d'examen et les filtres
        return Inertia::render('Admin/ExamResults/Index', [
            'examResults' => $examResults,
            'exams' => $exams,
            'trainingSessions' => $trainingSessions,
            'domains' => $domains,
            'filters' => [
                'search' => $request->search ?? '',
                'exam_id' => $request->exam_id ?? '',
                'training_session_id' => $request->training_session_id ?? '',
                'domain_id' => $request->domain_id ?? '',
                'passed' => $request->passed ?? '',
                'start_date' => $request->start_date ?? '',
                'end_date' => $request->end_date ?? '',
            ]
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Récupérer les examens actifs
        $exams = Exam::where('is_published', true)
            ->with('trainingSession')
            ->orderBy('title')
            ->get();

        // Récupérer les inscriptions actives
        $enrollments = Enrollment::with(['user', 'trainingSession'])
            ->whereIn('status', ['approved', 'completed'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Retourner la vue pour créer un nouveau résultat d'examen
        return Inertia::render('Admin/ExamResults/Create', [
            'exams' => $exams,
            'enrollments' => $enrollments
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'exam_id' => 'required|exists:exams,id',
            'enrollment_id' => 'required|exists:enrollments,id',
            'score' => 'required|numeric|min:0|max:100',
            'passed' => 'required|boolean',
            'answers' => 'nullable|json',
            'feedback' => 'nullable|string',
        ]);

        // Vérifier si le résultat d'examen existe déjà
        $existingResult = ExamResult::where('exam_id', $validated['exam_id'])
            ->where('enrollment_id', $validated['enrollment_id'])
            ->first();

        if ($existingResult) {
            return redirect()->back()->withErrors([
                'enrollment_id' => 'Cet apprenant a déjà passé cet examen.'
            ]);
        }

        // Créer le résultat d'examen
        $examResult = ExamResult::create($validated);

        // Si l'examen est réussi et que c'est le dernier examen de la formation, mettre à jour le statut de l'inscription
        if ($validated['passed']) {
            $enrollment = Enrollment::findOrFail($validated['enrollment_id']);
            $trainingSession = $enrollment->trainingSession;
            $examsCount = $trainingSession->exams()->count();
            $passedExamsCount = ExamResult::where('enrollment_id', $enrollment->id)
                ->where('passed', true)
                ->whereIn('exam_id', $trainingSession->exams()->pluck('id'))
                ->count();

            if ($passedExamsCount == $examsCount) {
                $enrollment->update(['status' => 'completed']);
                // Certificate generation is now handled automatically by ExamResultObserver
            }
        }

        // Rediriger vers la liste des résultats d'examen avec un message de succès
        return redirect()->route('admin.exam-results.index')
            ->with('success', 'Résultat d\'examen créé avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Récupérer le résultat d'examen avec ses relations
        $examResult = ExamResult::with([
            'exam',
            'exam.questions',
            'enrollment',
            'enrollment.user',
            'enrollment.trainingSession',
            'enrollment.trainingSession.trainingDomain'
        ])->findOrFail($id);

        // Vérifier si un certificat a été généré pour cette inscription
        $certificate = Certificate::where('enrollment_id', $examResult->enrollment_id)->first();

        // Retourner la vue avec le résultat d'examen
        return Inertia::render('Admin/ExamResults/Show', [
            'examResult' => $examResult,
            'certificate' => $certificate
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Récupérer le résultat d'examen avec toutes les relations nécessaires
        $examResult = ExamResult::with([
            'exam',
            'enrollment',
            'enrollment.user',
            'enrollment.trainingSession'
        ])->findOrFail($id);

        // Retourner la vue pour éditer le résultat d'examen
        return Inertia::render('Admin/ExamResults/Edit', [
            'examResult' => $examResult
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Récupérer le résultat d'examen
        $examResult = ExamResult::findOrFail($id);

        // Valider les données du formulaire
        $validated = $request->validate([
            'score' => 'required|numeric|min:0|max:100',
            'passed' => 'required|boolean',
            'feedback' => 'nullable|string',
        ]);

        // Mettre à jour le résultat d'examen
        $examResult->update($validated);

        // Si l'examen est réussi et que c'est le dernier examen de la formation, mettre à jour le statut de l'inscription
        if ($validated['passed']) {
            $enrollment = $examResult->enrollment;
            $trainingSession = $enrollment->trainingSession;
            $examsCount = $trainingSession->exams()->count();
            $passedExamsCount = ExamResult::where('enrollment_id', $enrollment->id)
                ->where('passed', true)
                ->whereIn('exam_id', $trainingSession->exams()->pluck('id'))
                ->count();

            if ($passedExamsCount == $examsCount) {
                $enrollment->update(['status' => 'completed']);
                // Certificate generation is now handled automatically by ExamResultObserver
            }
        }

        // Rediriger vers la liste des résultats d'examen avec un message de succès
        return redirect()->route('admin.exam-results.index')
            ->with('success', 'Résultat d\'examen mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Récupérer le résultat d'examen
        $examResult = ExamResult::findOrFail($id);

        // Vérifier si un certificat a été généré pour cette inscription
        $certificateExists = Certificate::where('enrollment_id', $examResult->enrollment_id)->exists();
        if ($certificateExists) {
            return redirect()->route('admin.exam-results.index')
                ->with('error', 'Impossible de supprimer ce résultat d\'examen car un certificat a été généré.');
        }

        // Supprimer le résultat d'examen
        $examResult->delete();

        // Rediriger vers la liste des résultats d'examen avec un message de succès
        return redirect()->route('admin.exam-results.index')
            ->with('success', 'Résultat d\'examen supprimé avec succès.');
    }

    /**
     * Valider un résultat d'examen et générer un certificat si nécessaire.
     */
    public function validateResult(Request $request, string $id)
    {
        // Récupérer le résultat d'examen
        $examResult = ExamResult::findOrFail($id);

        // Valider les données du formulaire
        $validated = $request->validate([
            'passed' => 'required|boolean',
            'feedback' => 'nullable|string',
        ]);

        // Mettre à jour le résultat d'examen
        $examResult->update($validated);

        // Si l'examen est réussi et que c'est le dernier examen de la formation, mettre à jour le statut de l'inscription
        if ($validated['passed']) {
            $enrollment = $examResult->enrollment;
            $trainingSession = $enrollment->trainingSession;
            $examsCount = $trainingSession->exams()->count();
            $passedExamsCount = ExamResult::where('enrollment_id', $enrollment->id)
                ->where('passed', true)
                ->whereIn('exam_id', $trainingSession->exams()->pluck('id'))
                ->count();

            if ($passedExamsCount == $examsCount) {
                $enrollment->update(['status' => 'completed']);
                // Certificate generation is now handled automatically by ExamResultObserver
            }
        }

        // Rediriger vers la page du résultat d'examen avec un message de succès
        return redirect()->route('admin.exam-results.show', $id)
            ->with('success', 'Résultat d\'examen validé avec succès.');
    }
}
