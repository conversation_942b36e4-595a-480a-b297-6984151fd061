<template>
  <Head :title="'Rapport de progression - ' + session.title" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Rapport de progression - {{ session.title }}
        </h2>
        <Link
          :href="route('trainer.sessions.show', session.id)"
          class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
        >
          Retour à la session
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-6">Rapport de progression détaillé</h3>
            
            <div v-if="reportData.length > 0">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Apprenant</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progression des cours</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Résultats d'examen</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Présence</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progression globale</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="(data, index) in reportData" :key="data.student.id">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center bg-gray-200 rounded-full">
                            <span class="text-gray-700 font-medium">{{ data.student.name.charAt(0) }}</span>
                          </div>
                          <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">{{ data.student.name }}</div>
                            <div class="text-sm text-gray-500">{{ data.student.email }}</div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="text-sm text-gray-900">{{ data.course_completion.completed }} / {{ data.course_completion.total }} cours complétés</div>
                        <div class="mt-1 relative">
                          <div class="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                            <div
                              :style="{ width: data.course_completion.rate + '%' }"
                              :class="{
                                'shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center': true,
                                'bg-red-500': data.course_completion.rate < 30,
                                'bg-yellow-500': data.course_completion.rate >= 30 && data.course_completion.rate < 70,
                                'bg-green-500': data.course_completion.rate >= 70
                              }"
                            ></div>
                          </div>
                          <div class="text-xs text-gray-500 mt-1">{{ data.course_completion.rate }}%</div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="text-sm text-gray-900">{{ data.exam_results.passed }} / {{ data.exam_results.taken }} examens réussis</div>
                        <div class="text-xs text-gray-500">Participation: {{ data.exam_results.participation_rate }}%</div>
                        <div class="mt-1 relative">
                          <div class="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                            <div
                              :style="{ width: data.exam_results.success_rate + '%' }"
                              :class="{
                                'shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center': true,
                                'bg-red-500': data.exam_results.success_rate < 30,
                                'bg-yellow-500': data.exam_results.success_rate >= 30 && data.exam_results.success_rate < 70,
                                'bg-green-500': data.exam_results.success_rate >= 70
                              }"
                            ></div>
                          </div>
                          <div class="text-xs text-gray-500 mt-1">{{ data.exam_results.success_rate }}%</div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="text-sm text-gray-900">{{ data.attendance.present_days }} / {{ data.attendance.total_days }} jours présent</div>
                        <div class="mt-1 relative">
                          <div class="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                            <div
                              :style="{ width: data.attendance.rate + '%' }"
                              :class="{
                                'shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center': true,
                                'bg-red-500': data.attendance.rate < 30,
                                'bg-yellow-500': data.attendance.rate >= 30 && data.attendance.rate < 70,
                                'bg-green-500': data.attendance.rate >= 70
                              }"
                            ></div>
                          </div>
                          <div class="text-xs text-gray-500 mt-1">{{ data.attendance.rate }}%</div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="text-sm font-medium" :class="{
                          'text-red-600': data.overall_progress < 30,
                          'text-yellow-600': data.overall_progress >= 30 && data.overall_progress < 70,
                          'text-green-600': data.overall_progress >= 70
                        }">
                          {{ data.overall_progress }}%
                        </div>
                        <div class="mt-1 relative">
                          <div class="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                            <div
                              :style="{ width: data.overall_progress + '%' }"
                              :class="{
                                'shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center': true,
                                'bg-red-500': data.overall_progress < 30,
                                'bg-yellow-500': data.overall_progress >= 30 && data.overall_progress < 70,
                                'bg-green-500': data.overall_progress >= 70
                              }"
                            ></div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              
              <div class="mt-6">
                <button
                  @click="exportToPDF"
                  class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                >
                  Exporter en PDF
                </button>
              </div>
            </div>
            <div v-else class="text-gray-500 italic text-center py-8">
              Aucune donnée de progression disponible pour cette session.
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  session: Object,
  reportData: Array,
});

// Méthodes
const exportToPDF = () => {
  // Cette fonction serait implémentée pour exporter le rapport en PDF
  // Utilisation d'une bibliothèque comme jsPDF ou appel à une API backend
  alert('Fonctionnalité d\'exportation PDF à implémenter');
};
</script>
