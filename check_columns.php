<?php

// Connexion à la base de données
$host = 'localhost';
$db   = 'formation_pcmet';
$user = 'root';
$pass = '';
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;dbname=$db;charset=$charset";
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

try {
    $pdo = new PDO($dsn, $user, $pass, $options);
    echo "Connexion à la base de données réussie.\n";
} catch (\PDOException $e) {
    throw new \PDOException($e->getMessage(), (int)$e->getCode());
}

// Récupérer les informations sur les colonnes de la table exam_questions
$stmt = $pdo->query("SHOW COLUMNS FROM exam_questions");
$columns = $stmt->fetchAll();

echo "Colonnes de la table exam_questions :\n";
foreach ($columns as $column) {
    echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
}

// Récupérer un exemple de question
$stmt = $pdo->query("SELECT * FROM exam_questions WHERE question_type = 'multiple_choice' LIMIT 1");
$question = $stmt->fetch();

echo "\nExemple de question :\n";
foreach ($question as $key => $value) {
    echo "- $key: " . (is_null($value) ? "NULL" : $value) . "\n";
}
