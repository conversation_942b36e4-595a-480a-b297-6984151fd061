<template>
  <Head title="Domaines de formation" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Domaines de formation
        </h2>
        <Link :href="route('admin.training-domains.create')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          Ajouter un domaine
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Messages de notification -->
            <div v-if="$page.props.flash.success" class="mb-4 p-4 bg-green-100 text-green-700 rounded-md">
              {{ $page.props.flash.success }}
            </div>
            <div v-if="$page.props.flash.error" class="mb-4 p-4 bg-red-100 text-red-700 rounded-md">
              {{ $page.props.flash.error }}
            </div>

            <!-- Formulaire de recherche et filtrage simplifié -->
            <div class="mb-6 flex flex-col md:flex-row gap-4 items-end">
              <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
                <input
                  type="text"
                  id="search"
                  class="px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Rechercher..."
                  v-model="searchQuery"
                  @input="debounceSearch"
                />
              </div>

              <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                <select
                  id="status"
                  v-model="statusFilter"
                  class="px-3 py-2 border border-gray-300 rounded-md"
                  @change="applyFilters"
                >
                  <option value="">Tous</option>
                  <option value="active">Actif</option>
                  <option value="inactive">Inactif</option>
                </select>
              </div>

              <button
                type="button"
                class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                @click="resetFilters"
              >
                Réinitialiser
              </button>
            </div>

            <!-- Liste des domaines -->
            <div v-if="domains.length > 0" class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="domain in domains" :key="domain.id">
                    <td class="px-6 py-4 whitespace-nowrap">{{ domain.name }}</td>
                    <td class="px-6 py-4">
                      <div class="truncate max-w-xs">{{ domain.description || 'Aucune description' }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="{
                        'px-2 py-1 text-xs rounded-full': true,
                        'bg-green-100 text-green-800': domain.active,
                        'bg-red-100 text-red-800': !domain.active
                      }">
                        {{ domain.active ? 'Actif' : 'Inactif' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                      <div class="flex space-x-2">
                        <Link :href="route('admin.training-domains.show', domain.id)" class="text-blue-600 hover:text-blue-800">
                          Voir
                        </Link>
                        <Link :href="route('admin.training-domains.edit', domain.id)" class="text-yellow-600 hover:text-yellow-800">
                          Modifier
                        </Link>
                        <button @click="confirmDelete(domain)" class="text-red-600 hover:text-red-800">
                          Supprimer
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-else class="text-gray-500 italic">
              Aucun domaine de formation trouvé.
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmation de suppression -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
        <h3 class="text-lg font-semibold mb-4">Confirmer la suppression</h3>
        <p class="mb-4">Êtes-vous sûr de vouloir supprimer le domaine "{{ domainToDelete?.name }}" ?</p>
        <div class="flex justify-end space-x-2">
          <button @click="showDeleteModal = false" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
            Annuler
          </button>
          <button @click="deleteDomain" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
            Supprimer
          </button>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { debounce } from 'lodash';

// Props
const props = defineProps({
  domains: Array,
  filters: Object,
});

// État
const showDeleteModal = ref(false);
const domainToDelete = ref(null);
const searchQuery = ref(props.filters?.search || '');
const statusFilter = ref(props.filters?.status || '');

// Méthodes
const confirmDelete = (domain) => {
  domainToDelete.value = domain;
  showDeleteModal.value = true;
};

const deleteDomain = () => {
  router.delete(route('admin.training-domains.destroy', domainToDelete.value.id), {
    onSuccess: () => {
      showDeleteModal.value = false;
      domainToDelete.value = null;
    },
  });
};

// Fonction pour appliquer les filtres
const applyFilters = () => {
  const params = {};

  // Ajouter la recherche si elle existe
  if (searchQuery.value) {
    params.search = searchQuery.value;
  }

  // Ajouter le filtre de statut s'il existe
  if (statusFilter.value) {
    params.status = statusFilter.value;
  }

  // Naviguer vers l'URL avec les paramètres
  router.get(route('admin.training-domains.index'), params, {
    preserveState: true,
    replace: true,
    preserveScroll: true
  });
};

// Fonction debounce pour la recherche
const debounceSearch = debounce(() => {
  applyFilters();
}, 500);

// Fonction pour réinitialiser les filtres
const resetFilters = () => {
  searchQuery.value = '';
  statusFilter.value = '';

  // Naviguer vers l'URL sans paramètres
  router.get(route('admin.training-domains.index'), {}, {
    preserveState: true,
    replace: true,
    preserveScroll: true
  });
};
</script>
