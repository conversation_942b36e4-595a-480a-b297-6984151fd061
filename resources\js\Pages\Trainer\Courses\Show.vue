<template>
  <Head :title="course.title" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        {{ course.title }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Boutons d'action -->
        <div class="flex justify-end mb-4 space-x-2">
          <Link :href="route('trainer.courses.edit', course.id)" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            Modifier
          </Link>
          <Link :href="route('trainer.courses.index')" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
            Retour à la liste
          </Link>
        </div>

        <!-- Informations du cours -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4">Informations du cours</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <p class="text-sm text-gray-600">Session de formation</p>
                <p class="font-medium">{{ course.training_session.title }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-600">Domaine</p>
                <p class="font-medium">{{ course.training_session.training_domain.name }}</p>
              </div>
            </div>

            <div class="mb-4">
              <p class="text-sm text-gray-600">Description</p>
              <p class="whitespace-pre-line">{{ course.description || 'Aucune description' }}</p>
            </div>

            <div class="flex items-center">
              <span class="px-2 py-1 text-xs rounded-full" :class="course.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                {{ course.active ? 'Actif' : 'Inactif' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Modules -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-semibold">Modules ({{ course.modules.length }})</h3>
              <Link :href="route('trainer.modules.create', { course_id: course.id })" class="px-3 py-1 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700">
                Ajouter un module
              </Link>
            </div>

            <div v-if="course.modules.length > 0">
              <div v-for="module in course.modules" :key="module.id" class="border-b border-gray-200 py-3 last:border-b-0">
                <div class="flex justify-between items-center">
                  <div>
                    <h4 class="font-medium">{{ module.title }}</h4>
                    <p class="text-sm text-gray-600">{{ module.description || 'Aucune description' }}</p>
                  </div>
                  <div class="flex space-x-2">
                    <Link :href="route('trainer.modules.edit', module.id)" class="text-blue-600 hover:text-blue-900">
                      Modifier
                    </Link>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-4 text-gray-500">
              Aucun module n'a été créé pour ce cours.
            </div>
          </div>
        </div>

        <!-- Matériels de cours -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-semibold">Matériels de cours ({{ course.materials.length }})</h3>
              <Link :href="route('trainer.course-materials.create', { course_id: course.id })" class="px-3 py-1 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700">
                Ajouter un matériel
              </Link>
            </div>

            <div v-if="course.materials.length > 0">
              <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                  <thead>
                    <tr>
                      <th class="py-2 px-4 border-b text-left">Titre</th>
                      <th class="py-2 px-4 border-b text-left">Type</th>
                      <th class="py-2 px-4 border-b text-left">Module</th>
                      <th class="py-2 px-4 border-b text-left">Statut</th>
                      <th class="py-2 px-4 border-b text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="material in course.materials" :key="material.id" class="hover:bg-gray-50">
                      <td class="py-2 px-4 border-b">{{ material.title }}</td>
                      <td class="py-2 px-4 border-b">{{ formatMaterialType(material.type) }}</td>
                      <td class="py-2 px-4 border-b">{{ material.module ? material.module.title : 'Non assigné' }}</td>
                      <td class="py-2 px-4 border-b">
                        <span :class="material.active ? 'text-green-600' : 'text-red-600'">
                          {{ material.active ? 'Actif' : 'Inactif' }}
                        </span>
                      </td>
                      <td class="py-2 px-4 border-b text-center">
                        <div class="flex justify-center space-x-2">
                          <Link :href="route('trainer.course-materials.edit', material.id)" class="text-blue-600 hover:text-blue-900">
                            Modifier
                          </Link>
                          <Link v-if="material.allow_download" :href="route('trainer.course-materials.download', material.id)" class="text-green-600 hover:text-green-900">
                            Télécharger
                          </Link>
                          <Link :href="route('trainer.course-materials.show', material.id)" class="text-purple-600 hover:text-purple-900">
                            Voir
                          </Link>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div v-else class="text-center py-4 text-gray-500">
              Aucun matériel n'a été ajouté à ce cours.
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  course: Object,
});

// Formater le type de matériel
const formatMaterialType = (type) => {
  const types = {
    'pdf': 'PDF',
    'video': 'Vidéo',
    'text': 'Texte',
    'archive': 'Archive',
    'image': 'Image',
    'audio': 'Audio',
    'gallery': 'Galerie',
    'embed_video': 'Vidéo externe'
  };

  return types[type] || type;
};
</script>
