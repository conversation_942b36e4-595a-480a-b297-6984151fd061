<script setup>
import AuthLayout from '@/Layouts/AuthLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import { Head, Link, useForm } from '@inertiajs/vue3';

const discoveryOptions = [
    { value: 'facebook', label: 'Facebook' },
    { value: 'instagram', label: 'Instagram' },
    { value: 'tiktok', label: 'TikTok' },
    { value: 'word_of_mouth', label: 'Bouche à oreille' },
    { value: 'other', label: 'Autre' },
];

const form = useForm({
    name: '',
    email: '',
    phone: '',
    id_card_number: '',
    birth_date: '',
    profession: '',
    company: '',
    address: '',
    password: '',
    password_confirmation: '',
    discovery_source: '',
    discovery_source_other: '',
    notes: '',
});

const submit = () => {
    form.post(route('register'), {
        onFinish: () => form.reset('password', 'password_confirmation'),
    });
};
</script>

<template>
    <AuthLayout>
        <Head title="Inscription" />
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <h1 class="text-2xl font-bold mb-6 text-center text-gray-900">Créer un compte</h1>
            <form @submit.prevent="submit">
            <div class="mb-6">
                <h3 class="text-md font-medium text-gray-700 mb-3">Informations personnelles</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                        <InputLabel for="name" value="Nom complet *" />
                        <TextInput id="name" type="text" class="mt-1 block w-full" v-model="form.name" required autofocus autocomplete="name" />
                <InputError class="mt-2" :message="form.errors.name" />
            </div>
                    <div>
                        <InputLabel for="email" value="Email *" />
                        <TextInput id="email" type="email" class="mt-1 block w-full" v-model="form.email" required autocomplete="username" />
                <InputError class="mt-2" :message="form.errors.email" />
                    </div>
                    <div>
                        <InputLabel for="phone" value="Téléphone *" />
                        <TextInput id="phone" type="tel" class="mt-1 block w-full" v-model="form.phone" required />
                        <InputError class="mt-2" :message="form.errors.phone" />
                    </div>
                    <div>
                        <InputLabel for="id_card_number" value="Numéro de carte d'identité/passeport *" />
                        <TextInput id="id_card_number" type="text" class="mt-1 block w-full" v-model="form.id_card_number" required />
                        <InputError class="mt-2" :message="form.errors.id_card_number" />
                    </div>
                    <div>
                        <InputLabel for="birth_date" value="Date de naissance" />
                        <TextInput id="birth_date" type="date" class="mt-1 block w-full" v-model="form.birth_date" />
                        <InputError class="mt-2" :message="form.errors.birth_date" />
                    </div>
                    <div>
                        <InputLabel for="profession" value="Profession" />
                        <TextInput id="profession" type="text" class="mt-1 block w-full" v-model="form.profession" />
                        <InputError class="mt-2" :message="form.errors.profession" />
                    </div>
                    <div>
                        <InputLabel for="company" value="Entreprise" />
                        <TextInput id="company" type="text" class="mt-1 block w-full" v-model="form.company" />
                        <InputError class="mt-2" :message="form.errors.company" />
                    </div>
                    <div class="md:col-span-2">
                        <InputLabel for="address" value="Adresse postale" />
                        <textarea id="address" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" v-model="form.address" rows="2"></textarea>
                        <InputError class="mt-2" :message="form.errors.address" />
                    </div>
                </div>
            </div>
            <div class="mb-6">
                <h3 class="text-md font-medium text-gray-700 mb-3">Mot de passe</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <InputLabel for="password" value="Mot de passe *" />
                        <TextInput id="password" type="password" class="mt-1 block w-full" v-model="form.password" required autocomplete="new-password" />
                <InputError class="mt-2" :message="form.errors.password" />
                    </div>
                    <div>
                        <InputLabel for="password_confirmation" value="Confirmer le mot de passe *" />
                        <TextInput id="password_confirmation" type="password" class="mt-1 block w-full" v-model="form.password_confirmation" required autocomplete="new-password" />
                        <InputError class="mt-2" :message="form.errors.password_confirmation" />
                    </div>
                </div>
            </div>
            <div class="mb-6">
                <h3 class="text-md font-medium text-gray-700 mb-3">Informations supplémentaires</h3>
                <div>
                    <InputLabel for="discovery_source" value="Comment avez-vous découvert notre formation ?" />
                    <select id="discovery_source" v-model="form.discovery_source" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">Sélectionnez une option</option>
                        <option v-for="option in discoveryOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                    </select>
                    <InputError class="mt-2" :message="form.errors.discovery_source" />
                </div>
                <div v-if="form.discovery_source === 'other'" class="mt-3">
                    <InputLabel for="discovery_source_other" value="Précisez" />
                    <TextInput id="discovery_source_other" type="text" class="mt-1 block w-full" v-model="form.discovery_source_other" />
                    <InputError class="mt-2" :message="form.errors.discovery_source_other" />
                </div>
                <div class="mt-3">
                    <InputLabel for="notes" value="Notes ou commentaires (facultatif)" />
                    <textarea id="notes" v-model="form.notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                </div>
            </div>
                <div class="flex items-center justify-end mt-6">
                    <Link :href="route('login')" class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Déjà inscrit ?
                    </Link>
                    <PrimaryButton class="ms-4" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                        Créer un compte
                    </PrimaryButton>
                </div>
            </form>
        </div>
    </AuthLayout>
</template>
