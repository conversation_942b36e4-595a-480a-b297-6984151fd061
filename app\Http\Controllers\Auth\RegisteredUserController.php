<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): Response
    {
        return Inertia::render('Auth/Register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'phone' => 'required|string|max:30',
            'id_card_number' => 'required|string|max:50',
            'birth_date' => 'nullable|date',
            'profession' => 'nullable|string|max:100',
            'company' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:255',
            'discovery_source' => 'nullable|string|max:50',
            'discovery_source_other' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:500',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'id_card_number' => $request->id_card_number,
            'birth_date' => $request->birth_date,
            'profession' => $request->profession,
            'company' => $request->company,
            'address' => $request->address,
            'discovery_source' => $request->discovery_source,
            'discovery_source_other' => $request->discovery_source_other,
            'notes' => $request->notes,
            'password' => Hash::make($request->password),
        ]);

        event(new Registered($user));

        Auth::login($user);

        return redirect(RouteServiceProvider::HOME);
    }
}
