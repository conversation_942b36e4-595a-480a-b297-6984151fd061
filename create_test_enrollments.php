<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\TrainingSession;
use App\Models\User;
use App\Models\Enrollment;

// Récupérer l'étudiant Sophie <PERSON>
$student = User::where('email', '<EMAIL>')->first();

if (!$student) {
    echo "Étudiant non trouvé\n";
    exit(1);
}

echo "Création d'inscriptions de test pour: {$student->name}\n\n";

// 1. Inscription en attente pour Secourisme Niveau 2
$secourismeNiveau2 = TrainingSession::where('department', 'Secourisme')
    ->where('level', 'Niveau 2')
    ->where('title', 'LIKE', '%Formation Test%')
    ->first();

if ($secourismeNiveau2) {
    Enrollment::updateOrCreate(
        [
            'user_id' => $student->id,
            'training_session_id' => $secourismeNiveau2->id,
        ],
        [
            'status' => 'pending',
            'enrollment_date' => now(),
            'payment_confirmed' => false,
            'payment_amount' => $secourismeNiveau2->price,
        ]
    );
    echo "✓ Inscription en attente créée pour: {$secourismeNiveau2->title}\n";
}

// 2. Inscription approuvée pour Langue Niveau 1
$langueNiveau1 = TrainingSession::where('department', 'Langue')
    ->where('level', 'Niveau 1')
    ->where('title', 'LIKE', '%Formation Test%')
    ->first();

if ($langueNiveau1) {
    Enrollment::updateOrCreate(
        [
            'user_id' => $student->id,
            'training_session_id' => $langueNiveau1->id,
        ],
        [
            'status' => 'approved',
            'enrollment_date' => now()->subDays(5),
            'payment_confirmed' => true,
            'payment_amount' => $langueNiveau1->price,
        ]
    );
    echo "✓ Inscription approuvée créée pour: {$langueNiveau1->title}\n";
}

// 3. Formation terminée pour Formation à la carte Niveau 1
$formationCarteNiveau1 = TrainingSession::where('department', 'Formation à la carte')
    ->where('level', 'Niveau 1')
    ->where('title', 'LIKE', '%Formation Test%')
    ->first();

if ($formationCarteNiveau1) {
    Enrollment::updateOrCreate(
        [
            'user_id' => $student->id,
            'training_session_id' => $formationCarteNiveau1->id,
        ],
        [
            'status' => 'completed',
            'enrollment_date' => now()->subDays(30),
            'payment_confirmed' => true,
            'payment_amount' => $formationCarteNiveau1->price,
        ]
    );
    echo "✓ Formation terminée créée pour: {$formationCarteNiveau1->title}\n";
}

echo "\nInscriptions de test créées avec succès!\n";
echo "Vous pouvez maintenant voir les différents états sur le dashboard étudiant.\n";
