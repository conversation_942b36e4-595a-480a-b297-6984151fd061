<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use App\Models\User;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        //
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // Enregistrer les politiques d'autorisation
        $this->registerPolicies();

        // Définir les gates pour les rôles
        Gate::define('admin', function (User $user) {
            return $user->role === 'admin';
        });

        Gate::define('trainer', function (User $user) {
            return $user->role === 'trainer';
        });

        Gate::define('student', function (User $user) {
            return $user->role === 'student';
        });

        // Définir les gates pour les actions spécifiques
        Gate::define('manage-domains', function (User $user) {
            return $user->role === 'admin';
        });

        Gate::define('manage-sessions', function (User $user) {
            return $user->role === 'admin';
        });

        Gate::define('manage-enrollments', function (User $user) {
            return $user->role === 'admin';
        });

        Gate::define('manage-courses', function (User $user) {
            return $user->role === 'trainer';
        });

        Gate::define('manage-exams', function (User $user) {
            return $user->role === 'trainer';
        });

        Gate::define('view-course', function (User $user, $course) {
            // Vérifier si l'utilisateur est inscrit à la session de formation associée au cours
            return $user->enrolledSessions()->where('training_session_id', $course->training_session_id)
                ->where('status', 'approved')
                ->exists();
        });

        Gate::define('take-exam', function (User $user, $exam) {
            // Vérifier si l'utilisateur est inscrit à la session de formation associée à l'examen
            return $user->enrolledSessions()->where('training_session_id', $exam->training_session_id)
                ->where('status', 'approved')
                ->exists();
        });
    }
}
