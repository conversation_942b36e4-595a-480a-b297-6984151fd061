<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Certificate;

class UpdateCertificateNumbers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'certificates:update-numbers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update existing certificates to remove -DRAFT suffix and generate QR codes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating certificate numbers and generating QR codes...');

        // Get all certificates with -DRAFT suffix
        $draftCertificates = Certificate::where('certificate_number', 'LIKE', '%-DRAFT')->get();

        $this->info("Found {$draftCertificates->count()} certificates with -DRAFT suffix.");

        $updated = 0;

        foreach ($draftCertificates as $certificate) {
            try {
                // Remove -DRAFT suffix from certificate number
                $newCertificateNumber = str_replace('-DRAFT', '', $certificate->certificate_number);

                // Update certificate number
                $certificate->update(['certificate_number' => $newCertificateNumber]);

                // Generate QR code for the updated certificate
                $certificate->generateQrCode();

                $this->line("✓ Updated certificate {$certificate->id}: {$certificate->certificate_number} → {$newCertificateNumber}");
                $updated++;

            } catch (\Exception $e) {
                $this->error("✗ Failed to update certificate {$certificate->id}: " . $e->getMessage());
            }
        }

        // Generate QR codes for certificates that don't have them
        $certificatesWithoutQr = Certificate::whereNull('qr_code')->orWhere('qr_code', '')->get();
        
        $this->info("Found {$certificatesWithoutQr->count()} certificates without QR codes.");

        $qrGenerated = 0;

        foreach ($certificatesWithoutQr as $certificate) {
            try {
                $certificate->generateQrCode();
                $this->line("✓ Generated QR code for certificate {$certificate->certificate_number}");
                $qrGenerated++;
            } catch (\Exception $e) {
                $this->error("✗ Failed to generate QR code for certificate {$certificate->certificate_number}: " . $e->getMessage());
            }
        }

        $this->info("Successfully updated {$updated} certificate numbers and generated {$qrGenerated} QR codes.");
        
        return Command::SUCCESS;
    }
}
