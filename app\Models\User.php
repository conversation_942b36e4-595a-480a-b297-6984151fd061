<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasA<PERSON>Tokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'phone',
        'address',
        'profile_photo',
        'bio',
        'active',
        'birth_date',
        'id_card_number',
        'profession',
        'company',
        'discovery_source',
        'discovery_source_other',
        'student_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'active' => 'boolean',
        'birth_date' => 'date',
    ];

    /**
     * Serialize birth_date for JSON
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format('Y-m-d');
    }

    /**
     * Vérifie si l'utilisateur est un administrateur
     */
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    /**
     * Vérifie si l'utilisateur est un formateur
     */
    public function isTrainer()
    {
        return $this->role === 'trainer';
    }

    /**
     * Vérifie si l'utilisateur est un apprenant
     */
    public function isStudent()
    {
        return $this->role === 'student';
    }

    /**
     * Relation avec les sessions de formation (pour les formateurs)
     */
    public function trainingSessions()
    {
        return $this->hasMany(TrainingSession::class, 'trainer_id');
    }

    /**
     * Relation avec les inscriptions
     */
    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    /**
     * Relation avec les sessions de formation via les inscriptions (pour les apprenants)
     */
    public function enrolledSessions()
    {
        return $this->belongsToMany(TrainingSession::class, 'enrollments')
            ->withPivot(['status', 'enrollment_date', 'payment_confirmed'])
            ->withTimestamps();
    }

    /**
     * Relation avec les résultats d'examen
     */
    public function examResults()
    {
        return $this->hasMany(ExamResult::class);
    }

    /**
     * Relation avec les certificats
     */
    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }

    /**
     * Relation avec les notifications
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get unread notifications count
     */
    public function getUnreadNotificationsCountAttribute(): int
    {
        return $this->notifications()->unread()->count();
    }

    /**
     * Get recent unread notifications
     */
    public function getRecentUnreadNotificationsAttribute()
    {
        return $this->notifications()
            ->unread()
            ->recent()
            ->limit(10)
            ->get();
    }

    /**
     * Relation avec la progression des cours
     */
    public function courseProgress()
    {
        return $this->hasMany(CourseProgress::class);
    }
}
