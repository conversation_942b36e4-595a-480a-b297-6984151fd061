<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasA<PERSON>Tokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'phone',
        'address',
        'profile_photo',
        'bio',
        'active',
        'birth_date',
        'id_card_number',
        'profession',
        'company',
        'discovery_source',
        'discovery_source_other',
        'student_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'active' => 'boolean',
        'birth_date' => 'date',
    ];

    /**
     * Serialize birth_date for JSON
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format('Y-m-d');
    }

    /**
     * Vérifie si l'utilisateur est un administrateur
     */
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    /**
     * Vérifie si l'utilisateur est un formateur
     */
    public function isTrainer()
    {
        return $this->role === 'trainer';
    }

    /**
     * Vérifie si l'utilisateur est un apprenant
     */
    public function isStudent()
    {
        return $this->role === 'student';
    }

    /**
     * Relation avec les sessions de formation (pour les formateurs)
     */
    public function trainingSessions()
    {
        return $this->hasMany(TrainingSession::class, 'trainer_id');
    }

    /**
     * Relation avec les inscriptions
     */
    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    /**
     * Relation avec les sessions de formation via les inscriptions (pour les apprenants)
     */
    public function enrolledSessions()
    {
        return $this->belongsToMany(TrainingSession::class, 'enrollments')
            ->withPivot(['status', 'enrollment_date', 'payment_confirmed'])
            ->withTimestamps();
    }

    /**
     * Relation avec les résultats d'examen
     */
    public function examResults()
    {
        return $this->hasMany(ExamResult::class);
    }

    /**
     * Relation avec les certificats
     */
    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }

    /**
     * Vérifie si l'apprenant a terminé un niveau spécifique dans un département donné
     */
    public function hasCompletedLevel($department, $level)
    {
        return $this->enrollments()
            ->whereHas('trainingSession', function($query) use ($department, $level) {
                $query->where('department', $department)
                      ->where('level', $level)
                      ->where('active', true);
            })
            ->where('status', 'completed')
            ->exists();
    }

    /**
     * Récupère tous les niveaux complétés par département
     */
    public function getCompletedLevelsByDepartment()
    {
        $completedEnrollments = $this->enrollments()
            ->with('trainingSession')
            ->whereHas('trainingSession', function($query) {
                $query->where('active', true);
            })
            ->where('status', 'completed')
            ->get();

        $completedLevels = [];
        foreach ($completedEnrollments as $enrollment) {
            $session = $enrollment->trainingSession;
            $department = $session->department;
            $level = $session->level;

            if (!isset($completedLevels[$department])) {
                $completedLevels[$department] = [];
            }

            if (!in_array($level, $completedLevels[$department])) {
                $completedLevels[$department][] = $level;
            }
        }

        return $completedLevels;
    }

    /**
     * Vérifie si l'apprenant peut s'inscrire à un niveau donné (prérequis respectés)
     */
    public function canEnrollInLevel($department, $level)
    {
        // Extraire le numéro du niveau (ex: "Niveau 3" -> 3)
        $levelNumber = (int) str_replace('Niveau ', '', $level);

        // Le niveau 1 est toujours accessible
        if ($levelNumber <= 1) {
            return true;
        }

        // Vérifier que tous les niveaux précédents sont complétés
        for ($i = 1; $i < $levelNumber; $i++) {
            $previousLevel = "Niveau $i";
            if (!$this->hasCompletedLevel($department, $previousLevel)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Récupère le prochain niveau accessible dans un département
     */
    public function getNextAvailableLevel($department)
    {
        $completedLevels = $this->getCompletedLevelsByDepartment();
        $departmentLevels = $completedLevels[$department] ?? [];

        // Convertir les niveaux en nombres et trier
        $levelNumbers = [];
        foreach ($departmentLevels as $level) {
            $levelNumbers[] = (int) str_replace('Niveau ', '', $level);
        }
        sort($levelNumbers);

        // Trouver le prochain niveau
        $nextLevel = 1;
        foreach ($levelNumbers as $completedLevel) {
            if ($completedLevel == $nextLevel) {
                $nextLevel++;
            } else {
                break;
            }
        }

        // Vérifier que le niveau suivant existe (max 7)
        if ($nextLevel <= 7) {
            return "Niveau $nextLevel";
        }

        return null; // Tous les niveaux sont complétés
    }

    /**
     * Relation avec les notifications
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get unread notifications count
     */
    public function getUnreadNotificationsCountAttribute(): int
    {
        return $this->notifications()->unread()->count();
    }

    /**
     * Get recent unread notifications
     */
    public function getRecentUnreadNotificationsAttribute()
    {
        return $this->notifications()
            ->unread()
            ->recent()
            ->limit(10)
            ->get();
    }

    /**
     * Relation avec la progression des cours
     */
    public function courseProgress()
    {
        return $this->hasMany(CourseProgress::class);
    }
}
