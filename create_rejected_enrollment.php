<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\TrainingSession;
use App\Models\User;
use App\Models\Enrollment;

// Récupérer l'étudiant Sophie Bernard
$student = User::where('email', '<EMAIL>')->first();

if (!$student) {
    echo "Étudiant non trouvé\n";
    exit(1);
}

echo "Création d'une inscription refusée pour: {$student->name}\n\n";

// Inscription refusée pour Secourisme Niveau 3
$secourismeNiveau3 = TrainingSession::where('department', 'Secourisme')
    ->where('level', 'Niveau 3')
    ->where('title', 'LIKE', '%Formation Test%')
    ->first();

if ($secourismeNiveau3) {
    Enrollment::updateOrCreate(
        [
            'user_id' => $student->id,
            'training_session_id' => $secourismeNiveau3->id,
        ],
        [
            'status' => 'rejected',
            'enrollment_date' => now()->subDays(10),
            'payment_confirmed' => false,
            'payment_amount' => $secourismeNiveau3->price,
        ]
    );
    echo "✓ Inscription refusée créée pour: {$secourismeNiveau3->title}\n";
} else {
    echo "❌ Session Secourisme Niveau 3 non trouvée\n";
}

echo "\nInscription refusée créée avec succès!\n";
echo "Vous pouvez maintenant voir l'état 'refusé' sur le dashboard étudiant.\n";
