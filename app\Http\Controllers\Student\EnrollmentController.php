<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Models\Enrollment;
use App\Models\TrainingSession;
use App\Models\Announcement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class EnrollmentController extends Controller
{
    /**
     * Affiche la liste des inscriptions de l'apprenant
     */
    public function index()
    {
        $student = Auth::user();
        
        // Récupérer toutes les inscriptions de l'apprenant avec les relations nécessaires
        $enrollments = Enrollment::where('user_id', $student->id)
            ->with(['trainingSession.trainingDomain', 'trainingSession.trainer'])
            ->orderBy('enrollment_date', 'desc')
            ->get();
            
        return Inertia::render('Student/Enrollments/Index', [
            'enrollments' => $enrollments
        ]);
    }
    
    /**
     * Affiche les détails d'une inscription
     */
    public function show($id)
    {
        $student = Auth::user();
        
        // Récupérer l'inscription avec les relations nécessaires
        $enrollment = Enrollment::where('id', $id)
            ->where('user_id', $student->id)
            ->with([
                'trainingSession.trainingDomain', 
                'trainingSession.trainer',
                'examResults.exam'
            ])
            ->firstOrFail();
            
        return Inertia::render('Student/Enrollments/Show', [
            'enrollment' => $enrollment
        ]);
    }
    
    /**
     * Téléverse un justificatif de paiement
     */
    public function uploadPaymentProof(Request $request, $id)
    {
        try {
            $student = Auth::user();

            // Valider les données avec des messages d'erreur personnalisés
            $request->validate([
                'payment_proof' => 'required|file|mimes:jpeg,png,jpg,pdf|max:2048',
            ], [
                'payment_proof.required' => 'Veuillez sélectionner un fichier.',
                'payment_proof.file' => 'Le fichier sélectionné n\'est pas valide.',
                'payment_proof.mimes' => 'Le fichier doit être au format JPEG, PNG, JPG ou PDF.',
                'payment_proof.max' => 'Le fichier ne doit pas dépasser 2 Mo.',
            ]);

            // Récupérer l'inscription
            $enrollment = Enrollment::where('id', $id)
                ->where('user_id', $student->id)
                ->firstOrFail();

            // Vérifier que l'inscription permet le téléversement
            if (!in_array($enrollment->payment_status, ['unpaid', 'pending'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Le téléversement n\'est pas autorisé pour cette inscription.'
                ], 400);
            }

            // Supprimer l'ancien justificatif s'il existe
            if ($enrollment->payment_proof) {
                Storage::disk('public')->delete($enrollment->payment_proof);
            }

            // Stocker le nouveau justificatif
            $path = $request->file('payment_proof')->store('payment_proofs', 'public');

            // Vérifier que le fichier a été stocké
            if (!$path) {
                return response()->json([
                    'success' => false,
                    'message' => 'Erreur lors de la sauvegarde du fichier.'
                ], 500);
            }

            // Mettre à jour l'inscription
            $enrollment->update([
                'payment_proof' => $path,
                'payment_status' => 'pending',
                'payment_date' => now(),
            ]);

            // Notifier les administrateurs du nouveau justificatif de paiement
            try {
                $emailService = app(\App\Services\PaymentEmailService::class);
                $emailService->notifyAdminsOfPaymentProof($enrollment);
            } catch (\Exception $e) {
                \Log::error('Failed to send payment proof notification: ' . $e->getMessage());
            }

            // Retourner une réponse JSON pour les requêtes AJAX
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Justificatif de paiement téléversé avec succès.'
                ]);
            }

            return redirect()->back()->with('success', 'Justificatif de paiement téléversé avec succès.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            // Retourner les erreurs de validation en JSON
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Erreur de validation.',
                    'errors' => $e->errors()
                ], 422);
            }
            throw $e;
        } catch (\Exception $e) {
            \Log::error('Payment proof upload error: ' . $e->getMessage(), [
                'enrollment_id' => $id,
                'user_id' => $student->id ?? null,
                'file_info' => $request->hasFile('payment_proof') ? [
                    'name' => $request->file('payment_proof')->getClientOriginalName(),
                    'size' => $request->file('payment_proof')->getSize(),
                    'mime' => $request->file('payment_proof')->getMimeType(),
                ] : 'No file'
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Une erreur inattendue s\'est produite lors du téléversement.'
                ], 500);
            }

            return redirect()->back()->with('error', 'Une erreur inattendue s\'est produite lors du téléversement.');
        }
    }
    
    /**
     * Annule une demande d'inscription
     */
    public function cancel($id)
    {
        $student = Auth::user();
        
        // Récupérer l'inscription
        $enrollment = Enrollment::where('id', $id)
            ->where('user_id', $student->id)
            ->where('status', 'pending')
            ->firstOrFail();
            
        // Mettre à jour le statut
        $enrollment->update([
            'status' => 'cancelled',
            'notes' => $enrollment->notes . "\n[" . now() . "] Annulé par l'apprenant."
        ]);
        
        return redirect()->route('student.enrollments.index')->with('success', 'Demande d\'inscription annulée avec succès.');
    }
    
    /**
     * Affiche la page des paiements
     */
    public function payments()
    {
        $student = Auth::user();
        
        // Récupérer toutes les inscriptions de l'apprenant avec les relations nécessaires
        $enrollments = Enrollment::where('user_id', $student->id)
            ->with(['trainingSession.trainingDomain'])
            ->orderBy('enrollment_date', 'desc')
            ->get();
            
        return Inertia::render('Student/Enrollments/Payments', [
            'enrollments' => $enrollments
        ]);
    }
}
