<?php

namespace App\Http\Controllers\Trainer;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\ExamResult;
use App\Models\TrainingSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ExamResultController extends Controller
{
    /**
     * Affiche la liste des résultats d'examens du formateur
     */
    public function index(Request $request)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $trainingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->with('trainingDomain')
            ->get();

        $sessionIds = $trainingSessions->pluck('id')->toArray();

        // Récupérer les examens du formateur
        $examIds = Exam::whereIn('training_session_id', $sessionIds)
            ->pluck('id')
            ->toArray();

        // Initialiser la requête pour les résultats d'examens
        $query = ExamResult::whereIn('exam_id', $examIds)
            ->with(['exam.trainingSession', 'student']);

        // Appliquer les filtres
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->whereHas('student', function($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                  ->orWhere('email', 'like', $searchTerm);
            });
        }

        if ($request->has('exam_id') && !empty($request->exam_id)) {
            $query->where('exam_id', $request->exam_id);
        }

        if ($request->has('training_session_id') && !empty($request->training_session_id)) {
            $query->whereHas('exam', function($q) use ($request) {
                $q->where('training_session_id', $request->training_session_id);
            });
        }

        if ($request->has('status') && !empty($request->status)) {
            // Vérifier si la colonne status existe
            try {
                $query->where('status', $request->status);
            } catch (\Exception $e) {
                // Si la colonne n'existe pas, utiliser d'autres colonnes pour filtrer
                if ($request->status === 'passed') {
                    $query->where('passed', true);
                } elseif ($request->status === 'failed') {
                    $query->where('passed', false);
                } elseif ($request->status === 'completed') {
                    $query->whereNotNull('completed_at');
                } elseif ($request->status === 'graded') {
                    $query->whereNotNull('feedback');
                }
            }
        }

        // Récupérer les résultats avec pagination
        $results = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Récupérer les examens pour le filtre
        $exams = Exam::whereIn('id', $examIds)
            ->orderBy('title')
            ->get();

        // Retourner la vue avec les résultats et les filtres
        return Inertia::render('Trainer/ExamResults/Index', [
            'results' => $results,
            'trainingSessions' => $trainingSessions,
            'exams' => $exams,
            'filters' => [
                'search' => $request->search ?? '',
                'exam_id' => $request->exam_id ?? '',
                'training_session_id' => $request->training_session_id ?? '',
                'status' => $request->status ?? '',
            ],
            'statusOptions' => [
                ['value' => 'in_progress', 'label' => 'En cours'],
                ['value' => 'completed', 'label' => 'Terminé'],
                ['value' => 'graded', 'label' => 'Noté'],
                ['value' => 'failed', 'label' => 'Échoué'],
                ['value' => 'passed', 'label' => 'Réussi'],
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $sessionIds = TrainingSession::where('trainer_id', $trainer->id)
            ->pluck('id')
            ->toArray();

        // Récupérer les examens du formateur
        $examIds = Exam::whereIn('training_session_id', $sessionIds)
            ->pluck('id')
            ->toArray();

        // Récupérer le résultat d'examen avec ses relations
        $examResult = ExamResult::with([
            'exam',
            'exam.questions',
            'exam.trainingSession',
            'student'
        ])
        ->whereIn('exam_id', $examIds)
        ->findOrFail($id);

        // Retourner la vue avec le résultat d'examen
        return Inertia::render('Trainer/ExamResults/Show', [
            'examResult' => $examResult
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $sessionIds = TrainingSession::where('trainer_id', $trainer->id)
            ->pluck('id')
            ->toArray();

        // Récupérer les examens du formateur
        $examIds = Exam::whereIn('training_session_id', $sessionIds)
            ->pluck('id')
            ->toArray();

        // Récupérer le résultat d'examen avec ses relations
        $examResult = ExamResult::with([
            'exam',
            'exam.questions',
            'exam.trainingSession',
            'student'
        ])
        ->whereIn('exam_id', $examIds)
        ->findOrFail($id);

        // Retourner la vue avec le résultat d'examen
        return Inertia::render('Trainer/ExamResults/Edit', [
            'examResult' => $examResult
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $sessionIds = TrainingSession::where('trainer_id', $trainer->id)
            ->pluck('id')
            ->toArray();

        // Récupérer les examens du formateur
        $examIds = Exam::whereIn('training_session_id', $sessionIds)
            ->pluck('id')
            ->toArray();

        // Récupérer le résultat d'examen
        $examResult = ExamResult::whereIn('exam_id', $examIds)
            ->findOrFail($id);

        // Valider les données du formulaire
        $validated = $request->validate([
            'score' => 'required|numeric|min:0|max:100',
            'passed' => 'required|boolean',
            'feedback' => 'nullable|string',
        ]);

        // Déterminer le statut en fonction des données
        $status = $validated['passed'] ? 'passed' : 'failed';

        // Mettre à jour le résultat d'examen
        $examResult->update([
            'score' => $validated['score'],
            'passed' => $validated['passed'],
            'feedback' => $validated['feedback'],
            'status' => $status,
        ]);

        // Rediriger vers la page de détails du résultat d'examen
        return redirect()->route('trainer.exam-results.show', $examResult->id)
            ->with('success', 'Résultat d\'examen mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * Valider un résultat d'examen et mettre à jour son statut.
     */
    public function validateResult(Request $request, string $id)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $sessionIds = TrainingSession::where('trainer_id', $trainer->id)
            ->pluck('id')
            ->toArray();

        // Récupérer les examens du formateur
        $examIds = Exam::whereIn('training_session_id', $sessionIds)
            ->pluck('id')
            ->toArray();

        // Récupérer le résultat d'examen
        $examResult = ExamResult::whereIn('exam_id', $examIds)
            ->findOrFail($id);

        // Valider les données du formulaire
        $validated = $request->validate([
            'passed' => 'required|boolean',
            'feedback' => 'nullable|string',
        ]);

        // Mettre à jour le résultat d'examen
        $examResult->update([
            'passed' => $validated['passed'],
            'feedback' => $validated['feedback'],
            'status' => $validated['passed'] ? 'passed' : 'failed',
        ]);

        // Rediriger vers la liste des résultats d'examen avec un message de succès
        return redirect()->route('trainer.exam-results.index')
            ->with('success', 'Résultat d\'examen validé avec succès.');
    }
}
