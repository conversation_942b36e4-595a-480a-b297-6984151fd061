<?php

return [
    /*
    |--------------------------------------------------------------------------
    | QR Code Writer
    |--------------------------------------------------------------------------
    |
    | QR Code writer backend to use. Available options:
    | - 'imagick' (requires Imagick extension)
    | - 'gd' (requires GD extension)
    | - 'svg'
    |
    */
    'writer' => 'gd',

    /*
    |--------------------------------------------------------------------------
    | QR Code Size
    |--------------------------------------------------------------------------
    |
    | The default size for QR codes.
    |
    */
    'size' => 200,

    /*
    |--------------------------------------------------------------------------
    | QR Code Format
    |--------------------------------------------------------------------------
    |
    | The default format for QR codes. Available options:
    | - 'png'
    | - 'gif'
    | - 'jpg'
    | - 'svg'
    |
    */
    'format' => 'png',

    /*
    |--------------------------------------------------------------------------
    | QR Code Error Correction Level
    |--------------------------------------------------------------------------
    |
    | The default error correction level for QR codes. Available options:
    | - 'L' (Low ~7%)
    | - 'M' (Medium ~15%)
    | - 'Q' (Quartile ~25%)
    | - 'H' (High ~30%)
    |
    */
    'error_correction' => 'M',

    /*
    |--------------------------------------------------------------------------
    | QR Code Encoding
    |--------------------------------------------------------------------------
    |
    | The default encoding for QR codes.
    |
    */
    'encoding' => 'UTF-8',
];
