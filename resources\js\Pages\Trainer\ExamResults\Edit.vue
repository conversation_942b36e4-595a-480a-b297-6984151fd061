<template>
  <Head :title="'Évaluer le résultat d\'examen - ' + (examResult.student ? examResult.student.name : '')" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Évaluer le résultat d'examen
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <div class="mb-6">
              <h3 class="text-lg font-semibold mb-2">Résultat d'examen de {{ examResult.student ? examResult.student.name : 'l\'étudiant' }}</h3>
              <div class="text-sm text-gray-600 mb-4">
                <div>Examen: {{ examResult.exam ? examResult.exam.title : '' }}</div>
                <div>Session: {{ examResult.exam && examResult.exam.trainingSession ? examResult.exam.trainingSession.title : '' }}</div>
                <div>Date de soumission: {{ formatDate(examResult.completed_at) }}</div>
              </div>
            </div>

            <!-- Formulaire de modification de résultat d'examen -->
            <form @submit.prevent="submit">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Score -->
                <div>
                  <InputLabel for="score" value="Score (%)" />
                  <TextInput
                    id="score"
                    type="number"
                    class="mt-1 block w-full"
                    v-model="form.score"
                    min="0"
                    max="100"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.score" />
                </div>

                <!-- Réussite -->
                <div>
                  <InputLabel for="passed" value="Résultat" />
                  <div class="mt-2">
                    <label class="inline-flex items-center">
                      <input
                        type="radio"
                        class="form-radio"
                        name="passed"
                        :value="true"
                        v-model="form.passed"
                      />
                      <span class="ml-2">Réussi</span>
                    </label>
                    <label class="inline-flex items-center ml-6">
                      <input
                        type="radio"
                        class="form-radio"
                        name="passed"
                        :value="false"
                        v-model="form.passed"
                      />
                      <span class="ml-2">Échoué</span>
                    </label>
                  </div>
                  <InputError class="mt-2" :message="form.errors.passed" />
                </div>
              </div>

              <!-- Commentaires -->
              <div class="mt-6">
                <InputLabel for="feedback" value="Commentaires" />
                <textarea
                  id="feedback"
                  v-model="form.feedback"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  rows="4"
                ></textarea>
                <InputError class="mt-2" :message="form.errors.feedback" />
              </div>

              <!-- Réponses de l'étudiant -->
              <div class="mt-6" v-if="examResult.answers">
                <h4 class="font-semibold mb-3">Réponses de l'étudiant</h4>
                <div class="bg-gray-50 p-4 rounded-lg">
                  <div v-if="parsedAnswers.length > 0">
                    <div v-for="(answer, index) in parsedAnswers" :key="index" class="mb-4 p-4 bg-white rounded-lg">
                      <div class="font-semibold mb-2">Question {{ index + 1 }}: {{ getQuestionText(answer.question_id) }}</div>
                      <div v-if="getQuestionType(answer.question_id) === 'multiple_choice'">
                        <div class="mb-1">Réponse: {{ answer.answer }}</div>
                        <div>Réponse correcte: {{ getCorrectAnswer(answer.question_id) }}</div>
                      </div>
                      <div v-else-if="getQuestionType(answer.question_id) === 'text'">
                        <div class="mb-1">Réponse: {{ answer.answer }}</div>
                      </div>
                      <div v-else-if="getQuestionType(answer.question_id) === 'file'">
                        <div class="mb-1">Fichier soumis: {{ answer.answer }}</div>
                      </div>
                    </div>
                  </div>
                  <div v-else class="text-gray-500 italic">
                    Aucune réponse enregistrée pour cet examen.
                  </div>
                </div>
              </div>

              <!-- Boutons -->
              <div class="flex justify-end mt-6">
                <Link :href="route('trainer.exam-results.show', examResult.id)" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150 mr-2">
                  Annuler
                </Link>
                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                  Enregistrer
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { computed } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  examResult: Object,
});

// Formulaire
const form = useForm({
  score: props.examResult.score,
  passed: props.examResult.passed,
  feedback: props.examResult.feedback || '',
});

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return 'Non définie';
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const parsedAnswers = computed(() => {
  if (!props.examResult.answers) return [];
  try {
    return typeof props.examResult.answers === 'string' 
      ? JSON.parse(props.examResult.answers) 
      : props.examResult.answers;
  } catch (e) {
    console.error('Erreur lors du parsing des réponses:', e);
    return [];
  }
});

const getQuestionText = (questionId) => {
  if (!props.examResult.exam || !props.examResult.exam.questions) return 'Question inconnue';
  const question = props.examResult.exam.questions.find(q => q.id === questionId);
  return question ? question.question_text : 'Question inconnue';
};

const getQuestionType = (questionId) => {
  if (!props.examResult.exam || !props.examResult.exam.questions) return 'unknown';
  const question = props.examResult.exam.questions.find(q => q.id === questionId);
  return question ? question.question_type : 'unknown';
};

const getCorrectAnswer = (questionId) => {
  if (!props.examResult.exam || !props.examResult.exam.questions) return 'Réponse inconnue';
  const question = props.examResult.exam.questions.find(q => q.id === questionId);
  return question ? question.correct_answer : 'Réponse inconnue';
};

const submit = () => {
  form.put(route('trainer.exam-results.update', props.examResult.id));
};
</script>
