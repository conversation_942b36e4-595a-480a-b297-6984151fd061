<script setup>
import { ref, computed } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

const props = defineProps({
  courses: Array
});

// État pour les filtres
const searchQuery = ref('');
const selectedSession = ref('Toutes les sessions');
const selectedDomain = ref('Tous les domaines');

// Extraire les sessions et domaines uniques pour les filtres
const sessions = ref(['Toutes les sessions', ...new Set(props.courses.map(course => course.session))]);
const domains = ref(['Tous les domaines', ...new Set(props.courses.map(course => course.domain))]);

// Filtrer les cours
const filteredCourses = computed(() => {
  return props.courses.filter(course => {
    // Filtre par recherche
    const matchesSearch = searchQuery.value === '' ||
      course.title.toLowerCase().includes(searchQuery.value.toLowerCase());

    // Filtre par session
    const matchesSession = selectedSession.value === 'Toutes les sessions' ||
      course.session === selectedSession.value;

    // Filtre par domaine
    const matchesDomain = selectedDomain.value === 'Tous les domaines' ||
      course.domain === selectedDomain.value;

    return matchesSearch && matchesSession && matchesDomain;
  });
});

// Réinitialiser les filtres
const resetFilters = () => {
  searchQuery.value = '';
  selectedSession.value = 'Toutes les sessions';
  selectedDomain.value = 'Tous les domaines';
};
</script>

<template>
  <Head title="Mes cours" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Mes cours
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Filtres -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h3 class="font-semibold mb-4">Filtres</h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="Rechercher..."
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Session de formation</label>
                <select
                  v-model="selectedSession"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option v-for="session in sessions" :key="session">{{ session }}</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Domaine de formation</label>
                <select
                  v-model="selectedDomain"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option v-for="domain in domains" :key="domain">{{ domain }}</option>
                </select>
              </div>
            </div>

            <div class="mt-4 flex justify-end space-x-2">
              <button
                @click="resetFilters"
                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Réinitialiser
              </button>
              <button
                class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
              >
                Appliquer les filtres
              </button>
            </div>
          </div>
        </div>

        <!-- Liste des cours -->
        <div v-if="filteredCourses.length === 0" class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6 text-gray-500">
          Aucun cours ne correspond à vos critères de recherche.
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="course in filteredCourses" :key="course.id" class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
              <h3 class="font-semibold text-lg mb-2">{{ course.title }}</h3>
              <div class="text-sm text-gray-600 mb-4">
                {{ course.session }} - {{ course.domain }}
              </div>

              <div class="mb-4">
                <div class="text-sm text-gray-600 mb-1">
                  Progression: {{ course.progress }}% ({{ course.completed }}/{{ course.total }})
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    class="bg-blue-600 h-2.5 rounded-full"
                    :style="{ width: `${course.progress}%` }"
                  ></div>
                </div>
              </div>

              <div class="flex justify-end">
                <Link
                  :href="route('student.courses.show', course.id)"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Accéder au cours
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>