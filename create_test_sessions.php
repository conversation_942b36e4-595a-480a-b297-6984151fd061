<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\TrainingSession;
use App\Models\TrainingDomain;
use App\Models\User;

// Récupérer un domaine de formation et un formateur
$domain = TrainingDomain::first();
$trainer = User::where('role', 'trainer')->first();

if (!$domain || !$trainer) {
    echo "Erreur: Domaine de formation ou formateur non trouvé\n";
    exit(1);
}

// Créer des sessions de test pour chaque département et niveau
$departments = ['Secourisme', 'Langue', 'Formation à la carte'];
$levels = ['Niveau 1', 'Niveau 2', 'Niveau 3'];

foreach ($departments as $department) {
    foreach ($levels as $level) {
        $session = TrainingSession::create([
            'title' => "$department - $level - Formation Test",
            'description' => "Formation de test pour $department au $level",
            'certificate_name' => "Certificat $department $level",
            'training_objectives' => "Objectifs de formation pour $department $level",
            'training_domain_id' => $domain->id,
            'department' => $department,
            'level' => $level,
            'trainer_id' => $trainer->id,
            'start_date' => now()->addDays(rand(1, 30)),
            'end_date' => now()->addDays(rand(31, 60)),
            'max_participants' => rand(10, 20),
            'price' => rand(100, 500),
            'location' => 'Centre de formation',
            'prerequisites' => $level === 'Niveau 1' ? 'Aucun prérequis' : "Avoir terminé le niveau précédent en $department",
            'active' => true,
        ]);
        
        echo "Session créée: {$session->title} (ID: {$session->id})\n";
    }
}

echo "\nSessions de test créées avec succès!\n";
