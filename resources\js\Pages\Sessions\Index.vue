<template>
  <SessionLayout>
    <div class="container mx-auto px-4 py-8">
      <!-- En-tête -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">Sessions de formation disponibles</h1>
        <p class="text-xl text-gray-600">Découvrez nos prochaines sessions de formation et inscrivez-vous dès maintenant</p>
      </div>

      <!-- Filtres -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <form @submit.prevent="applyFilters" class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label for="domain" class="block text-sm font-medium text-gray-700 mb-1">Domaine de formation</label>
            <select
              id="domain"
              v-model="filters.domain"
              class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">Tous les domaines</option>
              <option v-for="domain in domains" :key="domain.id" :value="domain.id">
                {{ domain.name }}
              </option>
            </select>
          </div>
          <div>
            <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Date de début</label>
            <input
              type="date"
              id="start_date"
              v-model="filters.start_date"
              class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
          <div class="flex items-end">
            <button
              type="submit"
              class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition"
            >
              Filtrer
            </button>
          </div>
        </form>
      </div>

      <!-- Liste des sessions -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div v-for="session in sessions.data" :key="session.id" class="bg-white rounded-lg shadow-lg overflow-hidden">
          <div class="p-6">
            <div class="flex items-center justify-between mb-4">
              <span class="px-3 py-1 text-sm font-semibold text-blue-600 bg-blue-100 rounded-full">
                {{ session.training_domain?.name }}
              </span>
              <span class="text-sm text-gray-500">
                {{ formatDate(session.start_date) }}
              </span>
            </div>
            <h2 class="text-xl font-bold text-gray-900 mb-2">{{ session.title }}</h2>
            <p class="text-gray-600 mb-4 line-clamp-3">{{ session.description }}</p>
            <div class="flex items-center justify-between">
              <div class="flex items-center text-gray-500">
                <ClockIcon class="w-5 h-5 mr-1" />
                <span>{{ session.duration }} heures</span>
              </div>
              <Link
                :href="route('sessions.show', session.id)"
                class="text-blue-600 hover:text-blue-700 font-semibold"
              >
                Voir les détails
              </Link>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div class="mt-8">
        <Pagination :links="sessions.links" />
      </div>
    </div>
  </SessionLayout>
</template>

<script setup>
import { ref, watch } from 'vue';
import { Link, router } from '@inertiajs/vue3';
import SessionLayout from '@/Layouts/SessionLayout.vue';
import Pagination from '@/Components/Pagination.vue';
import { ClockIcon } from '@heroicons/vue/24/outline';

const props = defineProps({
  sessions: Object,
  domains: Array,
  filters: Object
});

const filters = ref({
  domain: props.filters?.domain || '',
  start_date: props.filters?.start_date || ''
});

const applyFilters = () => {
  router.get(route('sessions.index'), filters.value, {
    preserveState: true,
    preserveScroll: true
  });
};

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('fr-FR', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });
};

// Réinitialiser les filtres quand l'URL change
watch(() => route().url, () => {
  filters.value = {
    domain: props.filters?.domain || '',
    start_date: props.filters?.start_date || ''
  };
});
</script> 