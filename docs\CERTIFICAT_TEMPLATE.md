# 📜 Template de Certificat PCMET

## 🎯 Vue d'ensemble

Le nouveau template de certificat PCMET a été conçu pour correspondre exactement au design officiel fourni, avec tous les éléments visuels et les informations requises pour un certificat professionnel conforme aux standards tunisiens.

## 🎨 Éléments de Design

### **Bordure et Décoration**
- **Bordure principale** : Double bordure turquoise (#2E8B8B) avec coins arrondis
- **Décorations d'angle** : Motifs décoratifs aux quatre coins avec design fleur de lys stylisée
- **Format** : A4 (210mm x 297mm) optimisé pour l'impression

### **En-tête Officiel**
- **Cô<PERSON> gauche** :
  - Dr<PERSON>eau tunisien (SVG intégré)
  - République de Tunisie
  - Ministère de l'Emploi et de la Formation Professionnelle
  - PCMET Training Center
  - Numéro d'enregistrement : 11-2000-21

- **C<PERSON><PERSON> droit** :
  - <PERSON><PERSON> du ministère (SVG stylisé)
  - Texte en arabe correspondant
  - Informations officielles en arabe

### **Contenu Principal**
- **Titre** : "ATTESTATION DE FORMATION" en grand format
- **Texte officiel** : Déclaration du directeur
- **Nom de l'étudiant** : Affiché en grand format au centre
- **Numéro CIN** : Placeholder pour le numéro de carte d'identité
- **Formation** : Titre de la formation ou nom personnalisé du certificat
- **Date et lieu** : Date de délivrance et lieu (Tunis)
- **Objectifs** : Description des compétences acquises

### **Pied de page**
- **Signatures** : Espaces pour signature du formateur et du directeur
- **QR Code** : Code QR pour vérification (SVG intégré)
- **Validité** : Information sur la durée de validité (2 ans)
- **Numéro de certificat** : Numéro unique du certificat

## 🔧 Fonctionnalités Techniques

### **Nouveaux Champs de Base de Données**
```sql
-- Ajoutés à la table training_sessions
certificate_name VARCHAR(255) NULL     -- Nom personnalisé pour le certificat
training_objectives TEXT NULL          -- Objectifs de formation détaillés
```

### **Variables Blade Disponibles**
```php
$certificate->certificate_number       // Numéro unique du certificat
$certificate->issued_at                // Date de délivrance
$user->name                           // Nom complet de l'étudiant
$trainingSession->title               // Titre de la session
$trainingSession->certificate_name    // Nom personnalisé (optionnel)
$trainingSession->training_objectives // Objectifs détaillés (optionnel)
$trainingDomain->name                 // Domaine de formation
```

### **Compatibilité PDF**
- **DomPDF optimisé** : CSS adapté pour la génération PDF
- **Encodage UTF-8** : Support complet des caractères arabes et français
- **SVG intégré** : Éléments graphiques en SVG pour une qualité optimale
- **Polices** : Times New Roman pour un rendu professionnel

## 📝 Utilisation

### **1. Création d'une Session de Formation**
Lors de la création d'une session, vous pouvez maintenant remplir :
- **Nom du certificat** : Nom qui apparaîtra sur le certificat (optionnel)
- **Objectifs de formation** : Description détaillée des compétences (optionnel)

### **2. Génération Automatique**
Le certificat utilise automatiquement :
- Le nom personnalisé si défini, sinon le titre de la session
- Les objectifs personnalisés si définis, sinon un texte par défaut
- La date de délivrance formatée automatiquement
- Un numéro de certificat unique

### **3. Personnalisation**
```php
// Exemple d'utilisation dans le contrôleur
$trainingSession = TrainingSession::create([
    'title' => 'Formation Premiers Secours',
    'certificate_name' => 'Premiers Secours et Gestes de Survie',
    'training_objectives' => 'Cette formation permet de maîtriser les gestes essentiels...'
]);
```

## 🚀 Routes et Tests

### **Routes Disponibles**
- `/test-certificate` : Génération PDF de test avec données fictives
- `/test-certificate.html` : Version HTML pour prévisualisation
- `/admin/certificates/test-pdf` : Route admin pour test (nécessite authentification)

### **Test du Template**
1. Accédez à `http://votre-domaine/test-certificate`
2. Le PDF se génère automatiquement avec des données de test
3. Vérifiez tous les éléments visuels et le contenu

## 🎯 Conformité et Standards

### **Standards PCMET**
- ✅ Logo et informations officielles
- ✅ Numéro d'enregistrement correct (11-2000-21)
- ✅ Format A4 standard
- ✅ Texte bilingue (français/arabe)
- ✅ QR Code pour vérification
- ✅ Espaces de signature officiels

### **Validation Légale**
- ✅ Informations du ministère conformes
- ✅ Déclaration officielle du directeur
- ✅ Numéro de certificat unique
- ✅ Date et lieu de délivrance
- ✅ Validité de 2 ans mentionnée

## 🔄 Maintenance

### **Mise à jour du Design**
Pour modifier le template :
1. Éditez `resources/views/certificates/pdf.blade.php`
2. Testez avec `/test-certificate`
3. Vérifiez la compatibilité PDF

### **Ajout d'Éléments**
- **Images** : Utilisez des SVG intégrés pour une meilleure compatibilité
- **Styles** : Évitez flexbox, préférez float et position
- **Polices** : Restez sur des polices standard (Times New Roman, Arial)

## 📋 Checklist de Validation

Avant mise en production, vérifiez :
- [ ] Génération PDF sans erreur
- [ ] Tous les textes sont lisibles
- [ ] Les éléments SVG s'affichent correctement
- [ ] Le format A4 est respecté
- [ ] Les marges sont appropriées
- [ ] Le QR Code est visible
- [ ] Les signatures sont bien positionnées
- [ ] Les informations officielles sont exactes

## 🆘 Dépannage

### **Problèmes Courants**
1. **PDF vide** : Vérifiez les logs Laravel pour les erreurs DomPDF
2. **SVG non affiché** : Assurez-vous que les SVG sont bien encodés en UTF-8
3. **Mise en page cassée** : Évitez les propriétés CSS non supportées par DomPDF
4. **Caractères arabes** : Vérifiez l'encodage UTF-8 du fichier Blade

### **Logs à Consulter**
```bash
tail -f storage/logs/laravel.log
```

Le template est maintenant prêt pour la production et respecte tous les standards PCMET ! 🎉
