<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exam_results', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('enrollment_id');
            $table->foreignId('exam_id')->constrained()->onDelete('cascade');
            $table->integer('score');
            $table->json('answers')->nullable(); // Stocke les réponses de l'utilisateur en JSON
            $table->boolean('passed')->default(false);
            $table->integer('attempt_number')->default(1);
            $table->string('file_submission')->nullable(); // Pour les examens de type 'file'
            $table->text('feedback')->nullable(); // Commentaires du formateur
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_results');
    }
};
