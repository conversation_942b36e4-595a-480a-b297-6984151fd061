<template>
  <Head title="<PERSON>réer une inscription" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Créer une inscription
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link :href="route('admin.enrollments.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à la liste
              </Link>
            </div>

            <!-- Formulaire de création d'inscription -->
            <form @submit.prevent="submit">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Apprenant -->
                <div>
                  <InputLabel for="user_id" value="Apprenant" />
                  <select
                    id="user_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.user_id"
                    required
                  >
                    <option value="">Sélectionnez un apprenant</option>
                    <option v-for="student in students" :key="student.id" :value="student.id">
                      {{ student.name }} ({{ student.email }})
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.user_id" />
                </div>

                <!-- Session de formation -->
                <div>
                  <InputLabel for="training_session_id" value="Session de formation" />
                  <select
                    id="training_session_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.training_session_id"
                    required
                  >
                    <option value="">Sélectionnez une session</option>
                    <option v-for="session in trainingSessions" :key="session.id" :value="session.id">
                      {{ session.title }} ({{ session.training_domain.name }})
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.training_session_id" />
                </div>

                <!-- Statut -->
                <div>
                  <InputLabel for="status" value="Statut" />
                  <select
                    id="status"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.status"
                    required
                  >
                    <option value="pending">En attente</option>
                    <option value="approved">Approuvée</option>
                    <option value="rejected">Rejetée</option>
                    <option value="completed">Terminée</option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.status" />
                </div>

                <!-- Date d'inscription -->
                <div>
                  <InputLabel for="enrollment_date" value="Date d'inscription" />
                  <input
                    id="enrollment_date"
                    type="date"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.enrollment_date"
                  />
                  <InputError class="mt-2" :message="form.errors.enrollment_date" />
                </div>

                <!-- Notes -->
                <div class="md:col-span-2">
                  <InputLabel for="notes" value="Notes" />
                  <textarea
                    id="notes"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.notes"
                    rows="4"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.notes" />
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="flex items-center justify-end mt-6">
                <PrimaryButton class="ml-4" :disabled="form.processing">
                  Créer l'inscription
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';

// Props
const props = defineProps({
  trainingSessions: Array,
  students: Array,
});

// Formulaire
const form = useForm({
  user_id: '',
  training_session_id: '',
  status: 'pending',
  enrollment_date: new Date().toISOString().substr(0, 10), // Date du jour au format YYYY-MM-DD
  notes: '',
});

// Méthodes
const submit = () => {
  form.post(route('admin.enrollments.store'));
};
</script>
