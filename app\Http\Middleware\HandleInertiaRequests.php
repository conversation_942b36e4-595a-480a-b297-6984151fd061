<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Inertia\Middleware;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that is loaded on the first page visit.
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determine the current asset version.
     */
    public function version(Request $request): string|null
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        return [
            ...parent::share($request),
            'auth' => [
                'user' => $request->user(),
            ],
            'flash' => [
                'success' => fn () => $request->session()->get('success'),
                'error' => fn () => $request->session()->get('error'),
            ],
        ];
    }

    /**
     * Determine if the request is for a file download.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    public function shouldIntercept(Request $request): bool
    {
        // Ne pas intercepter les routes de téléchargement de fichiers
        if ($request->is('admin/certificates/*/download') ||
            $request->is('admin/certificates/*/view') ||
            $request->is('admin/course-materials/*/download') ||
            $request->is('admin/course-materials/*/view')) {
            return false;
        }

        return parent::shouldIntercept($request);
    }
}
