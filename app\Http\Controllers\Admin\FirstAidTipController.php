<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FirstAidTip;
use Illuminate\Http\Request;
use Inertia\Inertia;

class FirstAidTipController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Initialiser la requête
        $query = FirstAidTip::with(['materials']);

        // Appliquer la recherche si elle existe
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where('title', 'like', $searchTerm);
        }

        // Filtrer par statut
        if ($request->has('active') && $request->active !== '') {
            $query->where('active', $request->active === 'true');
        }

        // Trier par ordre puis par titre
        $query->orderBy('order')->orderBy('title');

        // Paginer les résultats
        $firstAidTips = $query->paginate(10)->withQueryString();

        // Calculer les statistiques
        $stats = [
            'total' => FirstAidTip::count(),
            'active' => FirstAidTip::where('active', true)->count(),
            'inactive' => FirstAidTip::where('active', false)->count(),
            'with_materials' => FirstAidTip::has('materials')->count(),
        ];

        return Inertia::render('Admin/FirstAidTips/Index', [
            'firstAidTips' => $firstAidTips,
            'stats' => $stats,
            'filters' => $request->only(['search', 'active'])
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/FirstAidTips/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'order' => 'nullable|integer|min:0',
            'active' => 'boolean',
        ]);

        // Créer le conseil en premiers secours
        FirstAidTip::create($validated);

        // Rediriger vers la liste avec un message de succès
        return redirect()->route('admin.first-aid-tips.index')
            ->with('success', 'Conseil en premiers secours créé avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Récupérer le conseil avec ses matériels
        $firstAidTip = FirstAidTip::with([
            'materials' => function ($query) {
                $query->orderBy('order');
            }
        ])->findOrFail($id);

        return Inertia::render('Admin/FirstAidTips/Show', [
            'firstAidTip' => $firstAidTip
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Récupérer le conseil
        $firstAidTip = FirstAidTip::findOrFail($id);

        return Inertia::render('Admin/FirstAidTips/Edit', [
            'firstAidTip' => $firstAidTip
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Récupérer le conseil
        $firstAidTip = FirstAidTip::findOrFail($id);

        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'order' => 'nullable|integer|min:0',
            'active' => 'boolean',
        ]);

        // Mettre à jour le conseil
        $firstAidTip->update($validated);

        // Rediriger vers la liste avec un message de succès
        return redirect()->route('admin.first-aid-tips.index')
            ->with('success', 'Conseil en premiers secours mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Récupérer le conseil
        $firstAidTip = FirstAidTip::findOrFail($id);

        // Vérifier si le conseil a des matériels associés
        if ($firstAidTip->materials()->count() > 0) {
            return redirect()->route('admin.first-aid-tips.index')
                ->with('error', 'Impossible de supprimer ce conseil car il contient des matériels.');
        }

        // Supprimer le conseil
        $firstAidTip->delete();

        // Rediriger vers la liste avec un message de succès
        return redirect()->route('admin.first-aid-tips.index')
            ->with('success', 'Conseil en premiers secours supprimé avec succès.');
    }
}
