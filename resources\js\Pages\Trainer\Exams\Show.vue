<template>
  <Head :title="'Détails de l\'examen - ' + exam.title" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Détails de l'examen
        </h2>
        <div class="flex space-x-2">
          <Link :href="route('trainer.exam-questions.index', { exam_id: exam.id })" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
            Voir les questions
          </Link>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Informations générales -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4">Informations générales</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p class="text-sm text-gray-600 mb-1">Titre</p>
                <p class="font-medium">{{ exam.title }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-600 mb-1">Session de formation</p>
                <p class="font-medium">{{ exam.training_session.title }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-600 mb-1">Durée</p>
                <p class="font-medium">{{ exam.duration_minutes }} minutes</p>
              </div>
              <div>
                <p class="text-sm text-gray-600 mb-1">Score de réussite</p>
                <p class="font-medium">{{ exam.passing_score }}%</p>
              </div>
              <div>
                <p class="text-sm text-gray-600 mb-1">Statut</p>
                <p class="font-medium">
                  <span v-if="exam.status === 'draft'" class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">Brouillon</span>
                  <span v-else-if="exam.status === 'published'" class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Publié</span>
                  <span v-else-if="exam.status === 'closed'" class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Fermé</span>
                </p>
              </div>
              <div>
                <p class="text-sm text-gray-600 mb-1">Nombre de questions</p>
                <p class="font-medium">{{ exam.questions ? exam.questions.length : 0 }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-600 mb-1">Créé par</p>
                <p class="font-medium">{{ exam.creator ? exam.creator.name : 'Administrateur' }}</p>
              </div>
            </div>
            <div class="mt-4">
              <p class="text-sm text-gray-600 mb-1">Description</p>
              <p class="font-medium">{{ exam.description || 'Aucune description' }}</p>
            </div>
            <div class="mt-4">
              <p class="text-sm text-gray-600 mb-1">Instructions</p>
              <p class="font-medium">{{ exam.instructions || 'Aucune instruction' }}</p>
            </div>
          </div>
        </div>

        <!-- Statistiques -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4">Statistiques</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div class="bg-blue-50 p-4 rounded-lg">
                <p class="text-sm text-blue-600 mb-1">Total des étudiants</p>
                <p class="text-2xl font-bold text-blue-800">{{ stats.totalStudents }}</p>
              </div>
              <div class="bg-green-50 p-4 rounded-lg">
                <p class="text-sm text-green-600 mb-1">Réussis</p>
                <p class="text-2xl font-bold text-green-800">{{ stats.passedStudents }}</p>
              </div>
              <div class="bg-red-50 p-4 rounded-lg">
                <p class="text-sm text-red-600 mb-1">Échoués</p>
                <p class="text-2xl font-bold text-red-800">{{ stats.failedStudents }}</p>
              </div>
              <div class="bg-yellow-50 p-4 rounded-lg">
                <p class="text-sm text-yellow-600 mb-1">En attente</p>
                <p class="text-2xl font-bold text-yellow-800">{{ stats.pendingStudents }}</p>
              </div>
            </div>
            <div class="mt-4">
              <p class="text-sm text-gray-600 mb-1">Taux de réussite</p>
              <div class="w-full bg-gray-200 rounded-full h-4">
                <div class="bg-green-600 h-4 rounded-full" :style="{ width: stats.passRate + '%' }"></div>
              </div>
              <p class="mt-1 text-sm text-gray-600">{{ stats.passRate }}%</p>
            </div>
          </div>
        </div>

        <!-- Résultats des étudiants -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4">Résultats des étudiants</h3>
            <div v-if="examResults.length > 0">
              <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200">
                  <thead>
                    <tr>
                      <th class="py-2 px-4 border-b text-left">Étudiant</th>
                      <th class="py-2 px-4 border-b text-left">Date de soumission</th>
                      <th class="py-2 px-4 border-b text-left">Score</th>
                      <th class="py-2 px-4 border-b text-left">Statut</th>
                      <th class="py-2 px-4 border-b text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="result in examResults" :key="result.id" class="hover:bg-gray-50">
                      <td class="py-2 px-4 border-b">{{ result.student ? result.student.name : 'Étudiant inconnu' }}</td>
                      <td class="py-2 px-4 border-b">{{ formatDate(result.completed_at) }}</td>
                      <td class="py-2 px-4 border-b">{{ result.score !== null ? result.score + '%' : '-' }}</td>
                      <td class="py-2 px-4 border-b">
                        <span v-if="result.passed === true" class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Réussi</span>
                        <span v-else-if="result.passed === false" class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Échoué</span>
                        <span v-else-if="result.completed_at" class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">En attente d'évaluation</span>
                        <span v-else class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">Non commencé</span>
                      </td>
                      <td class="py-2 px-4 border-b text-center">
                        <Link :href="route('trainer.exam-results.show', result.id)" class="text-blue-600 hover:text-blue-900">
                          Voir
                        </Link>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <p class="text-gray-500">Aucun résultat d'examen trouvé.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  exam: Object,
  stats: Object,
  examResults: Array,
});

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return 'Non définie';
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};
</script>
