<template>
  <Head :title="`Résultats - ${examResult.exam.title}`" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Résultats de l'examen
        </h2>
        <div class="flex space-x-2">
          <Link :href="route('student.exams.index')" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
            Retour aux examens
          </Link>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- En-tête des résultats -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div>
                <h1 class="text-2xl font-bold mb-2">{{ examResult.exam.title }}</h1>
                <p class="text-gray-600">{{ examResult.enrollment.training_session.title }}</p>
              </div>
              <div :class="{
                'px-4 py-2 rounded-md text-white font-medium': true,
                'bg-green-600': examResult.passed,
                'bg-red-600': !examResult.passed
              }">
                {{ examResult.passed ? 'Réussi' : 'Échoué' }}
              </div>
            </div>
          </div>
        </div>

        <!-- Résumé des résultats -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h2 class="text-xl font-semibold mb-6">Résumé</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <!-- Score -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <div class="flex justify-center">
                  <div class="relative">
                    <svg class="w-24 h-24">
                      <circle
                        class="text-gray-300"
                        stroke-width="5"
                        stroke="currentColor"
                        fill="transparent"
                        r="45"
                        cx="50%"
                        cy="50%"
                      />
                      <circle
                        :class="{
                          'text-green-600': examResult.passed,
                          'text-red-600': !examResult.passed
                        }"
                        stroke-width="5"
                        :stroke-dasharray="circumference"
                        :stroke-dashoffset="circumference - (examResult.score / 100) * circumference"
                        stroke-linecap="round"
                        stroke="currentColor"
                        fill="transparent"
                        r="45"
                        cx="50%"
                        cy="50%"
                      />
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                      <span class="text-2xl font-bold">{{ examResult.score }}%</span>
                    </div>
                  </div>
                </div>
                <p class="text-center mt-2 font-medium">Score</p>
                <p class="text-center text-sm text-gray-500">Score minimum requis: {{ examResult.exam.passing_score }}%</p>
              </div>

              <!-- Temps passé -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <div class="flex justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-blue-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <p class="text-center font-medium">Temps passé</p>
                <p class="text-center text-xl">{{ formatDuration(examDuration) }}</p>
              </div>

              <!-- Réponses correctes -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <div class="flex justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-green-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <p class="text-center font-medium">Réponses correctes</p>
                <p class="text-center text-xl">{{ correctAnswers }} / {{ totalQuestions }}</p>
              </div>

              <!-- Tentative -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <div class="flex justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-purple-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <p class="text-center font-medium">Tentative</p>
                <p class="text-center text-xl">{{ examResult.attempt_number }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Certificat (si examen réussi et type certification ou certification_rattrapage) -->
        <div v-if="examResult.passed && (examResult.exam.exam_type === 'certification' || examResult.exam.exam_type === 'certification_rattrapage') && certificate" class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-xl font-semibold">Certificat</h2>
              <button @click="downloadCertificate" class="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Télécharger le certificat
              </button>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
              <div class="flex flex-col items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-green-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
                <h3 class="text-lg font-semibold mb-2">Certificat de réussite</h3>
                <p class="text-gray-600 mb-4">Numéro: {{ certificate.certificate_number }}</p>
                <p class="text-gray-600">Délivré le: {{ formatDate(certificate.issued_at) }}</p>
                <p class="text-gray-600">Valide jusqu'au: {{ formatDate(certificate.expiry_date) }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Rattrapage (si examen échoué et type certification) -->
        <div v-if="!examResult.passed && examResult.exam.exam_type === 'certification'" class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <div class="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <div>
                <h2 class="text-xl font-semibold mb-2">Examen de rattrapage</h2>
                <p class="text-gray-600 mb-4">Vous n'avez pas obtenu le score minimum requis pour cet examen de certification. Vous pouvez passer un examen de rattrapage pour obtenir votre certificat.</p>
                <!-- Rechercher un examen de rattrapage existant -->
                <Link :href="route('student.exams.index', { exam_type: 'certification_rattrapage' })" class="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
                  Voir les examens de rattrapage disponibles
                </Link>
              </div>
            </div>
          </div>
        </div>

        <!-- Analyse détaillée -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h2 class="text-xl font-semibold mb-6">Analyse détaillée</h2>

            <div class="mb-8">
              <h3 class="text-lg font-medium mb-4">Performance par catégorie</h3>
              <div class="h-64">
                <canvas ref="radarChart"></canvas>
              </div>
            </div>

            <div>
              <h3 class="text-lg font-medium mb-4">Réponses aux questions</h3>
              <div class="space-y-6">
                <div v-for="(question, index) in examResult.exam.questions" :key="question.id" class="border rounded-lg overflow-hidden">
                  <div :class="{
                    'p-4 border-b': true,
                    'bg-green-50': isAnswerCorrect(question.id),
                    'bg-red-50': !isAnswerCorrect(question.id)
                  }">
                    <div class="flex justify-between items-center">
                      <h4 class="font-medium">Question {{ index + 1 }}</h4>
                      <span :class="{
                        'px-2 py-1 text-xs rounded-full': true,
                        'bg-green-100 text-green-800': isAnswerCorrect(question.id),
                        'bg-red-100 text-red-800': !isAnswerCorrect(question.id)
                      }">
                        {{ isAnswerCorrect(question.id) ? 'Correcte' : 'Incorrecte' }}
                      </span>
                    </div>
                  </div>
                  <div class="p-4">
                    <p class="mb-4">{{ question.question_text }}</p>

                    <!-- Affichage des réponses selon le type de question -->
                    <div v-if="question.question_type === 'multiple_choice'">
                      <div v-for="(option, key) in getOptions(question.options)" :key="key" :class="{
                        'p-3 rounded-md mb-2 flex items-start': true,
                        'bg-green-50 border border-green-200': isOptionCorrect(question, key),
                        'bg-red-50 border border-red-200': isOptionSelected(question.id, key) && !isOptionCorrect(question, key),
                        'bg-gray-50 border border-gray-200': !isOptionSelected(question.id, key) && !isOptionCorrect(question, key)
                      }">
                        <div :class="{
                          'w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0': true,
                          'bg-green-500 text-white': isOptionCorrect(question, key),
                          'bg-red-500 text-white': isOptionSelected(question.id, key) && !isOptionCorrect(question, key),
                          'bg-gray-300 text-gray-700': !isOptionSelected(question.id, key) && !isOptionCorrect(question, key)
                        }">
                          {{ key }}
                        </div>
                        <div class="flex-grow">{{ option }}</div>
                        <div v-if="isOptionSelected(question.id, key)" class="ml-2">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    </div>

                    <div v-else-if="question.question_type === 'text'">
                      <div class="mb-4">
                        <h5 class="text-sm font-medium text-gray-700 mb-1">Votre réponse:</h5>
                        <div class="p-3 bg-gray-50 border border-gray-200 rounded-md">
                          {{ getUserAnswer(question.id) || 'Aucune réponse' }}
                        </div>
                      </div>
                      <div v-if="question.correct_answer">
                        <h5 class="text-sm font-medium text-gray-700 mb-1">Réponse attendue:</h5>
                        <div class="p-3 bg-green-50 border border-green-200 rounded-md">
                          {{ question.correct_answer }}
                        </div>
                      </div>
                    </div>

                    <div v-else-if="question.question_type === 'file_upload'">
                      <p class="text-gray-600 italic">Les réponses par fichier sont évaluées manuellement par le formateur.</p>
                    </div>

                    <!-- Explication (si disponible) -->
                    <div v-if="question.explanation" class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <h5 class="text-sm font-medium text-gray-700 mb-1 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                        Explication:
                      </h5>
                      <p class="text-gray-800">{{ question.explanation }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recommandations -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <h2 class="text-xl font-semibold mb-4">Recommandations</h2>

            <div v-if="examResult.passed" class="bg-green-50 p-4 rounded-md mb-4">
              <div class="flex">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h3 class="font-medium text-green-800">Félicitations pour votre réussite !</h3>
                  <p class="text-green-700">Vous avez démontré une bonne maîtrise des concepts évalués dans cet examen.</p>
                </div>
              </div>
            </div>

            <div v-else class="bg-yellow-50 p-4 rounded-md mb-4">
              <div class="flex">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <div>
                  <h3 class="font-medium text-yellow-800">Continuez vos efforts !</h3>
                  <p class="text-yellow-700">Vous avez besoin de renforcer vos connaissances dans certains domaines.</p>
                </div>
              </div>
            </div>

            <div class="space-y-4">
              <div v-for="(category, index) in weakCategories" :key="index" class="p-4 border rounded-md">
                <h4 class="font-medium mb-2">{{ category.name }}</h4>
                <p class="text-gray-600 mb-2">{{ category.description }}</p>
                <div class="flex flex-wrap gap-2">
                  <a v-for="(resource, i) in category.resources" :key="i" :href="resource.url" target="_blank" class="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    {{ resource.name }}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Chart from 'chart.js/auto';

// Props
const props = defineProps({
  examResult: Object,
  certificate: Object,
});

// Références
const radarChart = ref(null);

// Constantes
const circumference = 2 * Math.PI * 45;

// Calculs
const totalQuestions = computed(() => props.examResult.exam.questions.length);

// Calculer le nombre de réponses correctes en analysant les réponses
const correctAnswers = computed(() => {
  let count = 0;

  // Si le score est disponible, utiliser la formule basée sur le score
  if (props.examResult.score !== undefined) {
    // Calculer le nombre de réponses correctes en fonction du score
    count = Math.round((props.examResult.score / 100) * totalQuestions.value);
  } else {
    // Sinon, compter manuellement les réponses correctes
    props.examResult.exam.questions.forEach(question => {
      if (isAnswerCorrect(question.id)) {
        count++;
      }
    });
  }

  return count;
});

const examDuration = computed(() => {
  if (!props.examResult.completed_at) return 0;
  const startTime = new Date(props.examResult.created_at);
  const endTime = new Date(props.examResult.completed_at);
  return Math.floor((endTime - startTime) / 1000);
});

// Catégories faibles (exemple - à personnaliser selon les besoins)
const weakCategories = ref([
  {
    name: 'Concepts fondamentaux',
    description: 'Renforcer votre compréhension des concepts de base.',
    resources: [
      { name: 'Guide des fondamentaux', url: '#' },
      { name: 'Vidéos explicatives', url: '#' },
    ]
  },
  {
    name: 'Applications pratiques',
    description: 'Améliorer vos compétences pratiques avec des exercices supplémentaires.',
    resources: [
      { name: 'Exercices pratiques', url: '#' },
      { name: 'Études de cas', url: '#' },
    ]
  }
]);

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return 'Non définie';
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const formatDuration = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  let result = '';
  if (hours > 0) result += `${hours}h `;
  if (minutes > 0 || hours > 0) result += `${minutes}m `;
  result += `${secs}s`;

  return result;
};

const isAnswerCorrect = (questionId) => {
  // Vérifier si la question a été répondue correctement en utilisant les données du serveur

  // Récupérer la question
  const question = props.examResult.exam.questions.find(q => q.id === questionId);
  if (!question) return false;

  // Récupérer la réponse de l'utilisateur
  const userAnswer = getUserAnswer(questionId);
  if (!userAnswer && userAnswer !== 0) return false;

  // Si le serveur nous a fourni des informations sur les réponses correctes, les utiliser
  if (props.examResult.correct_answers && props.examResult.correct_answers[questionId]) {
    return true;
  }

  // Sinon, essayer de déterminer si la réponse est correcte en fonction du type de question
  if (question.question_type === 'multiple_choice') {
    // Pour les questions à choix multiple, vérifier si la réponse correspond aux options correctes
    const userAnswerArray = Array.isArray(userAnswer) ? userAnswer : [userAnswer];

    // Récupérer les options correctes
    let correctOptions = [];

    // Vérifier si correct_options existe et n'est pas null
    if (question.correct_options) {
      // Si c'est une chaîne JSON, essayer de la parser
      if (typeof question.correct_options === 'string') {
        try {
          const parsed = JSON.parse(question.correct_options);
          correctOptions = Array.isArray(parsed) ? parsed : [parsed];
        } catch (e) {
          console.error('Erreur de parsing des options correctes:', e);
          correctOptions = [question.correct_options];
        }
      } else {
        // Sinon, utiliser directement
        correctOptions = Array.isArray(question.correct_options) ? question.correct_options : [question.correct_options];
      }
    }
    // Si correct_options n'existe pas, essayer correct_answer
    else if (question.correct_answer) {
      // Si c'est une chaîne JSON, essayer de la parser
      if (typeof question.correct_answer === 'string') {
        try {
          const parsed = JSON.parse(question.correct_answer);
          correctOptions = Array.isArray(parsed) ? parsed : [parsed];
        } catch (e) {
          console.error('Erreur de parsing de la réponse correcte:', e);
          correctOptions = [question.correct_answer];
        }
      } else {
        // Sinon, utiliser directement
        correctOptions = Array.isArray(question.correct_answer) ? question.correct_answer : [question.correct_answer];
      }
    }

    // Si nous n'avons pas d'information sur les options correctes, nous ne pouvons pas déterminer
    if (correctOptions.length === 0) {
      console.log('Pas d\'options correctes trouvées pour la question:', questionId);
      // Dans ce cas, nous utilisons le score global pour estimer
      return false;
    }

    console.log('Question ID:', questionId, 'User Answer:', userAnswerArray, 'Correct Options:', correctOptions);

    // Vérifier si la réponse de l'utilisateur correspond aux options correctes
    // Convertir les options en chaînes pour la comparaison
    const userAnswerStrings = userAnswerArray.map(opt => String(opt));
    const correctOptionStrings = correctOptions.map(opt => String(opt));

    return userAnswerStrings.some(option => correctOptionStrings.includes(option));
  }

  // Pour les autres types de questions, nous ne pouvons pas déterminer automatiquement
  return false;
};

const isOptionCorrect = (question, optionKey) => {
  // Vérifier si l'option est correcte en utilisant les données de la question

  // Récupérer les options correctes
  let correctOptions = [];

  // Vérifier si correct_options existe et n'est pas null
  if (question.correct_options) {
    // Si c'est une chaîne JSON, essayer de la parser
    if (typeof question.correct_options === 'string') {
      try {
        const parsed = JSON.parse(question.correct_options);
        correctOptions = Array.isArray(parsed) ? parsed : [parsed];
      } catch (e) {
        console.error('Erreur de parsing des options correctes:', e);
        correctOptions = [question.correct_options];
      }
    } else {
      // Sinon, utiliser directement
      correctOptions = Array.isArray(question.correct_options) ? question.correct_options : [question.correct_options];
    }
  }
  // Si correct_options n'existe pas, essayer correct_answer
  else if (question.correct_answer) {
    // Si c'est une chaîne JSON, essayer de la parser
    if (typeof question.correct_answer === 'string') {
      try {
        const parsed = JSON.parse(question.correct_answer);
        correctOptions = Array.isArray(parsed) ? parsed : [parsed];
      } catch (e) {
        console.error('Erreur de parsing de la réponse correcte:', e);
        correctOptions = [question.correct_answer];
      }
    } else {
      // Sinon, utiliser directement
      correctOptions = Array.isArray(question.correct_answer) ? question.correct_answer : [question.correct_answer];
    }
  }

  // Convertir les options en chaînes pour la comparaison
  const correctOptionStrings = correctOptions.map(opt => String(opt));
  const optionKeyString = String(optionKey);

  // Vérifier si l'option est dans les options correctes
  return correctOptionStrings.includes(optionKeyString);
};

const isOptionSelected = (questionId, optionKey) => {
  const userAnswer = getUserAnswer(questionId);
  if (!userAnswer && userAnswer !== 0) return false;

  // Convertir en tableau si ce n'est pas déjà le cas
  const userAnswerArray = Array.isArray(userAnswer) ? userAnswer : [userAnswer];

  // Convertir les options en chaînes pour la comparaison
  const userAnswerStrings = userAnswerArray.map(opt => String(opt));
  const optionKeyString = String(optionKey);

  return userAnswerStrings.includes(optionKeyString);
};

const getUserAnswer = (questionId) => {
  if (!props.examResult.answers) return null;
  return props.examResult.answers[questionId];
};

const getOptions = (options) => {
  // Si aucune option n'est fournie, créer des options par défaut
  if (!options) {
    return {
      'A': 'Option A',
      'B': 'Option B',
      'C': 'Option C',
      'D': 'Option D'
    };
  }

  // Si les options sont une chaîne, essayer de les parser en JSON
  if (typeof options === 'string') {
    try {
      return JSON.parse(options);
    } catch (e) {
      console.error('Erreur de parsing des options:', e);
      return {
        'A': 'Option A',
        'B': 'Option B',
        'C': 'Option C',
        'D': 'Option D'
      };
    }
  }

  // Si les options sont déjà un objet, les retourner telles quelles
  if (typeof options === 'object' && options !== null) {
    return options;
  }

  // Par défaut, retourner des options standard
  return {
    'A': 'Option A',
    'B': 'Option B',
    'C': 'Option C',
    'D': 'Option D'
  };
};

const downloadCertificate = () => {
  // Rediriger vers la route de téléchargement du certificat
  window.open(route('student.certificates.download', props.certificate.id), '_blank');
};

// Initialisation du graphique radar
onMounted(() => {
  // Afficher les données dans la console pour le débogage
  console.log('Results page mounted successfully!');
  console.log('Exam Result:', props.examResult);
  console.log('Questions:', props.examResult.exam.questions);
  console.log('Certificate:', props.certificate);

  // Pour chaque question, afficher les options et les réponses correctes
  props.examResult.exam.questions.forEach(question => {
    console.log('Question ID:', question.id);
    console.log('Question Text:', question.question_text);
    console.log('Question Type:', question.question_type);
    console.log('Options:', question.options);
    console.log('Correct Options:', question.correct_options);
    console.log('User Answer:', getUserAnswer(question.id));
    console.log('Is Answer Correct:', isAnswerCorrect(question.id));
    console.log('-------------------');
  });

  if (radarChart.value) {
    // Exemple de données pour le graphique radar
    // Ces données devraient être calculées en fonction des résultats réels
    const data = {
      labels: ['Concepts fondamentaux', 'Applications pratiques', 'Résolution de problèmes', 'Analyse', 'Synthèse'],
      datasets: [{
        label: 'Votre performance',
        data: [85, 65, 75, 90, 60],
        fill: true,
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgb(54, 162, 235)',
        pointBackgroundColor: 'rgb(54, 162, 235)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(54, 162, 235)'
      }]
    };

    new Chart(radarChart.value, {
      type: 'radar',
      data: data,
      options: {
        elements: {
          line: {
            borderWidth: 3
          }
        },
        scales: {
          r: {
            angleLines: {
              display: true
            },
            suggestedMin: 0,
            suggestedMax: 100
          }
        }
      }
    });
  }
});
</script>
