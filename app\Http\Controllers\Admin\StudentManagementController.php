<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Enrollment;
use App\Models\Certificate;
use App\Models\ExamResult;
use Illuminate\Http\Request;
use Inertia\Inertia;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;

class StudentManagementController extends Controller
{
    /**
     * Afficher la liste de tous les apprenants
     */
    public function index(Request $request)
    {
        $query = User::where('role', 'student')
            ->withCount(['enrollments', 'certificates'])
            ->with(['enrollments', 'certificates']);

        // Recherche
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filtres
        if ($request->filled('status')) {
            $query->where('active', $request->status === 'active');
        }

        // Tri
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $students = $query->paginate(15)->withQueryString();

        // Statistiques réelles
        $stats = [
            'total_students' => User::where('role', 'student')->count(),
            'active_students' => User::where('role', 'student')->where('active', true)->count(),
            'total_enrollments' => Enrollment::count(),
            'completed_courses' => Enrollment::where('status', 'completed')->count(),
        ];

        return Inertia::render('Admin/Students/Index', [
            'students' => $students,
            'stats' => $stats,
            'filters' => $request->only(['search', 'status', 'sort', 'direction']),
            'discovery_sources' => [
                'facebook' => 'Facebook',
                'instagram' => 'Instagram',
                'tiktok' => 'TikTok',
                'word_of_mouth' => 'Bouche à oreille',
                'other' => 'Autre'
            ]
        ]);
    }

    /**
     * Afficher le profil détaillé d'un apprenant
     */
    public function show($id)
    {
        $student = User::where('role', 'student')
            ->with([
                'enrollments' => function($query) {
                    $query->with(['trainingSession' => function($q) {
                        $q->with('trainingDomain');
                    }]);
                },
                'certificates',
                'examResults'
            ])
            ->findOrFail($id);

        // Générer le QR code pour ce profil
        $qrCodePath = $this->generateStudentQrCode($student);

        // Statistiques réelles
        $stats = [
            'total_enrollments' => $student->enrollments->count(),
            'completed_courses' => $student->enrollments->where('status', 'completed')->count(),
            'certificates_earned' => $student->certificates->count(),
            'average_exam_score' => $student->examResults->avg('score') ?? 0,
            'total_payments' => $student->enrollments->sum('payment_amount') ?? 0
        ];

        return Inertia::render('Admin/Students/Show', [
            'student' => $student,
            'qrCodePath' => $qrCodePath,
            'stats' => $stats
        ]);
    }

    /**
     * Générer un QR code pour un apprenant
     */
    private function generateStudentQrCode(User $student)
    {
        $qrCodeDir = 'qrcodes/students';
        $fileName = "student-{$student->id}.svg";
        $filePath = "{$qrCodeDir}/{$fileName}";

        // Créer le répertoire s'il n'existe pas
        if (!Storage::disk('public')->exists($qrCodeDir)) {
            Storage::disk('public')->makeDirectory($qrCodeDir);
        }

        // URL vers le profil public de l'apprenant
        $profileUrl = route('students.public-profile', $student->id);

        // Générer le QR code
        $qrCode = QrCode::format('svg')
            ->size(200)
            ->margin(1)
            ->generate($profileUrl);

        // Sauvegarder le QR code
        Storage::disk('public')->put($filePath, $qrCode);

        return $filePath;
    }

    /**
     * Télécharger le QR code d'un apprenant
     */
    public function downloadQrCode($id)
    {
        $student = User::where('role', 'student')->findOrFail($id);
        $qrCodePath = $this->generateStudentQrCode($student);

        return Storage::disk('public')->download($qrCodePath, "qr-code-{$student->name}.svg");
    }

    /**
     * Profil public d'un apprenant (accessible via QR code)
     */
    public function publicProfile($id)
    {
        $student = User::where('role', 'student')
            ->where('active', true)
            ->with([
                'certificates' => function($query) {
                    $query->visibleToStudents()
                        ->with(['enrollment.trainingSession.trainingDomain']);
                },
                'enrollments' => function($query) {
                    $query->with(['trainingSession.trainingDomain']);
                }
            ])
            ->findOrFail($id);

        // Préparer les certificats pour l'affichage public
        $certificates = $student->certificates->map(function($certificate) {
            return [
                'id' => $certificate->id,
                'certificate_number' => $certificate->certificate_number,
                'training_name' => $certificate->enrollment?->trainingSession?->title ?? 'Formation non définie',
                'domain_name' => $certificate->enrollment?->trainingSession?->trainingDomain?->name ?? 'Domaine non défini',
                'issue_date' => $certificate->issue_date ?? $certificate->issued_at,
                'expiry_date' => $certificate->expiry_date,
            ];
        });

        // Préparer les formations terminées pour l'affichage public
        $completedTrainings = $student->enrollments
            ->filter(function($enrollment) {
                return in_array($enrollment->status, ['completed', 'approved']); // Inclure approved aussi
            })
            ->map(function($enrollment) {
                return [
                    'training_name' => $enrollment->trainingSession?->title ?? 'Formation non définie',
                    'domain_name' => $enrollment->trainingSession?->trainingDomain?->name ?? 'Domaine non défini',
                    'completion_date' => $enrollment->updated_at, // Date de fin approximative
                ];
            })
            ->values(); // Réindexer le tableau

        // Générer le QR code pour ce profil
        $qrCodePath = $this->generateStudentQrCode($student);

        // Données publiques
        $publicData = [
            'id' => $student->id,
            'name' => $student->name,
            'profile_photo' => $student->profile_photo,
            'qr_code_path' => $qrCodePath,
            'certificates' => $certificates,
            'completed_trainings' => $completedTrainings
        ];

        return Inertia::render('Public/StudentProfile', [
            'student' => $publicData
        ]);
    }

    /**
     * Actions groupées sur les apprenants
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'student_ids' => 'required|array',
            'student_ids.*' => 'exists:users,id'
        ]);

        $students = User::whereIn('id', $request->student_ids)
            ->where('role', 'student');

        switch ($request->action) {
            case 'activate':
                $students->update(['active' => true]);
                $message = 'Apprenants activés avec succès.';
                break;
            case 'deactivate':
                $students->update(['active' => false]);
                $message = 'Apprenants désactivés avec succès.';
                break;
            case 'delete':
                $students->delete();
                $message = 'Apprenants supprimés avec succès.';
                break;
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Exporter les données des apprenants en PDF
     */
    public function exportPdf(Request $request)
    {
        $query = User::where('role', 'student');

        // Appliquer les filtres
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $students = $query->get();

        return view('admin.students.export-pdf', compact('students'));
    }

    /**
     * Générer la carte d'apprenant numérique
     */
    public function generateStudentCard($id)
    {
        $student = User::where('role', 'student')
            ->with(['certificates', 'enrollments'])
            ->findOrFail($id);

        // Générer un numéro d'identification unique si pas existant
        if (!$student->student_id) {
            $student->student_id = 'STU-' . str_pad($student->id, 6, '0', STR_PAD_LEFT);
            $student->save();
        }

        // Générer le QR code pour la carte
        $qrCodePath = $this->generateStudentQrCode($student);

        return view('admin.students.student-card', compact('student', 'qrCodePath'));
    }

    /**
     * Télécharger la carte d'apprenant en PNG (méthode alternative côté serveur)
     */
    public function downloadStudentCardPng($id)
    {
        $student = User::where('role', 'student')
            ->with(['certificates', 'enrollments'])
            ->findOrFail($id);

        // Générer un numéro d'identification unique si pas existant
        if (!$student->student_id) {
            $student->student_id = 'STU-' . str_pad($student->id, 6, '0', STR_PAD_LEFT);
            $student->save();
        }

        // Générer le QR code pour la carte
        $qrCodePath = $this->generateStudentQrCode($student);

        // Retourner une réponse JSON avec les données nécessaires pour le frontend
        return response()->json([
            'success' => true,
            'student' => $student,
            'qrCodePath' => $qrCodePath,
            'message' => 'Utilisez le bouton de téléchargement sur la page de la carte'
        ]);
    }

    /**
     * Générer la fiche apprenant PDF complète
     */
    public function generateStudentSheet($id)
    {
        $student = User::where('role', 'student')
            ->with([
                'enrollments' => function($query) {
                    $query->with(['trainingSession.trainingDomain']);
                },
                'certificates' => function($query) {
                    $query->with(['enrollment.trainingSession.trainingDomain']);
                },
                'examResults' => function($query) {
                    $query->with(['exam']);
                }
            ])
            ->findOrFail($id);

        // Calculer les statistiques
        $stats = [
            'total_enrollments' => $student->enrollments->count(),
            'completed_courses' => $student->enrollments->where('status', 'completed')->count(),
            'certificates_earned' => $student->certificates->count(),
            'average_exam_score' => $student->examResults->avg('score') ?? 0,
            'total_payments' => $student->enrollments->sum('payment_amount') ?? 0,
            'success_rate' => $student->examResults->count() > 0
                ? ($student->examResults->where('score', '>=', 60)->count() / $student->examResults->count()) * 100
                : 0
        ];

        // Générer le PDF avec options optimisées
        $pdf = Pdf::loadView('admin.students.student-sheet', compact('student', 'stats'));
        $pdf->setPaper('a4', 'portrait');
        $pdf->setOptions([
            'defaultFont' => 'DejaVu Sans',
            'isHtml5ParserEnabled' => true,
            'isRemoteEnabled' => false,
            'isPhpEnabled' => true,
            'fontSubsetting' => false,
            'debugKeepTemp' => false,
            'debugCss' => false,
            'debugLayout' => false,
            'debugLayoutLines' => false,
            'debugLayoutBlocks' => false,
            'debugLayoutInline' => false,
            'debugLayoutPaddingBox' => false,
            'enable_font_subsetting' => false,
            'dpi' => 150,
            'defaultPaperSize' => 'a4',
            'defaultPaperOrientation' => 'portrait'
        ]);

        return $pdf->download("fiche-apprenant-{$student->name}.pdf");
    }

    /**
     * Voir la fiche apprenant (version HTML avec bouton export)
     */
    public function viewStudentSheet($id)
    {
        $student = User::where('role', 'student')
            ->with([
                'enrollments' => function($query) {
                    $query->with(['trainingSession.trainingDomain']);
                },
                'certificates' => function($query) {
                    $query->with(['enrollment.trainingSession.trainingDomain']);
                },
                'examResults' => function($query) {
                    $query->with(['exam']);
                }
            ])
            ->findOrFail($id);

        // Calculer les statistiques
        $stats = [
            'total_enrollments' => $student->enrollments->count(),
            'completed_courses' => $student->enrollments->where('status', 'completed')->count(),
            'certificates_earned' => $student->certificates->count(),
            'average_exam_score' => $student->examResults->avg('score') ?? 0,
            'total_payments' => $student->enrollments->sum('payment_amount') ?? 0,
            'success_rate' => $student->examResults->count() > 0
                ? ($student->examResults->where('score', '>=', 60)->count() / $student->examResults->count()) * 100
                : 0
        ];

        return view('admin.students.student-sheet-view', compact('student', 'stats'));
    }
}
