<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('announcements', function (Blueprint $table) {
            // Renommer la colonne visibility en visible_to
            if (Schema::hasColumn('announcements', 'visibility') && !Schema::hasColumn('announcements', 'visible_to')) {
                $table->renameColumn('visibility', 'visible_to');
            }

            // Ajouter la colonne author_id si elle n'existe pas
            if (!Schema::hasColumn('announcements', 'author_id') && Schema::hasColumn('announcements', 'created_by')) {
                $table->renameColumn('created_by', 'author_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('announcements', function (Blueprint $table) {
            // Renommer la colonne visible_to en visibility
            if (Schema::hasColumn('announcements', 'visible_to') && !Schema::hasColumn('announcements', 'visibility')) {
                $table->renameColumn('visible_to', 'visibility');
            }

            // Renommer la colonne author_id en created_by
            if (Schema::hasColumn('announcements', 'author_id') && !Schema::hasColumn('announcements', 'created_by')) {
                $table->renameColumn('author_id', 'created_by');
            }
        });
    }
};
