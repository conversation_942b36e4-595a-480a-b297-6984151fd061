<?php

// Ce script exécute le seeder FixQuestionsSeeder pour réparer les questions

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Exécuter le seeder
$seeder = new Database\Seeders\FixQuestionsSeeder();
$seeder->setContainer($app)->setCommand(new Illuminate\Console\Command());
$seeder->run();

echo "Script terminé.\n";
