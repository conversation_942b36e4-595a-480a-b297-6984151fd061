<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ExamResult;
use App\Models\Certificate;
use App\Models\User;
use App\Models\Exam;
use App\Models\Enrollment;
use App\Models\TrainingSession;
use Illuminate\Support\Facades\Auth;

class DiagnoseStudentExamData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exam:diagnose-student-data {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Diagnose student exam data retrieval issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        
        if ($userId) {
            $student = User::find($userId);
            if (!$student) {
                $this->error("User with ID {$userId} not found");
                return Command::FAILURE;
            }
        } else {
            // Find a student with exam results
            $student = User::whereHas('examResults')->first();
            if (!$student) {
                $this->error("No students with exam results found");
                return Command::FAILURE;
            }
        }
        
        $this->info("🔍 Diagnosing exam data for student: {$student->name} (ID: {$student->id})");
        
        $this->checkStudentEnrollments($student);
        $this->checkStudentExamResults($student);
        $this->checkAvailableExams($student);
        $this->simulateControllerLogic($student);
        
        return Command::SUCCESS;
    }
    
    /**
     * Check student enrollments
     */
    private function checkStudentEnrollments($student): void
    {
        $this->info('📚 Checking student enrollments...');
        
        $enrollments = Enrollment::where('user_id', $student->id)->with('trainingSession')->get();
        $this->line("   Total enrollments: {$enrollments->count()}");
        
        $approvedEnrollments = $enrollments->where('status', 'approved');
        $this->line("   Approved enrollments: {$approvedEnrollments->count()}");
        
        if ($approvedEnrollments->count() > 0) {
            $this->line("   Approved enrollment details:");
            foreach ($approvedEnrollments as $enrollment) {
                $sessionActive = $enrollment->trainingSession->active ? 'Active' : 'Inactive';
                $this->line("   - Session: {$enrollment->trainingSession->title} ({$sessionActive})");
            }
        }
        
        $sessionIds = $approvedEnrollments->pluck('training_session_id')->toArray();
        $activeSessionIds = TrainingSession::whereIn('id', $sessionIds)
            ->where('active', true)
            ->pluck('id')
            ->toArray();
        
        $this->line("   Active session IDs: " . implode(', ', $activeSessionIds));
    }
    
    /**
     * Check student exam results
     */
    private function checkStudentExamResults($student): void
    {
        $this->info('📝 Checking student exam results...');
        
        $examResults = ExamResult::where('user_id', $student->id)
            ->with(['exam', 'exam.trainingSession'])
            ->get();
        
        $this->line("   Total exam results: {$examResults->count()}");
        
        if ($examResults->count() > 0) {
            $this->line("   Exam result details:");
            foreach ($examResults as $result) {
                $status = $result->passed ? 'PASSED' : 'FAILED';
                $sessionTitle = $result->exam->trainingSession->title ?? 'No session';
                $this->line("   - {$result->exam->title}: {$result->score}% ({$status}) - Session: {$sessionTitle}");
            }
            
            // Group by exam_id
            $groupedResults = $examResults->groupBy('exam_id');
            $this->line("   Results grouped by exam:");
            foreach ($groupedResults as $examId => $results) {
                $exam = $results->first()->exam;
                $attempts = $results->count();
                $hasPassed = $results->contains('passed', true);
                $hasFailed = $results->contains('passed', false);
                $this->line("   - Exam {$examId} ({$exam->title}): {$attempts} attempts, Passed: " . ($hasPassed ? 'Yes' : 'No') . ", Failed: " . ($hasFailed ? 'Yes' : 'No'));
            }
        }
    }
    
    /**
     * Check available exams for student
     */
    private function checkAvailableExams($student): void
    {
        $this->info('🎯 Checking available exams...');
        
        // Get student's approved enrollments
        $enrollments = Enrollment::where('user_id', $student->id)
            ->where('status', 'approved')
            ->pluck('id')
            ->toArray();
        
        // Get active session IDs
        $sessionIds = Enrollment::where('user_id', $student->id)
            ->where('status', 'approved')
            ->pluck('training_session_id')
            ->toArray();
        
        $activeSessionIds = TrainingSession::whereIn('id', $sessionIds)
            ->where('active', true)
            ->pluck('id')
            ->toArray();
        
        $this->line("   Student enrollment IDs: " . implode(', ', $enrollments));
        $this->line("   Active session IDs: " . implode(', ', $activeSessionIds));
        
        // Get exams for active sessions
        $exams = Exam::whereIn('training_session_id', $activeSessionIds)
            ->where('is_published', true)
            ->with(['trainingSession', 'questions'])
            ->get();
        
        $this->line("   Total published exams in active sessions: {$exams->count()}");
        
        if ($exams->count() > 0) {
            $this->line("   Available exam details:");
            foreach ($exams as $exam) {
                $this->line("   - {$exam->title} (Type: {$exam->exam_type}) - Session: {$exam->trainingSession->title}");
            }
        }
    }
    
    /**
     * Simulate controller logic
     */
    private function simulateControllerLogic($student): void
    {
        $this->info('🔄 Simulating controller logic...');
        
        // Simulate the exact logic from ExamController@index
        $enrollments = Enrollment::where('user_id', $student->id)
            ->where('status', 'approved')
            ->pluck('id')
            ->toArray();
        
        $sessionIds = Enrollment::where('user_id', $student->id)
            ->where('status', 'approved')
            ->pluck('training_session_id')
            ->toArray();
        
        $activeSessionIds = TrainingSession::whereIn('id', $sessionIds)
            ->where('active', true)
            ->pluck('id')
            ->toArray();

        // Récupérer les sessions avec des résultats d'examen pour l'apprenant
        $examResultSessionIds = ExamResult::where('user_id', $student->id)
            ->join('exams', 'exam_results.exam_id', '=', 'exams.id')
            ->whereIn('exams.training_session_id', $sessionIds)
            ->distinct()
            ->pluck('exams.training_session_id')
            ->toArray();

        // Combiner les sessions actives et les sessions avec des résultats d'examen
        $allRelevantSessionIds = array_unique(array_merge($activeSessionIds, $examResultSessionIds));

        $query = Exam::whereIn('training_session_id', $allRelevantSessionIds)
            ->where('is_published', true)
            ->with(['trainingSession', 'questions']);

        $exams = $query->orderBy('created_at', 'desc')->get();
        
        $examResults = ExamResult::where('user_id', $student->id)
            ->with('exam')
            ->get()
            ->groupBy('exam_id');
        
        $this->line("   Controller simulation results:");
        $this->line("   - Found {$exams->count()} exams");
        $this->line("   - Found {$examResults->count()} exam result groups");
        
        $examData = $exams->map(function($exam) use ($examResults, $activeSessionIds) {
            $results = $examResults->get($exam->id, collect([]));
            $latestResult = $results->sortByDesc('created_at')->first();
            $attempts = $results->count();
            $hasPassed = $results->contains('passed', true);
            $hasFailed = $results->contains('passed', false);
            $inProgress = $results->contains(function($result) {
                return $result->status === 'in_progress';
            });

            $isSessionActive = in_array($exam->training_session_id, $activeSessionIds);
            $status = $this->getExamStatus($exam, $hasPassed, $hasFailed, $inProgress, $attempts, $isSessionActive);
            
            return [
                'id' => $exam->id,
                'title' => $exam->title,
                'exam_type' => $exam->exam_type,
                'attempts' => $attempts,
                'has_passed' => $hasPassed,
                'has_failed' => $hasFailed,
                'status' => $status,
                'latest_score' => $latestResult ? $latestResult->score : null,
            ];
        });
        
        // Categorize exams
        $upcomingExams = $examData->filter(fn($exam) => $exam['status'] === 'upcoming');
        $inProgressExams = $examData->filter(fn($exam) => $exam['status'] === 'in_progress');
        $availableExams = $examData->filter(fn($exam) => $exam['status'] === 'available');
        $completedExams = $examData->filter(fn($exam) => in_array($exam['status'], ['passed', 'failed']));
        
        $this->line("   Exam categorization:");
        $this->line("   - Upcoming: {$upcomingExams->count()}");
        $this->line("   - In Progress: {$inProgressExams->count()}");
        $this->line("   - Available: {$availableExams->count()}");
        $this->line("   - Completed: {$completedExams->count()}");
        
        if ($completedExams->count() > 0) {
            $this->line("   Completed exam details:");
            foreach ($completedExams as $exam) {
                $this->line("   - {$exam['title']}: {$exam['status']} (Score: {$exam['latest_score']}%)");
            }
        }
    }
    
    /**
     * Get exam status (copied from controller)
     */
    private function getExamStatus($exam, $hasPassed, $hasFailed, $inProgress, $attempts, $isSessionActive = true)
    {
        $now = now();

        if ($inProgress) {
            return 'in_progress';
        }

        if ($hasPassed) {
            return 'passed';
        }

        if ($hasFailed && $attempts > 0) {
            return 'failed';
        }

        if (!$isSessionActive) {
            return 'expired';
        }

        if ($exam->available_from && $exam->available_from > $now) {
            return 'upcoming';
        }

        if ($exam->available_until && $exam->available_until < $now) {
            return 'expired';
        }

        return 'available';
    }
}
