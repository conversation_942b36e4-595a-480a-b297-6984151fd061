<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\TrainingSession;
use App\Models\TrainingDomain;
use App\Models\Enrollment;
use Illuminate\Support\Facades\Auth;

class SessionController extends Controller
{
    /**
     * Affiche la liste des sessions de formation disponibles
     */
    public function index(Request $request)
    {
        // Récupérer l'utilisateur connecté (apprenant)
        $student = Auth::user();

        // Initialiser la requête
        $query = TrainingSession::with(['trainingDomain', 'trainer'])
            ->where('active', true)
            ->withCount('enrollments');

        // Appliquer la recherche si elle existe
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function($q) use ($searchTerm) {
                $q->where('title', 'like', $searchTerm)
                  ->orWhere('description', 'like', $searchTerm)
                  ->orWhereHas('trainingDomain', function($q) use ($searchTerm) {
                      $q->where('name', 'like', $searchTerm);
                  });
            });
        }

        // Filtrer par domaine de formation
        if ($request->has('domain_id') && !empty($request->domain_id)) {
            $query->where('training_domain_id', $request->domain_id);
        }

        // Récupérer les sessions paginées
        $sessions = $query->orderBy('start_date', 'desc')
            ->paginate(9)
            ->withQueryString();

        // Récupérer les niveaux complétés par l'étudiant pour chaque département
        $completedLevels = $student->getCompletedLevelsByDepartment();

        // Ajouter les informations de verrouillage à chaque session
        $sessions->getCollection()->transform(function ($session) use ($student, $completedLevels) {
            $session->is_locked = $this->isSessionLocked($session, $completedLevels);
            $session->lock_reason = $this->getLockReason($session, $completedLevels);
            $session->required_level = $this->getRequiredPreviousLevel($session);
            return $session;
        });

        // Récupérer les domaines pour le filtre
        $domains = TrainingDomain::where('active', true)
            ->orderBy('name')
            ->get();

        // Retourner la vue avec les sessions
        return Inertia::render('Student/Sessions/Index', [
            'sessions' => $sessions,
            'domains' => $domains,
            'completedLevels' => $completedLevels,
            'filters' => [
                'search' => $request->search ?? '',
                'domain_id' => $request->domain_id ?? '',
            ]
        ]);
    }

    /**
     * Affiche les détails d'une session de formation
     */
    public function show(string $id)
    {
        // Récupérer l'utilisateur connecté (apprenant)
        $student = Auth::user();

        // Récupérer la session avec ses relations et créneaux horaires
        $session = TrainingSession::with(['trainingDomain', 'trainer', 'timeSlots'])
            ->where('active', true)
            ->withCount('enrollments')
            ->findOrFail($id);

        // Vérifier si l'apprenant est déjà inscrit à cette session
        $enrollment = Enrollment::where('user_id', $student->id)
            ->where('training_session_id', $session->id)
            ->first();

        $isEnrolled = !is_null($enrollment);

        // Vérifier si l'apprenant peut s'inscrire à cette session
        $canEnroll = !$isEnrolled &&
            ($session->max_participants === null || $session->enrollments_count < $session->max_participants) &&
            new \DateTime($session->start_date) > new \DateTime();

        // Récupérer les cours associés à cette session
        $courses = $session->courses()
            ->where('active', true)
            ->orderBy('order')
            ->get();

        // Retourner la vue avec les données
        return Inertia::render('Student/Sessions/Show', [
            'session' => $session,
            'courses' => $courses,
            'enrollment' => $enrollment,
            'isEnrolled' => $isEnrolled,
            'canEnroll' => $canEnroll,
        ]);
    }

    /**
     * Vérifie si une session est verrouillée pour l'étudiant
     */
    private function isSessionLocked($session, $completedLevels)
    {
        // Si la session n'a pas de niveau ou de département, elle n'est pas verrouillée
        if (!$session->level || !$session->department) {
            return false;
        }

        // Extraire le numéro du niveau (ex: "Niveau 2" -> 2)
        $currentLevelNumber = (int) str_replace('Niveau ', '', $session->level);

        // Si c'est le niveau 1, il n'est jamais verrouillé
        if ($currentLevelNumber <= 1) {
            return false;
        }

        // Vérifier si le niveau précédent a été complété dans le même département
        $requiredLevel = "Niveau " . ($currentLevelNumber - 1);
        $departmentCompletedLevels = $completedLevels[$session->department] ?? [];

        return !in_array($requiredLevel, $departmentCompletedLevels);
    }

    /**
     * Retourne la raison du verrouillage d'une session
     */
    private function getLockReason($session, $completedLevels)
    {
        if (!$this->isSessionLocked($session, $completedLevels)) {
            return null;
        }

        $currentLevelNumber = (int) str_replace('Niveau ', '', $session->level);
        $requiredLevel = "Niveau " . ($currentLevelNumber - 1);

        return "Cette session sera disponible lorsque vous aurez terminé avec succès la session du {$requiredLevel} du département {$session->department}";
    }

    /**
     * Retourne le niveau précédent requis pour une session
     */
    private function getRequiredPreviousLevel($session)
    {
        if (!$session->level || !$session->department) {
            return null;
        }

        $currentLevelNumber = (int) str_replace('Niveau ', '', $session->level);

        if ($currentLevelNumber <= 1) {
            return null;
        }

        return "Niveau " . ($currentLevelNumber - 1);
    }
}
