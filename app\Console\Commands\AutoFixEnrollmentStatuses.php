<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Enrollment;
use App\Models\ExamResult;

class AutoFixEnrollmentStatuses extends Command
{
    protected $signature = 'fix:enrollment-statuses-auto';
    protected $description = 'Automatically fix enrollment statuses for students with exam results';

    public function handle()
    {
        $this->info('🔧 Auto-fixing enrollment status issues...');
        
        // Find students with exam results but non-approved enrollments
        $studentsWithResults = ExamResult::select('user_id')
            ->distinct()
            ->get()
            ->pluck('user_id');
        
        $this->line("Students with exam results: {$studentsWithResults->count()}");
        
        $issuesFound = 0;
        $issuesFixed = 0;
        
        foreach ($studentsWithResults as $userId) {
            $enrollments = Enrollment::where('user_id', $userId)
                ->where('status', '!=', 'approved')
                ->with(['user', 'trainingSession'])
                ->get();
            
            foreach ($enrollments as $enrollment) {
                $issuesFound++;
                $this->warn("❌ Issue found: {$enrollment->user->name} - Enrollment status: '{$enrollment->status}' for session: {$enrollment->trainingSession->title}");
                
                // Auto-fix: If student has exam results, they should have approved enrollment
                $enrollment->status = 'approved';
                $enrollment->save();
                $issuesFixed++;
                $this->info("✅ Auto-fixed enrollment for {$enrollment->user->name}");
            }
        }
        
        $this->info("Summary:");
        $this->line("- Issues found: {$issuesFound}");
        $this->line("- Issues fixed: {$issuesFixed}");
        
        if ($issuesFixed > 0) {
            $this->info("🎉 Auto-fixed {$issuesFixed} enrollment status issues!");
            $this->line("Students can now see their exam results in the interface.");
        } else {
            $this->info("✅ No enrollment status issues found.");
        }
        
        return Command::SUCCESS;
    }
}
