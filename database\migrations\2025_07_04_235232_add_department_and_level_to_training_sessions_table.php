<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('training_sessions', function (Blueprint $table) {
            // Ajouter le champ département avec les options spécifiées
            $table->enum('department', ['Secourisme', 'Langue', 'Formation à la carte'])
                  ->after('training_domain_id')
                  ->comment('Département de la formation');

            // Ajouter le champ niveau avec les options spécifiées
            $table->enum('level', ['Niveau 1', 'Niveau 2', 'Niveau 3', 'Niveau 4', 'Niveau 5', 'Niveau 6', 'Niveau 7'])
                  ->after('department')
                  ->comment('Niveau de la formation');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('training_sessions', function (Blueprint $table) {
            $table->dropColumn(['department', 'level']);
        });
    }
};
