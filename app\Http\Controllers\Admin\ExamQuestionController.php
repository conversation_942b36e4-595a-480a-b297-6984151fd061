<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\ExamQuestion;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ExamQuestionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $examId = $request->query('exam_id');

        if (!$examId) {
            return redirect()->route('admin.exams.index')
                ->with('error', 'Veuillez sélectionner un examen pour voir ses questions.');
        }

        $exam = Exam::with('trainingSession')->findOrFail($examId);
        $questions = ExamQuestion::where('exam_id', $examId)
            ->orderBy('order')
            ->get();

        return Inertia::render('Admin/ExamQuestions/Index', [
            'exam' => $exam,
            'questions' => $questions
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $examId = $request->query('exam_id');

        if (!$examId) {
            return redirect()->route('admin.exams.index')
                ->with('error', 'Veuillez sélectionner un examen pour ajouter une question.');
        }

        $exam = Exam::with('trainingSession')->findOrFail($examId);

        // Déterminer l'ordre de la prochaine question
        $nextOrder = ExamQuestion::where('exam_id', $examId)->max('order') + 1;

        return Inertia::render('Admin/ExamQuestions/Create', [
            'exam' => $exam,
            'nextOrder' => $nextOrder
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'exam_id' => 'required|exists:exams,id',
            'question_text' => 'required|string',
            'question_type' => 'required|in:multiple_choice,single_choice,text,file_upload',
            'options' => 'nullable',
            'correct_options' => 'nullable',
            'correct_answer' => 'nullable|string',
            'explanation' => 'nullable|string',
            'file_type_allowed' => 'nullable|string',
            'points' => 'required|integer|min:1',
            'order' => 'required|integer|min:0',
        ]);

        // Traitement selon le type de question
        if ($validated['question_type'] === 'multiple_choice' || $validated['question_type'] === 'single_choice') {
            // S'assurer que les options sont au format JSON
            if (isset($validated['options']) && !is_string($validated['options'])) {
                $validated['options'] = json_encode($validated['options']);
            }

            // S'assurer que les options correctes sont au format JSON
            if (isset($validated['correct_options']) && !is_string($validated['correct_options'])) {
                $validated['correct_options'] = json_encode($validated['correct_options']);
            }

            // Si les options sont vides, créer des options par défaut
            if (empty($validated['options'])) {
                $validated['options'] = json_encode([
                    'A' => 'Option A',
                    'B' => 'Option B',
                    'C' => 'Option C',
                    'D' => 'Option D'
                ]);
            }

            // Si les options correctes sont vides, ne pas définir d'option par défaut
            if (empty($validated['correct_options'])) {
                $validated['correct_options'] = json_encode([]);
            }
        } else if ($validated['question_type'] === 'text') {
            // Pour les questions de type texte, on utilise correct_answer
            $validated['options'] = null;
            $validated['correct_options'] = null;
        } else if ($validated['question_type'] === 'file_upload') {
            // Pour les questions de type fichier, on n'a pas d'options ni de réponses correctes
            $validated['options'] = null;
            $validated['correct_options'] = null;
            $validated['correct_answer'] = null;
        }

        ExamQuestion::create($validated);

        return redirect()->route('admin.exam-questions.index', ['exam_id' => $validated['exam_id']])
            ->with('success', 'Question ajoutée avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $question = ExamQuestion::with('exam')->findOrFail($id);

        return Inertia::render('Admin/ExamQuestions/Show', [
            'question' => $question
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $question = ExamQuestion::with('exam')->findOrFail($id);

        return Inertia::render('Admin/ExamQuestions/Edit', [
            'question' => $question,
            'exam' => $question->exam
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $question = ExamQuestion::findOrFail($id);

        $validated = $request->validate([
            'question_text' => 'required|string',
            'question_type' => 'required|in:multiple_choice,single_choice,text,file_upload',
            'options' => 'nullable',
            'correct_options' => 'nullable',
            'correct_answer' => 'nullable|string',
            'explanation' => 'nullable|string',
            'file_type_allowed' => 'nullable|string',
            'points' => 'required|integer|min:1',
            'order' => 'nullable|integer|min:0',
        ]);

        // Si l'ordre n'est pas spécifié, conserver l'ordre actuel
        if (!isset($validated['order'])) {
            $validated['order'] = $question->order;
        }

        // Traitement selon le type de question
        if ($validated['question_type'] === 'multiple_choice' || $validated['question_type'] === 'single_choice') {
            // S'assurer que les options sont au format JSON
            if (isset($validated['options']) && !is_string($validated['options'])) {
                $validated['options'] = json_encode($validated['options']);
            }

            // S'assurer que les options correctes sont au format JSON
            if (isset($validated['correct_options']) && !is_string($validated['correct_options'])) {
                $validated['correct_options'] = json_encode($validated['correct_options']);
            }

            // Si les options sont vides, créer des options par défaut
            if (empty($validated['options'])) {
                $validated['options'] = json_encode([
                    'A' => 'Option A',
                    'B' => 'Option B',
                    'C' => 'Option C',
                    'D' => 'Option D'
                ]);
            }

            // Si les options correctes sont vides, ne pas définir d'option par défaut
            if (empty($validated['correct_options'])) {
                $validated['correct_options'] = json_encode([]);
            }
        } else if ($validated['question_type'] === 'text') {
            // Pour les questions de type texte, on utilise correct_answer
            $validated['options'] = null;
            $validated['correct_options'] = null;
        } else if ($validated['question_type'] === 'file_upload') {
            // Pour les questions de type fichier, on n'a pas d'options ni de réponses correctes
            $validated['options'] = null;
            $validated['correct_options'] = null;
            $validated['correct_answer'] = null;
        }

        $question->update($validated);

        return redirect()->route('admin.exam-questions.index', ['exam_id' => $question->exam_id])
            ->with('success', 'Question mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $question = ExamQuestion::findOrFail($id);
        $examId = $question->exam_id;

        $question->delete();

        // Réorganiser les questions restantes
        $remainingQuestions = ExamQuestion::where('exam_id', $examId)
            ->orderBy('order')
            ->get();

        $order = 1;
        foreach ($remainingQuestions as $q) {
            $q->update(['order' => $order]);
            $order++;
        }

        return redirect()->route('admin.exam-questions.index', ['exam_id' => $examId])
            ->with('success', 'Question supprimée avec succès.');
    }
}
