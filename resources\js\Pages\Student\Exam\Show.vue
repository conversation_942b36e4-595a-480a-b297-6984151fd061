<template>
  <Head :title="exam.title" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Examen: {{ exam.title }}
        </h2>
        <div v-if="!examStarted" class="flex space-x-2">
          <Link :href="route('student.courses.show', exam.training_session_id)" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
            Retour au cours
          </Link>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Informations sur l'examen -->
        <div v-if="!examStarted && !examCompleted" class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 text-gray-900">
            <div class="mb-4">
              <h3 class="text-lg font-semibold mb-2">Description</h3>
              <p class="text-gray-700">{{ exam.description || 'Aucune description' }}</p>
            </div>
            
            <div class="mb-4">
              <h3 class="text-lg font-semibold mb-2">Informations</h3>
              <ul class="list-disc list-inside space-y-1 text-gray-700">
                <li>Type d'examen: {{ formatExamType(exam.type) }}</li>
                <li>Durée: {{ exam.duration }} minutes</li>
                <li>Score minimum pour réussir: {{ exam.passing_score }}%</li>
                <li>Tentatives autorisées: {{ exam.attempts_allowed }}</li>
                <li>Tentatives effectuées: {{ previousAttempts.length }}</li>
              </ul>
            </div>
            
            <div class="mb-4">
              <h3 class="text-lg font-semibold mb-2">Instructions</h3>
              <ul class="list-disc list-inside space-y-1 text-gray-700">
                <li>Une fois l'examen commencé, vous ne pourrez pas le quitter avant de l'avoir terminé.</li>
                <li>Assurez-vous d'avoir suffisamment de temps pour compléter l'examen.</li>
                <li>Répondez à toutes les questions avant de soumettre l'examen.</li>
                <li v-if="exam.type === 'mcq'">Pour les questions à choix multiples, sélectionnez toutes les réponses que vous pensez être correctes.</li>
                <li v-if="exam.type === 'text'">Pour les questions à réponse libre, soyez concis et précis.</li>
                <li v-if="exam.type === 'file'">Pour les soumissions de fichiers, assurez-vous que votre fichier est au format demandé.</li>
              </ul>
            </div>
            
            <div class="flex justify-end">
              <button 
                @click="startExam" 
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                :disabled="previousAttempts.length >= exam.attempts_allowed && !hasPassed"
                :class="{ 'opacity-50 cursor-not-allowed': previousAttempts.length >= exam.attempts_allowed && !hasPassed }"
              >
                {{ hasPassed ? 'Voir mes résultats' : 'Commencer l\'examen' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Examen en cours (QCM) -->
        <div v-if="examStarted && exam.type === 'mcq'" class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 text-gray-900">
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">Questions</h3>
              <div class="text-sm text-gray-600">
                Temps restant: {{ formatTime(remainingTime) }}
              </div>
            </div>
            
            <div class="space-y-8">
              <div v-for="(question, index) in questions" :key="question.id" class="border-b pb-6 last:border-b-0">
                <div class="mb-2">
                  <span class="font-semibold">Question {{ index + 1 }}:</span> {{ question.question }}
                </div>
                
                <div class="space-y-2 mt-3">
                  <div v-for="(option, optIndex) in question.options" :key="optIndex" class="flex items-center">
                    <input 
                      :id="`q${question.id}_opt${optIndex}`" 
                      type="checkbox" 
                      class="rounded border-gray-300 text-blue-600 shadow-sm focus:ring-blue-500"
                      v-model="answers[question.id][optIndex]"
                    />
                    <label :for="`q${question.id}_opt${optIndex}`" class="ml-2 text-gray-700">{{ option }}</label>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="flex justify-end mt-6">
              <button 
                @click="submitExam" 
                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Soumettre l'examen
              </button>
            </div>
          </div>
        </div>

        <!-- Examen en cours (Texte) -->
        <div v-if="examStarted && exam.type === 'text'" class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 text-gray-900">
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">Questions</h3>
              <div class="text-sm text-gray-600">
                Temps restant: {{ formatTime(remainingTime) }}
              </div>
            </div>
            
            <div class="space-y-8">
              <div v-for="(question, index) in questions" :key="question.id" class="border-b pb-6 last:border-b-0">
                <div class="mb-2">
                  <span class="font-semibold">Question {{ index + 1 }}:</span> {{ question.question }}
                </div>
                
                <div class="mt-3">
                  <textarea 
                    :id="`q${question.id}_answer`" 
                    v-model="textAnswers[question.id]"
                    class="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                    rows="6"
                    placeholder="Votre réponse..."
                  ></textarea>
                </div>
              </div>
            </div>
            
            <div class="flex justify-end mt-6">
              <button 
                @click="submitTextExam" 
                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Soumettre l'examen
              </button>
            </div>
          </div>
        </div>

        <!-- Examen en cours (Fichier) -->
        <div v-if="examStarted && exam.type === 'file'" class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 text-gray-900">
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">Soumission de fichier</h3>
              <div class="text-sm text-gray-600">
                Temps restant: {{ formatTime(remainingTime) }}
              </div>
            </div>
            
            <div class="space-y-4">
              <div v-for="(question, index) in questions" :key="question.id" class="border-b pb-6 last:border-b-0">
                <div class="mb-2">
                  <span class="font-semibold">Consigne {{ index + 1 }}:</span> {{ question.question }}
                </div>
              </div>
              
              <div class="mt-4">
                <label for="file_submission" class="block text-sm font-medium text-gray-700 mb-2">
                  Fichier à soumettre
                </label>
                <input 
                  id="file_submission" 
                  type="file" 
                  class="block w-full text-sm text-gray-500
                    file:mr-4 file:py-2 file:px-4
                    file:rounded-md file:border-0
                    file:text-sm file:font-semibold
                    file:bg-blue-50 file:text-blue-700
                    hover:file:bg-blue-100"
                  @change="handleFileUpload"
                />
                <p class="mt-1 text-sm text-gray-500">
                  Formats acceptés: PDF, DOC, DOCX, ZIP (max. 10MB)
                </p>
              </div>
            </div>
            
            <div class="flex justify-end mt-6">
              <button 
                @click="submitFileExam" 
                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                :disabled="!fileSubmission"
                :class="{ 'opacity-50 cursor-not-allowed': !fileSubmission }"
              >
                Soumettre l'examen
              </button>
            </div>
          </div>
        </div>

        <!-- Résultats d'examen -->
        <div v-if="examCompleted" class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <div class="mb-6">
              <h3 class="text-lg font-semibold mb-2">Résultats de l'examen</h3>
              
              <div class="flex items-center mb-4">
                <div :class="{
                  'text-2xl font-bold mr-2': true,
                  'text-green-600': selectedResult.passed,
                  'text-red-600': !selectedResult.passed
                }">
                  {{ selectedResult.passed ? 'Réussi' : 'Échoué' }}
                </div>
                <div class="text-gray-600">
                  Score: {{ selectedResult.score }}/{{ selectedResult.total_points }} 
                  ({{ Math.round((selectedResult.score / selectedResult.total_points) * 100) }}%)
                </div>
              </div>
              
              <div v-if="selectedResult.feedback" class="mb-4 p-4 bg-blue-50 rounded-md">
                <h4 class="font-semibold text-blue-800 mb-1">Commentaires du formateur:</h4>
                <p class="text-blue-700">{{ selectedResult.feedback }}</p>
              </div>
            </div>
            
            <!-- Détails des réponses (pour QCM) -->
            <div v-if="exam.type === 'mcq' && selectedResult.answers" class="space-y-6">
              <h4 class="font-semibold text-lg">Détail des réponses</h4>
              
              <div v-for="(question, index) in questions" :key="question.id" class="border-b pb-6 last:border-b-0">
                <div class="mb-2">
                  <span class="font-semibold">Question {{ index + 1 }}:</span> {{ question.question }}
                </div>
                
                <div class="space-y-2 mt-3">
                  <div v-for="(option, optIndex) in question.options" :key="optIndex" class="flex items-center">
                    <div :class="{
                      'w-5 h-5 flex items-center justify-center rounded-sm mr-2': true,
                      'bg-green-100 text-green-700 border border-green-500': isCorrectAnswer(question, optIndex),
                      'bg-red-100 text-red-700 border border-red-500': isWrongAnswer(question, optIndex, selectedResult),
                      'bg-gray-100 text-gray-700 border border-gray-300': !isAnswerSelected(question.id, optIndex, selectedResult) && !isCorrectAnswer(question, optIndex)
                    }">
                      <svg v-if="isCorrectAnswer(question, optIndex)" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      <svg v-if="isWrongAnswer(question, optIndex, selectedResult)" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <span class="text-gray-700">{{ option }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Fichier soumis (pour examen de type fichier) -->
            <div v-if="exam.type === 'file' && selectedResult.file_submission" class="mt-6">
              <h4 class="font-semibold text-lg mb-2">Fichier soumis</h4>
              <a 
                :href="'/storage/' + selectedResult.file_submission" 
                target="_blank" 
                class="text-blue-600 hover:text-blue-800 flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
                Télécharger le fichier
              </a>
            </div>
            
            <div class="flex justify-between mt-8">
              <Link :href="route('student.courses.show', exam.training_session_id)" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                Retour au cours
              </Link>
              
              <button 
                v-if="!selectedResult.passed && previousAttempts.length < exam.attempts_allowed"
                @click="retryExam" 
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Réessayer l'examen
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  exam: Object,
  questions: Array,
  previousAttempts: Array,
});

// État
const examStarted = ref(false);
const examCompleted = ref(false);
const startTime = ref(null);
const remainingTime = ref(props.exam.duration * 60); // en secondes
const answers = ref({});
const textAnswers = ref({});
const fileSubmission = ref(null);
const selectedResult = ref(props.previousAttempts.length > 0 ? props.previousAttempts[0] : {});
const timer = ref(null);

// Initialiser les réponses pour les QCM
props.questions.forEach(question => {
  answers.value[question.id] = {};
  question.options.forEach((_, index) => {
    answers.value[question.id][index] = false;
  });
});

// Computed
const hasPassed = computed(() => {
  return props.previousAttempts.some(attempt => attempt.passed);
});

// Méthodes
const formatExamType = (type) => {
  const types = {
    'mcq': 'QCM',
    'text': 'Rédaction',
    'file': 'Soumission de fichier'
  };
  return types[type] || type;
};

const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
};

const startExam = () => {
  if (hasPassed.value || props.previousAttempts.length > 0) {
    // Si l'examen a déjà été passé, afficher les résultats
    examCompleted.value = true;
    return;
  }
  
  examStarted.value = true;
  startTime.value = new Date();
  
  // Démarrer le timer
  timer.value = setInterval(() => {
    remainingTime.value--;
    
    if (remainingTime.value <= 0) {
      clearInterval(timer.value);
      submitExam();
    }
  }, 1000);
};

const submitExam = () => {
  clearInterval(timer.value);
  
  // Préparer les données pour la soumission
  const formattedAnswers = {};
  Object.keys(answers.value).forEach(questionId => {
    formattedAnswers[questionId] = [];
    Object.keys(answers.value[questionId]).forEach(optIndex => {
      if (answers.value[questionId][optIndex]) {
        formattedAnswers[questionId].push(parseInt(optIndex));
      }
    });
  });
  
  // Soumettre l'examen
  router.post(route('student.exams.submit', props.exam.id), {
    answers: formattedAnswers,
    started_at: startTime.value,
    completed_at: new Date(),
  });
};

const submitTextExam = () => {
  clearInterval(timer.value);
  
  // Soumettre l'examen
  router.post(route('student.exams.submit', props.exam.id), {
    answers: textAnswers.value,
    started_at: startTime.value,
    completed_at: new Date(),
  });
};

const handleFileUpload = (event) => {
  fileSubmission.value = event.target.files[0];
};

const submitFileExam = () => {
  clearInterval(timer.value);
  
  // Créer un FormData pour l'upload de fichier
  const formData = new FormData();
  formData.append('file', fileSubmission.value);
  formData.append('started_at', startTime.value);
  formData.append('completed_at', new Date());
  
  // Soumettre l'examen
  router.post(route('student.exams.submit', props.exam.id), formData);
};

const retryExam = () => {
  examCompleted.value = false;
  examStarted.value = false;
  
  // Réinitialiser les réponses
  props.questions.forEach(question => {
    answers.value[question.id] = {};
    question.options.forEach((_, index) => {
      answers.value[question.id][index] = false;
    });
  });
  
  textAnswers.value = {};
  fileSubmission.value = null;
  
  // Réinitialiser le timer
  remainingTime.value = props.exam.duration * 60;
};

const isCorrectAnswer = (question, optIndex) => {
  return question.correct_answers && question.correct_answers.includes(optIndex);
};

const isAnswerSelected = (questionId, optIndex, result) => {
  if (!result.answers) return false;
  return result.answers[questionId] && result.answers[questionId].includes(optIndex);
};

const isWrongAnswer = (question, optIndex, result) => {
  // Réponse sélectionnée mais incorrecte
  return isAnswerSelected(question.id, optIndex, result) && !isCorrectAnswer(question, optIndex);
};

// Lifecycle hooks
onMounted(() => {
  // Si l'utilisateur a déjà passé l'examen et l'a réussi, afficher directement les résultats
  if (hasPassed.value) {
    examCompleted.value = true;
  }
});

onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
  }
});
</script>
