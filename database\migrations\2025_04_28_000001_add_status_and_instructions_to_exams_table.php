<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('exams', function (Blueprint $table) {
            if (!Schema::hasColumn('exams', 'status')) {
                $table->string('status')->default('draft')->after('passing_score');
            }
            if (!Schema::hasColumn('exams', 'instructions')) {
                $table->text('instructions')->nullable()->after('description');
            }
        });

        // Mettre à jour les statuts existants
        DB::table('exams')
            ->where('is_published', true)
            ->update(['status' => 'published']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exams', function (Blueprint $table) {
            if (Schema::hasColumn('exams', 'status')) {
                $table->dropColumn('status');
            }
            if (Schema::hasColumn('exams', 'instructions')) {
                $table->dropColumn('instructions');
            }
        });
    }
};
