<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Champs qui seront collectés lors de l'inscription aux sessions
            $table->string('phone', 30)->nullable()->after('email');
            $table->string('id_card_number', 50)->nullable()->after('phone');
            $table->string('passport_number', 50)->nullable()->after('id_card_number');
            $table->date('birth_date')->nullable()->after('passport_number');
            $table->string('profession', 100)->nullable()->after('birth_date');
            $table->string('company', 100)->nullable()->after('profession');
            $table->text('address')->nullable()->after('company');
            $table->string('discovery_source', 50)->nullable()->after('address');
            $table->string('discovery_source_other', 100)->nullable()->after('discovery_source');
            $table->text('notes')->nullable()->after('discovery_source_other');
            $table->boolean('is_foreign_student')->default(false)->after('notes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone',
                'id_card_number',
                'passport_number',
                'birth_date',
                'profession',
                'company',
                'address',
                'discovery_source',
                'discovery_source_other',
                'notes',
                'is_foreign_student'
            ]);
        });
    }
};
