<template>
  <Head :title="`Conseil: ${firstAidTip.title}`" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Conseil: {{ firstAidTip.title }}
        </h2>
        <Link :href="route('admin.first-aid-tips.index')" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
          Retour à la liste
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
        <!-- Informations du conseil -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Informations du conseil</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700">Titre</label>
                <p class="mt-1 text-sm text-gray-900">{{ firstAidTip.title }}</p>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">Statut</label>
                <span :class="[
                  'mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                  firstAidTip.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                ]">
                  {{ firstAidTip.active ? 'Actif' : 'Inactif' }}
                </span>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">Ordre d'affichage</label>
                <p class="mt-1 text-sm text-gray-900">{{ firstAidTip.order }}</p>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">Nombre de matériels</label>
                <p class="mt-1 text-sm text-gray-900">{{ firstAidTip.materials ? firstAidTip.materials.length : 0 }}</p>
              </div>
            </div>
            
            <div class="mt-6" v-if="firstAidTip.description">
              <label class="block text-sm font-medium text-gray-700">Description</label>
              <p class="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{{ firstAidTip.description }}</p>
            </div>

            <!-- Actions -->
            <div class="flex justify-between items-center mb-6 mt-6">
              <h3 class="text-lg font-semibold">Gestion du conseil</h3>
              <div class="flex space-x-2">
                <Link
                  :href="route('admin.first-aid-tip-materials.create', { first_aid_tip_id: firstAidTip.id })"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Ajouter un matériel
                </Link>
                <Link
                  :href="route('admin.first-aid-tips.edit', firstAidTip.id)"
                  class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                >
                  Modifier le conseil
                </Link>
              </div>
            </div>
          </div>
        </div>

        <!-- Liste des matériels -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-semibold">Matériels pédagogiques</h3>
              <Link
                :href="route('admin.first-aid-tip-materials.index', { first_aid_tip_id: firstAidTip.id })"
                class="text-blue-600 hover:text-blue-900"
              >
                Voir tous les matériels
              </Link>
            </div>

            <div v-if="firstAidTip.materials && firstAidTip.materials.length > 0" class="space-y-4">
              <div
                v-for="material in firstAidTip.materials"
                :key="material.id"
                class="border border-gray-200 rounded-lg p-4"
              >
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <h4 class="font-medium text-gray-900">{{ material.title }}</h4>
                    <p class="text-sm text-gray-600 mt-1" v-if="material.description">
                      {{ material.description }}
                    </p>
                    <div class="flex items-center space-x-4 mt-2">
                      <span :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        getTypeColor(material.type)
                      ]">
                        {{ getTypeLabel(material.type) }}
                      </span>
                      <span :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        material.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      ]">
                        {{ material.active ? 'Actif' : 'Inactif' }}
                      </span>
                    </div>
                  </div>
                  <div class="flex space-x-2 ml-4">
                    <Link
                      :href="route('admin.first-aid-tip-materials.show', material.id)"
                      class="text-blue-600 hover:text-blue-900 text-sm"
                    >
                      Voir
                    </Link>
                    <Link
                      :href="route('admin.first-aid-tip-materials.edit', material.id)"
                      class="text-indigo-600 hover:text-indigo-900 text-sm"
                    >
                      Modifier
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="text-center py-8 text-gray-500">
              <p>Aucun matériel pédagogique pour ce conseil.</p>
              <Link
                :href="route('admin.first-aid-tip-materials.create', { first_aid_tip_id: firstAidTip.id })"
                class="mt-2 inline-block text-blue-600 hover:text-blue-900"
              >
                Ajouter le premier matériel
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  firstAidTip: Object,
});

// Méthodes utilitaires
const getTypeLabel = (type) => {
  const labels = {
    text: 'Texte',
    pdf: 'PDF',
    video: 'Vidéo',
    audio: 'Audio',
    image: 'Image',
    archive: 'Archive',
    gallery: 'Galerie',
    embed_video: 'Vidéo externe'
  };
  return labels[type] || type;
};

const getTypeColor = (type) => {
  const colors = {
    text: 'bg-gray-100 text-gray-800',
    pdf: 'bg-red-100 text-red-800',
    video: 'bg-blue-100 text-blue-800',
    audio: 'bg-green-100 text-green-800',
    image: 'bg-yellow-100 text-yellow-800',
    archive: 'bg-purple-100 text-purple-800',
    gallery: 'bg-pink-100 text-pink-800',
    embed_video: 'bg-indigo-100 text-indigo-800'
  };
  return colors[type] || 'bg-gray-100 text-gray-800';
};
</script>
