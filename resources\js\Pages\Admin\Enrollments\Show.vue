<template>
  <Head :title="'Inscription - ' + enrollment.user.name" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Détails de l'inscription
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Bouton de retour -->
        <div class="mb-6">
          <Link :href="route('admin.enrollments.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Retour à la liste
          </Link>
        </div>

        <!-- Message de succès -->
        <div v-if="$page.props.flash.success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span class="block sm:inline">{{ $page.props.flash.success }}</span>
        </div>

        <!-- Message d'erreur -->
        <div v-if="$page.props.flash.error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span class="block sm:inline">{{ $page.props.flash.error }}</span>
        </div>

        <!-- Informations de l'inscription -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-2xl font-semibold mb-4">Inscription de {{ enrollment.user.name }}</h3>
                <div class="mb-2">
                  <span :class="{
                    'px-2 py-1 text-xs rounded-full': true,
                    'bg-yellow-100 text-yellow-800': enrollment.status === 'pending',
                    'bg-green-100 text-green-800': enrollment.status === 'approved',
                    'bg-red-100 text-red-800': enrollment.status === 'rejected',
                    'bg-blue-100 text-blue-800': enrollment.status === 'completed'
                  }">
                    {{ formatStatus(enrollment.status) }}
                  </span>
                </div>
              </div>
              <div class="flex space-x-2">
                <Link :href="route('admin.enrollments.edit', enrollment.id)" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 0L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Modifier
                </Link>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <!-- Informations sur l'apprenant -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold mb-3">Informations sur l'apprenant</h4>
                <div class="mb-2">
                  <span class="font-semibold">Nom:</span> {{ enrollment.user.name }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Email:</span> {{ enrollment.user.email }}
                </div>
                <div class="mb-2" v-if="enrollment.user.phone">
                  <span class="font-semibold">Téléphone:</span> {{ enrollment.user.phone }}
                </div>
                <div class="mb-2" v-if="enrollment.user.address">
                  <span class="font-semibold">Adresse:</span> {{ enrollment.user.address }}
                </div>
              </div>

              <!-- Informations sur la formation -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold mb-3">Informations sur la formation</h4>
                <div class="mb-2">
                  <span class="font-semibold">Titre:</span> {{ enrollment.training_session.title }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Domaine:</span> {{ enrollment.training_session.training_domain.name }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Formateur:</span> {{ enrollment.training_session.trainer.name }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Dates:</span> {{ formatDate(enrollment.training_session.start_date) }} - {{ formatDate(enrollment.training_session.end_date) }}
                </div>
                <div class="mb-2" v-if="enrollment.training_session.location">
                  <span class="font-semibold">Lieu:</span> {{ enrollment.training_session.location }}
                </div>
              </div>
            </div>

            <!-- Détails de l'inscription -->
            <div class="mt-6">
              <h4 class="text-lg font-semibold mb-3">Détails de l'inscription</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span class="font-semibold">Date d'inscription:</span> {{ formatDate(enrollment.created_at) }}
                </div>
                <div>
                  <span class="font-semibold">Dernière mise à jour:</span> {{ formatDate(enrollment.updated_at) }}
                </div>
              </div>
              <div class="mt-4" v-if="enrollment.notes">
                <span class="font-semibold">Notes:</span>
                <div class="bg-gray-50 p-4 rounded mt-2">
                  <p>{{ enrollment.notes }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Informations de paiement -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4">Informations de paiement</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Détails du paiement -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-md font-semibold mb-3">Détails du paiement</h4>
                <div class="mb-2">
                  <span class="font-semibold">Montant:</span> {{ formatCurrency(enrollment.payment_amount) }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Statut:</span>
                  <span :class="getPaymentStatusClass(enrollment.payment_status)" class="ml-2 px-2 py-1 text-xs rounded-full">
                    {{ getPaymentStatusLabel(enrollment.payment_status) }}
                  </span>
                </div>
                <div class="mb-2" v-if="enrollment.payment_date">
                  <span class="font-semibold">Date de paiement:</span> {{ formatDate(enrollment.payment_date) }}
                </div>
                <div class="mb-2" v-if="enrollment.payment_due_date">
                  <span class="font-semibold">Date limite:</span> {{ formatDate(enrollment.payment_due_date) }}
                </div>
                <div class="mb-2" v-if="enrollment.payment_method">
                  <span class="font-semibold">Méthode:</span> {{ enrollment.payment_method }}
                </div>
              </div>

              <!-- Actions de paiement -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-md font-semibold mb-3">Actions</h4>

                <div class="mb-4">
                  <button
                    @click="showUpdatePaymentModal = true"
                    class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Mettre à jour le paiement
                  </button>
                </div>

                <div v-if="enrollment.payment_proof" class="mb-2">
                  <span class="font-semibold">Justificatif:</span>
                  <div class="mt-2">
                    <a
                      :href="`/storage/${enrollment.payment_proof}`"
                      target="_blank"
                      class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      Voir le justificatif
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Notes de paiement -->
            <div class="mt-4" v-if="enrollment.payment_notes">
              <span class="font-semibold">Notes de paiement:</span>
              <div class="bg-gray-50 p-4 rounded mt-2">
                <p>{{ enrollment.payment_notes }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Résultats d'examens -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <h3 class="text-lg font-semibold mb-4">Résultats d'examens</h3>

            <div v-if="enrollment.exam_results && enrollment.exam_results.length > 0" class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Examen</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Résultat</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="result in enrollment.exam_results" :key="result.id">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">{{ result.exam.title }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500">{{ formatDate(result.created_at) }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ result.score }}%</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="{
                        'px-2 py-1 text-xs rounded-full': true,
                        'bg-green-100 text-green-800': result.passed,
                        'bg-red-100 text-red-800': !result.passed
                      }">
                        {{ result.passed ? 'Réussi' : 'Échoué' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Link :href="route('admin.exam-results.show', result.id)" class="text-indigo-600 hover:text-indigo-900">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </Link>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-else class="text-gray-500 italic text-center py-4">
              Aucun résultat d'examen pour cette inscription.
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de mise à jour du paiement -->
    <div v-if="showUpdatePaymentModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg overflow-hidden shadow-xl max-w-lg w-full">
        <div class="p-6">
          <h3 class="text-lg font-semibold mb-4">Mettre à jour le paiement</h3>

          <form @submit.prevent="updatePayment">
            <div class="mb-4">
              <label for="payment_amount" class="block text-sm font-medium text-gray-700 mb-1">Montant</label>
              <input
                type="number"
                id="payment_amount"
                v-model="paymentForm.payment_amount"
                step="0.01"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
              <div v-if="paymentForm.errors.payment_amount" class="text-red-500 text-sm mt-1">
                {{ paymentForm.errors.payment_amount }}
              </div>
            </div>

            <div class="mb-4">
              <label for="payment_status" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
              <select
                id="payment_status"
                v-model="paymentForm.payment_status"
                class="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="unpaid">Non payé</option>
                <option value="pending">En attente</option>
                <option value="partial">Partiel</option>
                <option value="paid">Payé</option>
                <option value="refunded">Remboursé</option>
                <option value="cancelled">Annulé</option>
              </select>
              <div v-if="paymentForm.errors.payment_status" class="text-red-500 text-sm mt-1">
                {{ paymentForm.errors.payment_status }}
              </div>
            </div>

            <div class="mb-4">
              <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-1">Méthode de paiement</label>
              <select
                id="payment_method"
                v-model="paymentForm.payment_method"
                class="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="">Sélectionner une méthode</option>
                <option value="cash">Espèces</option>
                <option value="bank_transfer">Virement bancaire</option>
                <option value="check">Chèque</option>
                <option value="card">Carte bancaire</option>
                <option value="mobile_money">Mobile Money</option>
              </select>
              <div v-if="paymentForm.errors.payment_method" class="text-red-500 text-sm mt-1">
                {{ paymentForm.errors.payment_method }}
              </div>
            </div>

            <div class="mb-4">
              <label for="payment_date" class="block text-sm font-medium text-gray-700 mb-1">Date de paiement</label>
              <input
                type="date"
                id="payment_date"
                v-model="paymentForm.payment_date"
                class="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
              <div v-if="paymentForm.errors.payment_date" class="text-red-500 text-sm mt-1">
                {{ paymentForm.errors.payment_date }}
              </div>
            </div>

            <div class="mb-4">
              <label for="payment_due_date" class="block text-sm font-medium text-gray-700 mb-1">Date limite de paiement</label>
              <input
                type="date"
                id="payment_due_date"
                v-model="paymentForm.payment_due_date"
                class="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
              <div v-if="paymentForm.errors.payment_due_date" class="text-red-500 text-sm mt-1">
                {{ paymentForm.errors.payment_due_date }}
              </div>
            </div>

            <div class="mb-4">
              <label for="payment_notes" class="block text-sm font-medium text-gray-700 mb-1">Notes de paiement</label>
              <textarea
                id="payment_notes"
                v-model="paymentForm.payment_notes"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md"
              ></textarea>
              <div v-if="paymentForm.errors.payment_notes" class="text-red-500 text-sm mt-1">
                {{ paymentForm.errors.payment_notes }}
              </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                @click="showUpdatePaymentModal = false"
                class="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400"
              >
                Annuler
              </button>
              <button
                type="submit"
                class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                :disabled="paymentForm.processing"
              >
                Mettre à jour
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  enrollment: Object,
});

// État pour la modal de mise à jour du paiement
const showUpdatePaymentModal = ref(false);

// Formulaire pour la mise à jour du paiement
const paymentForm = useForm({
  payment_amount: props.enrollment.payment_amount || 0,
  payment_status: props.enrollment.payment_status || 'unpaid',
  payment_method: props.enrollment.payment_method || '',
  payment_date: props.enrollment.payment_date || '',
  payment_due_date: props.enrollment.payment_due_date || '',
  payment_notes: props.enrollment.payment_notes || '',
});

// Méthode pour mettre à jour le paiement
const updatePayment = () => {
  paymentForm.put(route('admin.enrollments.update-payment', props.enrollment.id), {
    onSuccess: () => {
      showUpdatePaymentModal.value = false;
    },
  });
};

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return 'Non définie';
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const formatStatus = (status) => {
  const statusMap = {
    'pending': 'En attente',
    'approved': 'Approuvée',
    'rejected': 'Rejetée',
    'completed': 'Terminée'
  };
  return statusMap[status] || status;
};

const formatCurrency = (amount) => {
  if (amount === null || amount === undefined) return '0,00 DT';
  return new Intl.NumberFormat('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(amount) + ' DT';
};

const getPaymentStatusLabel = (status) => {
  const statusMap = {
    'paid': 'Payé',
    'pending': 'En attente',
    'unpaid': 'Non payé',
    'partial': 'Partiel',
    'refunded': 'Remboursé',
    'cancelled': 'Annulé'
  };
  return statusMap[status] || status;
};

const getPaymentStatusClass = (status) => {
  const classMap = {
    'paid': 'bg-green-100 text-green-800',
    'pending': 'bg-yellow-100 text-yellow-800',
    'unpaid': 'bg-red-100 text-red-800',
    'partial': 'bg-blue-100 text-blue-800',
    'refunded': 'bg-purple-100 text-purple-800',
    'cancelled': 'bg-gray-100 text-gray-800'
  };
  return classMap[status] || 'bg-gray-100 text-gray-800';
};
</script>
