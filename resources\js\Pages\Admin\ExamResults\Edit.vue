<template>
  <Head title="Modifier un résultat d'examen" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Modifier un résultat d'examen
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link :href="route('admin.exam-results.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à la liste
              </Link>
            </div>

            <!-- Informations sur l'examen et l'apprenant -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 class="text-lg font-semibold mb-2">Informations sur l'examen</h3>
                  <p><span class="font-medium">Examen:</span> {{ examResult.exam.title }}</p>
                  <p><span class="font-medium">Formation:</span> {{ examResult.enrollment.training_session.title }}</p>
                  <p><span class="font-medium">Date de l'examen:</span> {{ formatDate(examResult.created_at) }}</p>
                </div>
                <div>
                  <h3 class="text-lg font-semibold mb-2">Informations sur l'apprenant</h3>
                  <p><span class="font-medium">Nom:</span> {{ examResult.enrollment.user.name }}</p>
                  <p><span class="font-medium">Email:</span> {{ examResult.enrollment.user.email }}</p>
                </div>
              </div>
            </div>

            <!-- Formulaire de modification de résultat d'examen -->
            <form @submit.prevent="submit">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Score -->
                <div>
                  <InputLabel for="score" value="Score (%)" />
                  <TextInput
                    id="score"
                    type="number"
                    class="mt-1 block w-full"
                    v-model="form.score"
                    min="0"
                    max="100"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.score" />
                </div>

                <!-- Résultat -->
                <div>
                  <InputLabel for="passed" value="Résultat" />
                  <div class="mt-2">
                    <label class="inline-flex items-center">
                      <input type="radio" class="form-radio" name="passed" :value="true" v-model="form.passed">
                      <span class="ml-2">Réussi</span>
                    </label>
                    <label class="inline-flex items-center ml-6">
                      <input type="radio" class="form-radio" name="passed" :value="false" v-model="form.passed">
                      <span class="ml-2">Échoué</span>
                    </label>
                  </div>
                  <InputError class="mt-2" :message="form.errors.passed" />
                </div>

                <!-- Feedback -->
                <div class="md:col-span-2">
                  <InputLabel for="feedback" value="Feedback" />
                  <textarea
                    id="feedback"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.feedback"
                    rows="4"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.feedback" />
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="flex items-center justify-end mt-6">
                <PrimaryButton class="ml-4" :disabled="form.processing">
                  Mettre à jour le résultat
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  examResult: Object,
});

// Formulaire
const form = useForm({
  score: props.examResult.score,
  passed: props.examResult.passed,
  feedback: props.examResult.feedback || '',
});

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return 'Non définie';
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const submit = () => {
  form.put(route('admin.exam-results.update', props.examResult.id));
};
</script>
