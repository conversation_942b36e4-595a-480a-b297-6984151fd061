<template>
  <Head :title="module ? `Ajouter un matériel au module ${module.title}` : `Ajouter un matériel au cours ${course.title}`" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        {{ module ? `Ajouter un matériel au module ${module.title}` : `Ajouter un matériel au cours ${course.title}` }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Fil d'Ariane -->
            <div class="mb-6">
              <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                  <li class="inline-flex items-center">
                    <Link :href="route('trainer.dashboard')" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                      <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                      </svg>
                      Tableau de bord
                    </Link>
                  </li>
                  <li>
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <Link :href="route('trainer.courses.index')" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                        Mes cours
                      </Link>
                    </div>
                  </li>
                  <li v-if="module">
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <Link :href="route('trainer.modules.index', { course_id: course.id })" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                        Modules du cours
                      </Link>
                    </div>
                  </li>
                  <li>
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <Link :href="route('trainer.course-materials.index', module ? { module_id: module.id } : { course_id: course.id })" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                        Matériels pédagogiques
                      </Link>
                    </div>
                  </li>
                  <li aria-current="page">
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">
                        Ajouter un matériel
                      </span>
                    </div>
                  </li>
                </ol>
              </nav>
            </div>

            <!-- Formulaire d'ajout de matériel -->
            <form @submit.prevent="submit" enctype="multipart/form-data">
              <!-- Informations de base -->
              <div class="mb-6">
                <InputLabel for="title" value="Titre" />
                <TextInput
                  id="title"
                  type="text"
                  class="mt-1 block w-full"
                  v-model="form.title"
                  required
                  autofocus
                />
                <InputError class="mt-2" :message="form.errors.title" />
              </div>

              <div class="mb-6">
                <InputLabel for="description" value="Description (optionnelle)" />
                <textarea
                  id="description"
                  v-model="form.description"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  rows="3"
                ></textarea>
                <InputError class="mt-2" :message="form.errors.description" />
              </div>

              <!-- Type de matériel -->
              <div class="mb-6">
                <InputLabel for="type" value="Type de matériel" />
                <select
                  id="type"
                  v-model="form.type"
                  @change="handleTypeChange"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  required
                >
                  <option value="">Sélectionnez un type</option>
                  <option v-for="type in materialTypes" :key="type.value" :value="type.value">
                    {{ type.label }}
                  </option>
                </select>
                <InputError class="mt-2" :message="form.errors.type" />
              </div>

              <!-- Contenu spécifique au type -->
              <!-- Texte -->
              <div v-if="form.type === 'text'" class="mb-6">
                <InputLabel for="content" value="Contenu textuel" />
                <textarea
                  id="content"
                  v-model="form.content"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  rows="10"
                  required
                ></textarea>
                <InputError class="mt-2" :message="form.errors.content" />
              </div>

              <!-- Vidéo externe -->
              <div v-if="form.type === 'embed_video'" class="mb-6">
                <InputLabel for="video_url" value="URL de la vidéo (YouTube, Dailymotion, Vimeo)" />
                <TextInput
                  id="video_url"
                  type="url"
                  class="mt-1 block w-full"
                  v-model="form.video_url"
                  placeholder="https://www.youtube.com/watch?v=..."
                  required
                />
                <p class="text-sm text-gray-500 mt-1">
                  Collez simplement l'URL de la vidéo, pas le code d'intégration.
                </p>
                <InputError class="mt-2" :message="form.errors.video_url" />
              </div>

              <!-- Fichier (PDF, Vidéo, Audio, Image, Archive) -->
              <div v-if="['pdf', 'video', 'audio', 'image', 'archive'].includes(form.type)" class="mb-6">
                <InputLabel for="file" :value="`Fichier ${form.type}`" />
                <input
                  id="file"
                  type="file"
                  class="mt-1 block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none"
                  @change="handleFileChange"
                  required
                />
                <p class="mt-1 text-sm text-gray-500">
                  {{ getFileTypeHelp(form.type) }}
                </p>
                <InputError class="mt-2" :message="form.errors.file" />
              </div>

              <!-- Galerie d'images -->
              <div v-if="form.type === 'gallery'" class="mb-6">
                <InputLabel for="gallery_files" value="Images pour la galerie" />
                <input
                  id="gallery_files"
                  type="file"
                  multiple
                  accept="image/*"
                  class="mt-1 block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none"
                  @change="handleGalleryFilesChange"
                  required
                />
                <p class="mt-1 text-sm text-gray-500">
                  Sélectionnez plusieurs images pour créer une galerie (JPG, PNG, GIF).
                </p>
                <InputError class="mt-2" :message="form.errors.gallery_files" />
              </div>

              <!-- Ordre -->
              <div class="mb-6">
                <InputLabel for="order" value="Ordre d'affichage" />
                <TextInput
                  id="order"
                  type="number"
                  class="mt-1 block w-full"
                  v-model="form.order"
                  min="1"
                />
                <InputError class="mt-2" :message="form.errors.order" />
              </div>

              <!-- Options -->
              <div class="mb-6">
                <div class="flex items-center">
                  <input
                    id="active"
                    type="checkbox"
                    v-model="form.active"
                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                  />
                  <label for="active" class="ml-2 text-sm text-gray-600">
                    Actif (visible pour les apprenants)
                  </label>
                </div>
                <InputError class="mt-2" :message="form.errors.active" />
              </div>

              <div v-if="['pdf', 'video', 'audio', 'image', 'archive'].includes(form.type)" class="mb-6">
                <div class="flex items-center">
                  <input
                    id="allow_download"
                    type="checkbox"
                    v-model="form.allow_download"
                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                  />
                  <label for="allow_download" class="ml-2 text-sm text-gray-600">
                    Autoriser le téléchargement
                  </label>
                </div>
                <InputError class="mt-2" :message="form.errors.allow_download" />
              </div>

              <div v-if="['pdf', 'video', 'audio', 'image'].includes(form.type)" class="mb-6">
                <div class="flex items-center">
                  <input
                    id="allow_online_viewing"
                    type="checkbox"
                    v-model="form.allow_online_viewing"
                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                  />
                  <label for="allow_online_viewing" class="ml-2 text-sm text-gray-600">
                    Autoriser la visualisation en ligne
                  </label>
                </div>
                <InputError class="mt-2" :message="form.errors.allow_online_viewing" />
              </div>

              <!-- Boutons -->
              <div class="flex justify-end">
                <Link
                  :href="route('trainer.course-materials.index', module ? { module_id: module.id } : { course_id: course.id })"
                  class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150 mr-2"
                >
                  Annuler
                </Link>
                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                  Ajouter
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  course: Object,
  module: Object,
  nextOrder: Number,
  materialTypes: Array,
});

// Formulaire
const form = useForm({
  title: '',
  description: '',
  course_id: props.course.id,
  module_id: props.module?.id || null,
  type: '',
  content: '',
  video_url: '',
  file: null,
  gallery_files: [],
  order: props.nextOrder || 1,
  active: true,
  allow_download: true,
  allow_online_viewing: true,
});

// Méthodes
const handleTypeChange = () => {
  // Réinitialiser les champs spécifiques au type
  form.content = '';
  form.video_url = '';
  form.file = null;
  form.gallery_files = [];
};

const handleFileChange = (e) => {
  if (e.target.files.length > 0) {
    form.file = e.target.files[0];
  } else {
    form.file = null;
  }
};

const handleGalleryFilesChange = (e) => {
  if (e.target.files.length > 0) {
    form.gallery_files = e.target.files;
  } else {
    form.gallery_files = [];
  }
};

const getFileTypeHelp = (type) => {
  const helps = {
    'pdf': 'Formats acceptés: PDF',
    'video': 'Formats acceptés: MP4, WebM, OGG',
    'audio': 'Formats acceptés: MP3, WAV, OGG',
    'image': 'Formats acceptés: JPG, PNG, GIF, SVG',
    'archive': 'Formats acceptés: ZIP, RAR, 7Z',
  };

  return helps[type] || '';
};

const submit = () => {
  if (form.type === 'gallery') {
    form.post(route('trainer.course-materials.store'), {
      forceFormData: true,
    });
  } else {
    form.post(route('trainer.course-materials.store'));
  }
};
</script>
