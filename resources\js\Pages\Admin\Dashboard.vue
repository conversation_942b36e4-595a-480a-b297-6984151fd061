<template>
  <Head title="Tableau de bord administrateur" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Tableau de bord administrateur
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Filtre de date -->
        <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Filtrer les statistiques</h3>
          <div class="flex flex-wrap items-end gap-4">
            <div>
              <label for="start-date" class="block text-sm font-medium text-gray-700 mb-1">Date de début</label>
              <input
                type="date"
                id="start-date"
                v-model="startDate"
                class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
              >
            </div>
            <div>
              <label for="end-date" class="block text-sm font-medium text-gray-700 mb-1">Date de fin</label>
              <input
                type="date"
                id="end-date"
                v-model="endDate"
                class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
              >
            </div>
            <div class="flex space-x-2">
              <button
                @click="applyDateFilter"
                class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Appliquer
              </button>
              <button
                @click="resetDateFilter"
                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Réinitialiser
              </button>
            </div>
          </div>
        </div>

        <!-- Statistiques générales modernisées -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 mb-6">
          <StatCard :value="stats.total_students" label="Apprenants" :icon="UserIcon" bgGradient="bg-gradient-to-r from-blue-500 to-blue-700" />
          <StatCard :value="stats.total_trainers" label="Formateurs" :icon="AcademicCapIcon" bgGradient="bg-gradient-to-r from-green-500 to-green-700" />
          <StatCard :value="stats.total_sessions" label="Sessions totales" :icon="ChartBarIcon" bgGradient="bg-gradient-to-r from-purple-500 to-purple-700" />
          <StatCard :value="stats.active_sessions" label="Sessions actives" :icon="BoltIcon" bgGradient="bg-gradient-to-r from-yellow-400 to-yellow-600" />
        </div>

        <!-- Statistiques d'inscriptions et de paiements -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- Statistiques d'inscriptions -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
              <h3 class="text-lg font-semibold mb-4">Inscriptions</h3>
              <div class="grid grid-cols-2 gap-4">
                <div class="bg-indigo-50 p-4 rounded-lg">
                  <div class="text-3xl font-bold text-indigo-600">{{ enrollmentStats.total }}</div>
                  <div class="text-sm text-gray-600">Total</div>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg">
                  <div class="text-3xl font-bold text-yellow-600">{{ enrollmentStats.pending }}</div>
                  <div class="text-sm text-gray-600">En attente</div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                  <div class="text-3xl font-bold text-green-600">{{ enrollmentStats.approved }}</div>
                  <div class="text-sm text-gray-600">Approuvées</div>
                </div>
                <div class="bg-red-50 p-4 rounded-lg">
                  <div class="text-3xl font-bold text-red-600">{{ enrollmentStats.rejected }}</div>
                  <div class="text-sm text-gray-600">Rejetées</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Statistiques de paiements -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
              <h3 class="text-lg font-semibold mb-4">Paiements</h3>
              <div class="grid grid-cols-2 gap-4">
                <div class="bg-emerald-50 p-4 rounded-lg">
                  <div class="text-3xl font-bold text-emerald-600">{{ formatCurrency(paymentStats.total_amount) }}</div>
                  <div class="text-sm text-gray-600">Montant total</div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                  <div class="text-3xl font-bold text-green-600">{{ formatCurrency(paymentStats.paid_amount) }}</div>
                  <div class="text-sm text-gray-600">Payé ({{ paymentStats.paid_count }})</div>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg">
                  <div class="text-3xl font-bold text-yellow-600">{{ formatCurrency(paymentStats.pending_amount) }}</div>
                  <div class="text-sm text-gray-600">En attente ({{ paymentStats.pending_count }})</div>
                </div>
                <div class="bg-red-50 p-4 rounded-lg">
                  <div class="text-3xl font-bold text-red-600">{{ formatCurrency(paymentStats.unpaid_amount) }}</div>
                  <div class="text-sm text-gray-600">Non payé ({{ paymentStats.unpaid_count }})</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Graphiques -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- Graphique des inscriptions -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
              <h3 class="text-lg font-semibold mb-4">Évolution des inscriptions</h3>
              <div class="h-64">
                <canvas ref="enrollmentsChart"></canvas>
              </div>
            </div>
          </div>

          <!-- Graphique des revenus -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
              <h3 class="text-lg font-semibold mb-4">Évolution des revenus</h3>
              <div class="h-64">
                <canvas ref="revenueChart"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Dernières activités -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- Dernières inscriptions -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Dernières inscriptions</h3>
                <Link :href="route('admin.enrollments.index')" class="text-sm text-indigo-600 hover:text-indigo-900">
                  Voir tout
                </Link>
              </div>

              <div v-if="recentEnrollments.length > 0" class="space-y-4">
                <div v-for="enrollment in recentEnrollments" :key="enrollment.id" class="border-b pb-3 last:border-b-0">
                  <div class="flex justify-between">
                    <div>
                      <div class="font-medium">{{ enrollment.user.name }}</div>
                      <div class="text-sm text-gray-600">{{ enrollment.training_session.title }}</div>
                    </div>
                    <div class="text-right">
                      <div>
                        <span :class="getStatusClass(enrollment.status)" class="px-2 py-1 text-xs rounded-full">
                          {{ getStatusLabel(enrollment.status) }}
                        </span>
                      </div>
                      <div class="text-sm text-gray-500 mt-1">{{ formatDate(enrollment.created_at) }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="text-center py-4 text-gray-500">
                Aucune inscription récente.
              </div>
            </div>
          </div>

          <!-- Derniers paiements -->
          <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Derniers paiements</h3>
                <Link :href="route('admin.enrollments.index')" class="text-sm text-indigo-600 hover:text-indigo-900">
                  Voir tout
                </Link>
              </div>

              <div v-if="recentPayments.length > 0" class="space-y-4">
                <div v-for="payment in recentPayments" :key="payment.id" class="border-b pb-3 last:border-b-0">
                  <div class="flex justify-between">
                    <div>
                      <div class="font-medium">{{ payment.user.name }}</div>
                      <div class="text-sm text-gray-600">{{ payment.training_session.title }}</div>
                    </div>
                    <div class="text-right">
                      <div class="font-medium">{{ formatCurrency(payment.payment_amount) }}</div>
                      <div>
                        <span :class="getPaymentStatusClass(payment.payment_status)" class="px-2 py-1 text-xs rounded-full">
                          {{ getPaymentStatusLabel(payment.payment_status) }}
                        </span>
                      </div>
                      <div class="text-sm text-gray-500 mt-1">{{ formatDate(payment.payment_date) }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="text-center py-4 text-gray-500">
                Aucun paiement récent.
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';
import { router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Chart from 'chart.js/auto';
import StatCard from '@/Components/StatCard.vue';
import { UserIcon, AcademicCapIcon, ChartBarIcon, BoltIcon } from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  stats: Object,
  enrollmentStats: Object,
  paymentStats: Object,
  recentEnrollments: Array,
  recentPayments: Array,
  enrollmentsByMonth: Array,
  revenueByMonth: Array,
  startDate: String,
  endDate: String,
});

// Références pour les graphiques
const enrollmentsChart = ref(null);
const revenueChart = ref(null);

// Dates formatées en YYYY-MM-DD pour le filtre de date
const today = new Date().toISOString().split('T')[0];
const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

// Initialisation des dates de début et de fin
const startDate = ref(props.startDate || thirtyDaysAgo);
const endDate = ref(props.endDate || today);

// Méthodes pour le filtre de date
const applyDateFilter = () => {
  // Vérifier que la date de début est antérieure à la date de fin
  if (startDate.value > endDate.value) {
    alert('La date de début doit être antérieure à la date de fin');
    return;
  }

  router.get(route('admin.dashboard'), {
    start_date: startDate.value,
    end_date: endDate.value
  }, {
    preserveState: true,
    preserveScroll: true,
    only: ['stats', 'enrollmentStats', 'paymentStats', 'enrollmentsByMonth', 'revenueByMonth', 'startDate', 'endDate']
  });
};

const resetDateFilter = () => {
  startDate.value = thirtyDaysAgo;
  endDate.value = today;
  applyDateFilter();
};

// Méthodes
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(amount || 0) + ' DT';
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  const options = { day: 'numeric', month: 'long', year: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const getStatusLabel = (status) => {
  const labels = {
    'pending': 'En attente',
    'approved': 'Approuvée',
    'rejected': 'Rejetée',
    'completed': 'Terminée',
    'cancelled': 'Annulée'
  };
  return labels[status] || status;
};

const getStatusClass = (status) => {
  const classes = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'approved': 'bg-green-100 text-green-800',
    'rejected': 'bg-red-100 text-red-800',
    'completed': 'bg-blue-100 text-blue-800',
    'cancelled': 'bg-gray-100 text-gray-800'
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

const getPaymentStatusLabel = (status) => {
  const labels = {
    'unpaid': 'Non payé',
    'pending': 'En attente',
    'paid': 'Payé',
    'refunded': 'Remboursé'
  };
  return labels[status] || 'Non payé';
};

const getPaymentStatusClass = (status) => {
  const classes = {
    'unpaid': 'bg-red-100 text-red-800',
    'pending': 'bg-yellow-100 text-yellow-800',
    'paid': 'bg-green-100 text-green-800',
    'refunded': 'bg-blue-100 text-blue-800'
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

// Initialisation des graphiques
onMounted(() => {
  // Graphique des inscriptions
  if (enrollmentsChart.value) {
    new Chart(enrollmentsChart.value, {
      type: 'bar',
      data: {
        labels: props.enrollmentsByMonth.map(item => `${item.month} ${item.year}`),
        datasets: [{
          label: 'Inscriptions',
          data: props.enrollmentsByMonth.map(item => item.count),
          backgroundColor: 'rgba(79, 70, 229, 0.2)',
          borderColor: 'rgba(79, 70, 229, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0
            }
          }
        }
      }
    });
  }

  // Graphique des revenus
  if (revenueChart.value) {
    new Chart(revenueChart.value, {
      type: 'line',
      data: {
        labels: props.revenueByMonth.map(item => `${item.month} ${item.year}`),
        datasets: [{
          label: 'Revenus (€)',
          data: props.revenueByMonth.map(item => item.amount),
          backgroundColor: 'rgba(16, 185, 129, 0.2)',
          borderColor: 'rgba(16, 185, 129, 1)',
          borderWidth: 2,
          tension: 0.3,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }
});
</script>
