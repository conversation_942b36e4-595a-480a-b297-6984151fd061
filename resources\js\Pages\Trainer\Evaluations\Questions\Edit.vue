<template>
  <Head title="Modifier une question" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Modifier une question de l'évaluation: {{ evaluation.title }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Liens de navigation -->
            <div class="mb-6">
              <Link :href="route('trainer.evaluations.questions.index', evaluation.id)" class="text-indigo-600 hover:text-indigo-900">
                &larr; Retour aux questions
              </Link>
            </div>

            <!-- Formulaire de modification de question -->
            <form @submit.prevent="submit">
              <div class="grid grid-cols-1 gap-6">
                <!-- Type de question -->
                <div>
                  <InputLabel for="question_type" value="Type de question" />
                  <select
                    id="question_type"
                    v-model="form.question_type"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    required
                  >
                    <option value="">Sélectionnez un type</option>
                    <option v-for="type in questionTypes" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.question_type" />
                </div>

                <!-- Texte de la question -->
                <div>
                  <InputLabel for="question_text" value="Texte de la question" />
                  <textarea
                    id="question_text"
                    v-model="form.question_text"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    rows="3"
                    required
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.question_text" />
                </div>

                <!-- Options pour les questions à choix multiples -->
                <div v-if="form.question_type === 'multiple_choice'">
                  <InputLabel value="Options de réponse" />
                  <div class="mt-2 space-y-3">
                    <div v-for="(option, index) in options" :key="index" class="flex items-center">
                      <div class="flex-grow">
                        <TextInput
                          :id="`option_${index}`"
                          v-model="options[index]"
                          type="text"
                          class="block w-full"
                          placeholder="Option de réponse"
                          required
                        />
                      </div>
                      <button
                        type="button"
                        @click="removeOption(index)"
                        class="ml-2 text-red-600 hover:text-red-900"
                        :disabled="options.length <= 2"
                      >
                        <XMarkIcon class="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                  <div class="mt-2">
                    <button
                      type="button"
                      @click="addOption"
                      class="text-sm text-indigo-600 hover:text-indigo-900"
                      :disabled="options.length >= 10"
                    >
                      + Ajouter une option
                    </button>
                  </div>
                  <InputError class="mt-2" :message="form.errors.options" />
                </div>

                <!-- Obligatoire -->
                <div>
                  <div class="flex items-center">
                    <input
                      id="required"
                      v-model="form.required"
                      type="checkbox"
                      class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                    />
                    <label for="required" class="ml-2 text-sm text-gray-600">
                      Question obligatoire
                    </label>
                  </div>
                  <InputError class="mt-2" :message="form.errors.required" />
                </div>

                <!-- Ordre -->
                <div>
                  <InputLabel for="order" value="Ordre" />
                  <TextInput
                    id="order"
                    v-model="form.order"
                    type="number"
                    class="mt-1 block w-full"
                    min="1"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.order" />
                </div>

                <!-- Boutons de soumission -->
                <div class="flex items-center justify-end mt-4">
                  <Link
                    :href="route('trainer.evaluations.questions.index', evaluation.id)"
                    class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-4"
                  >
                    Annuler
                  </Link>
                  <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    Mettre à jour la question
                  </PrimaryButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import { XMarkIcon } from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  evaluation: Object,
  question: Object,
  questionTypes: Array,
});

// Options pour les questions à choix multiples
const options = ref([]);

// Formulaire
const form = useForm({
  question_text: props.question.question_text,
  question_type: props.question.question_type,
  options: props.question.options || [],
  required: props.question.required,
  order: props.question.order,
});

// Initialiser les options
onMounted(() => {
  if (props.question.question_type === 'multiple_choice' && props.question.options) {
    options.value = [...props.question.options];
  } else {
    options.value = ['', ''];
  }
});

// Méthodes
const addOption = () => {
  if (options.value.length < 10) { // Limiter à 10 options maximum
    options.value.push('');
  }
};

const removeOption = (index) => {
  if (options.value.length > 2) { // Garder au moins 2 options
    options.value.splice(index, 1);
  }
};

const submit = () => {
  // Préparer les options pour les questions à choix multiples
  if (form.question_type === 'multiple_choice') {
    form.options = options.value;
  }
  
  form.put(route('trainer.evaluations.questions.update', [props.evaluation.id, props.question.id]));
};

// Watchers
watch(() => form.question_type, (newValue, oldValue) => {
  if (newValue !== 'multiple_choice') {
    // Réinitialiser les options si ce n'est pas une question à choix multiples
    options.value = ['', ''];
  } else if (oldValue !== 'multiple_choice' && newValue === 'multiple_choice') {
    // Si on passe à choix multiple, initialiser avec 2 options vides
    options.value = ['', ''];
  }
});
</script>
