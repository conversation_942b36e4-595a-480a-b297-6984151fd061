<template>
  <Head title="Questions d'évaluation" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Questions pour l'évaluation: {{ evaluation.title }}
        </h2>
        <Link :href="route('trainer.evaluations.questions.create', evaluation.id)" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
          Ajouter une question
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Liens de navigation -->
            <div class="mb-6">
              <Link :href="route('trainer.evaluations.show', evaluation.id)" class="text-indigo-600 hover:text-indigo-900">
                &larr; Retour à l'évaluation
              </Link>
            </div>

            <!-- Liste des questions -->
            <div v-if="questions.length > 0">
              <div class="space-y-4">
                <div v-for="(question, index) in questions" :key="question.id" class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <div class="flex justify-between items-start">
                    <div>
                      <h4 class="font-medium">{{ index + 1 }}. {{ question.question_text }}</h4>
                      <div class="mt-2 text-sm text-gray-500">
                        <p><span class="font-medium">Type:</span> {{ formatQuestionType(question.question_type) }}</p>
                        <p><span class="font-medium">Obligatoire:</span> {{ question.required ? 'Oui' : 'Non' }}</p>
                        <p><span class="font-medium">Ordre:</span> {{ question.order }}</p>
                      </div>
                      
                      <!-- Options pour les questions à choix multiples -->
                      <div v-if="question.question_type === 'multiple_choice' && question.options && question.options.length > 0" class="mt-2">
                        <p class="text-sm font-medium">Options:</p>
                        <ul class="list-disc list-inside text-sm text-gray-600 ml-2">
                          <li v-for="(option, optionIndex) in question.options" :key="optionIndex">
                            {{ option }}
                          </li>
                        </ul>
                      </div>
                    </div>
                    <div class="flex space-x-2">
                      <Link :href="route('trainer.evaluations.questions.edit', [evaluation.id, question.id])" class="text-indigo-600 hover:text-indigo-900">
                        Modifier
                      </Link>
                      <button @click="confirmDelete(question)" class="text-red-600 hover:text-red-900">
                        Supprimer
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Bouton pour réorganiser les questions -->
              <div class="mt-6 flex justify-end">
                <PrimaryButton @click="reorderQuestions" :disabled="!hasReordered">
                  Enregistrer l'ordre
                </PrimaryButton>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <p class="text-gray-500">Aucune question n'a été ajoutée à cette évaluation.</p>
              <Link :href="route('trainer.evaluations.questions.create', evaluation.id)" class="mt-4 inline-block px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                Ajouter la première question
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de suppression -->
    <Modal :show="showDeleteModal" @close="closeDeleteModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">
          Confirmer la suppression
        </h2>
        <p class="mt-1 text-sm text-gray-600">
          Êtes-vous sûr de vouloir supprimer cette question ? Cette action est irréversible.
        </p>
        <div class="mt-6 flex justify-end">
          <SecondaryButton @click="closeDeleteModal" class="mr-2">
            Annuler
          </SecondaryButton>
          <DangerButton @click="deleteQuestion" :class="{ 'opacity-25': deleteForm.processing }" :disabled="deleteForm.processing">
            Supprimer
          </DangerButton>
        </div>
      </div>
    </Modal>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';

// Props
const props = defineProps({
  evaluation: Object,
  questions: Array,
});

// État pour la modal de suppression
const showDeleteModal = ref(false);
const questionToDelete = ref(null);

// Formulaire pour la suppression
const deleteForm = useForm({});

// Formulaire pour la réorganisation
const reorderForm = useForm({
  questions: [],
});

// État pour suivre si l'ordre a été modifié
const hasReordered = ref(false);

// Méthodes
const formatQuestionType = (type) => {
  const types = {
    'text': 'Texte libre',
    'multiple_choice': 'Choix multiple',
    'rating': 'Notation (0-10)',
    'yes_no': 'Oui/Non',
  };
  return types[type] || type;
};

const confirmDelete = (question) => {
  questionToDelete.value = question;
  showDeleteModal.value = true;
};

const closeDeleteModal = () => {
  showDeleteModal.value = false;
  questionToDelete.value = null;
};

const deleteQuestion = () => {
  if (!questionToDelete.value) return;
  
  deleteForm.delete(route('trainer.evaluations.questions.destroy', [props.evaluation.id, questionToDelete.value.id]), {
    onSuccess: () => {
      closeDeleteModal();
    },
  });
};

const reorderQuestions = () => {
  // Préparer les données pour la réorganisation
  const questions = props.questions.map((question, index) => ({
    id: question.id,
    order: index + 1,
  }));
  
  reorderForm.questions = questions;
  
  reorderForm.post(route('trainer.evaluations.questions.reorder', props.evaluation.id), {
    onSuccess: () => {
      hasReordered.value = false;
    },
  });
};
</script>
