<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FirstAidTip;
use App\Models\FirstAidTipMaterial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class FirstAidTipMaterialController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $firstAidTipId = $request->query('first_aid_tip_id');

        if (!$firstAidTipId) {
            return redirect()->route('admin.first-aid-tips.index')
                ->with('error', 'Veuillez sélectionner un conseil pour voir ses matériels.');
        }

        // Récupérer le conseil et ses matériels
        $firstAidTip = FirstAidTip::findOrFail($firstAidTipId);
        $materials = FirstAidTipMaterial::where('first_aid_tip_id', $firstAidTipId)
            ->orderBy('order')
            ->paginate(10);

        return Inertia::render('Admin/FirstAidTipMaterials/Index', [
            'firstAidTip' => $firstAidTip,
            'materials' => $materials
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $firstAidTipId = $request->query('first_aid_tip_id');

        if (!$firstAidTipId) {
            return redirect()->route('admin.first-aid-tips.index')
                ->with('error', 'Veuillez sélectionner un conseil pour ajouter un matériel.');
        }

        // Récupérer le conseil
        $firstAidTip = FirstAidTip::findOrFail($firstAidTipId);

        return Inertia::render('Admin/FirstAidTipMaterials/Create', [
            'firstAidTip' => $firstAidTip,
            'materialTypes' => [
                ['value' => 'text', 'label' => 'Texte'],
                ['value' => 'pdf', 'label' => 'Document PDF'],
                ['value' => 'video', 'label' => 'Vidéo'],
                ['value' => 'audio', 'label' => 'Audio (podcast)'],
                ['value' => 'image', 'label' => 'Image'],
                ['value' => 'archive', 'label' => 'Archive (ZIP, RAR, etc.)'],
                ['value' => 'gallery', 'label' => 'Galerie d\'images'],
                ['value' => 'embed_video', 'label' => 'Vidéo externe (YouTube, Dailymotion, Vimeo)']
            ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'first_aid_tip_id' => 'required|exists:first_aid_tips,id',
            'type' => 'required|in:text,pdf,video,audio,image,archive,gallery,embed_video',
            'content' => 'nullable|string',
            'embed_code' => 'nullable|string',
            'video_url' => 'nullable|url',
            'file' => 'nullable|file',
            'gallery_files.*' => 'nullable|file|image',
            'order' => 'nullable|integer|min:0',
            'active' => 'boolean',
            'allow_download' => 'boolean',
            'allow_online_viewing' => 'boolean',
        ]);

        // Initialiser les données du matériel
        $materialData = [
            'title' => $validated['title'],
            'description' => $validated['description'] ?? null,
            'first_aid_tip_id' => $validated['first_aid_tip_id'],
            'type' => $validated['type'],
            'order' => $validated['order'] ?? 0,
            'active' => $validated['active'] ?? true,
            'allow_download' => $validated['allow_download'] ?? true,
            'allow_online_viewing' => $validated['allow_online_viewing'] ?? true,
        ];

        // Traiter le contenu en fonction du type
        if ($validated['type'] === 'text') {
            $materialData['content'] = $validated['content'] ?? null;
        } elseif ($validated['type'] === 'embed_video') {
            // Pour les vidéos externes
            if (isset($validated['video_url'])) {
                $embedCode = $this->generateEmbedCodeFromUrl($validated['video_url']);
                $materialData['embed_code'] = $embedCode;
            } else if (isset($validated['embed_code'])) {
                $materialData['embed_code'] = $validated['embed_code'];
            }
            $materialData['allow_download'] = false;
        } elseif ($validated['type'] === 'gallery') {
            // Pour les galeries d'images
            if ($request->hasFile('gallery_files')) {
                $galleryFiles = $request->file('gallery_files');
                $images = [];
                $firstPath = null;

                foreach ($galleryFiles as $index => $file) {
                    $path = $file->store('first-aid-materials/gallery', 'public');

                    $images[] = [
                        'path' => $path,
                        'caption' => $validated['title'] . ' - Image ' . ($index + 1),
                        'order' => $index
                    ];

                    if ($index === 0) {
                        $firstPath = $path;
                        $materialData['file_path'] = $path;
                        $materialData['mime_type'] = $file->getMimeType();
                        $materialData['file_size'] = $file->getSize() / 1024;
                        $materialData['thumbnail_path'] = $path;
                    }
                }

                $metadata = ['images' => $images];
                $materialData['metadata'] = json_encode($metadata);
            }
        } elseif (in_array($validated['type'], ['pdf', 'video', 'audio', 'image', 'archive'])) {
            if ($request->hasFile('file')) {
                $file = $request->file('file');
                $path = $file->store('first-aid-materials/' . $validated['type'], 'public');
                $materialData['file_path'] = $path;
                $materialData['mime_type'] = $file->getMimeType();
                $materialData['file_size'] = $file->getSize() / 1024;

                if ($validated['type'] === 'image') {
                    $materialData['thumbnail_path'] = $path;
                }

                if ($validated['type'] === 'archive') {
                    $extension = strtolower($file->getClientOriginalExtension());
                    $mimeTypes = [
                        'zip' => 'application/zip',
                        'rar' => 'application/x-rar-compressed',
                        '7z' => 'application/x-7z-compressed',
                        'tar' => 'application/x-tar',
                        'gz' => 'application/gzip',
                    ];

                    if (isset($mimeTypes[$extension])) {
                        $materialData['mime_type'] = $mimeTypes[$extension];
                    }

                    $materialData['allow_online_viewing'] = false;
                }
            }
        }

        // Créer le matériel
        FirstAidTipMaterial::create($materialData);

        // Rediriger vers la liste des matériels avec un message de succès
        return redirect()->route('admin.first-aid-tip-materials.index', ['first_aid_tip_id' => $validated['first_aid_tip_id']])
            ->with('success', 'Matériel ajouté avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Récupérer le matériel avec ses relations
        $material = FirstAidTipMaterial::with(['firstAidTip'])->findOrFail($id);

        return Inertia::render('Admin/FirstAidTipMaterials/Show', [
            'material' => $material
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Récupérer le matériel
        $material = FirstAidTipMaterial::with('firstAidTip')->findOrFail($id);

        return Inertia::render('Admin/FirstAidTipMaterials/Edit', [
            'material' => $material,
            'materialTypes' => [
                ['value' => 'text', 'label' => 'Texte'],
                ['value' => 'pdf', 'label' => 'Document PDF'],
                ['value' => 'video', 'label' => 'Vidéo'],
                ['value' => 'audio', 'label' => 'Audio (podcast)'],
                ['value' => 'image', 'label' => 'Image'],
                ['value' => 'archive', 'label' => 'Archive (ZIP, RAR, etc.)'],
                ['value' => 'gallery', 'label' => 'Galerie d\'images'],
                ['value' => 'embed_video', 'label' => 'Vidéo externe (YouTube, Dailymotion, Vimeo)']
            ]
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Récupérer le matériel
        $material = FirstAidTipMaterial::findOrFail($id);

        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:text,pdf,video,audio,image,archive,gallery,embed_video',
            'content' => 'nullable|string',
            'embed_code' => 'nullable|string',
            'video_url' => 'nullable|url',
            'file' => 'nullable|file',
            'gallery_files.*' => 'nullable|file|image',
            'order' => 'nullable|integer|min:0',
            'active' => 'boolean',
            'allow_download' => 'boolean',
            'allow_online_viewing' => 'boolean',
        ]);

        // Initialiser les données du matériel
        $materialData = [
            'title' => $validated['title'],
            'description' => $validated['description'] ?? null,
            'type' => $validated['type'],
            'order' => $validated['order'] ?? 0,
            'active' => $validated['active'] ?? true,
            'allow_download' => $validated['allow_download'] ?? true,
            'allow_online_viewing' => $validated['allow_online_viewing'] ?? true,
        ];

        // Traiter le contenu en fonction du type (similar logic as store method)
        if ($validated['type'] === 'text') {
            $materialData['content'] = $validated['content'] ?? null;
            if ($material->type !== 'text' && $material->file_path) {
                Storage::disk('public')->delete($material->file_path);
                $materialData['file_path'] = null;
                $materialData['mime_type'] = null;
                $materialData['file_size'] = null;
                $materialData['thumbnail_path'] = null;
                $materialData['embed_code'] = null;
            }
        } elseif ($validated['type'] === 'embed_video') {
            if (isset($validated['video_url'])) {
                $embedCode = $this->generateEmbedCodeFromUrl($validated['video_url']);
                $materialData['embed_code'] = $embedCode;
            } else if (isset($validated['embed_code'])) {
                $materialData['embed_code'] = $validated['embed_code'];
            }
            $materialData['allow_download'] = false;

            if ($material->type !== 'embed_video' && $material->file_path) {
                Storage::disk('public')->delete($material->file_path);
                $materialData['file_path'] = null;
                $materialData['mime_type'] = null;
                $materialData['file_size'] = null;
                $materialData['thumbnail_path'] = null;
            }
        } elseif (in_array($validated['type'], ['pdf', 'video', 'audio', 'image', 'archive'])) {
            if ($request->hasFile('file')) {
                if ($material->file_path) {
                    Storage::disk('public')->delete($material->file_path);
                }

                $file = $request->file('file');
                $path = $file->store('first-aid-materials/' . $validated['type'], 'public');
                $materialData['file_path'] = $path;
                $materialData['mime_type'] = $file->getMimeType();
                $materialData['file_size'] = $file->getSize() / 1024;
                $materialData['embed_code'] = null;

                if ($validated['type'] === 'image') {
                    $materialData['thumbnail_path'] = $path;
                }

                if ($validated['type'] === 'archive') {
                    $extension = strtolower($file->getClientOriginalExtension());
                    $mimeTypes = [
                        'zip' => 'application/zip',
                        'rar' => 'application/x-rar-compressed',
                        '7z' => 'application/x-7z-compressed',
                        'tar' => 'application/x-tar',
                        'gz' => 'application/gzip',
                    ];

                    if (isset($mimeTypes[$extension])) {
                        $materialData['mime_type'] = $mimeTypes[$extension];
                    }

                    $materialData['allow_online_viewing'] = false;
                }
            } elseif ($material->type !== $validated['type']) {
                return redirect()->back()->withErrors([
                    'file' => 'Un fichier est requis pour ce type de matériel.'
                ]);
            }
        }

        // Mettre à jour le matériel
        $material->update($materialData);

        // Rediriger vers la liste des matériels avec un message de succès
        return redirect()->route('admin.first-aid-tip-materials.index', ['first_aid_tip_id' => $material->first_aid_tip_id])
            ->with('success', 'Matériel mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Récupérer le matériel
        $material = FirstAidTipMaterial::findOrFail($id);
        $firstAidTipId = $material->first_aid_tip_id;

        // Supprimer le fichier associé s'il existe
        if ($material->file_path) {
            Storage::disk('public')->delete($material->file_path);
        }

        // Supprimer la miniature s'il en existe une
        if ($material->thumbnail_path && $material->thumbnail_path !== $material->file_path) {
            Storage::disk('public')->delete($material->thumbnail_path);
        }

        // Supprimer le matériel
        $material->delete();

        // Rediriger vers la liste des matériels avec un message de succès
        return redirect()->route('admin.first-aid-tip-materials.index', ['first_aid_tip_id' => $firstAidTipId])
            ->with('success', 'Matériel supprimé avec succès.');
    }

    /**
     * Télécharger le fichier associé au matériel
     */
    public function download(string $id)
    {
        // Récupérer le matériel
        $material = FirstAidTipMaterial::findOrFail($id);

        // Vérifier si le téléchargement est autorisé
        if (!$material->allow_download) {
            return redirect()->back()->with('error', 'Le téléchargement de ce matériel n\'est pas autorisé.');
        }

        // Vérifier si un fichier est associé
        if (!$material->file_path) {
            return redirect()->back()->with('error', 'Aucun fichier n\'est associé à ce matériel.');
        }

        // Obtenir le chemin complet du fichier
        $filePath = Storage::disk('public')->path($material->file_path);

        // Vérifier si le fichier existe
        if (!file_exists($filePath)) {
            return redirect()->back()->with('error', 'Le fichier n\'existe pas sur le serveur.');
        }

        // Déterminer l'extension du fichier
        $extension = pathinfo($material->file_path, PATHINFO_EXTENSION);

        // Créer un nom de fichier pour le téléchargement
        $downloadName = $material->title . '.' . $extension;

        // Télécharger le fichier avec le bon type MIME
        return response()->download($filePath, $downloadName, [
            'Content-Type' => $material->mime_type ?? 'application/octet-stream',
        ]);
    }

    /**
     * Afficher le fichier associé au matériel
     */
    public function view(Request $request, string $id)
    {
        // Récupérer le matériel
        $material = FirstAidTipMaterial::findOrFail($id);

        // Vérifier si la visualisation en ligne est autorisée
        if (!$material->allow_online_viewing) {
            return redirect()->back()->with('error', 'La visualisation en ligne de ce matériel n\'est pas autorisée.');
        }

        // Vérifier si un fichier est associé
        if (!$material->file_path && $material->type !== 'gallery') {
            return redirect()->back()->with('error', 'Aucun fichier n\'est associé à ce matériel.');
        }

        // Traitement spécial pour les galeries d'images
        if ($material->type === 'gallery') {
            $imageIndex = $request->query('image_index', 0);
            $metadata = is_array($material->metadata) ? $material->metadata : json_decode($material->metadata, true);

            $galleryPaths = [];

            if (is_array($metadata)) {
                if (isset($metadata['images']) && is_array($metadata['images'])) {
                    $galleryPaths = array_map(function($img) {
                        return is_array($img) ? ($img['path'] ?? '') : $img;
                    }, $metadata['images']);
                }
            }

            $galleryPaths = array_filter($galleryPaths);

            if (empty($galleryPaths)) {
                return response()->json(['error' => 'Aucune image trouvée dans la galerie'], 404);
            }

            if (!isset($galleryPaths[$imageIndex])) {
                return response()->json(['error' => 'Image non trouvée à cet index'], 404);
            }

            $imagePath = $galleryPaths[$imageIndex];
            $filePath = Storage::disk('public')->path($imagePath);

            if (!file_exists($filePath)) {
                return response()->json(['error' => 'Image non trouvée'], 404);
            }

            $mimeType = mime_content_type($filePath) ?: 'image/jpeg';

            return response()->file($filePath, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => 'inline; filename="' . basename($imagePath) . '"',
            ]);
        }

        // Pour les autres types de matériels
        $filePath = Storage::disk('public')->path($material->file_path);

        if (!file_exists($filePath)) {
            return response()->json(['error' => 'Fichier non trouvé'], 404);
        }

        return response()->file($filePath, [
            'Content-Type' => $material->mime_type ?? $this->getMimeTypeFromExtension($filePath),
            'Content-Disposition' => 'inline; filename="' . basename($material->file_path) . '"',
        ]);
    }

    /**
     * Générer un code d'intégration à partir d'une URL de vidéo
     */
    private function generateEmbedCodeFromUrl($url)
    {
        // YouTube
        if (preg_match('/youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/', $url, $matches) ||
            preg_match('/youtu\.be\/([a-zA-Z0-9_-]+)/', $url, $matches)) {
            $videoId = $matches[1];
            return '<iframe width="100%" height="400" src="https://www.youtube.com/embed/' . $videoId . '" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>';
        }

        // Dailymotion
        if (preg_match('/dailymotion\.com\/video\/([a-zA-Z0-9_-]+)/', $url, $matches)) {
            $videoId = $matches[1];
            return '<iframe width="100%" height="400" src="https://www.dailymotion.com/embed/video/' . $videoId . '" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>';
        }

        // Vimeo
        if (preg_match('/vimeo\.com\/([0-9]+)/', $url, $matches)) {
            $videoId = $matches[1];
            return '<iframe width="100%" height="400" src="https://player.vimeo.com/video/' . $videoId . '" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>';
        }

        // Si aucun format reconnu, retourner l'URL comme texte
        return '<p>Vidéo non reconnue : ' . htmlspecialchars($url) . '</p>';
    }

    /**
     * Détermine le type MIME à partir de l'extension du fichier
     */
    private function getMimeTypeFromExtension($filePath)
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        $mimeTypes = [
            'pdf' => 'application/pdf',
            'mp4' => 'video/mp4',
            'webm' => 'video/webm',
            'ogg' => 'video/ogg',
            'mp3' => 'audio/mpeg',
            'wav' => 'audio/wav',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'zip' => 'application/zip',
            'rar' => 'application/x-rar-compressed',
            '7z' => 'application/x-7z-compressed',
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
}
