import './bootstrap';
import '../css/app.css';

import { createApp, h } from 'vue';
import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { ZiggyVue } from '../../vendor/tightenco/ziggy';

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

// Add console log for debugging
console.log('Starting Inertia app initialization');
console.log('Available pages:', import.meta.glob('./Pages/**/*.vue'));

// Fonction utilitaire pour vérifier et corriger les données JSON
window.fixJsonData = function(data) {
    if (!data) return data;

    // Parcourir récursivement les objets
    if (typeof data === 'object' && data !== null) {
        // Si c'est un tableau
        if (Array.isArray(data)) {
            return data.map(item => window.fixJsonData(item));
        }

        // Si c'est un objet
        const result = {};
        for (const key in data) {
            if (key === 'options' || key === 'correct_options') {
                // Traitement spécial pour les options et correct_options
                try {
                    if (typeof data[key] === 'string' && data[key] !== '[object Object]') {
                        result[key] = JSON.parse(data[key]);
                    } else if (data[key] === '[object Object]' || data[key] === null) {
                        // Valeurs par défaut
                        result[key] = key === 'options'
                            ? { 'A': 'Option A', 'B': 'Option B', 'C': 'Option C', 'D': 'Option D' }
                            : ['A'];
                    } else {
                        result[key] = data[key];
                    }
                } catch (e) {
                    console.error(`Erreur lors du parsing de ${key}:`, e);
                    // Valeurs par défaut en cas d'erreur
                    result[key] = key === 'options'
                        ? { 'A': 'Option A', 'B': 'Option B', 'C': 'Option C', 'D': 'Option D' }
                        : ['A'];
                }
            } else {
                // Traitement normal pour les autres propriétés
                result[key] = window.fixJsonData(data[key]);
            }
        }
        return result;
    }

    // Retourner les valeurs primitives telles quelles
    return data;
};

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => {
        console.log('Resolving page:', name);
        return resolvePageComponent(`./Pages/${name}.vue`, import.meta.glob('./Pages/**/*.vue'));
    },
    setup({ el, App, props, plugin }) {
        console.log('Setting up app with props:', props);

        // Corriger les données JSON avant de les passer à l'application
        if (props && props.initialPage && props.initialPage.props) {
            props.initialPage.props = window.fixJsonData(props.initialPage.props);
        }

        console.log('Props after fixing JSON data:', props);

        const app = createApp({ render: () => h(App, props) });

        // Ajouter un gestionnaire d'erreurs global
        app.config.errorHandler = (err, vm, info) => {
            console.error('Vue Error:', err);
            console.error('Component:', vm);
            console.error('Info:', info);
        };

        return app
            .use(plugin)
            .use(ZiggyVue)
            .mount(el);
    },
    progress: {
        color: '#4B5563',
    },
});
