<template>
  <Head :title="'Session - ' + session.title" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Détails de la session de formation
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Bouton de retour -->
        <div class="mb-6">
          <Link :href="route('admin.training-sessions.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Retour à la liste
          </Link>
        </div>

        <!-- Informations de la session -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <div class="flex flex-col md:flex-row">
              <!-- Image de la session -->
              <div class="md:w-1/3 mb-6 md:mb-0 md:pr-6">
                <div v-if="session.image" class="h-64 bg-gray-200 rounded-lg overflow-hidden">
                  <img :src="getImageUrl(session.image)" :alt="session.title" class="w-full h-full object-cover" @error="handleImageError">
                </div>
                <div v-else class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
              </div>

              <!-- Détails de la session -->
              <div class="md:w-2/3">
                <div class="flex justify-between items-start">
                  <div>
                    <h3 class="text-2xl font-semibold mb-2">{{ session.title }}</h3>
                    <div class="mb-2">
                      <span :class="{
                        'px-2 py-1 text-xs rounded-full': true,
                        'bg-green-100 text-green-800': session.active,
                        'bg-red-100 text-red-800': !session.active
                      }">
                        {{ session.active ? 'Active' : 'Inactive' }}
                      </span>
                    </div>
                  </div>
                  <div class="flex space-x-2">
                    <Link :href="route('admin.training-sessions.edit', session.id)" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 0L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                      Modifier
                    </Link>
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div>
                    <p class="text-sm text-gray-600">Domaine de formation</p>
                    <p class="font-medium">{{ session.training_domain.name }}</p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-600">Formateur</p>
                    <p class="font-medium">{{ session.trainer.name }}</p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-600">Date de début</p>
                    <p class="font-medium">{{ formatDate(session.start_date) }}</p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-600">Date de fin</p>
                    <p class="font-medium">{{ formatDate(session.end_date) }}</p>
                  </div>
                  <div v-if="session.location">
                    <p class="text-sm text-gray-600">Lieu</p>
                    <p class="font-medium">{{ session.location }}</p>
                  </div>
                  <div v-if="session.max_participants">
                    <p class="text-sm text-gray-600">Nombre maximum de participants</p>
                    <p class="font-medium">{{ session.max_participants }}</p>
                  </div>
                  <div v-if="session.price">
                    <p class="text-sm text-gray-600">Prix</p>
                    <p class="font-medium">{{ formatPrice(session.price) }}</p>
                  </div>
                </div>

                <!-- Description -->
                <div class="mt-6" v-if="session.description">
                  <p class="text-sm text-gray-600 mb-2">Description</p>
                  <div class="bg-gray-50 p-4 rounded">
                    <p>{{ session.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Onglets -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="border-b border-gray-200">
            <nav class="flex -mb-px">
              <button
                @click="activeTab = 'enrollments'"
                :class="[
                  'py-4 px-6 text-center border-b-2 font-medium text-sm',
                  activeTab === 'enrollments'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                ]"
              >
                Inscriptions ({{ enrollments.length }})
              </button>
              <button
                @click="activeTab = 'exams'"
                :class="[
                  'py-4 px-6 text-center border-b-2 font-medium text-sm',
                  activeTab === 'exams'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                ]"
              >
                Examens ({{ exams.length }})
              </button>
            </nav>
          </div>

          <!-- Contenu des onglets -->
          <div class="p-6">
            <!-- Inscriptions -->
            <div v-if="activeTab === 'enrollments'">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Liste des inscriptions</h3>
                <Link :href="route('admin.enrollments.create')" class="inline-flex items-center px-4 py-2 bg-yellow-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-yellow-700 focus:bg-yellow-700 active:bg-yellow-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  Ajouter une inscription
                </Link>
              </div>

              <div v-if="enrollments.length > 0" class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Apprenant</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'inscription</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="enrollment in enrollments" :key="enrollment.id">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">{{ enrollment.user.name }}</div>
                        <div class="text-sm text-gray-500">{{ enrollment.user.email }}</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-500">{{ formatDate(enrollment.created_at) }}</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span :class="{
                          'px-2 py-1 text-xs rounded-full': true,
                          'bg-yellow-100 text-yellow-800': enrollment.status === 'pending',
                          'bg-green-100 text-green-800': enrollment.status === 'approved',
                          'bg-red-100 text-red-800': enrollment.status === 'rejected',
                          'bg-blue-100 text-blue-800': enrollment.status === 'completed'
                        }">
                          {{ formatStatus(enrollment.status) }}
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Link :href="route('admin.enrollments.show', enrollment.id)" class="text-indigo-600 hover:text-indigo-900 mr-2">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </Link>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div v-else class="text-gray-500 italic text-center py-4">
                Aucune inscription pour cette session de formation.
              </div>
            </div>

            <!-- Examens -->
            <div v-if="activeTab === 'exams'">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Liste des examens</h3>
                <Link :href="route('admin.exams.create', { training_session_id: session.id })" class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  Créer un examen
                </Link>
              </div>

              <div v-if="exams.length > 0" class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Titre</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Créé par</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="exam in exams" :key="exam.id">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">{{ exam.title }}</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-500">{{ exam.creator.name }}</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-500">{{ exam.duration_minutes }} minutes</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span :class="{
                          'px-2 py-1 text-xs rounded-full': true,
                          'bg-green-100 text-green-800': exam.is_published,
                          'bg-yellow-100 text-yellow-800': !exam.is_published
                        }">
                          {{ exam.is_published ? 'Publié' : 'Brouillon' }}
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Link :href="route('admin.exams.show', exam.id)" class="text-indigo-600 hover:text-indigo-900 mr-2">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </Link>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div v-else class="text-gray-500 italic text-center py-4">
                Aucun examen pour cette session de formation.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  session: Object,
  enrollments: Array,
  exams: Array,
});

// État
const activeTab = ref('enrollments');

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return 'Non définie';
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const formatPrice = (price) => {
  // Convertir le prix en dinars tunisiens (DT)
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'TND',
    currencyDisplay: 'symbol'
  }).format(price);
};

const formatStatus = (status) => {
  const statusMap = {
    'pending': 'En attente',
    'approved': 'Approuvée',
    'rejected': 'Rejetée',
    'completed': 'Terminée'
  };
  return statusMap[status] || status;
};

// Gestion des images
const getImageUrl = (imagePath) => {
  if (!imagePath) {
    return '/images/default-session.png';
  }

  // Vérifier si le chemin commence déjà par /storage
  if (imagePath.startsWith('/storage/')) {
    return imagePath;
  }

  // Sinon, ajouter le préfixe /storage/
  return `/storage/${imagePath}`;
};

const handleImageError = (event) => {
  console.error('Erreur de chargement de l\'image:', event);
  // Remplacer par une image par défaut en cas d'erreur
  event.target.src = '/images/default-session.png';
};
</script>
