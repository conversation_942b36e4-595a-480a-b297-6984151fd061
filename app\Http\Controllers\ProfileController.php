<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class ProfileController extends Controller
{
    /**
     * Display the user's profile form.
     */
    public function edit(Request $request): Response
    {
        return Inertia::render('Profile/Edit', [
            'mustVerifyEmail' => $request->user() instanceof MustVerifyEmail,
            'status' => session('status'),
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        \Log::info('Profile update started', [
            'user_id' => $request->user()->id,
            'has_file' => $request->hasFile('profile_photo'),
            'profile_photo_input' => $request->input('profile_photo'),
            'all_inputs' => $request->all()
        ]);

        $validated = $request->validated();

        // Formater la date de naissance si elle est présente
        if (isset($validated['birth_date'])) {
            $validated['birth_date'] = \Carbon\Carbon::parse($validated['birth_date'])->format('Y-m-d');
        }

        // Gérer l'upload de la photo de profil
        if ($request->hasFile('profile_photo')) {
            \Log::info('Processing file upload');

            // Supprimer l'ancienne photo si elle existe
            if ($request->user()->profile_photo) {
                Storage::disk('public')->delete($request->user()->profile_photo);
            }

            // Stocker la nouvelle photo
            $photoPath = $request->file('profile_photo')->store('profile-photos', 'public');
            $validated['profile_photo'] = $photoPath;

            \Log::info('Photo uploaded', ['path' => $photoPath]);
        } elseif ($request->input('profile_photo') === 'DELETE') {
            \Log::info('Deleting photo', [
                'current_photo' => $request->user()->profile_photo,
                'user_id' => $request->user()->id
            ]);

            // Supprimer la photo existante
            if ($request->user()->profile_photo) {
                $deleted = Storage::disk('public')->delete($request->user()->profile_photo);
                \Log::info('Photo file deleted', ['success' => $deleted]);
            }
            $validated['profile_photo'] = null;
        } else {
            \Log::info('No photo action needed - keeping existing photo');
            // Ne pas modifier profile_photo si aucun fichier n'est envoyé
            unset($validated['profile_photo']);
        }

        $request->user()->fill($validated);

        if ($request->user()->isDirty('email')) {
            $request->user()->email_verified_at = null;
        }

        \Log::info('Before save', [
            'profile_photo' => $request->user()->profile_photo,
            'validated_data' => $validated
        ]);

        $request->user()->save();

        \Log::info('After save', [
            'profile_photo' => $request->user()->fresh()->profile_photo
        ]);

        return Redirect::route('profile.edit')->with('status', 'profile-updated');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validate([
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }
}
