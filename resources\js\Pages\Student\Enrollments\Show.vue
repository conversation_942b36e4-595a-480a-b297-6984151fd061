<template>
  <Head :title="`Détails de l'inscription - ${enrollment.training_session.title}`" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Détails de l'inscription
        </h2>
        <Link 
          :href="route('student.enrollments.index')" 
          class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
        >
          Retour à la liste
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Informations sur la formation -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200 mb-6">
          <div class="p-6 text-gray-900">
            <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-4 gap-4">
              <div>
                <h3 class="text-2xl font-bold mb-1">{{ enrollment.training_session.title }}</h3>
                <div class="text-sm text-gray-500 mb-1">{{ enrollment.training_session.training_domain.name }}</div>
                <div class="text-sm text-gray-700 mb-1"><span class="font-medium">Formateur :</span> {{ enrollment.training_session.trainer.name }}</div>
                <div class="text-sm text-gray-700 mb-1"><span class="font-medium">Dates :</span> {{ formatDate(enrollment.training_session.start_date) }} - {{ formatDate(enrollment.training_session.end_date) }}</div>
                <div class="text-sm text-gray-700 mb-1"><span class="font-medium">Prix :</span> {{ enrollment.training_session.price ? `${enrollment.training_session.price} DT` : 'Gratuit' }}</div>
              </div>
              <div class="flex flex-col items-end gap-2">
                <span :class="getStatusClass(enrollment.status)" class="px-3 py-1 text-xs rounded-full mb-2">
                    {{ getStatusLabel(enrollment.status) }}
                  </span>
                <!-- Barre de progression fictive -->
                <div class="w-40">
                  <div class="text-xs text-gray-500 mb-1">Progression</div>
                  <div class="w-full bg-gray-200 rounded-full h-3">
                    <div class="bg-green-500 h-3 rounded-full" :style="{ width: '60%' }"></div>
                </div>
                  <div class="text-xs text-gray-600 mt-1 text-right">60%</div>
                </div>
              </div>
            </div>
            <div class="mt-6 flex flex-wrap gap-3">
              <template v-if="enrollment.status === 'approved'">
                <Link 
                  :href="route('student.courses.show', enrollment.training_session.id)" 
                  class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                >
                  Accéder au contenu
                </Link>
              </template>
              <template v-if="enrollment.status === 'pending'">
                <button 
                  @click="showCancelModal = true" 
                  class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                >
                  Annuler la demande
                </button>
              </template>
            </div>
            <div v-if="enrollment.notes" class="mt-6">
              <p class="font-medium text-sm mb-1">Notes :</p>
              <p class="text-sm text-gray-600 whitespace-pre-line">{{ enrollment.notes }}</p>
            </div>
          </div>
        </div>
        
        <!-- Informations de paiement -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Informations de paiement</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p class="text-sm mb-2">
                  <span class="font-medium">Montant:</span> {{ enrollment.payment_amount ? `${enrollment.payment_amount} DT` : (enrollment.training_session.price ? `${enrollment.training_session.price} DT` : 'Gratuit') }}
                </p>
                <p class="text-sm mb-2">
                  <span class="font-medium">Statut du paiement:</span> 
                  <span :class="getPaymentStatusClass(enrollment.payment_status)" class="ml-2 px-2 py-1 text-xs rounded-full">
                    {{ getPaymentStatusLabel(enrollment.payment_status) }}
                  </span>
                </p>
                <p v-if="enrollment.payment_date" class="text-sm mb-2">
                  <span class="font-medium">Date de paiement:</span> {{ formatDate(enrollment.payment_date) }}
                </p>
                <p v-if="enrollment.payment_due_date" class="text-sm mb-2">
                  <span class="font-medium">Date limite de paiement:</span> {{ formatDate(enrollment.payment_due_date) }}
                </p>
              </div>
              
              <div>
                <h4 class="text-md font-medium mb-2">Instructions de paiement</h4>
                <div v-if="enrollment.payment_instructions" class="text-sm text-gray-600 whitespace-pre-line mb-4">
                  {{ enrollment.payment_instructions }}
                </div>
                <div v-else class="text-sm text-gray-600 mb-4">
                  <p>Pour finaliser votre inscription, veuillez effectuer le paiement selon les modalités suivantes :</p>
                  <ul class="list-disc pl-5 mt-2">
                    <li>Virement bancaire au compte : FR76 1234 5678 9012 3456 7890 123</li>
                    <li>Paiement en espèces au secrétariat</li>
                    <li>Paiement par D17 au numéro : +123 456 789</li>
                  </ul>
                  <p class="mt-2">Veuillez indiquer votre nom et le titre de la formation dans la référence du paiement.</p>
                </div>
                
                <!-- Téléversement de justificatif -->
                <div v-if="['unpaid', 'pending'].includes(enrollment.payment_status)">
                  <h4 class="text-md font-medium mb-2">Téléverser un justificatif de paiement</h4>
                  <form @submit.prevent="submitPaymentProof" class="mt-2">
                    <div class="mb-3">
                      <input 
                        type="file" 
                        ref="paymentProofInput"
                        class="block w-full text-sm text-gray-500
                          file:mr-4 file:py-2 file:px-4
                          file:rounded-md file:border-0
                          file:text-sm file:font-semibold
                          file:bg-indigo-50 file:text-indigo-700
                          hover:file:bg-indigo-100"
                        accept=".jpg,.jpeg,.png,.pdf"
                      />
                    </div>
                    <button 
                      type="submit" 
                      class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                    >
                      Téléverser
                    </button>
                  </form>
                </div>
                
                <!-- Affichage du justificatif -->
                <div v-if="enrollment.payment_proof" class="mt-4">
                  <h4 class="text-md font-medium mb-2">Justificatif de paiement</h4>
                  <a 
                    :href="`/storage/${enrollment.payment_proof}`" 
                    target="_blank" 
                    class="text-indigo-600 hover:text-indigo-900"
                  >
                    Voir le justificatif
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Modal de confirmation d'annulation -->
    <div v-if="showCancelModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Confirmer l'annulation</h3>
        <p class="text-gray-600 mb-6">
          Êtes-vous sûr de vouloir annuler votre inscription à la formation "{{ enrollment.training_session.title }}" ?
        </p>
        <div class="flex justify-end space-x-3">
          <button 
            @click="showCancelModal = false" 
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
          >
            Annuler
          </button>
          <button 
            @click="cancelEnrollment" 
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Confirmer
          </button>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import { ref } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  enrollment: Object,
});

// État local
const showCancelModal = ref(false);
const paymentProofInput = ref(null);

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return '';
  const options = { day: 'numeric', month: 'long', year: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const getStatusLabel = (status) => {
  const labels = {
    'pending': 'En attente',
    'approved': 'Approuvée',
    'rejected': 'Rejetée',
    'completed': 'Terminée',
    'cancelled': 'Annulée'
  };
  return labels[status] || status;
};

const getStatusClass = (status) => {
  const classes = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'approved': 'bg-green-100 text-green-800',
    'rejected': 'bg-red-100 text-red-800',
    'completed': 'bg-blue-100 text-blue-800',
    'cancelled': 'bg-gray-100 text-gray-800'
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

const getPaymentStatusLabel = (status) => {
  const labels = {
    'unpaid': 'Non payé',
    'pending': 'En attente de validation',
    'paid': 'Payé',
    'refunded': 'Remboursé'
  };
  return labels[status] || 'Non payé';
};

const getPaymentStatusClass = (status) => {
  const classes = {
    'unpaid': 'bg-red-100 text-red-800',
    'pending': 'bg-yellow-100 text-yellow-800',
    'paid': 'bg-green-100 text-green-800',
    'refunded': 'bg-blue-100 text-blue-800'
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

const cancelEnrollment = () => {
  window.location.href = route('student.enrollments.cancel', props.enrollment.id);
};

const submitPaymentProof = () => {
  const fileInput = paymentProofInput.value;
  if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
    alert('Veuillez sélectionner un fichier');
    return;
  }

  const file = fileInput.files[0];

  // Validation côté client
  const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
  if (!allowedTypes.includes(file.type)) {
    alert('Le fichier doit être au format JPEG, PNG, JPG ou PDF.');
    return;
  }

  const maxSize = 2 * 1024 * 1024; // 2MB
  if (file.size > maxSize) {
    alert('Le fichier ne doit pas dépasser 2 Mo.');
    return;
  }

  const formData = new FormData();
  formData.append('payment_proof', file);

  // Utiliser fetch pour envoyer le fichier
  fetch(route('student.enrollments.upload-payment-proof', props.enrollment.id), {
    method: 'POST',
    body: formData,
    headers: {
      'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
      'Accept': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert(data.message || 'Justificatif de paiement téléversé avec succès.');
      window.location.reload();
    } else {
      // Afficher les erreurs spécifiques
      if (data.errors) {
        const errorMessages = Object.values(data.errors).flat().join('\n');
        alert(errorMessages);
      } else {
        alert(data.message || 'Une erreur est survenue lors du téléversement du fichier');
      }
    }
  })
  .catch(error => {
    console.error('Erreur:', error);
    alert('Une erreur de connexion est survenue. Veuillez réessayer.');
  });
};
</script>
