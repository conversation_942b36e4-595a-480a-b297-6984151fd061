<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\TrainingSession;
use App\Models\TrainingDomain;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ExamController extends Controller
{
    /**
     * Affiche la liste des examens
     */
    public function index(Request $request)
    {
        // Initialiser la requête
        $query = Exam::with(['trainingSession', 'trainingSession.trainingDomain', 'creator']);

        // Appliquer la recherche si elle existe
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where('title', 'like', $searchTerm)
                  ->orWhere('description', 'like', $searchTerm);
        }

        // Filtrer par session de formation
        if ($request->has('training_session_id') && !empty($request->training_session_id)) {
            $query->where('training_session_id', $request->training_session_id);
        }

        // Filtrer par domaine de formation
        if ($request->has('domain_id') && !empty($request->domain_id)) {
            $query->whereHas('trainingSession', function($q) use ($request) {
                $q->where('training_domain_id', $request->domain_id);
            });
        }

        // Filtrer par créateur (formateur)
        if ($request->has('creator_id') && !empty($request->creator_id)) {
            $query->where('creator_id', $request->creator_id);
        }

        // Filtrer par statut
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Filtrer par disponibilité
        if ($request->has('availability') && !empty($request->availability)) {
            $now = now();

            if ($request->availability === 'current') {
                // Examens actuellement disponibles
                $query->where(function($q) use ($now) {
                    $q->where(function($q) use ($now) {
                        $q->whereNull('available_from')
                          ->whereNull('available_until');
                    })->orWhere(function($q) use ($now) {
                        $q->whereNull('available_from')
                          ->where('available_until', '>=', $now);
                    })->orWhere(function($q) use ($now) {
                        $q->where('available_from', '<=', $now)
                          ->whereNull('available_until');
                    })->orWhere(function($q) use ($now) {
                        $q->where('available_from', '<=', $now)
                          ->where('available_until', '>=', $now);
                    });
                });
            } elseif ($request->availability === 'upcoming') {
                // Examens à venir
                $query->where('available_from', '>', $now);
            } elseif ($request->availability === 'expired') {
                // Examens terminés
                $query->where('available_until', '<', $now);
            }
        }

        // Récupérer les examens avec pagination
        $exams = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Récupérer toutes les sessions de formation pour le filtre
        $trainingSessions = TrainingSession::orderBy('title')->get();

        // Récupérer tous les domaines de formation pour le filtre
        $domains = TrainingDomain::orderBy('name')->get();

        // Récupérer tous les formateurs pour le filtre
        $trainers = User::where('role', 'trainer')
            ->orderBy('name')
            ->get();

        // Retourner la vue avec les examens et les filtres
        return Inertia::render('Admin/Exams/Index', [
            'exams' => $exams,
            'trainingSessions' => $trainingSessions,
            'domains' => $domains,
            'trainers' => $trainers,
            'filters' => [
                'search' => $request->search ?? '',
                'training_session_id' => $request->training_session_id ?? '',
                'domain_id' => $request->domain_id ?? '',
                'creator_id' => $request->creator_id ?? '',
                'status' => $request->status ?? '',
                'availability' => $request->availability ?? '',
            ]
        ]);
    }

    /**
     * Affiche le formulaire de création d'un examen
     */
    public function create()
    {
        $trainingSessions = TrainingSession::with('trainingDomain')
            ->orderBy('title')
            ->get();

        $trainers = User::where('role', 'trainer')
            ->where('active', true)
            ->orderBy('name')
            ->get();

        return Inertia::render('Admin/Exams/Create', [
            'trainingSessions' => $trainingSessions,
            'trainers' => $trainers,
            'examTypes' => [
                ['value' => 'certification', 'label' => 'Certification'],
                ['value' => 'certification_rattrapage', 'label' => 'Certification de rattrapage'],
                ['value' => 'evaluation', 'label' => 'Évaluation'],
                ['value' => 'practice', 'label' => 'Entraînement'],
                ['value' => 'quiz', 'label' => 'Quiz'],
            ]
        ]);
    }

    /**
     * Enregistre un nouvel examen
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'exam_type' => 'required|string|in:certification,certification_rattrapage,evaluation,practice,quiz',
            'description' => 'nullable|string',
            'training_session_id' => 'required|exists:training_sessions,id',
            'creator_id' => 'required|exists:users,id',
            'duration_minutes' => 'required|integer|min:1',
            'passing_score' => 'required|integer|min:0|max:100',
            'is_published' => 'required|boolean',
            'available_from' => 'nullable|date',
            'available_until' => 'nullable|date|after_or_equal:available_from',
        ]);

        // Synchroniser is_published avec status
        $status = $validated['is_published'] ? 'published' : 'draft';

        // Créer l'examen avec les données validées et status
        Exam::create(array_merge($validated, [
            'status' => $status,
        ]));

        return redirect()->route('admin.exams.index')
            ->with('success', 'Examen créé avec succès.');
    }

    /**
     * Affiche les détails d'un examen
     */
    public function show(Exam $exam)
    {
        $exam->load([
            'trainingSession',
            'trainingSession.trainingDomain',
            'creator',
            'questions'
        ]);

        // Récupérer les statistiques de l'examen
        $totalAttempts = $exam->examResults()->count();
        $passedAttempts = $exam->examResults()->where('passed', true)->count();
        $passRate = $totalAttempts > 0 ? round(($passedAttempts / $totalAttempts) * 100, 2) : 0;

        $averageScore = $totalAttempts > 0
            ? round($exam->examResults()->avg('score'), 2)
            : 0;

        return Inertia::render('Admin/Exams/Show', [
            'exam' => $exam,
            'stats' => [
                'totalAttempts' => $totalAttempts,
                'passedAttempts' => $passedAttempts,
                'passRate' => $passRate,
                'averageScore' => $averageScore
            ]
        ]);
    }

    /**
     * Affiche le formulaire de modification d'un examen
     */
    public function edit(Exam $exam)
    {
        $trainingSessions = TrainingSession::with('trainingDomain')
            ->orderBy('title')
            ->get();

        $trainers = User::where('role', 'trainer')
            ->orderBy('name')
            ->get();

        return Inertia::render('Admin/Exams/Edit', [
            'exam' => $exam,
            'trainingSessions' => $trainingSessions,
            'trainers' => $trainers,
            'examTypes' => [
                ['value' => 'certification', 'label' => 'Certification'],
                ['value' => 'certification_rattrapage', 'label' => 'Certification de rattrapage'],
                ['value' => 'evaluation', 'label' => 'Évaluation'],
                ['value' => 'practice', 'label' => 'Entraînement'],
                ['value' => 'quiz', 'label' => 'Quiz'],
            ]
        ]);
    }

    /**
     * Met à jour les informations d'un examen
     */
    public function update(Request $request, Exam $exam)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'exam_type' => 'required|string|in:certification,certification_rattrapage,evaluation,practice,quiz',
            'description' => 'nullable|string',
            'training_session_id' => 'required|exists:training_sessions,id',
            'creator_id' => 'required|exists:users,id',
            'duration_minutes' => 'required|integer|min:1',
            'passing_score' => 'required|integer|min:0|max:100',
            'is_published' => 'required|boolean',
            'available_from' => 'nullable|date',
            'available_until' => 'nullable|date|after_or_equal:available_from',
        ]);

        // Synchroniser is_published avec status
        $status = $validated['is_published'] ? 'published' : 'draft';

        // Mettre à jour l'examen avec les données validées et status
        $exam->update(array_merge($validated, [
            'status' => $status,
        ]));

        return redirect()->route('admin.exams.index')
            ->with('success', 'Examen mis à jour avec succès.');
    }

    /**
     * Affiche les questions d'un examen
     */
    public function showQuestions(Exam $exam)
    {
        // Charger les questions avec leurs options et réponses correctes
        $exam->load(['questions' => function($query) {
            $query->orderBy('order');
        }]);

        // S'assurer que les options et les réponses correctes sont correctement formatées
        foreach ($exam->questions as $question) {
            if ($question->question_type === 'multiple_choice') {
                // Vérifier et corriger les options
                if (empty($question->options) || !is_array($question->options)) {
                    $question->options = [
                        'A' => 'Option A',
                        'B' => 'Option B',
                        'C' => 'Option C',
                        'D' => 'Option D'
                    ];
                }

                // Vérifier et corriger les réponses correctes
                if (empty($question->correct_options) || !is_array($question->correct_options)) {
                    $question->correct_options = [];
                }
            }
        }

        return Inertia::render('Admin/Exams/ExamQuestions', [
            'exam' => $exam
        ]);
    }

    /**
     * Publie ou dépublie un examen
     */
    public function togglePublish(Exam $exam)
    {
        // Inverser le statut de publication
        $exam->is_published = !$exam->is_published;
        $exam->status = $exam->is_published ? 'published' : 'draft';
        $exam->save();

        return redirect()->back()
            ->with('success', $exam->is_published ? 'Examen publié avec succès.' : 'Examen dépublié avec succès.');
    }

    /**
     * Supprime un examen
     */
    public function destroy(Exam $exam)
    {
        // Vérifier si l'examen a des résultats associés
        if ($exam->examResults()->count() > 0) {
            return redirect()->route('admin.exams.index')
                ->with('error', 'Impossible de supprimer cet examen car il a des résultats associés.');
        }

        // Supprimer d'abord les questions associées
        $exam->questions()->delete();

        // Puis supprimer l'examen
        $exam->delete();

        return redirect()->route('admin.exams.index')
            ->with('success', 'Examen supprimé avec succès.');
    }
}
