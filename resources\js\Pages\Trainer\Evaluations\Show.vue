<template>
  <Head :title="evaluation.title" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Évaluation: {{ evaluation.title }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Liens de navigation -->
            <div class="mb-6">
              <Link :href="route('trainer.evaluations.index')" class="text-indigo-600 hover:text-indigo-900">
                &larr; Retour aux évaluations
              </Link>
            </div>

            <!-- Informations sur l'évaluation -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 class="text-lg font-semibold mb-2">Informations sur l'évaluation</h3>
                  <p><span class="font-medium">Session de formation:</span> {{ evaluation.training_session.title }}</p>
                  <p v-if="evaluation.course"><span class="font-medium">Cours:</span> {{ evaluation.course.title }}</p>
                  <p><span class="font-medium">Type:</span> {{ formatType(evaluation.type) }}</p>
                  <p><span class="font-medium">Statut:</span> {{ formatStatus(evaluation.status) }}</p>
                  <p v-if="evaluation.start_date"><span class="font-medium">Date de début:</span> {{ formatDate(evaluation.start_date) }}</p>
                  <p v-if="evaluation.end_date"><span class="font-medium">Date de fin:</span> {{ formatDate(evaluation.end_date) }}</p>
                  <p><span class="font-medium">Réponses anonymes:</span> {{ evaluation.is_anonymous ? 'Oui' : 'Non' }}</p>
                </div>
                <div>
                  <h3 class="text-lg font-semibold mb-2">Description</h3>
                  <p>{{ evaluation.description || 'Aucune description' }}</p>
                </div>
              </div>
              <div class="flex justify-end mt-4">
                <Link :href="route('trainer.evaluations.edit', evaluation.id)" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 mr-2">
                  Modifier
                </Link>
                <Link :href="route('trainer.evaluations.questions.index', evaluation.id)" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 mr-2">
                  Questions ({{ evaluation.questions_count }})
                </Link>
                <Link :href="route('trainer.evaluations.results', evaluation.id)" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                  Résultats
                </Link>
              </div>
            </div>

            <!-- Statistiques -->
            <div class="mb-6">
              <h3 class="text-lg font-semibold mb-4">Statistiques</h3>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <div class="text-2xl font-bold text-indigo-600">{{ evaluation.responses_count }}</div>
                  <div class="text-sm text-gray-500">Réponses totales</div>
                </div>
                <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <div class="text-2xl font-bold text-green-600">{{ stats.completed_responses }}</div>
                  <div class="text-sm text-gray-500">Réponses complétées</div>
                </div>
                <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <div class="text-2xl font-bold text-blue-600">{{ stats.completion_rate }}%</div>
                  <div class="text-sm text-gray-500">Taux de complétion</div>
                </div>
              </div>
            </div>

            <!-- Liste des réponses -->
            <div>
              <h3 class="text-lg font-semibold mb-4">Réponses des étudiants</h3>
              <div v-if="responses.length > 0">
                <div class="overflow-x-auto">
                  <table class="min-w-full bg-white border border-gray-200">
                    <thead>
                      <tr>
                        <th class="py-2 px-4 border-b text-left">Étudiant</th>
                        <th class="py-2 px-4 border-b text-left">Date de soumission</th>
                        <th class="py-2 px-4 border-b text-left">Statut</th>
                        <th class="py-2 px-4 border-b text-left">Note globale</th>
                        <th class="py-2 px-4 border-b text-center">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="response in responses" :key="response.id" class="hover:bg-gray-50">
                        <td class="py-2 px-4 border-b">
                          {{ evaluation.is_anonymous ? 'Anonyme' : response.student.name }}
                        </td>
                        <td class="py-2 px-4 border-b">
                          {{ response.submitted_at ? formatDate(response.submitted_at) : 'Non soumis' }}
                        </td>
                        <td class="py-2 px-4 border-b">
                          <span v-if="response.status === 'started'" class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Commencé</span>
                          <span v-else-if="response.status === 'completed'" class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Terminé</span>
                        </td>
                        <td class="py-2 px-4 border-b">
                          {{ response.overall_rating !== null ? response.overall_rating + '/10' : '-' }}
                        </td>
                        <td class="py-2 px-4 border-b text-center">
                          <Link :href="route('trainer.evaluations.response', [evaluation.id, response.id])" class="text-indigo-600 hover:text-indigo-900">
                            Voir les réponses
                          </Link>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <div v-else class="text-center py-8">
                <p class="text-gray-500">Aucune réponse pour cette évaluation.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  evaluation: Object,
  questions: Array,
  responses: Array,
  stats: Object,
});

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return '';
  const options = { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const formatType = (type) => {
  const types = {
    'feedback': 'Feedback',
    'survey': 'Sondage',
    'rating': 'Évaluation',
  };
  return types[type] || type;
};

const formatStatus = (status) => {
  const statuses = {
    'draft': 'Brouillon',
    'active': 'Active',
    'closed': 'Fermée',
  };
  return statuses[status] || status;
};
</script>
