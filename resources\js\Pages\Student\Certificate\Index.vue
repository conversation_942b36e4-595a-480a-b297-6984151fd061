<template>
  <Head title="Mes certificats" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Mes certificats
        </h2>

      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <div v-if="certificates.length > 0">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div v-for="certificate in certificates" :key="certificate.id" class="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                  <div class="p-4 bg-blue-50 border-b">
                    <h3 class="font-semibold text-lg text-blue-800">{{ certificate.training_session.title }}</h3>
                    <p class="text-sm text-blue-600">{{ certificate.training_session.training_domain.name }}</p>
                  </div>

                  <div class="p-4">
                    <div class="mb-4">
                      <div class="text-sm text-gray-600 mb-1">Numéro de certificat:</div>
                      <div class="font-medium">{{ certificate.certificate_number }}</div>
                    </div>

                    <div class="mb-4">
                      <div class="text-sm text-gray-600 mb-1">Date d'obtention:</div>
                      <div class="font-medium">{{ formatDate(certificate.formatted_issue_date) }}</div>
                    </div>

                    <div v-if="certificate.formatted_expiry_date" class="mb-4">
                      <div class="text-sm text-gray-600 mb-1">Date d'expiration:</div>
                      <div class="font-medium">{{ formatDate(certificate.formatted_expiry_date) }}</div>
                    </div>

                    <div class="flex justify-between items-center mt-6">
                      <button @click="previewCertificate(certificate)" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                        </svg>
                        Aperçu
                      </button>

                      <a :href="route('student.certificates.download', certificate.id)" class="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                        Télécharger
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 class="text-lg font-medium text-gray-900 mb-1">Aucun certificat disponible</h3>
              <p class="text-gray-500">Complétez vos formations et réussissez les examens pour obtenir des certificats.</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal d'aperçu du certificat - Mobile Optimized -->
    <div v-if="showPreviewModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 overflow-y-auto">
      <!-- Mobile-first modal container -->
      <div class="min-h-screen px-4 py-4 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-sm sm:max-w-lg md:max-w-4xl lg:max-w-6xl overflow-hidden flex flex-col" style="max-height: calc(100vh - 2rem);">
          <!-- Header -->
          <div class="flex justify-between items-center p-3 sm:p-4 md:p-6 border-b border-gray-200 flex-shrink-0">
            <div class="min-w-0 flex-1">
              <h3 class="text-base sm:text-lg md:text-xl font-semibold text-gray-900 truncate">Aperçu du certificat</h3>
              <p class="text-xs sm:text-sm text-gray-600 mt-1 truncate">{{ selectedCertificate.training_session?.title }}</p>
            </div>
            <button @click="showPreviewModal = false" class="ml-2 text-gray-400 hover:text-gray-600 transition-colors p-2 touch-button">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Mobile-specific notice -->
          <div class="bg-blue-50 border-b border-blue-200 p-3 sm:hidden">
            <div class="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div class="text-xs text-blue-700">
                <p class="font-medium mb-1">📱 Affichage mobile</p>
                <p>Pour une meilleure visualisation, utilisez "Nouvel onglet" ou "Télécharger".</p>
              </div>
            </div>
          </div>

          <!-- Certificate Content -->
          <div class="flex-1 p-3 sm:p-4 md:p-6 overflow-hidden">
            <div class="h-full border border-gray-300 rounded-lg overflow-hidden bg-gray-50 relative" style="min-height: 300px; max-height: 60vh;">
              <!-- Loading state -->
              <div v-if="certificateLoading" class="absolute inset-0 flex items-center justify-center bg-gray-50">
                <div class="text-center">
                  <div class="animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p class="text-gray-600 text-sm">Chargement du certificat...</p>
                </div>
              </div>

              <!-- Certificate iframe with mobile optimization -->
              <iframe
                v-if="selectedCertificate.id && !certificateError"
                :src="route('student.certificates.view', selectedCertificate.id)"
                class="w-full h-full certificate-iframe"
                title="Certificat PDF"
                frameborder="0"
                scrolling="auto"
                @load="handleCertificateLoad"
                @error="handleCertificateError"
                :style="{
                  minHeight: isMobile ? '250px' : '400px',
                  transform: isMobile ? 'scale(0.8)' : 'scale(1)',
                  transformOrigin: 'top left',
                  width: isMobile ? '125%' : '100%',
                  height: isMobile ? '125%' : '100%'
                }"
              ></iframe>

              <!-- Error state -->
              <div v-if="certificateError" class="absolute inset-0 flex items-center justify-center bg-gray-50">
                <div class="text-center p-4">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 sm:h-16 sm:w-16 text-red-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <p class="text-red-600 font-medium mb-2 text-sm sm:text-base">Erreur de chargement</p>
                  <p class="text-gray-600 text-xs sm:text-sm mb-4">Le certificat ne peut pas être affiché dans cette vue.</p>
                  <div class="space-y-2">
                    <button @click="reloadCertificate" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm touch-button">
                      Réessayer
                    </button>
                    <p class="text-xs text-gray-500">Utilisez les boutons ci-dessous pour voir le certificat</p>
                  </div>
                </div>
              </div>

              <!-- Mobile fallback message -->
              <div v-if="!certificateLoading && !certificateError && isMobile" class="absolute bottom-2 left-2 right-2 bg-white bg-opacity-90 border border-gray-300 rounded p-2 text-xs text-gray-600">
                💡 Pincer pour zoomer, faire défiler pour naviguer
              </div>
            </div>
          </div>

          <!-- Footer Actions -->
          <div class="p-3 sm:p-4 md:p-6 border-t border-gray-200 flex-shrink-0">
            <div class="flex flex-col gap-3">
              <!-- Certificate number -->
              <div class="text-xs sm:text-sm text-gray-600">
                <span class="font-medium">Numéro:</span>
                <span class="break-all">{{ selectedCertificate.certificate_number }}</span>
              </div>

              <!-- Action buttons -->
              <div class="flex flex-col sm:flex-row gap-2">
                <a
                  :href="route('student.certificates.view', selectedCertificate.id)"
                  target="_blank"
                  class="flex-1 px-4 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center justify-center transition-colors text-sm font-medium touch-button"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  <span class="hidden sm:inline">Ouvrir dans un nouvel onglet</span>
                  <span class="sm:hidden">Nouvel onglet</span>
                </a>
                <a
                  :href="route('student.certificates.download', selectedCertificate.id)"
                  class="flex-1 px-4 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center justify-center transition-colors text-sm font-medium touch-button"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                  Télécharger
                </a>
                <button
                  @click="showPreviewModal = false"
                  class="px-4 py-3 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors text-sm font-medium touch-button"
                >
                  Fermer
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  certificates: Array,
});

// État
const showPreviewModal = ref(false);
const selectedCertificate = ref({});
const certificateLoading = ref(false);
const certificateError = ref(false);

// Mobile detection
const isMobile = ref(false);

// Check if device is mobile
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// Initialize mobile detection
onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return 'Non définie';

  // Si c'est un objet avec une propriété date (Carbon serialized)
  if (typeof dateString === 'object' && dateString.date) {
    dateString = dateString.date;
  }

  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    return 'Date invalide';
  }

  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return date.toLocaleDateString('fr-FR', options);
};

const previewCertificate = (certificate) => {
  selectedCertificate.value = certificate;
  certificateLoading.value = true;
  certificateError.value = false;
  showPreviewModal.value = true;

  // On mobile, show error state immediately to encourage using new tab/download
  if (isMobile.value) {
    setTimeout(() => {
      if (certificateLoading.value) {
        certificateLoading.value = false;
        certificateError.value = true;
      }
    }, 3000); // Give 3 seconds for PDF to load on mobile
  }
};

const handleCertificateLoad = () => {
  certificateLoading.value = false;
  certificateError.value = false;
};

const handleCertificateError = () => {
  certificateLoading.value = false;
  certificateError.value = true;
};

const reloadCertificate = () => {
  certificateError.value = false;
  certificateLoading.value = true;

  // Force reload the iframe by changing its src
  const iframe = document.querySelector('iframe[title="Certificat PDF"]');
  if (iframe && selectedCertificate.value.id) {
    const currentSrc = iframe.src;
    iframe.src = '';
    setTimeout(() => {
      iframe.src = currentSrc;
      // On mobile, set timeout again
      if (isMobile.value) {
        setTimeout(() => {
          if (certificateLoading.value) {
            certificateLoading.value = false;
            certificateError.value = true;
          }
        }, 3000);
      }
    }, 100);
  }
};
</script>
