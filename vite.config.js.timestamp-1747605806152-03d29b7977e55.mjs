// vite.config.js
import { defineConfig } from "file:///C:/formationpcmet5/node_modules/vite/dist/node/index.js";
import laravel from "file:///C:/formationpcmet5/node_modules/laravel-vite-plugin/dist/index.js";
import vue from "file:///C:/formationpcmet5/node_modules/@vitejs/plugin-vue/dist/index.mjs";
var vite_config_default = defineConfig({
  plugins: [
    laravel({
      input: [
        "resources/js/app.js",
        "resources/js/Pages/Admin/Dashboard.vue",
        "resources/js/Pages/Admin/TrainingDomains/Index.vue",
        "resources/js/Pages/Admin/TrainingDomains/Create.vue",
        "resources/js/Pages/Admin/TrainingDomains/Edit.vue",
        "resources/js/Pages/Admin/TrainingDomains/Show.vue",
        "resources/js/Pages/Admin/Trainers/Index.vue",
        "resources/js/Pages/Admin/Trainers/Create.vue",
        "resources/js/Pages/Admin/Trainers/Edit.vue",
        "resources/js/Pages/Admin/Trainers/Show.vue",
        "resources/js/Pages/Admin/Exams/Index.vue",
        "resources/js/Pages/Admin/Exams/Create.vue",
        "resources/js/Pages/Admin/Exams/Edit.vue",
        "resources/js/Pages/Admin/Exams/Show.vue",
        "resources/js/Pages/Admin/TrainingSessions/Index.vue",
        "resources/js/Pages/Admin/TrainingSessions/Create.vue",
        "resources/js/Pages/Admin/TrainingSessions/Edit.vue",
        "resources/js/Pages/Admin/TrainingSessions/Show.vue",
        "resources/js/Pages/Admin/Enrollments/Index.vue",
        "resources/js/Pages/Admin/Enrollments/Create.vue",
        "resources/js/Pages/Admin/Enrollments/Edit.vue",
        "resources/js/Pages/Admin/Enrollments/Show.vue",
        "resources/js/Pages/Admin/ExamResults/Index.vue",
        "resources/js/Pages/Admin/ExamResults/Create.vue",
        "resources/js/Pages/Admin/ExamResults/Edit.vue",
        "resources/js/Pages/Admin/ExamResults/Show.vue",
        "resources/js/Pages/Admin/Certificates/Index.vue",
        "resources/js/Pages/Admin/Certificates/Create.vue",
        "resources/js/Pages/Admin/Certificates/Edit.vue",
        "resources/js/Pages/Admin/Certificates/Show.vue",
        "resources/js/Pages/Admin/ExamQuestions/Index.vue",
        "resources/js/Pages/Admin/ExamQuestions/Create.vue",
        "resources/js/Pages/Admin/ExamQuestions/Edit.vue",
        "resources/js/Pages/Admin/ExamQuestions/Show.vue",
        "resources/js/Pages/Trainer/QuestionBank/Index.vue",
        "resources/js/Pages/Trainer/QuestionBank/Create.vue",
        "resources/js/Pages/Trainer/QuestionBank/Edit.vue",
        "resources/js/Pages/Trainer/Modules/Index.vue",
        "resources/js/Pages/Trainer/Modules/Create.vue",
        "resources/js/Pages/Trainer/Modules/Edit.vue",
        "resources/js/Pages/Trainer/Modules/Show.vue",
        "resources/js/Pages/Trainer/Courses/Index.vue",
        "resources/js/Pages/Trainer/Evaluations/Index.vue",
        "resources/js/Pages/Trainer/Evaluations/Create.vue",
        "resources/js/Pages/Trainer/Evaluations/Edit.vue",
        "resources/js/Pages/Trainer/Evaluations/Show.vue",
        "resources/js/Pages/Trainer/Evaluations/Results.vue",
        "resources/js/Pages/Trainer/Evaluations/Questions/Index.vue",
        "resources/js/Pages/Trainer/Evaluations/Questions/Create.vue",
        "resources/js/Pages/Trainer/Evaluations/Questions/Edit.vue",
        "resources/js/Pages/Trainer/Exams/Index.vue",
        "resources/js/Pages/Trainer/ExamResults/Index.vue",
        "resources/js/Pages/Welcome.vue",
        "resources/js/Pages/Sessions/Show.vue"
      ],
      refresh: true
    }),
    vue({
      template: {
        transformAssetUrls: {
          base: null,
          includeAbsolute: false
        }
      }
    })
  ]
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
