<?php

// Ce script corrige directement les questions dans la base de données

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Options par défaut
$defaultOptions = json_encode([
    'A' => 'Option A',
    'B' => 'Option B',
    'C' => 'Option C',
    'D' => 'Option D'
]);

// Réponses correctes par défaut
$defaultCorrectOptions = json_encode(['A']);

// Mettre à jour toutes les questions à choix multiple
$updated = DB::table('exam_questions')
    ->where('question_type', 'multiple_choice')
    ->update([
        'options' => $defaultOptions,
        'correct_options' => $defaultCorrectOptions
    ]);

echo "Mise à jour terminée. $updated questions ont été mises à jour.\n";
