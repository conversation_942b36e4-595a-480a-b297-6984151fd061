<?php

namespace App\Http\Controllers;

use App\Models\Enrollment;
use App\Models\TrainingSession;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;

class SessionEnrollmentController extends Controller
{
    /**
     * Affiche le formulaire d'inscription pour une session de formation
     */
    public function create($sessionId)
    {
        // Récupérer la session de formation avec ses créneaux horaires
        $session = TrainingSession::with(['trainingDomain', 'trainer', 'activeTimeSlots'])
            ->where('active', true)
            ->findOrFail($sessionId);

        // Vérifier si l'utilisateur est déjà connecté
        if (Auth::check()) {
            // Vérifier si l'utilisateur est déjà inscrit à cette session
            $enrollment = Enrollment::where('user_id', Auth::id())
                ->where('training_session_id', $session->id)
                ->first();

            if ($enrollment) {
                return redirect()->route('student.dashboard')
                    ->with('info', 'Vous êtes déjà inscrit à cette session de formation.');
            }

            // Rediriger vers la page de confirmation d'inscription
            return Inertia::render('Sessions/Enroll', [
                'session' => $session,
                'user' => Auth::user(),
                'isLoggedIn' => true
            ]);
        }

        // Si l'utilisateur n'est pas connecté, afficher le formulaire d'inscription
        return Inertia::render('Sessions/Enroll', [
            'session' => $session,
            'isLoggedIn' => false,
            'discoveryOptions' => [
                ['value' => 'facebook', 'label' => 'Facebook'],
                ['value' => 'instagram', 'label' => 'Instagram'],
                ['value' => 'tiktok', 'label' => 'TikTok'],
                ['value' => 'word_of_mouth', 'label' => 'Bouche à oreille'],
                ['value' => 'other', 'label' => 'Autre'],
            ]
        ]);
    }

    /**
     * Traite l'inscription d'un utilisateur existant à une session
     */
    public function storeExistingUser(Request $request, $sessionId)
    {
        // Vérifier que l'utilisateur est connecté
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // Validation conditionnelle selon le statut d'étudiant étranger
        $isForeignStudent = $request->boolean('is_foreign_student');

        $rules = [
            'phone' => 'required|string|max:30',
            'is_foreign_student' => 'boolean',
            'profession' => 'nullable|string|max:100',
            'company' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:255',
            'discovery_source' => 'nullable|string|max:50',
            'discovery_source_other' => 'nullable|string|max:100',
            'payment_method' => 'nullable|string|max:50',
            'payment_date' => 'nullable|date',
            'payment_due_date' => 'nullable|date',
            'payment_proof' => 'nullable|file|mimes:jpeg,png,jpg,gif,pdf|max:2048',
            'notes' => 'nullable|string|max:500',
        ];

        // Validation conditionnelle pour les champs d'identification
        if ($isForeignStudent) {
            $rules['passport_number'] = 'required|string|max:50';
            $rules['id_card_number'] = 'nullable|string|max:50';
        } else {
            $rules['id_card_number'] = 'required|string|max:50';
            $rules['passport_number'] = 'nullable|string|max:50';
        }

        $messages = [
            'id_card_number.required' => 'Le numéro de carte d\'identité est obligatoire.',
            'passport_number.required' => 'Le numéro de passeport est obligatoire pour les étudiants étrangers.',
            'phone.required' => 'Le numéro de téléphone est obligatoire.',
        ];

        $validated = $request->validate($rules, $messages);

        // Récupérer la session de formation avec ses créneaux horaires
        $session = TrainingSession::with('activeTimeSlots')->where('active', true)->findOrFail($sessionId);

        // Valider le créneau horaire si fourni
        $timeSlotId = null;
        if ($request->has('time_slot_id') && $request->time_slot_id) {
            $timeSlot = $session->activeTimeSlots()->find($request->time_slot_id);
            if (!$timeSlot) {
                return redirect()->back()->with('error', 'Créneau horaire invalide.');
            }

            // Vérifier si le créneau est complet
            if ($timeSlot->isFull()) {
                return redirect()->back()->with('error', 'Ce créneau horaire est complet.');
            }

            $timeSlotId = $timeSlot->id;
        }

        // Vérifier si l'utilisateur est déjà inscrit à cette session
        $existingEnrollment = Enrollment::where('user_id', Auth::id())
            ->where('training_session_id', $session->id)
            ->first();

        if ($existingEnrollment) {
            return redirect()->route('student.dashboard')
                ->with('info', 'Vous êtes déjà inscrit à cette session de formation.');
        }

        // Vérifier si la session a un nombre maximum de participants et si elle est pleine
        if ($session->max_participants &&
            $session->enrollments()->count() >= $session->max_participants) {
            return redirect()->back()
                ->with('error', 'Cette session de formation est complète.');
        }

        // Mettre à jour les informations de l'utilisateur
        $user = Auth::user();
        $user->update([
            'phone' => $validated['phone'],
            'id_card_number' => $validated['id_card_number'] ?? null,
            'passport_number' => $validated['passport_number'] ?? null,
            'is_foreign_student' => $validated['is_foreign_student'] ?? false,
            'profession' => $validated['profession'] ?? null,
            'company' => $validated['company'] ?? null,
            'address' => $validated['address'] ?? null,
            'discovery_source' => $validated['discovery_source'] ?? null,
            'discovery_source_other' => $validated['discovery_source_other'] ?? null,
            'notes' => $validated['notes'] ?? '',
        ]);

        // Gérer l'upload du justificatif de paiement
        $paymentProofPath = null;
        if ($request->hasFile('payment_proof')) {
            $paymentProofPath = $request->file('payment_proof')->store('payment_proofs', 'public');
        }

        // Créer l'inscription
        $enrollment = Enrollment::create([
            'user_id' => Auth::id(),
            'training_session_id' => $session->id,
            'time_slot_id' => $timeSlotId,
            'status' => 'pending',
            'enrollment_date' => now(),
            'notes' => $validated['notes'] ?? '',
            'payment_method' => $validated['payment_method'] ?? null,
            'payment_date' => $validated['payment_date'] ?? null,
            'payment_due_date' => $validated['payment_due_date'] ?? null,
            'payment_proof' => $paymentProofPath,
        ]);

        // Rediriger vers la page de confirmation
        return redirect()->route('session.enrollment.confirmation', $enrollment->id);
    }

    /**
     * Traite l'inscription d'un nouvel utilisateur à une session
     */
    public function storeNewUser(Request $request, $sessionId)
    {
        // Validation conditionnelle selon le statut d'étudiant étranger
        $isForeignStudent = $request->boolean('is_foreign_student');

        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:users',
            'phone' => 'required|string|max:20',
            'is_foreign_student' => 'boolean',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'birth_date' => 'nullable|date',
            'profession' => 'nullable|string|max:100',
            'company' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:255',
            'discovery_source' => 'nullable|string|max:50',
            'discovery_source_other' => 'nullable|string|max:100',
            'payment_method' => 'nullable|string|max:50',
            'payment_date' => 'nullable|date',
            'payment_due_date' => 'nullable|date',
            'payment_proof' => 'nullable|file|mimes:jpeg,png,jpg,gif,pdf|max:2048',
            'notes' => 'nullable|string|max:500',
        ];

        // Validation conditionnelle pour les champs d'identification
        if ($isForeignStudent) {
            $rules['passport_number'] = 'required|string|max:50';
            $rules['id_card_number'] = 'nullable|string|max:50';
        } else {
            $rules['id_card_number'] = 'required|string|max:50';
            $rules['passport_number'] = 'nullable|string|max:50';
        }

        $messages = [
            'id_card_number.required' => 'Le numéro de carte d\'identité est obligatoire.',
            'passport_number.required' => 'Le numéro de passeport est obligatoire pour les étudiants étrangers.',
            'phone.required' => 'Le numéro de téléphone est obligatoire.',
            'name.required' => 'Le nom complet est obligatoire.',
            'email.required' => 'L\'adresse email est obligatoire.',
            'email.unique' => 'Cette adresse email est déjà utilisée.',
            'password.required' => 'Le mot de passe est obligatoire.',
            'password.confirmed' => 'La confirmation du mot de passe ne correspond pas.',
        ];

        $validated = $request->validate($rules, $messages);

        // Récupérer la session de formation avec ses créneaux horaires
        $session = TrainingSession::with('activeTimeSlots')->where('active', true)->findOrFail($sessionId);

        // Valider le créneau horaire si fourni
        $timeSlotId = null;
        if ($request->has('time_slot_id') && $request->time_slot_id) {
            $timeSlot = $session->activeTimeSlots()->find($request->time_slot_id);
            if (!$timeSlot) {
                return redirect()->back()->with('error', 'Créneau horaire invalide.');
            }

            // Vérifier si le créneau est complet
            if ($timeSlot->isFull()) {
                return redirect()->back()->with('error', 'Ce créneau horaire est complet.');
            }

            $timeSlotId = $timeSlot->id;
        }

        // Vérifier si la session a un nombre maximum de participants et si elle est pleine
        if ($session->max_participants &&
            $session->enrollments()->count() >= $session->max_participants) {
            return redirect()->back()
                ->with('error', 'Cette session de formation est complète.');
        }

        // Gérer l'upload du justificatif de paiement
        $paymentProofPath = null;
        if ($request->hasFile('payment_proof')) {
            $paymentProofPath = $request->file('payment_proof')->store('payment_proofs', 'public');
        }

        // Créer le nouvel utilisateur
        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'phone' => $validated['phone'],
            'id_card_number' => $validated['id_card_number'],
            'passport_number' => $validated['passport_number'] ?? null,
            'is_foreign_student' => $validated['is_foreign_student'] ?? false,
            'birth_date' => $validated['birth_date'] ?? null,
            'profession' => $validated['profession'] ?? null,
            'company' => $validated['company'] ?? null,
            'address' => $validated['address'] ?? null,
            'discovery_source' => $validated['discovery_source'] ?? null,
            'discovery_source_other' => $validated['discovery_source_other'] ?? null,
            'notes' => $validated['notes'] ?? null,
            'role' => 'student',
            'active' => true,
        ]);

        // Créer l'inscription
        $enrollment = Enrollment::create([
            'user_id' => $user->id,
            'training_session_id' => $session->id,
            'time_slot_id' => $timeSlotId,
            'status' => 'pending',
            'enrollment_date' => now(),
            'notes' => $validated['notes'] ?? '',
            'payment_method' => $validated['payment_method'] ?? null,
            'payment_date' => $validated['payment_date'] ?? null,
            'payment_due_date' => $validated['payment_due_date'] ?? null,
            'payment_proof' => $paymentProofPath,
        ]);

        // Connecter l'utilisateur
        Auth::login($user);

        // Rediriger vers la page de confirmation
        return redirect()->route('session.enrollment.confirmation', $enrollment->id);
    }

    /**
     * Affiche la page de confirmation d'inscription
     */
    public function confirmation($enrollmentId)
    {
        try {
            // Récupérer l'inscription avec ses relations
            $enrollment = Enrollment::with(['user', 'trainingSession', 'trainingSession.trainingDomain', 'trainingSession.trainer'])
                ->findOrFail($enrollmentId);

            // Vérifier que l'utilisateur connecté est bien celui qui s'est inscrit
            if (Auth::id() !== $enrollment->user_id) {
                return redirect()->route('welcome')
                    ->with('error', 'Vous n\'avez pas accès à cette page.');
            }

            // Vérifier que les relations sont bien chargées
            if (!$enrollment->trainingSession) {
                // Si la session n'est pas trouvée, on la recherche manuellement
                $trainingSession = \App\Models\TrainingSession::find($enrollment->training_session_id);
                if ($trainingSession) {
                    $enrollment->trainingSession = $trainingSession;
                    $enrollment->trainingSession->load(['trainingDomain', 'trainer']);
                }
            }

            return Inertia::render('Sessions/EnrollmentConfirmation', [
                'enrollment' => $enrollment
            ]);
        } catch (\Exception $e) {
            // En cas d'erreur, on affiche quand même la page avec un message d'erreur
            return Inertia::render('Sessions/EnrollmentConfirmation', [
                'enrollment' => null,
                'error' => 'Une erreur est survenue lors du chargement des informations d\'inscription.'
            ]);
        }
    }
}
