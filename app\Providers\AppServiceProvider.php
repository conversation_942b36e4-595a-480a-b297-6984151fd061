<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\Enrollment;
use App\Models\ExamResult;
use App\Models\Certificate;
use App\Observers\EnrollmentObserver;
use App\Observers\ExamResultObserver;
use App\Observers\CertificateObserver;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register observers for automated certificate management and notifications
        Enrollment::observe(EnrollmentObserver::class);
        ExamResult::observe(ExamResultObserver::class);
        Certificate::observe(CertificateObserver::class);
    }
}
