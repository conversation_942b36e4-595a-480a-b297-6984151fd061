<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Evaluation extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'evaluations';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'training_session_id',
        'course_id',
        'trainer_id',
        'type',
        'status',
        'start_date',
        'end_date',
        'is_anonymous',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_anonymous' => 'boolean',
    ];

    /**
     * Relation avec la session de formation
     */
    public function trainingSession()
    {
        return $this->belongsTo(TrainingSession::class);
    }

    /**
     * Relation avec le cours
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Relation avec le formateur
     */
    public function trainer()
    {
        return $this->belongsTo(User::class, 'trainer_id');
    }

    /**
     * Relation avec les questions d'évaluation
     */
    public function questions()
    {
        return $this->hasMany(EvaluationQuestion::class);
    }

    /**
     * Relation avec les réponses d'évaluation
     */
    public function responses()
    {
        return $this->hasMany(EvaluationResponse::class);
    }
}
