<template>
  <Head :title="session.title" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Détails de la session
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Informations de la session -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <div class="flex flex-col md:flex-row">
              <!-- Image de la session -->
              <div class="md:w-1/3 mb-6 md:mb-0 md:pr-6">
                <div v-if="session.image" class="h-64 bg-gray-200 rounded-lg overflow-hidden">
                  <img :src="'/storage/' + session.image" :alt="session.title" class="w-full h-full object-cover" @error="handleImageError">
                </div>
                <div v-else class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>

              <!-- Détails de la session -->
              <div class="md:w-2/3">
                <div class="flex items-center justify-between mb-4">
                  <h1 class="text-2xl font-bold text-gray-900">{{ session.title }}</h1>
                  <span class="px-3 py-1 text-sm font-semibold rounded-full" :class="getStatusClass(session)">
                    {{ getStatusLabel(session) }}
                  </span>
                </div>

                <div class="mb-4">
                  <span class="px-3 py-1 text-sm font-semibold text-blue-600 bg-blue-100 rounded-full">
                    {{ session.training_domain?.name || 'Non catégorisé' }}
                  </span>
                </div>

                <div class="text-gray-700 mb-4">
                  {{ session.description }}
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <h3 class="text-sm font-semibold text-gray-700">Formateur</h3>
                    <p class="text-gray-600">{{ session.trainer?.name || 'Non assigné' }}</p>
                  </div>
                  <div>
                    <h3 class="text-sm font-semibold text-gray-700">Dates</h3>
                    <p class="text-gray-600">{{ formatDate(session.start_date) }} - {{ formatDate(session.end_date) }}</p>
                  </div>
                  <div>
                    <h3 class="text-sm font-semibold text-gray-700">Lieu</h3>
                    <p class="text-gray-600">{{ session.location || 'Non spécifié' }}</p>
                  </div>
                  <div>
                    <h3 class="text-sm font-semibold text-gray-700">Places</h3>
                    <p class="text-gray-600">{{ session.enrollments_count || 0 }} / {{ session.max_participants || 'Illimité' }}</p>
                  </div>
                  <div>
                    <h3 class="text-sm font-semibold text-gray-700">Prix</h3>
                    <p class="text-gray-600">{{ session.price ? formatPrice(session.price) : 'Gratuit' }}</p>
                  </div>
                </div>

                <div v-if="session.prerequisites" class="mb-6">
                  <h3 class="text-sm font-semibold text-gray-700 mb-2">Prérequis</h3>
                  <div class="text-gray-600" v-html="session.prerequisites"></div>
                </div>

                <!-- Créneaux horaires -->
                <div v-if="session.time_slots && session.time_slots.length > 0" class="mb-6">
                  <h3 class="text-sm font-semibold text-gray-700 mb-3">Créneaux horaires disponibles</h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div
                      v-for="timeSlot in session.time_slots"
                      :key="timeSlot.id"
                      :class="[
                        'p-4 border rounded-lg',
                        timeSlot.is_full
                          ? 'border-red-200 bg-red-50'
                          : 'border-green-200 bg-green-50'
                      ]"
                    >
                      <div class="flex items-center justify-between">
                        <div>
                          <div class="font-medium text-gray-900">
                            {{ timeSlot.name || 'Créneau' }}
                          </div>
                          <div class="text-sm text-gray-600">
                            {{ timeSlot.formatted_start_time }} - {{ timeSlot.formatted_end_time }}
                          </div>
                        </div>
                        <div class="text-sm">
                          <span v-if="timeSlot.is_full" class="text-red-600 font-medium">
                            Complet
                          </span>
                          <span v-else-if="timeSlot.max_participants" class="text-green-600">
                            {{ timeSlot.available_spots }} places restantes
                          </span>
                          <span v-else class="text-green-600">
                            Places illimitées
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="flex space-x-4">
                  <Link
                    v-if="!isEnrolled && canEnroll"
                    :href="route('session.enrollment.create', session.id)"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    S'inscrire à cette session
                  </Link>
                  <Link
                    v-if="isEnrolled"
                    :href="route('student.enrollments.show', enrollment.id)"
                    class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Voir mon inscription
                  </Link>
                  <Link
                    :href="route('student.sessions.index')"
                    class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                  >
                    Retour aux sessions
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Contenu de la formation -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Cours de la session -->
          <div class="md:col-span-2">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
              <div class="p-6">
                <h3 class="text-lg font-semibold mb-4">Contenu de la formation</h3>
                
                <div v-if="courses.length > 0">
                  <div v-for="(course, index) in courses" :key="course.id" class="mb-4 last:mb-0">
                    <div class="border rounded-lg p-4 hover:bg-gray-50">
                      <div class="flex justify-between items-start">
                        <div>
                          <h4 class="font-medium">{{ course.title }}</h4>
                          <p class="text-sm text-gray-600 mt-1">{{ course.description }}</p>
                        </div>
                        <div class="text-sm text-gray-500">
                          Module {{ index + 1 }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div v-else class="text-gray-500 italic">
                  Aucun cours n'est encore disponible pour cette session.
                </div>
              </div>
            </div>
          </div>

          <!-- Informations complémentaires -->
          <div>
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
              <div class="p-6">
                <h3 class="text-lg font-semibold mb-4">Informations complémentaires</h3>
                
                <div v-if="isEnrolled" class="mb-4">
                  <div class="bg-green-50 border border-green-200 rounded-md p-4">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                      </div>
                      <div class="ml-3">
                        <h3 class="text-sm font-medium text-green-800">Vous êtes inscrit à cette session</h3>
                        <div class="mt-2 text-sm text-green-700">
                          <p>Statut: {{ getEnrollmentStatusLabel(enrollment.status) }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="space-y-4">
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">Durée</h4>
                    <p class="text-sm text-gray-600">{{ getDuration(session.start_date, session.end_date) }}</p>
                  </div>
                  
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">Domaine</h4>
                    <p class="text-sm text-gray-600">{{ session.training_domain?.name || 'Non catégorisé' }}</p>
                  </div>
                  
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">Formateur</h4>
                    <p class="text-sm text-gray-600">{{ session.trainer?.name || 'Non assigné' }}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
              <div class="p-6">
                <h3 class="text-lg font-semibold mb-4">Actions</h3>
                
                <div class="space-y-2">
                  <Link
                    :href="route('student.sessions.index')"
                    class="block w-full px-4 py-2 bg-gray-200 text-gray-700 text-center rounded-md hover:bg-gray-300"
                  >
                    Retour aux sessions
                  </Link>
                  
                  <Link
                    v-if="!isEnrolled && canEnroll"
                    :href="route('session.enrollment.create', session.id)"
                    class="block w-full px-4 py-2 bg-blue-600 text-white text-center rounded-md hover:bg-blue-700"
                  >
                    S'inscrire à cette session
                  </Link>
                  
                  <Link
                    v-if="isEnrolled"
                    :href="route('student.enrollments.show', enrollment.id)"
                    class="block w-full px-4 py-2 bg-green-600 text-white text-center rounded-md hover:bg-green-700"
                  >
                    Voir mon inscription
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { computed } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  session: Object,
  courses: Array,
  enrollment: Object,
  isEnrolled: Boolean,
  canEnroll: Boolean,
});

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return 'Date non définie';
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  });
};

const formatPrice = (price) => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR',
  }).format(price);
};

const getStatusClass = (session) => {
  const now = new Date();
  const startDate = new Date(session.start_date);
  const endDate = new Date(session.end_date);

  if (now < startDate) {
    return 'bg-yellow-100 text-yellow-800'; // À venir
  } else if (now >= startDate && now <= endDate) {
    return 'bg-green-100 text-green-800'; // En cours
  } else {
    return 'bg-gray-100 text-gray-800'; // Terminée
  }
};

const getStatusLabel = (session) => {
  const now = new Date();
  const startDate = new Date(session.start_date);
  const endDate = new Date(session.end_date);

  if (now < startDate) {
    return 'À venir';
  } else if (now >= startDate && now <= endDate) {
    return 'En cours';
  } else {
    return 'Terminée';
  }
};

const getEnrollmentStatusLabel = (status) => {
  const statusLabels = {
    'pending': 'En attente',
    'approved': 'Approuvée',
    'rejected': 'Rejetée',
    'cancelled': 'Annulée',
  };
  return statusLabels[status] || status;
};

const getDuration = (startDate, endDate) => {
  if (!startDate || !endDate) return 'Durée non définie';
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end - start);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) {
    return '1 jour';
  } else if (diffDays < 7) {
    return `${diffDays} jours`;
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return weeks === 1 ? '1 semaine' : `${weeks} semaines`;
  } else {
    const months = Math.floor(diffDays / 30);
    return months === 1 ? '1 mois' : `${months} mois`;
  }
};

const handleImageError = (event) => {
  console.error('Erreur de chargement de l\'image de la session');
  event.target.src = '/images/default-session.jpg';
};
</script>
