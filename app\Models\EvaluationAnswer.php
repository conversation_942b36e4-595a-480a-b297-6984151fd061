<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EvaluationAnswer extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'evaluation_answers';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'evaluation_response_id',
        'evaluation_question_id',
        'answer_text',
        'answer_option',
        'rating_value',
    ];

    /**
     * Relation avec la réponse d'évaluation
     */
    public function response()
    {
        return $this->belongsTo(EvaluationResponse::class, 'evaluation_response_id');
    }

    /**
     * Relation avec la question d'évaluation
     */
    public function question()
    {
        return $this->belongsTo(EvaluationQuestion::class, 'evaluation_question_id');
    }
}
