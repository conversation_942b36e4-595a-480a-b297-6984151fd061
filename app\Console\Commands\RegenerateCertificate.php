<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Certificate;
use Illuminate\Support\Facades\Storage;

class RegenerateCertificate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'certificate:regenerate {id : The certificate ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Regenerate a certificate PDF with the current template';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $id = $this->argument('id');
        
        // Récupérer le certificat
        $certificate = Certificate::find($id);
        
        if (!$certificate) {
            $this->error("Certificat ID {$id} non trouvé.");
            return 1;
        }
        
        $this->info("Certificat trouvé:");
        $this->info("- ID: " . $certificate->id);
        $this->info("- Numéro: " . $certificate->certificate_number);
        $this->info("- PDF Path: " . ($certificate->pdf_path ?? 'null'));
        
        // Supprimer le PDF existant s'il existe
        $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';
        $this->info("Chemin PDF calculé: " . $pdfPath);
        
        if (Storage::disk('public')->exists($pdfPath)) {
            $this->info("Suppression du PDF existant...");
            Storage::disk('public')->delete($pdfPath);
            $this->info("PDF supprimé.");
        } else {
            $this->info("Aucun PDF existant trouvé.");
        }
        
        // Réinitialiser le chemin du PDF dans la base de données
        $certificate->update(['pdf_path' => null]);
        $this->info("Chemin PDF réinitialisé dans la base de données.");
        
        $this->info("Régénération terminée. Le prochain accès au certificat générera un nouveau PDF avec le design actuel.");
        $this->info("Accédez à: http://127.0.0.1:8000/admin/certificates/{$id}/view");
        
        return 0;
    }
}
