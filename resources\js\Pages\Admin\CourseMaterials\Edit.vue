<template>
  <Head title="Modifier un matériel pédagogique" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Modifier un matériel pédagogique
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link :href="route('admin.course-materials.index', { course_id: material.course_id })" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à la liste
              </Link>
            </div>

            <!-- Informations sur le cours -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <h3 class="text-lg font-semibold mb-2">Cours: {{ material.course.title }}</h3>
            </div>

            <!-- Formulaire de modification de matériel -->
            <form @submit.prevent="submit" enctype="multipart/form-data">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Titre -->
                <div>
                  <InputLabel for="title" value="Titre du matériel" />
                  <TextInput
                    id="title"
                    type="text"
                    class="mt-1 block w-full"
                    v-model="form.title"
                    required
                    autofocus
                  />
                  <InputError class="mt-2" :message="form.errors.title" />
                </div>

                <!-- Type de matériel -->
                <div>
                  <InputLabel for="type" value="Type de matériel" />
                  <select
                    id="type"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.type"
                    required
                    @change="handleTypeChange"
                  >
                    <option value="">Sélectionner un type</option>
                    <option v-for="type in materialTypes" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.type" />
                </div>

                <!-- Description -->
                <div class="md:col-span-2">
                  <InputLabel for="description" value="Description" />
                  <textarea
                    id="description"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.description"
                    rows="3"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.description" />
                </div>

                <!-- Contenu texte (si type = text) -->
                <div v-if="form.type === 'text'" class="md:col-span-2">
                  <InputLabel for="content" value="Contenu" />
                  <textarea
                    id="content"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    v-model="form.content"
                    rows="10"
                    required
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.content" />
                </div>

                <!-- Fichier actuel (si type = pdf, video, audio, image, archive) -->
                <div v-if="['pdf', 'video', 'audio', 'image', 'archive'].includes(form.type) && material.file_path" class="md:col-span-2">
                  <InputLabel value="Fichier actuel" />
                  <div class="mt-2 flex items-center">
                    <div v-if="form.type === 'image'" class="mr-4">
                      <img :src="`/storage/${material.file_path}`" alt="Aperçu" class="h-16 w-auto object-contain">
                    </div>
                    <div v-else class="mr-4">
                      <svg v-if="form.type === 'pdf'" class="h-8 w-8 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                      </svg>
                      <svg v-else-if="form.type === 'video'" class="h-8 w-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zm12.553 1.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                      </svg>
                      <svg v-else-if="form.type === 'audio'" class="h-8 w-8 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071a1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243a1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828a1 1 0 010-1.415z" clip-rule="evenodd" />
                      </svg>
                      <svg v-else-if="form.type === 'archive'" class="h-8 w-8 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V8a2 2 0 00-2-2h-5L9 4H4zm7 5a1 1 0 10-2 0v1H8a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <p class="text-sm font-medium">{{ getFileName(material.file_path) }}</p>
                      <p v-if="material.file_size" class="text-xs text-gray-500">{{ formatFileSize(material.file_size) }}</p>
                    </div>
                  </div>
                </div>

                <!-- Nouveau fichier (si type = pdf, video, audio, image, archive) -->
                <div v-if="['pdf', 'video', 'audio', 'image', 'archive'].includes(form.type)" class="md:col-span-2">
                  <InputLabel for="file" :value="`Nouveau fichier ${formatMaterialType(form.type)} (optionnel)`" />
                  <input
                    id="file"
                    type="file"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
                    @input="form.file = $event.target.files[0]"
                    :accept="getAcceptTypes(form.type)"
                  />
                  <p class="text-sm text-gray-500 mt-1">
                    Laissez vide pour conserver le fichier actuel. {{ getFileTypeHelp(form.type) }}
                  </p>
                  <InputError class="mt-2" :message="form.errors.file" />
                </div>

                <!-- Ordre -->
                <div>
                  <InputLabel for="order" value="Ordre d'affichage" />
                  <TextInput
                    id="order"
                    type="number"
                    class="mt-1 block w-full"
                    v-model="form.order"
                    min="0"
                  />
                  <InputError class="mt-2" :message="form.errors.order" />
                </div>

                <!-- Options -->
                <div class="md:col-span-2">
                  <div class="flex flex-col space-y-2">
                    <div class="flex items-center">
                      <input
                        id="active"
                        type="checkbox"
                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                        v-model="form.active"
                      />
                      <InputLabel for="active" value="Actif" class="ml-2" />
                    </div>
                    <div v-if="['pdf', 'video', 'audio', 'image', 'archive'].includes(form.type)" class="flex items-center">
                      <input
                        id="allow_download"
                        type="checkbox"
                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                        v-model="form.allow_download"
                      />
                      <InputLabel for="allow_download" value="Autoriser le téléchargement" class="ml-2" />
                    </div>
                    <div v-if="['pdf', 'video', 'audio', 'image'].includes(form.type)" class="flex items-center">
                      <input
                        id="allow_online_viewing"
                        type="checkbox"
                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                        v-model="form.allow_online_viewing"
                      />
                      <InputLabel for="allow_online_viewing" value="Autoriser la visualisation en ligne" class="ml-2" />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="flex items-center justify-end mt-6">
                <Link
                  :href="route('admin.course-materials.show', material.id)"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 mr-2"
                >
                  Voir le matériel
                </Link>
                <PrimaryButton class="ml-4" :disabled="form.processing">
                  Mettre à jour le matériel
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Props
const props = defineProps({
  material: Object,
  materialTypes: Array,
});

// Formulaire
const form = useForm({
  title: props.material.title,
  description: props.material.description || '',
  type: props.material.type,
  content: props.material.content || '',
  file: null,
  order: props.material.order,
  active: props.material.active,
  allow_download: props.material.allow_download ?? true,
  allow_online_viewing: props.material.allow_online_viewing ?? true,
});

// Méthodes
const formatMaterialType = (type) => {
  const types = {
    'text': 'Texte',
    'pdf': 'Document PDF',
    'video': 'Vidéo',
    'audio': 'Audio',
    'image': 'Image',
    'quiz': 'Quiz'
  };
  return types[type] || type;
};

const getAcceptTypes = (type) => {
  const acceptTypes = {
    'pdf': '.pdf',
    'video': 'video/*',
    'audio': 'audio/*',
    'image': 'image/*',
    'archive': '.zip,.rar,.7z,.tar,.gz'
  };
  return acceptTypes[type] || '';
};

const getFileTypeHelp = (type) => {
  const helpText = {
    'pdf': 'Formats acceptés: PDF. Taille illimitée.',
    'video': 'Formats acceptés: MP4, WebM, etc. Taille illimitée.',
    'audio': 'Formats acceptés: MP3, WAV, etc. Taille illimitée.',
    'image': 'Formats acceptés: JPEG, PNG, GIF, etc. Taille illimitée.',
    'archive': 'Formats acceptés: ZIP, RAR, 7Z, TAR, GZ, etc. Taille illimitée.'
  };
  return helpText[type] || '';
};

const getFileName = (path) => {
  if (!path) return '';
  return path.split('/').pop();
};

const formatFileSize = (sizeInKB) => {
  if (sizeInKB < 1024) {
    return `${Math.round(sizeInKB)} Ko`;
  } else {
    return `${(sizeInKB / 1024).toFixed(2)} Mo`;
  }
};

const handleTypeChange = () => {
  // Si le type change, réinitialiser certains champs
  if (props.material.type !== form.type) {
    form.content = '';
    form.file = null;
  }
};

const submit = () => {
  form.put(route('admin.course-materials.update', props.material.id));
};
</script>
