<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Certificate;
use App\Models\User;
use Illuminate\Support\Facades\Storage;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class FixQrCodes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'qr:fix {--type=all : Type of QR codes to fix (certificates, students, all)} {--dry-run : Show what would be fixed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix missing QR codes for certificates and students';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');
        $dryRun = $this->option('dry-run');

        $this->info('🔍 Checking QR code issues...');

        if ($type === 'all' || $type === 'certificates') {
            $this->fixCertificateQrCodes($dryRun);
        }

        if ($type === 'all' || $type === 'students') {
            $this->fixStudentQrCodes($dryRun);
        }

        $this->info('✅ QR code check completed!');
        
        return 0;
    }

    /**
     * Fix certificate QR codes
     */
    private function fixCertificateQrCodes($dryRun = false)
    {
        $this->info('📋 Checking certificate QR codes...');

        // Find certificates with missing QR code database entries
        $certificatesWithoutQr = Certificate::whereNull('qr_code')->get();

        if ($certificatesWithoutQr->isEmpty()) {
            $this->info('✅ All certificates have QR code database entries.');
            return;
        }

        $this->warn("Found {$certificatesWithoutQr->count()} certificate(s) with missing QR code database entries:");

        $headers = ['Certificate Number', 'Status', 'File Exists', 'Action'];
        $rows = [];
        $fixedCount = 0;

        foreach ($certificatesWithoutQr as $certificate) {
            $expectedPath = 'certificates/qr/' . $certificate->certificate_number . '.svg';
            $fileExists = Storage::disk('public')->exists($expectedPath);
            
            $action = '';
            if ($fileExists) {
                $action = $dryRun ? 'Would update DB' : 'Update DB';
                if (!$dryRun) {
                    $certificate->update(['qr_code' => $expectedPath]);
                    $fixedCount++;
                }
            } else {
                $action = $dryRun ? 'Would generate' : 'Generate';
                if (!$dryRun) {
                    $qrPath = $certificate->generateQrCode();
                    if ($qrPath) {
                        $fixedCount++;
                    }
                }
            }

            $rows[] = [
                $certificate->certificate_number,
                $certificate->status,
                $fileExists ? 'Yes' : 'No',
                $action
            ];
        }

        $this->table($headers, $rows);

        if (!$dryRun) {
            $this->info("✅ Fixed {$fixedCount} certificate QR code(s)!");
        }
    }

    /**
     * Fix student QR codes
     */
    private function fixStudentQrCodes($dryRun = false)
    {
        $this->info('👥 Checking student QR codes...');

        $students = User::where('role', 'student')->where('active', true)->get();
        $missingQrCodes = [];

        foreach ($students as $student) {
            $expectedPath = "qrcodes/students/student-{$student->id}.svg";
            if (!Storage::disk('public')->exists($expectedPath)) {
                $missingQrCodes[] = $student;
            }
        }

        if (empty($missingQrCodes)) {
            $this->info('✅ All active students have QR codes.');
            return;
        }

        $this->warn("Found " . count($missingQrCodes) . " student(s) with missing QR codes:");

        $headers = ['Student Name', 'Student ID', 'Email', 'Action'];
        $rows = [];
        $generatedCount = 0;

        foreach ($missingQrCodes as $student) {
            $action = $dryRun ? 'Would generate' : 'Generate';
            
            if (!$dryRun) {
                if ($this->generateStudentQrCode($student)) {
                    $generatedCount++;
                }
            }

            $rows[] = [
                $student->name,
                $student->id,
                $student->email,
                $action
            ];
        }

        $this->table($headers, $rows);

        if (!$dryRun) {
            $this->info("✅ Generated {$generatedCount} student QR code(s)!");
        }
    }

    /**
     * Generate QR code for a student
     */
    private function generateStudentQrCode(User $student)
    {
        try {
            $qrCodeDir = 'qrcodes/students';
            $fileName = "student-{$student->id}.svg";
            $filePath = "{$qrCodeDir}/{$fileName}";

            // Create directory if it doesn't exist
            if (!Storage::disk('public')->exists($qrCodeDir)) {
                Storage::disk('public')->makeDirectory($qrCodeDir);
            }

            // URL to student public profile
            $profileUrl = route('students.public-profile', $student->id);

            // Generate QR code
            $qrCode = QrCode::format('svg')
                ->size(200)
                ->margin(1)
                ->generate($profileUrl);

            // Save QR code
            Storage::disk('public')->put($filePath, $qrCode);

            return true;
        } catch (\Exception $e) {
            $this->error("Failed to generate QR code for student {$student->name}: " . $e->getMessage());
            return false;
        }
    }
}
