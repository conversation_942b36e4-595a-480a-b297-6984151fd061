<template>
  <Head :title="module ? `Matériels du module ${module.title}` : `Matériels du cours ${course.title}`" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        {{ module ? `Matériels du module ${module.title}` : `Matériels du cours ${course.title}` }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Fil d'Ariane -->
            <div class="mb-6">
              <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                  <li class="inline-flex items-center">
                    <Link :href="route('trainer.dashboard')" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                      <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                      </svg>
                      Tableau de bord
                    </Link>
                  </li>
                  <li>
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <Link :href="route('trainer.courses.index')" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                        Mes cours
                      </Link>
                    </div>
                  </li>
                  <li v-if="module">
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <Link :href="route('trainer.modules.index', { course_id: course.id })" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                        Modules du cours
                      </Link>
                    </div>
                  </li>
                  <li aria-current="page">
                    <div class="flex items-center">
                      <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                      </svg>
                      <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">
                        {{ module ? `Matériels du module` : `Matériels du cours` }}
                      </span>
                    </div>
                  </li>
                </ol>
              </nav>
            </div>

            <!-- En-tête avec bouton d'ajout -->
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">
                {{ module ? `Matériels du module "${module.title}"` : `Matériels du cours "${course.title}"` }}
              </h3>
              <Link 
                :href="route('trainer.course-materials.create', module ? { module_id: module.id } : { course_id: course.id })" 
                class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
              >
                Ajouter un matériel
              </Link>
            </div>

            <!-- Liste des matériels -->
            <div v-if="materials.length > 0">
              <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200">
                  <thead>
                    <tr>
                      <th class="py-2 px-4 border-b text-left">Titre</th>
                      <th class="py-2 px-4 border-b text-left">Type</th>
                      <th class="py-2 px-4 border-b text-left">Description</th>
                      <th class="py-2 px-4 border-b text-left">Statut</th>
                      <th class="py-2 px-4 border-b text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="material in materials" :key="material.id" class="hover:bg-gray-50">
                      <td class="py-2 px-4 border-b">{{ material.title }}</td>
                      <td class="py-2 px-4 border-b">{{ getMaterialTypeLabel(material.type) }}</td>
                      <td class="py-2 px-4 border-b">
                        <span v-if="material.description" class="line-clamp-2">{{ material.description }}</span>
                        <span v-else class="text-gray-400 italic">Aucune description</span>
                      </td>
                      <td class="py-2 px-4 border-b">
                        <span :class="material.active ? 'text-green-600' : 'text-red-600'">
                          {{ material.active ? 'Actif' : 'Inactif' }}
                        </span>
                      </td>
                      <td class="py-2 px-4 border-b text-center">
                        <div class="flex justify-center space-x-2">
                          <Link :href="route('trainer.course-materials.show', material.id)" class="text-blue-600 hover:text-blue-900">
                            Voir
                          </Link>
                          <Link :href="route('trainer.course-materials.edit', material.id)" class="text-green-600 hover:text-green-900">
                            Modifier
                          </Link>
                          <button @click="confirmDelete(material)" class="text-red-600 hover:text-red-900">
                            Supprimer
                          </button>
                          <Link v-if="material.file_path && material.allow_download" :href="route('trainer.course-materials.download', material.id)" class="text-purple-600 hover:text-purple-900">
                            Télécharger
                          </Link>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <p class="text-gray-500">Aucun matériel pédagogique trouvé.</p>
              <p class="text-gray-500 mt-2">
                Cliquez sur "Ajouter un matériel" pour commencer à créer du contenu pédagogique.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmation de suppression -->
    <Modal :show="deleteModalOpen" @close="closeModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">
          Confirmer la suppression
        </h2>

        <p class="mt-1 text-sm text-gray-600">
          Êtes-vous sûr de vouloir supprimer ce matériel pédagogique ? Cette action est irréversible.
        </p>

        <div class="mt-6 flex justify-end">
          <SecondaryButton @click="closeModal" class="mr-2">
            Annuler
          </SecondaryButton>

          <DangerButton @click="deleteCourseMaterial" :class="{ 'opacity-25': processing }" :disabled="processing">
            Supprimer
          </DangerButton>
        </div>
      </div>
    </Modal>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';

// Props
const props = defineProps({
  course: Object,
  module: Object,
  materials: Array,
});

// État pour la modal de suppression
const deleteModalOpen = ref(false);
const materialToDelete = ref(null);
const processing = ref(false);

// Méthodes pour la suppression
const confirmDelete = (material) => {
  materialToDelete.value = material;
  deleteModalOpen.value = true;
};

const closeModal = () => {
  deleteModalOpen.value = false;
  setTimeout(() => {
    materialToDelete.value = null;
  }, 300);
};

const deleteCourseMaterial = () => {
  if (!materialToDelete.value) return;

  processing.value = true;

  router.delete(route('trainer.course-materials.destroy', materialToDelete.value.id), {
    onSuccess: () => {
      closeModal();
      processing.value = false;
    },
    onError: () => {
      processing.value = false;
    },
  });
};

// Méthode pour obtenir le libellé du type de matériel
const getMaterialTypeLabel = (type) => {
  const types = {
    'text': 'Texte',
    'pdf': 'Document PDF',
    'video': 'Vidéo',
    'audio': 'Audio (podcast)',
    'image': 'Image',
    'archive': 'Archive',
    'gallery': 'Galerie d\'images',
    'embed_video': 'Vidéo externe'
  };

  return types[type] || type;
};
</script>
