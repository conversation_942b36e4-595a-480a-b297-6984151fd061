<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Apprenants</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3B82F6;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #1F2937;
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            color: #6B7280;
            margin: 5px 0 0 0;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            background-color: #F3F4F6;
            padding: 15px;
            border-radius: 8px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #3B82F6;
        }
        
        .stat-label {
            font-size: 10px;
            color: #6B7280;
            margin-top: 5px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        th, td {
            border: 1px solid #E5E7EB;
            padding: 8px;
            text-align: left;
        }
        
        th {
            background-color: #F9FAFB;
            font-weight: bold;
            color: #374151;
        }
        
        tr:nth-child(even) {
            background-color: #F9FAFB;
        }
        
        .status-active {
            color: #059669;
            font-weight: bold;
        }
        
        .status-inactive {
            color: #DC2626;
            font-weight: bold;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #6B7280;
            border-top: 1px solid #E5E7EB;
            padding-top: 15px;
        }
        
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Liste des Apprenants</h1>
        <p>Rapport généré le {{ now()->format('d/m/Y à H:i') }}</p>
    </div>

    <div class="stats">
        <div class="stat-item">
            <div class="stat-number">{{ $students->count() }}</div>
            <div class="stat-label">Total Apprenants</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ $students->where('active', true)->count() }}</div>
            <div class="stat-label">Apprenants Actifs</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ $students->sum(function($s) { return $s->enrollments->count(); }) }}</div>
            <div class="stat-label">Total Inscriptions</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ $students->sum(function($s) { return $s->certificates->count(); }) }}</div>
            <div class="stat-label">Total Certificats</div>
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>Nom</th>
                <th>Email</th>
                <th>Téléphone</th>
                <th>Profession</th>
                <th>Inscriptions</th>
                <th>Certificats</th>
                <th>Date d'inscription</th>
                <th>Statut</th>
            </tr>
        </thead>
        <tbody>
            @foreach($students as $student)
            <tr>
                <td>{{ $student->name }}</td>
                <td>{{ $student->email }}</td>
                <td>{{ $student->phone ?: 'Non renseigné' }}</td>
                <td>{{ $student->profession ?: 'Non renseigné' }}</td>
                <td>{{ $student->enrollments->count() }}</td>
                <td>{{ $student->certificates->count() }}</td>
                <td>{{ $student->created_at->format('d/m/Y') }}</td>
                <td class="{{ $student->active ? 'status-active' : 'status-inactive' }}">
                    {{ $student->active ? 'Actif' : 'Inactif' }}
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <p>Formation Platform - Système de Gestion des Apprenants</p>
        <p>Ce document contient des informations confidentielles</p>
    </div>
</body>
</html>
