<template>
    <Head :title="`Profil de ${student.name}`" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <Link
                        :href="route('admin.students.index')"
                        class="text-gray-500 hover:text-gray-700"
                    >
                        <ArrowLeftIcon class="h-6 w-6" />
                    </Link>
                    <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                        Profil de {{ student.name }}
                    </h2>
                </div>

            </div>
        </template>

        <div class="py-6">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">

                <!-- Informations personnelles et QR Code -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Informations personnelles -->
                    <div class="lg:col-span-2 bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Informations Personnelles</h3>
                            <div class="flex items-start space-x-6">
                                <div class="flex-shrink-0">
                                    <img
                                        :src="student.profile_photo ? `/storage/${student.profile_photo}` : '/images/default-avatar.svg'"
                                        :alt="student.name"
                                        class="h-24 w-24 rounded-full object-cover border-4 border-gray-200"
                                    />
                                </div>
                                <div class="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Nom complet</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ student.name }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Email</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ student.email }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Téléphone</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ student.phone || 'Non renseigné' }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Date de naissance</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ formatDate(student.birth_date) || 'Non renseigné' }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Numéro CIN</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ student.id_card_number || 'Non renseigné' }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Profession</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ student.profession || 'Non renseigné' }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Entreprise</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ student.company || 'Non renseigné' }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Source de découverte</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ getDiscoverySourceLabel(student.discovery_source) }}</p>
                                    </div>
                                    <div v-if="student.address" class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-500">Adresse</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ student.address }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- QR Code et Actions -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- QR Code -->
                                <div class="text-center">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">QR Code du Profil</h3>
                                    <div class="flex justify-center mb-4">
                                        <img
                                            :src="`/storage/${qrCodePath}`"
                                            :alt="`QR Code de ${student.name}`"
                                            class="w-48 h-48 border border-gray-200 rounded-lg"
                                        />
                                    </div>
                                    <p class="text-sm text-gray-600">
                                        Scannez ce code pour accéder au profil public de l'apprenant
                                    </p>
                                </div>

                                <!-- Actions -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Actions Disponibles</h3>
                                    <div class="space-y-3">
                                        <a
                                            :href="route('admin.students.qr-code', student.id)"
                                            class="w-full inline-flex items-center justify-center px-4 py-3 bg-blue-600 border border-transparent rounded-md font-semibold text-sm text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-900 focus:outline-none focus:border-blue-900 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150"
                                        >
                                            <QrCodeIcon class="w-5 h-5 mr-2" />
                                            Télécharger QR Code
                                        </a>

                                        <a
                                            :href="route('admin.students.card', student.id)"
                                            target="_blank"
                                            class="w-full inline-flex items-center justify-center px-4 py-3 bg-purple-600 border border-transparent rounded-md font-semibold text-sm text-white uppercase tracking-widest hover:bg-purple-700 active:bg-purple-900 focus:outline-none focus:border-purple-900 focus:ring ring-purple-300 disabled:opacity-25 transition ease-in-out duration-150"
                                        >
                                            <CreditCardIcon class="w-5 h-5 mr-2" />
                                            Générer Carte Apprenant
                                        </a>

                                        <a
                                            :href="route('admin.students.sheet', student.id)"
                                            class="w-full inline-flex items-center justify-center px-4 py-3 bg-red-600 border border-transparent rounded-md font-semibold text-sm text-white uppercase tracking-widest hover:bg-red-700 active:bg-red-900 focus:outline-none focus:border-red-900 focus:ring ring-red-300 disabled:opacity-25 transition ease-in-out duration-150"
                                        >
                                            <DocumentArrowDownIcon class="w-5 h-5 mr-2" />
                                            Télécharger Fiche PDF
                                        </a>

                                        <a
                                            :href="`/students/${student.id}/profile`"
                                            target="_blank"
                                            class="w-full inline-flex items-center justify-center px-4 py-3 bg-green-600 border border-transparent rounded-md font-semibold text-sm text-white uppercase tracking-widest hover:bg-green-700 active:bg-green-900 focus:outline-none focus:border-green-900 focus:ring ring-green-300 disabled:opacity-25 transition ease-in-out duration-150"
                                        >
                                            <EyeIcon class="w-5 h-5 mr-2" />
                                            Voir Profil Public
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistiques -->
                <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <AcademicCapIcon class="h-8 w-8 text-blue-600" />
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Inscriptions</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ stats.total_enrollments }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <CheckCircleIcon class="h-8 w-8 text-green-600" />
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Cours Terminés</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ stats.completed_courses }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <TrophyIcon class="h-8 w-8 text-yellow-600" />
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Certificats</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ stats.certificates_earned }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <ChartBarIcon class="h-8 w-8 text-purple-600" />
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Moyenne Examens</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ Math.round(stats.average_exam_score) }}%</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <CurrencyDollarIcon class="h-8 w-8 text-green-600" />
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Paiements</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ formatCurrency(stats.total_payments) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Onglets -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8 px-6">
                            <button
                                v-for="tab in tabs"
                                :key="tab.key"
                                @click="activeTab = tab.key"
                                :class="[
                                    'py-4 px-1 border-b-2 font-medium text-sm',
                                    activeTab === tab.key
                                        ? 'border-indigo-500 text-indigo-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                ]"
                            >
                                {{ tab.label }}
                            </button>
                        </nav>
                    </div>

                    <div class="p-6">
                        <!-- Onglet Inscriptions -->
                        <div v-if="activeTab === 'enrollments'">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Historique des Inscriptions</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Formation</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Domaine</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'inscription</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paiement</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr v-if="!student.enrollments || student.enrollments.length === 0">
                                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                                Aucune inscription trouvée
                                            </td>
                                        </tr>
                                        <tr v-for="enrollment in (student.enrollments || [])" :key="enrollment.id">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {{ enrollment.training_session?.title || 'Formation non définie' }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ enrollment.training_session?.training_domain?.name || 'Domaine non défini' }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ formatDate(enrollment.enrollment_date) }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span :class="getStatusClass(enrollment.status)">
                                                    {{ getStatusLabel(enrollment.status) }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span :class="getPaymentClass(enrollment.payment_confirmed)">
                                                    {{ enrollment.payment_confirmed ? 'Confirmé' : 'En attente' }}
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Onglet Certificats -->
                        <div v-if="activeTab === 'certificates'">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Certificats Obtenus</h3>
                            <div v-if="!student.certificates || student.certificates.length === 0" class="text-center text-gray-500 py-8">
                                Aucun certificat trouvé
                            </div>
                            <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                <div
                                    v-for="certificate in student.certificates"
                                    :key="certificate.id"
                                    class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                                >
                                    <div class="flex items-center justify-between mb-2">
                                        <TrophyIcon class="h-8 w-8 text-yellow-500" />
                                        <span class="text-xs text-gray-500">{{ certificate.certificate_number }}</span>
                                    </div>
                                    <h4 class="font-medium text-gray-900 mb-1">
                                        {{ certificate.enrollment?.training_session?.title || 'Formation non définie' }}
                                    </h4>
                                    <p class="text-sm text-gray-600 mb-2">
                                        {{ certificate.enrollment?.training_session?.training_domain?.name || 'Domaine non défini' }}
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        Délivré le {{ formatDate(certificate.issue_date || certificate.issued_at) }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Résultats d'examens -->
                        <div v-if="activeTab === 'exam_results'">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Résultats d'Examens</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Examen</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Formation</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr v-if="(!student.exam_results || student.exam_results.length === 0) && (!student.examResults || student.examResults.length === 0)">
                                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                                Aucun résultat d'examen trouvé
                                            </td>
                                        </tr>
                                        <tr v-for="result in (student.exam_results || student.examResults || [])" :key="result.id">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {{ result.exam?.title || 'Examen non défini' }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ result.exam?.training_session?.title || result.exam?.course?.title || 'Formation non définie' }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ result.score }}%
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ formatDate(result.completed_at) }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span :class="getExamStatusClass(result.score)">
                                                    {{ result.score >= 60 ? 'Réussi' : 'Échoué' }}
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import { ref } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import {
    ArrowLeftIcon,
    QrCodeIcon,
    AcademicCapIcon,
    CheckCircleIcon,
    TrophyIcon,
    ChartBarIcon,
    CurrencyDollarIcon,
    CreditCardIcon,
    DocumentArrowDownIcon,
    EyeIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
    student: Object,
    qrCodePath: String,
    stats: Object
});

// État réactif
const activeTab = ref('enrollments');

const tabs = [
    { key: 'enrollments', label: 'Inscriptions' },
    { key: 'certificates', label: 'Certificats' },
    { key: 'exam_results', label: 'Résultats d\'Examens' }
];

// Méthodes
const formatDate = (date) => {
    if (!date) return 'Non renseigné';
    return new Date(date).toLocaleDateString('fr-FR');
};

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-TN', {
        style: 'currency',
        currency: 'TND'
    }).format(amount || 0);
};

const getDiscoverySourceLabel = (source) => {
    const sources = {
        'facebook': 'Facebook',
        'instagram': 'Instagram',
        'tiktok': 'TikTok',
        'word_of_mouth': 'Bouche à oreille',
        'other': 'Autre'
    };
    return sources[source] || 'Non renseigné';
};

const getStatusClass = (status) => {
    const classes = {
        'pending': 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800',
        'confirmed': 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800',
        'completed': 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800',
        'cancelled': 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800'
    };
    return classes[status] || 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800';
};

const getStatusLabel = (status) => {
    const labels = {
        'pending': 'En attente',
        'confirmed': 'Confirmé',
        'completed': 'Terminé',
        'cancelled': 'Annulé'
    };
    return labels[status] || status;
};

const getPaymentClass = (confirmed) => {
    return confirmed
        ? 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800'
        : 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800';
};

const getExamStatusClass = (score) => {
    return score >= 60
        ? 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800'
        : 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800';
};
</script>
