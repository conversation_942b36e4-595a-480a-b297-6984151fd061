<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Enrollment;
use App\Models\User;
use App\Models\TrainingSession;

class EnrollmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer les sessions de formation
        $sessions = TrainingSession::all();

        // Récupérer les apprenants
        $students = User::where('role', 'student')->get();

        // Créer des inscriptions
        $statuses = ['pending', 'approved', 'completed', 'rejected'];

        foreach ($sessions as $session) {
            // Inscrire 2-4 étudiants aléatoires à chaque session
            $randomStudents = $students->random(rand(2, 4));

            foreach ($randomStudents as $student) {
                $status = $statuses[array_rand($statuses)];

                Enrollment::create([
                    'user_id' => $student->id,
                    'training_session_id' => $session->id,
                    'status' => $status,
                    'enrollment_date' => now()->subDays(rand(1, 30)),
                    'notes' => $status === 'rejected' ? 'Inscription rejetée pour raisons administratives.' : null,
                    'created_at' => now()->subDays(rand(1, 30)),
                ]);
            }
        }
    }
}
