<?php

namespace App\Http\Controllers;

use App\Models\Announcement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class AnnouncementController extends Controller
{
    /**
     * Display a listing of announcements for the authenticated user
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Get user's enrolled session IDs
        $sessionIds = [];
        if ($user->role === 'student') {
            $sessionIds = $user->enrollments()
                ->where('status', 'approved')
                ->pluck('training_session_id')
                ->toArray();
        }

        // Build query for announcements visible to the user
        $query = Announcement::published()
            ->visibleTo($user->role)
            ->with(['author', 'trainingSession.trainingDomain']);

        // Filter by training sessions for students
        if ($user->role === 'student') {
            $query->where(function($q) use ($sessionIds) {
                $q->whereNull('training_session_id') // General announcements
                  ->orWhereIn('training_session_id', $sessionIds); // Session-specific announcements
            });
        }

        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function($q) use ($searchTerm) {
                $q->where('title', 'like', $searchTerm)
                  ->orWhere('content', 'like', $searchTerm);
            });
        }

        // Apply importance filter
        if ($request->has('important') && $request->important === 'true') {
            $query->where('is_important', true);
        }

        // Order by importance and date
        $announcements = $query->orderBy('is_important', 'desc')
            ->orderBy('publish_date', 'desc')
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('Announcements/Index', [
            'announcements' => $announcements,
            'filters' => [
                'search' => $request->search ?? '',
                'important' => $request->important ?? '',
            ]
        ]);
    }

    /**
     * Display the specified announcement
     */
    public function show(string $id)
    {
        $user = Auth::user();
        
        // Get user's enrolled session IDs for access control
        $sessionIds = [];
        if ($user->role === 'student') {
            $sessionIds = $user->enrollments()
                ->where('status', 'approved')
                ->pluck('training_session_id')
                ->toArray();
        }

        // Find the announcement with access control
        $query = Announcement::published()
            ->visibleTo($user->role)
            ->with(['author', 'trainingSession.trainingDomain']);

        // Apply session filtering for students
        if ($user->role === 'student') {
            $query->where(function($q) use ($sessionIds) {
                $q->whereNull('training_session_id') // General announcements
                  ->orWhereIn('training_session_id', $sessionIds); // Session-specific announcements
            });
        }

        $announcement = $query->findOrFail($id);

        return Inertia::render('Announcements/Show', [
            'announcement' => $announcement
        ]);
    }
}
