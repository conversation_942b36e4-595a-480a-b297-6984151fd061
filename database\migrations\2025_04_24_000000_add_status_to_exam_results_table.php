<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\ExamResult;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('exam_results', function (Blueprint $table) {
            $table->string('status')->default('in_progress')->after('passed');
        });

        // Mettre à jour les statuts existants
        $this->updateExistingStatuses();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exam_results', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }

    /**
     * Mettre à jour les statuts des résultats d'examen existants
     */
    private function updateExistingStatuses(): void
    {
        // Mettre à jour les résultats terminés avec feedback
        DB::table('exam_results')
            ->whereNotNull('feedback')
            ->update(['status' => 'graded']);

        // Mettre à jour les résultats terminés sans feedback
        DB::table('exam_results')
            ->whereNull('feedback')
            ->whereNotNull('completed_at')
            ->update(['status' => 'completed']);

        // Mettre à jour les résultats réussis
        DB::table('exam_results')
            ->where('passed', true)
            ->update(['status' => 'passed']);

        // Mettre à jour les résultats échoués
        DB::table('exam_results')
            ->where('passed', false)
            ->whereNotNull('completed_at')
            ->whereNotNull('feedback')
            ->update(['status' => 'failed']);
    }
};
