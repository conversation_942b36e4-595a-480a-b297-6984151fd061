<template>
  <Head title="Vérification de certificat" />

  <div class="min-h-screen bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <img src="/images/logo.png" alt="Logo" class="h-8 w-auto" />
            <span class="ml-2 text-xl font-semibold text-gray-900">Formation Platform</span>
          </div>
          <div class="text-sm text-gray-500">
            Vérification de certificat
          </div>
        </div>
      </div>
    </header>

    <div class="py-6 sm:py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-4 sm:p-6 text-gray-900">
            <div class="text-center mb-6 sm:mb-8">
              <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Vérification de certificat</h1>
              <p class="text-sm sm:text-base text-gray-600 px-4">Vérifiez l'authenticité d'un certificat délivré par notre centre de formation.</p>
            </div>

            <div v-if="valid" class="border-2 border-green-500 rounded-lg p-4 sm:p-6 max-w-2xl mx-auto">
              <div class="flex items-center justify-center mb-4 sm:mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 sm:h-16 sm:w-16 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>

              <h2 class="text-xl sm:text-2xl font-bold text-center text-green-700 mb-4 sm:mb-6">Certificat authentique</h2>

              <div class="space-y-3 sm:space-y-4">
                <div class="border-b pb-2 sm:pb-3">
                  <div class="text-xs sm:text-sm text-gray-600 mb-1">Numéro de certificat</div>
                  <div class="font-medium text-sm sm:text-base break-all">{{ certificate.number }}</div>
                </div>

                <div class="border-b pb-2 sm:pb-3">
                  <div class="text-xs sm:text-sm text-gray-600 mb-1">Délivré à</div>
                  <div class="font-medium text-sm sm:text-base">{{ certificate.user }}</div>
                </div>

                <div class="border-b pb-2 sm:pb-3">
                  <div class="text-xs sm:text-sm text-gray-600 mb-1">Formation</div>
                  <div class="font-medium text-sm sm:text-base">{{ certificate.training }}</div>
                </div>

                <div class="border-b pb-2 sm:pb-3">
                  <div class="text-xs sm:text-sm text-gray-600 mb-1">Domaine</div>
                  <div class="font-medium text-sm sm:text-base">{{ certificate.domain }}</div>
                </div>

                <div class="border-b pb-2 sm:pb-3">
                  <div class="text-xs sm:text-sm text-gray-600 mb-1">Date de délivrance</div>
                  <div class="font-medium text-sm sm:text-base">{{ certificate.issue_date }}</div>
                </div>

                <div v-if="certificate.expiry_date" class="border-b pb-2 sm:pb-3">
                  <div class="text-xs sm:text-sm text-gray-600 mb-1">Date d'expiration</div>
                  <div class="font-medium text-sm sm:text-base">{{ certificate.expiry_date }}</div>
                </div>
              </div>

              <!-- Verification Badge -->
              <div class="mt-6 text-center">
                <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                  Certificat vérifié et authentique
                </div>
              </div>
            </div>

            <div v-else class="border-2 border-red-500 rounded-lg p-4 sm:p-6 max-w-2xl mx-auto">
              <div class="flex items-center justify-center mb-4 sm:mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 sm:h-16 sm:w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>

              <h2 class="text-xl sm:text-2xl font-bold text-center text-red-700 mb-4">Certificat non valide</h2>
              <p class="text-center text-gray-700 text-sm sm:text-base px-4">{{ message }}</p>

              <!-- Warning Badge -->
              <div class="mt-6 text-center">
                <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  Certificat non authentique
                </div>
              </div>
            </div>

            <div class="mt-6 sm:mt-8 text-center">
              <a href="/" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à l'accueil
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="text-center text-sm text-gray-500">
          <p>© 2024 Formation Platform. Tous droits réservés.</p>
          <p class="mt-1">
            Système de vérification de certificats sécurisé
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { Head } from '@inertiajs/vue3';

// Props
const props = defineProps({
  valid: Boolean,
  certificate: Object,
  message: String,
});
</script>
