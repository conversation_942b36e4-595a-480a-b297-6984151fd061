<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Models\FirstAidTip;
use App\Models\FirstAidTipMaterial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class FirstAidTipAccessController extends Controller
{
    /**
     * Display a listing of first aid tips for students.
     */
    public function index()
    {
        $user = auth()->user();

        // Récupérer tous les conseils actifs avec leurs matériels
        $firstAidTips = FirstAidTip::where('active', true)
            ->with(['materials' => function ($query) {
                $query->where('active', true)->orderBy('order');
            }])
            ->orderBy('order')
            ->orderBy('title')
            ->get();

        return Inertia::render('Student/FirstAidTips/Index', [
            'firstAidTips' => $firstAidTips
        ]);
    }

    /**
     * Display the specified first aid tip.
     */
    public function show(string $id)
    {
        $user = Auth::user();

        // Récupérer le conseil avec ses matériels actifs
        $firstAidTip = FirstAidTip::where('active', true)
            ->with(['materials' => function ($query) {
                $query->where('active', true)->orderBy('order');
            }])
            ->findOrFail($id);

        // Traiter les matériels pour les galeries d'images
        foreach ($firstAidTip->materials as $material) {
            if ($material->type === 'gallery') {
                $galleryImages = [];

                // Essayer d'abord avec metadata (format JSON)
                $metadata = null;
                if ($material->metadata) {
                    $metadata = is_array($material->metadata) ? $material->metadata : json_decode($material->metadata, true);
                }

                if (is_array($metadata)) {
                    if (isset($metadata['images']) && is_array($metadata['images'])) {
                        // Format utilisé dans l'interface admin
                        $galleryImages = $metadata['images'];
                    } elseif (isset($metadata['gallery_paths']) && is_array($metadata['gallery_paths'])) {
                        // Format utilisé dans l'interface formateur
                        foreach ($metadata['gallery_paths'] as $index => $path) {
                            $galleryImages[] = [
                                'path' => $path,
                                'caption' => 'Image ' . ($index + 1)
                            ];
                        }
                    }
                }

                // Si aucune image n'a été trouvée dans les métadonnées, essayer avec gallery_data
                if (empty($galleryImages) && $material->gallery_data) {
                    $galleryData = json_decode($material->gallery_data, true);
                    if (isset($galleryData['images']) && is_array($galleryData['images'])) {
                        $galleryImages = $galleryData['images'];
                    }
                }

                $material->gallery_images = $galleryImages;
            }

            // S'assurer que les chemins de fichiers sont corrects
            if ($material->file_path && !filter_var($material->file_path, FILTER_VALIDATE_URL)) {
                $storage_path = 'public/' . $material->file_path;
                if (!Storage::exists($storage_path)) {
                    $alt_path = 'public/first-aid-materials/' . $material->file_path;
                    if (Storage::exists($alt_path)) {
                        $material->file_path = 'first-aid-materials/' . $material->file_path;
                    }
                }
            }
        }

        return Inertia::render('Student/FirstAidTips/Show', [
            'firstAidTip' => $firstAidTip
        ]);
    }

    /**
     * Télécharger un fichier associé à un matériel
     */
    public function downloadMaterial(string $id)
    {
        $user = Auth::user();
        $material = FirstAidTipMaterial::with('firstAidTip')->findOrFail($id);

        // Vérifier que le conseil et le matériel sont actifs
        if (!$material->firstAidTip->active || !$material->active) {
            return redirect()->back()->with('error', 'Ce contenu n\'est pas disponible.');
        }

        // Vérifier si le téléchargement est autorisé
        if (!$material->allow_download) {
            return redirect()->back()->with('error', 'Le téléchargement de ce matériel n\'est pas autorisé.');
        }

        // Vérifier si un fichier est associé
        if (!$material->file_path) {
            return redirect()->back()->with('error', 'Aucun fichier n\'est associé à ce matériel.');
        }

        // Obtenir le chemin complet du fichier
        $filePath = Storage::disk('public')->path($material->file_path);

        // Vérifier si le fichier existe
        if (!file_exists($filePath)) {
            return redirect()->back()->with('error', 'Le fichier n\'existe pas sur le serveur.');
        }

        // Déterminer l'extension du fichier
        $extension = pathinfo($material->file_path, PATHINFO_EXTENSION);

        // Créer un nom de fichier pour le téléchargement
        $downloadName = $material->title . '.' . $extension;

        // Télécharger le fichier avec le bon type MIME
        return response()->download($filePath, $downloadName, [
            'Content-Type' => $material->mime_type ?? 'application/octet-stream',
        ]);
    }

    /**
     * Afficher le fichier associé à un matériel
     */
    public function viewMaterial(Request $request, string $id)
    {
        $user = Auth::user();
        $material = FirstAidTipMaterial::with('firstAidTip')->findOrFail($id);

        // Vérifier que le conseil et le matériel sont actifs
        if (!$material->firstAidTip->active || !$material->active) {
            return redirect()->back()->with('error', 'Ce contenu n\'est pas disponible.');
        }

        // Vérifier si la visualisation en ligne est autorisée
        if (!$material->allow_online_viewing) {
            return redirect()->back()->with('error', 'La visualisation en ligne de ce matériel n\'est pas autorisée.');
        }

        // Vérifier si un fichier est associé
        if (!$material->file_path && $material->type !== 'gallery') {
            return redirect()->back()->with('error', 'Aucun fichier n\'est associé à ce matériel.');
        }

        // Traitement spécial pour les galeries d'images
        if ($material->type === 'gallery') {
            $imageIndex = $request->query('image_index', 0);

            // Essayer d'abord avec metadata (format JSON)
            $metadata = null;
            if ($material->metadata) {
                $metadata = is_array($material->metadata) ? $material->metadata : json_decode($material->metadata, true);
            }

            // Extraire les chemins d'images en fonction de la structure des métadonnées
            $galleryPaths = [];

            if (is_array($metadata)) {
                if (isset($metadata['gallery_paths']) && is_array($metadata['gallery_paths'])) {
                    // Format utilisé dans l'interface formateur
                    $galleryPaths = array_values($metadata['gallery_paths']);
                } elseif (isset($metadata['images']) && is_array($metadata['images'])) {
                    // Format utilisé dans l'interface admin
                    $galleryPaths = array_map(function($img) {
                        return is_array($img) ? ($img['path'] ?? '') : $img;
                    }, $metadata['images']);
                } elseif (is_array($metadata) && !isset($metadata['gallery_paths']) && !isset($metadata['images'])) {
                    // Si les métadonnées sont directement un tableau de chemins
                    $galleryPaths = array_values($metadata);
                }
            }

            // Si aucun chemin n'a été trouvé dans les métadonnées, essayer avec gallery_data
            if (empty($galleryPaths) && $material->gallery_data) {
                $galleryData = json_decode($material->gallery_data, true);
                if (isset($galleryData['images']) && is_array($galleryData['images'])) {
                    $galleryPaths = array_map(function($img) {
                        return is_array($img) ? ($img['path'] ?? '') : $img;
                    }, $galleryData['images']);
                }
            }

            $galleryPaths = array_filter($galleryPaths);

            if (empty($galleryPaths)) {
                return response()->json(['error' => 'Aucune image trouvée dans la galerie'], 404);
            }

            if (!isset($galleryPaths[$imageIndex])) {
                return response()->json(['error' => 'Image non trouvée à cet index'], 404);
            }

            $imagePath = $galleryPaths[$imageIndex];
            $filePath = Storage::disk('public')->path($imagePath);

            if (!file_exists($filePath)) {
                return response()->json(['error' => 'Image non trouvée'], 404);
            }

            $mimeType = mime_content_type($filePath) ?: 'image/jpeg';

            return response()->file($filePath, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => 'inline; filename="' . basename($imagePath) . '"',
            ]);
        }

        // Pour les autres types de matériels
        $filePath = Storage::disk('public')->path($material->file_path);

        if (!file_exists($filePath)) {
            return response()->json(['error' => 'Fichier non trouvé'], 404);
        }

        return response()->file($filePath, [
            'Content-Type' => $material->mime_type ?? $this->getMimeTypeFromExtension($filePath),
            'Content-Disposition' => 'inline; filename="' . basename($material->file_path) . '"',
        ]);
    }

    /**
     * Détermine le type MIME à partir de l'extension du fichier
     */
    private function getMimeTypeFromExtension($filePath)
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        $mimeTypes = [
            'pdf' => 'application/pdf',
            'mp4' => 'video/mp4',
            'webm' => 'video/webm',
            'ogg' => 'video/ogg',
            'mp3' => 'audio/mpeg',
            'wav' => 'audio/wav',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'zip' => 'application/zip',
            'rar' => 'application/x-rar-compressed',
            '7z' => 'application/x-7z-compressed',
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
}
