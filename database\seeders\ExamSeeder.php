<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Exam;
use App\Models\ExamQuestion;
use App\Models\TrainingSession;
use App\Models\User;

class ExamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer les sessions de formation
        $sessions = TrainingSession::all();

        // Récupérer les formateurs
        $trainers = User::where('role', 'trainer')->get();

        // Créer des examens pour chaque session
        foreach ($sessions as $session) {
            // Créer un examen final pour chaque session
            $finalExam = Exam::create([
                'title' => 'Examen final - ' . $session->title,
                'description' => 'Examen final pour évaluer les connaissances acquises durant la formation.',
                'training_session_id' => $session->id,
                'creator_id' => $session->trainer_id,
                'duration_minutes' => 60,
                'passing_score' => 70,
                'is_published' => true,
                'created_at' => now()->subDays(rand(1, 15)),
            ]);

            // Créer des questions pour l'examen final
            $this->createQuestionsForExam($finalExam);

            // Créer un examen intermédiaire pour certaines sessions
            if (rand(0, 1)) {
                $midtermExam = Exam::create([
                    'title' => 'Examen intermédiaire - ' . $session->title,
                    'description' => 'Examen intermédiaire pour évaluer la progression des apprenants.',
                    'training_session_id' => $session->id,
                    'creator_id' => $session->trainer_id,
                    'duration_minutes' => 45,
                    'passing_score' => 60,
                    'is_published' => true,
                    'created_at' => now()->subDays(rand(16, 30)),
                ]);

                // Créer des questions pour l'examen intermédiaire
                $this->createQuestionsForExam($midtermExam);
            }
        }
    }

    /**
     * Créer des questions pour un examen
     */
    private function createQuestionsForExam(Exam $exam)
    {
        // Types de questions
        $questionTypes = ['multiple_choice', 'text', 'file'];

        // Nombre de questions (entre 5 et 10)
        $numQuestions = rand(5, 10);

        for ($i = 1; $i <= $numQuestions; $i++) {
            $type = $questionTypes[array_rand($questionTypes)];

            $question = [
                'exam_id' => $exam->id,
                'question_text' => 'Question ' . $i . ' pour ' . $exam->title,
                'question_type' => $type,
                'points' => rand(1, 5),
                'order' => $i,
            ];

            // Ajouter des options et réponses correctes pour les questions à choix multiples
            if ($type === 'multiple_choice') {
                $question['options'] = json_encode([
                    'A' => 'Option A pour la question ' . $i,
                    'B' => 'Option B pour la question ' . $i,
                    'C' => 'Option C pour la question ' . $i,
                    'D' => 'Option D pour la question ' . $i,
                ]);
                $question['correct_answer'] = chr(65 + rand(0, 3)); // A, B, C ou D
            }

            ExamQuestion::create($question);
        }
    }
}
