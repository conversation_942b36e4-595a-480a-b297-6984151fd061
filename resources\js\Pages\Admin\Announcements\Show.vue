<template>
  <Head :title="`Annonce - ${announcement.title}`" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Détails de l'annonce
        </h2>
        <div class="flex space-x-2">
          <Link :href="route('admin.announcements.edit', announcement.id)" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            Modifier
          </Link>
          <Link :href="route('admin.announcements.index')" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
            Retour à la liste
          </Link>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- En-tête de l'annonce -->
            <div class="border-b pb-4 mb-6">
              <div class="flex items-center justify-between mb-2">
                <h1 class="text-2xl font-bold">{{ announcement.title }}</h1>
                <div v-if="announcement.is_important" class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                  Important
                </div>
              </div>
              
              <div class="flex flex-wrap gap-4 text-sm text-gray-600">
                <div>
                  <span class="font-medium">Créée par:</span> {{ announcement.creator?.name || 'Inconnu' }}
                </div>
                <div>
                  <span class="font-medium">Date de publication:</span> {{ formatDate(announcement.publish_date) }}
                </div>
                <div v-if="announcement.expiry_date">
                  <span class="font-medium">Date d'expiration:</span> {{ formatDate(announcement.expiry_date) }}
                </div>
                <div>
                  <span class="font-medium">Visibilité:</span> 
                  <span :class="getVisibilityClass(announcement.visible_to)" class="ml-1 px-2 py-0.5 text-xs rounded-full">
                    {{ getVisibilityLabel(announcement.visible_to) }}
                  </span>
                </div>
                <div v-if="announcement.training_session">
                  <span class="font-medium">Session:</span> {{ announcement.training_session.title }}
                </div>
              </div>
            </div>
            
            <!-- Contenu de l'annonce -->
            <div class="prose max-w-none">
              <p class="whitespace-pre-line">{{ announcement.content }}</p>
            </div>
            
            <!-- Métadonnées -->
            <div class="mt-8 pt-4 border-t text-sm text-gray-500">
              <div>Créée le {{ formatDateTime(announcement.created_at) }}</div>
              <div v-if="announcement.updated_at !== announcement.created_at">
                Dernière modification le {{ formatDateTime(announcement.updated_at) }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- Actions -->
        <div class="mt-6 flex justify-end space-x-3">
          <button 
            @click="confirmDelete" 
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Supprimer cette annonce
          </button>
        </div>
      </div>
    </div>
    
    <!-- Modal de confirmation de suppression -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Confirmer la suppression</h3>
        <p class="text-gray-600 mb-6">
          Êtes-vous sûr de vouloir supprimer l'annonce "{{ announcement.title }}" ?
        </p>
        <div class="flex justify-end space-x-3">
          <button 
            @click="showDeleteModal = false" 
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
          >
            Annuler
          </button>
          <button 
            @click="deleteAnnouncement" 
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Supprimer
          </button>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3';
import { ref } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  announcement: Object,
});

// État local
const showDeleteModal = ref(false);

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return '';
  const options = { day: 'numeric', month: 'long', year: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const formatDateTime = (dateString) => {
  if (!dateString) return '';
  const options = { day: 'numeric', month: 'long', year: 'numeric', hour: '2-digit', minute: '2-digit' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const getVisibilityLabel = (visibility) => {
  const labels = {
    'all': 'Tous',
    'admin': 'Administrateurs',
    'trainer': 'Formateurs',
    'student': 'Apprenants'
  };
  return labels[visibility] || visibility;
};

const getVisibilityClass = (visibility) => {
  const classes = {
    'all': 'bg-purple-100 text-purple-800',
    'admin': 'bg-red-100 text-red-800',
    'trainer': 'bg-blue-100 text-blue-800',
    'student': 'bg-green-100 text-green-800'
  };
  return classes[visibility] || 'bg-gray-100 text-gray-800';
};

const confirmDelete = () => {
  showDeleteModal.value = true;
};

const deleteAnnouncement = () => {
  router.delete(route('admin.announcements.destroy', props.announcement.id), {
    onSuccess: () => {
      router.visit(route('admin.announcements.index'));
    },
  });
};
</script>
