<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Announcement;
use App\Models\TrainingSession;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class AnnouncementController extends Controller
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Initialiser la requête
        $query = Announcement::with(['author', 'trainingSession']);

        // Appliquer la recherche si elle existe
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function($q) use ($searchTerm) {
                $q->where('title', 'like', $searchTerm)
                  ->orWhere('content', 'like', $searchTerm);
            });
        }

        // Filtrer par visibilité
        if ($request->has('visible_to') && !empty($request->visible_to)) {
            $query->where('visible_to', $request->visible_to);
        }

        // Filtrer par importance
        if ($request->has('is_important')) {
            $query->where('is_important', $request->is_important === 'true');
        }

        // Filtrer par session de formation
        if ($request->has('training_session_id') && !empty($request->training_session_id)) {
            $query->where('training_session_id', $request->training_session_id);
        }

        // Récupérer les annonces avec pagination
        $announcements = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Récupérer toutes les sessions de formation pour le filtre
        $trainingSessions = TrainingSession::orderBy('title')->get();

        // Retourner la vue avec les annonces et les filtres
        return Inertia::render('Admin/Announcements/Index', [
            'announcements' => $announcements,
            'trainingSessions' => $trainingSessions,
            'filters' => [
                'search' => $request->search ?? '',
                'visible_to' => $request->visible_to ?? '',
                'is_important' => $request->is_important ?? '',
                'training_session_id' => $request->training_session_id ?? '',
            ]
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Récupérer les sessions de formation
        $trainingSessions = TrainingSession::orderBy('title')->get();

        // Retourner la vue pour créer une nouvelle annonce
        return Inertia::render('Admin/Announcements/Create', [
            'trainingSessions' => $trainingSessions
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'visible_to' => 'required|in:all,admin,trainer,student',
            'is_important' => 'boolean',
            'publish_date' => 'required|date',
            'expiry_date' => 'nullable|date|after_or_equal:publish_date',
            'training_session_id' => 'nullable|exists:training_sessions,id',
        ]);

        // Ajouter l'ID de l'utilisateur qui crée l'annonce
        $validated['author_id'] = Auth::id();

        // Ajouter des valeurs par défaut si nécessaire
        $validated['is_published'] = true;
        $validated['type'] = $validated['training_session_id'] ? 'session_specific' : 'general';

        // Créer l'annonce
        $announcement = Announcement::create($validated);

        // Envoyer les notifications aux utilisateurs concernés
        try {
            $notificationCount = $this->notificationService->notifyNewAnnouncement($announcement);
            $successMessage = "Annonce créée avec succès. {$notificationCount} notification(s) envoyée(s).";
        } catch (\Exception $e) {
            $successMessage = "Annonce créée avec succès, mais erreur lors de l'envoi des notifications: " . $e->getMessage();
        }

        // Rediriger vers la liste des annonces avec un message de succès
        return redirect()->route('admin.announcements.index')
            ->with('success', $successMessage);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Récupérer l'annonce avec ses relations
        $announcement = Announcement::with(['author', 'trainingSession'])->findOrFail($id);

        // Retourner la vue avec l'annonce
        return Inertia::render('Admin/Announcements/Show', [
            'announcement' => $announcement
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Récupérer l'annonce
        $announcement = Announcement::findOrFail($id);

        // Récupérer les sessions de formation
        $trainingSessions = TrainingSession::orderBy('title')->get();

        // Retourner la vue pour éditer l'annonce
        return Inertia::render('Admin/Announcements/Edit', [
            'announcement' => $announcement,
            'trainingSessions' => $trainingSessions
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Récupérer l'annonce
        $announcement = Announcement::findOrFail($id);

        // Valider les données du formulaire
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'visible_to' => 'required|in:all,admin,trainer,student',
            'is_important' => 'boolean',
            'publish_date' => 'required|date',
            'expiry_date' => 'nullable|date|after_or_equal:publish_date',
            'training_session_id' => 'nullable|exists:training_sessions,id',
        ]);

        // Mettre à jour l'annonce
        $announcement->update($validated);

        // Rediriger vers la liste des annonces avec un message de succès
        return redirect()->route('admin.announcements.index')
            ->with('success', 'Annonce mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Récupérer l'annonce
        $announcement = Announcement::findOrFail($id);

        // Supprimer l'annonce
        $announcement->delete();

        // Rediriger vers la liste des annonces avec un message de succès
        return redirect()->route('admin.announcements.index')
            ->with('success', 'Annonce supprimée avec succès.');
    }
}
