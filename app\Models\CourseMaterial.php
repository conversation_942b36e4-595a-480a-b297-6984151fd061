<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CourseMaterial extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'course_id',
        'module_id',
        'type',
        'content',
        'embed_code',
        'file_path',
        'order',
        'active',
        'allow_download',
        'allow_online_viewing',
        'mime_type',
        'file_size',
        'thumbnail_path',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'active' => 'boolean',
        'allow_download' => 'boolean',
        'allow_online_viewing' => 'boolean',
        'file_size' => 'integer',
        'metadata' => 'json',
    ];

    /**
     * Relation avec le cours
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Relation avec le module
     */
    public function module()
    {
        return $this->belongsTo(Module::class);
    }

    /**
     * Relation avec la progression des apprenants
     */
    public function progress()
    {
        return $this->hasMany(CourseProgress::class);
    }
}
