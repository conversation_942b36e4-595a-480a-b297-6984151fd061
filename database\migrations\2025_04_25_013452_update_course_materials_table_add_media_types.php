<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('course_materials', function (Blueprint $table) {
            // Modifier le type enum pour inclure les nouveaux types de médias
            DB::statement("ALTER TABLE course_materials MODIFY COLUMN type ENUM('pdf', 'video', 'text', 'archive', 'image', 'audio')");

            // Ajouter des champs pour la lecture en ligne et le téléchargement
            $table->boolean('allow_download')->default(true)->after('active');
            $table->boolean('allow_online_viewing')->default(true)->after('allow_download');
            $table->string('mime_type')->nullable()->after('allow_online_viewing');
            $table->integer('file_size')->nullable()->after('mime_type'); // <PERSON>lle en Ko
            $table->string('thumbnail_path')->nullable()->after('file_size');
            $table->text('metadata')->nullable()->after('thumbnail_path'); // Pour stocker des métadonnées JSON (durée, dimensions, etc.)
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('course_materials', function (Blueprint $table) {
            // Supprimer les nouveaux champs
            $table->dropColumn([
                'allow_download',
                'allow_online_viewing',
                'mime_type',
                'file_size',
                'thumbnail_path',
                'metadata'
            ]);

            // Restaurer le type enum d'origine
            DB::statement("ALTER TABLE course_materials MODIFY COLUMN type ENUM('pdf', 'video', 'text', 'quiz')");
        });
    }
};
