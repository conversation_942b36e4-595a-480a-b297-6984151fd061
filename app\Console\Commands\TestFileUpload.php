<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\UploadedFile;

class TestFileUpload extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:file-upload';

    /**
     * The console description of the command.
     *
     * @var string
     */
    protected $description = 'Test file upload functionality and storage permissions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing File Upload System...');

        // Test 1: Check storage disk configuration
        $this->info("\n--- Testing Storage Configuration ---");
        try {
            $disk = Storage::disk('public');
            $this->info('✅ Public disk accessible');
            
            // Check if payment_proofs directory exists
            if ($disk->exists('payment_proofs')) {
                $this->info('✅ payment_proofs directory exists');
            } else {
                $this->warn('⚠️  payment_proofs directory does not exist, creating...');
                $disk->makeDirectory('payment_proofs');
                $this->info('✅ payment_proofs directory created');
            }
        } catch (\Exception $e) {
            $this->error('❌ Storage disk error: ' . $e->getMessage());
            return 1;
        }

        // Test 2: Test file write permissions
        $this->info("\n--- Testing Write Permissions ---");
        try {
            $testContent = 'Test file content - ' . now();
            $testPath = 'payment_proofs/test_' . time() . '.txt';
            
            $disk->put($testPath, $testContent);
            $this->info('✅ File write successful');
            
            // Test file read
            $readContent = $disk->get($testPath);
            if ($readContent === $testContent) {
                $this->info('✅ File read successful');
            } else {
                $this->error('❌ File read failed - content mismatch');
            }
            
            // Clean up test file
            $disk->delete($testPath);
            $this->info('✅ Test file cleaned up');
            
        } catch (\Exception $e) {
            $this->error('❌ File operations error: ' . $e->getMessage());
            return 1;
        }

        // Test 3: Check existing files
        $this->info("\n--- Checking Existing Files ---");
        try {
            $files = $disk->files('payment_proofs');
            $this->info('Files in payment_proofs directory: ' . count($files));
            
            foreach ($files as $file) {
                $size = $disk->size($file);
                $lastModified = $disk->lastModified($file);
                $this->info("  - {$file} ({$size} bytes, modified: " . date('Y-m-d H:i:s', $lastModified) . ")");
            }
            
        } catch (\Exception $e) {
            $this->error('❌ Error listing files: ' . $e->getMessage());
        }

        // Test 4: Check public URL access
        $this->info("\n--- Testing Public URL Access ---");
        try {
            $publicPath = storage_path('app/public');
            $linkPath = public_path('storage');
            
            $this->info("Storage path: {$publicPath}");
            $this->info("Public link path: {$linkPath}");
            
            if (is_link($linkPath)) {
                $this->info('✅ Symbolic link exists');
                $target = readlink($linkPath);
                $this->info("Link target: {$target}");
            } else {
                $this->warn('⚠️  Symbolic link does not exist or is not a link');
            }
            
        } catch (\Exception $e) {
            $this->error('❌ Error checking symbolic link: ' . $e->getMessage());
        }

        // Test 5: Validate file types and sizes
        $this->info("\n--- Testing File Validation ---");
        $allowedMimes = ['jpeg', 'png', 'jpg', 'pdf'];
        $maxSize = 2048; // KB
        
        $this->info("Allowed MIME types: " . implode(', ', $allowedMimes));
        $this->info("Maximum file size: {$maxSize} KB");

        $this->info("\n✅ File upload system test completed!");
        $this->info("If you're still experiencing upload issues, check:");
        $this->info("1. Web server permissions on storage directories");
        $this->info("2. PHP upload_max_filesize and post_max_size settings");
        $this->info("3. Browser console for JavaScript errors");
        $this->info("4. Laravel logs for detailed error messages");

        return 0;
    }
}
