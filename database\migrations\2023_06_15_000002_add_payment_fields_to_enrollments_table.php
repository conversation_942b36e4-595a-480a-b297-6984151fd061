<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('enrollments', function (Blueprint $table) {
            // Champs pour le paiement
            $table->enum('payment_status', ['unpaid', 'pending', 'paid', 'refunded'])->default('unpaid')->after('payment_confirmed');
            $table->string('payment_method')->nullable()->after('payment_status');
            $table->string('payment_proof')->nullable()->after('payment_method');
            $table->timestamp('payment_date')->nullable()->after('payment_proof');
            $table->timestamp('payment_due_date')->nullable()->after('payment_date');
            $table->text('payment_notes')->nullable()->after('payment_due_date');
            $table->text('payment_instructions')->nullable()->after('payment_notes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('enrollments', function (Blueprint $table) {
            $table->dropColumn([
                'payment_status',
                'payment_method',
                'payment_proof',
                'payment_date',
                'payment_due_date',
                'payment_notes',
                'payment_instructions'
            ]);
        });
    }
};
