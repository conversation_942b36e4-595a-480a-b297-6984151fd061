<template>
  <Head title="Annonces" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Annonces
        </h2>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Search and Filters -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row gap-4">
              <!-- Search -->
              <div class="flex-1">
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="Rechercher dans les annonces..."
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  @keyup.enter="applyFilters"
                />
              </div>
              
              <!-- Important Filter -->
              <div class="flex items-center space-x-2">
                <input
                  id="important-filter"
                  v-model="importantFilter"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  @change="applyFilters"
                />
                <label for="important-filter" class="text-sm font-medium text-gray-700">
                  Annonces importantes uniquement
                </label>
              </div>

              <!-- Action Buttons -->
              <div class="flex space-x-2">
                <button
                  @click="applyFilters"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  Rechercher
                </button>
                <button
                  @click="resetFilters"
                  class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                >
                  Réinitialiser
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Announcements List -->
        <div class="space-y-6">
          <div
            v-if="announcements.data.length === 0"
            class="bg-white overflow-hidden shadow-sm sm:rounded-lg"
          >
            <div class="p-12 text-center">
              <MegaphoneIcon class="h-12 w-12 mx-auto text-gray-300 mb-4" />
              <h3 class="text-lg font-medium text-gray-900 mb-2">Aucune annonce</h3>
              <p class="text-gray-500">
                {{ filters.search || filters.important ? 'Aucune annonce ne correspond à vos critères de recherche.' : 'Aucune annonce disponible pour le moment.' }}
              </p>
            </div>
          </div>

          <div
            v-for="announcement in announcements.data"
            :key="announcement.id"
            class="bg-white overflow-hidden shadow-sm sm:rounded-lg hover:shadow-md transition-shadow cursor-pointer"
            @click="viewAnnouncement(announcement.id)"
          >
            <div class="p-6">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <!-- Title and Important Badge -->
                  <div class="flex items-center space-x-3 mb-2">
                    <h3 class="text-lg font-semibold text-gray-900">
                      {{ announcement.title }}
                    </h3>
                    <span
                      v-if="announcement.is_important"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
                    >
                      <ExclamationCircleIcon class="h-3 w-3 mr-1" />
                      Important
                    </span>
                  </div>

                  <!-- Content Preview -->
                  <p class="text-gray-600 mb-4 line-clamp-3">
                    {{ announcement.content }}
                  </p>

                  <!-- Meta Information -->
                  <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                    <div class="flex items-center">
                      <UserIcon class="h-4 w-4 mr-1" />
                      {{ announcement.author.name }}
                    </div>
                    <div class="flex items-center">
                      <CalendarIcon class="h-4 w-4 mr-1" />
                      {{ formatDate(announcement.publish_date) }}
                    </div>
                    <div
                      v-if="announcement.training_session"
                      class="flex items-center"
                    >
                      <AcademicCapIcon class="h-4 w-4 mr-1" />
                      {{ announcement.training_session.title }}
                    </div>
                  </div>
                </div>

                <!-- Arrow Icon -->
                <div class="ml-4">
                  <ChevronRightIcon class="h-5 w-5 text-gray-400" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="announcements.links.length > 3" class="mt-8">
          <nav class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
              <Link
                v-if="announcements.prev_page_url"
                :href="announcements.prev_page_url"
                class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Précédent
              </Link>
              <Link
                v-if="announcements.next_page_url"
                :href="announcements.next_page_url"
                class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Suivant
              </Link>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  Affichage de
                  <span class="font-medium">{{ announcements.from }}</span>
                  à
                  <span class="font-medium">{{ announcements.to }}</span>
                  sur
                  <span class="font-medium">{{ announcements.total }}</span>
                  résultats
                </p>
              </div>
              <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <Link
                    v-for="link in announcements.links"
                    :key="link.label"
                    :href="link.url"
                    v-html="link.label"
                    :class="[
                      'relative inline-flex items-center px-2 py-2 border text-sm font-medium',
                      link.active
                        ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                      link.url ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'
                    ]"
                  />
                </nav>
              </div>
            </div>
          </nav>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue'
import { Head, Link, router } from '@inertiajs/vue3'
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue'
import {
  MegaphoneIcon,
  ExclamationCircleIcon,
  UserIcon,
  CalendarIcon,
  AcademicCapIcon,
  ChevronRightIcon,
} from '@heroicons/vue/24/outline'

// Props
const props = defineProps({
  announcements: Object,
  filters: Object,
})

// Reactive state
const searchQuery = ref(props.filters.search)
const importantFilter = ref(props.filters.important === 'true')

// Methods
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

const viewAnnouncement = (id) => {
  router.visit(route('announcements.show', id))
}

const applyFilters = () => {
  router.get(route('announcements.index'), {
    search: searchQuery.value,
    important: importantFilter.value ? 'true' : '',
  }, {
    preserveState: true,
    replace: true,
  })
}

const resetFilters = () => {
  searchQuery.value = ''
  importantFilter.value = false
  applyFilters()
}
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
