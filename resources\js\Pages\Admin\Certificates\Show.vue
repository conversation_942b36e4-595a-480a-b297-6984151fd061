<template>
  <Head :title="'Certificat - ' + certificate.certificate_number" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Détails du certificat
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Bouton de retour -->
        <div class="mb-6">
          <Link :href="route('admin.certificates.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Retour à la liste
          </Link>
        </div>

        <!-- Message de succès -->
        <div v-if="$page.props.flash.success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
          <span class="block sm:inline">{{ $page.props.flash.success }}</span>
        </div>

        <!-- Message d'erreur -->
        <div v-if="$page.props.flash.error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <span class="block sm:inline">{{ $page.props.flash.error }}</span>
        </div>

        <!-- Informations du certificat -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-2xl font-semibold mb-2">Certificat de {{ certificate.enrollment.user.name }}</h3>
                <div class="mb-2">
                  <span :class="{
                    'px-2 py-1 text-xs rounded-full': true,
                    'bg-yellow-100 text-yellow-800': certificate.status === 'draft',
                    'bg-green-100 text-green-800': certificate.status === 'issued',
                    'bg-red-100 text-red-800': certificate.status === 'revoked'
                  }">
                    {{ formatStatus(certificate.status) }}
                  </span>
                </div>
              </div>
              <div class="flex space-x-2">
                <Link :href="route('admin.certificates.edit', certificate.id)" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 0L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Modifier
                </Link>
                <a :href="`/admin/certificates/${certificate.id}/download`" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150" download>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Télécharger
                </a>
                <a :href="`/admin/certificates/${certificate.id}/view`" target="_blank" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  Voir
                </a>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <!-- Informations sur le certificat -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold mb-3">Informations sur le certificat</h4>
                <div class="mb-2">
                  <span class="font-semibold">Numéro de certificat:</span> {{ certificate.certificate_number }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Date d'émission:</span> {{ formatDate(certificate.issued_at) }}
                </div>
                <div class="mb-2" v-if="certificate.expiry_date">
                  <span class="font-semibold">Date d'expiration:</span> {{ formatDate(certificate.expiry_date) }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Statut:</span>
                  <span :class="{
                    'px-2 py-1 text-xs rounded-full ml-2': true,
                    'bg-yellow-100 text-yellow-800': certificate.status === 'draft',
                    'bg-green-100 text-green-800': certificate.status === 'issued',
                    'bg-red-100 text-red-800': certificate.status === 'revoked'
                  }">
                    {{ formatStatus(certificate.status) }}
                  </span>
                </div>
                <div class="mb-2" v-if="certificate.pdf_path">
                  <span class="font-semibold">PDF:</span> Disponible
                </div>
                <div class="mb-2" v-if="certificate.signature_image">
                  <span class="font-semibold">Signature:</span>
                  <div class="mt-2">
                    <img :src="'/storage/' + certificate.signature_image" alt="Signature" class="h-16 w-auto object-contain rounded">
                  </div>
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Code QR:</span>
                  <div class="mt-2">
                    <div v-if="certificate.qr_code">
                      <img :src="'/storage/' + certificate.qr_code" alt="Code QR" class="h-32 w-auto object-contain rounded">
                    </div>
                    <div v-else class="text-gray-500 italic">
                      Code QR généré automatiquement lors de la création du certificat
                    </div>
                  </div>
                </div>
              </div>

              <!-- Informations sur l'apprenant et la formation -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold mb-3">Informations sur l'apprenant et la formation</h4>
                <div class="mb-2">
                  <span class="font-semibold">Apprenant:</span> {{ certificate.enrollment.user.name }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Email:</span> {{ certificate.enrollment.user.email }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Formation:</span> {{ certificate.enrollment.training_session.title }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Domaine:</span> {{ certificate.enrollment.training_session.training_domain.name }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Formateur:</span> {{ certificate.enrollment.training_session.trainer.name }}
                </div>
                <div class="mb-2">
                  <span class="font-semibold">Période de formation:</span> {{ formatDate(certificate.enrollment.training_session.start_date) }} - {{ formatDate(certificate.enrollment.training_session.end_date) }}
                </div>
              </div>
            </div>

            <!-- Aperçu du certificat PDF -->
            <div class="mt-6">
              <h4 class="text-lg font-semibold mb-3">Aperçu du certificat</h4>
              <div class="bg-gray-50 p-4 rounded-lg">
                <div class="w-full h-[800px] border border-gray-300 rounded-lg overflow-hidden bg-white">
                  <iframe
                    :src="`/admin/certificates/${certificate.id}/view`"
                    class="w-full h-full"
                    title="Aperçu du certificat PDF"
                    frameborder="0"
                  ></iframe>
                </div>
                <div class="mt-4 text-center">
                  <p class="text-sm text-gray-600">
                    Le certificat s'affiche ci-dessus. Vous pouvez utiliser les boutons "Visualiser" ou "Télécharger" pour une meilleure expérience.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  certificate: Object,
});

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return 'Non définie';
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const formatStatus = (status) => {
  const statusMap = {
    'draft': 'Brouillon',
    'issued': 'Émis',
    'revoked': 'Révoqué'
  };
  return statusMap[status] || status;
};

// Fonction pour télécharger le PDF en utilisant l'URL directe
const downloadPdf = async () => {
  try {
    // Récupérer l'URL du PDF
    const response = await fetch(`/admin/certificates/${props.certificate.id}/pdf-url`);

    // Vérifier si la requête a réussi
    if (!response.ok) {
      throw new Error(`Erreur HTTP: ${response.status}`);
    }

    // Récupérer les données JSON
    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    // Créer un élément <a> temporaire
    const link = document.createElement('a');
    link.href = data.url;
    link.download = data.filename;
    link.target = '_blank';

    // Ajouter l'élément au DOM
    document.body.appendChild(link);

    // Simuler un clic sur le lien
    link.click();

    // Supprimer l'élément du DOM
    document.body.removeChild(link);

    console.log('Téléchargement lancé');
  } catch (error) {
    console.error('Erreur lors du téléchargement:', error);
  }
};


</script>
