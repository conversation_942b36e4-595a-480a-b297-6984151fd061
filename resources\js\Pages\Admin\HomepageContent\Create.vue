<template>
    <Head title="Ajouter du contenu" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    Ajouter du contenu à la page d'accueil
                </h2>
                <Link :href="route('admin.homepage-content.index')" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Retour
                </Link>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <form @submit.prevent="submit" enctype="multipart/form-data">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Section -->
                                <div>
                                    <InputLabel for="section" value="Section" />
                                    <select id="section" v-model="form.section" 
                                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">Sélectionner une section</option>
                                        <option value="hero">Section Héro</option>
                                        <option value="vr_training">Formation VR</option>
                                        <option value="about">À propos</option>
                                        <option value="contact">Contact</option>
                                    </select>
                                    <InputError class="mt-2" :message="form.errors.section" />
                                </div>

                                <!-- Key -->
                                <div>
                                    <InputLabel for="key" value="Clé" />
                                    <TextInput
                                        id="key"
                                        type="text"
                                        class="mt-1 block w-full"
                                        v-model="form.key"
                                        placeholder="ex: title, subtitle, image_hero"
                                    />
                                    <InputError class="mt-2" :message="form.errors.key" />
                                </div>

                                <!-- Type -->
                                <div>
                                    <InputLabel for="type" value="Type de contenu" />
                                    <select id="type" v-model="form.type" 
                                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="text">Texte</option>
                                        <option value="image">Image</option>
                                        <option value="video">Vidéo</option>
                                        <option value="json">JSON</option>
                                    </select>
                                    <InputError class="mt-2" :message="form.errors.type" />
                                </div>

                                <!-- Sort Order -->
                                <div>
                                    <InputLabel for="sort_order" value="Ordre d'affichage" />
                                    <TextInput
                                        id="sort_order"
                                        type="number"
                                        class="mt-1 block w-full"
                                        v-model="form.sort_order"
                                        min="0"
                                    />
                                    <InputError class="mt-2" :message="form.errors.sort_order" />
                                </div>
                            </div>

                            <!-- Content Value -->
                            <div class="mt-6">
                                <!-- File Upload for Image/Video -->
                                <div v-if="form.type === 'image' || form.type === 'video'">
                                    <MediaUpload
                                        :label="form.type === 'image' ? 'Image' : 'Vidéo'"
                                        :accept="form.type === 'image' ? 'image/*' : 'video/*'"
                                        :max-size="form.type === 'image' ? 5 * 1024 * 1024 : 50 * 1024 * 1024"
                                        v-model="form.file"
                                        @file-selected="handleFileSelected"
                                        @file-cleared="handleFileCleared"
                                    />
                                    <InputError class="mt-2" :message="form.errors.file" />
                                </div>

                                <!-- Text/JSON Input -->
                                <div v-else>
                                    <InputLabel for="value" value="Contenu" />
                                    <textarea
                                        id="value"
                                        v-model="form.value"
                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        rows="6"
                                        :placeholder="form.type === 'json' ? 'Entrez du JSON valide' : 'Entrez le contenu texte'"
                                    ></textarea>
                                    <InputError class="mt-2" :message="form.errors.value" />
                                </div>
                            </div>

                            <!-- Metadata -->
                            <div class="mt-6">
                                <InputLabel for="metadata" value="Métadonnées (JSON optionnel)" />
                                <textarea
                                    id="metadata"
                                    v-model="metadataText"
                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    rows="3"
                                    placeholder='{"alt": "Description de l&apos;image", "title": "Titre"}'
                                ></textarea>
                                <InputError class="mt-2" :message="form.errors.metadata" />
                            </div>

                            <!-- Active Status -->
                            <div class="mt-6">
                                <label class="flex items-center">
                                    <input type="checkbox" v-model="form.is_active" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-600">Actif</span>
                                </label>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex items-center justify-end mt-6">
                                <PrimaryButton class="ml-4" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                                    Ajouter le contenu
                                </PrimaryButton>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import MediaUpload from '@/Components/MediaUpload.vue';
import { useForm } from '@inertiajs/vue3';
import { ref, watch } from 'vue';

const form = useForm({
    section: '',
    key: '',
    value: '',
    type: 'text',
    metadata: null,
    is_active: true,
    sort_order: 0,
    file: null
});

const metadataText = ref('');

const handleFileSelected = (file) => {
    form.file = file;
};

const handleFileCleared = () => {
    form.file = null;
};

watch(metadataText, (newValue) => {
    try {
        form.metadata = newValue ? JSON.parse(newValue) : null;
    } catch (e) {
        // Invalid JSON, keep as null
        form.metadata = null;
    }
});

const submit = () => {
    form.post(route('admin.homepage-content.store'));
};
</script>
