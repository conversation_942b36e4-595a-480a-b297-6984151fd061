<template>
  <Head title="Ajouter une question" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Ajouter une question à l'examen: {{ exam.title }}
        </h2>
        <Link :href="route('trainer.exam-questions.index', { exam_id: exam.id })" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
          Retour aux questions
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <form @submit.prevent="submit">
              <!-- ID de l'examen (caché) -->
              <input type="hidden" v-model="form.exam_id" />

              <!-- Texte de la question -->
              <div class="mb-4">
                <InputLabel for="question_text" value="Texte de la question" />
                <textarea
                  id="question_text"
                  v-model="form.question_text"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  rows="3"
                  required
                ></textarea>
                <InputError class="mt-2" :message="form.errors.question_text" />
              </div>

              <!-- Type de question -->
              <div class="mb-4">
                <InputLabel for="question_type" value="Type de question" />
                <select
                  id="question_type"
                  v-model="form.question_type"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  required
                  @change="handleTypeChange"
                >
                  <option value="" disabled>Sélectionnez un type</option>
                  <option v-for="type in questionTypes" :key="type.value" :value="type.value">
                    {{ type.label }}
                  </option>
                </select>
                <InputError class="mt-2" :message="form.errors.question_type" />
              </div>

              <!-- Points -->
              <div class="mb-4">
                <InputLabel for="points" value="Points" />
                <TextInput
                  id="points"
                  type="number"
                  class="mt-1 block w-full"
                  v-model="form.points"
                  min="1"
                  required
                />
                <InputError class="mt-2" :message="form.errors.points" />
              </div>

              <!-- Options (pour les questions à choix) -->
              <div v-if="['multiple_choice', 'single_choice'].includes(form.question_type)" class="mb-4">
                <div class="flex justify-between items-center mb-2">
                  <InputLabel value="Options" />
                  <SecondaryButton type="button" @click="addOption" class="text-sm">
                    Ajouter une option
                  </SecondaryButton>
                </div>

                <div v-for="(option, index) in form.options" :key="index" class="mb-2 flex items-start">
                  <div class="flex-grow mr-2">
                    <div class="flex items-center">
                      <input
                        :type="form.question_type === 'multiple_choice' ? 'checkbox' : 'radio'"
                        :name="'correct_option'"
                        :id="'option_' + index"
                        :checked="isOptionCorrect(index)"
                        @change="toggleCorrectOption(index)"
                        class="mr-2"
                        required
                      />
                      <TextInput
                        :id="'option_text_' + index"
                        type="text"
                        class="block w-full"
                        v-model="form.options[index]"
                        placeholder="Texte de l'option"
                        required
                      />
                    </div>
                  </div>
                  <button
                    type="button"
                    @click="removeOption(index)"
                    class="text-red-600 hover:text-red-900"
                  >
                    Supprimer
                  </button>
                </div>

                <InputError class="mt-2" :message="form.errors.options" />
                <InputError class="mt-2" :message="form.errors.correct_options" />
                <p v-if="optionsError" class="mt-2 text-sm text-red-600">
                  {{ optionsError }}
                </p>

                <p v-if="form.options.length === 0" class="text-sm text-gray-500 mt-2">
                  Ajoutez des options et sélectionnez la ou les réponses correctes.
                </p>
                <p v-else class="text-sm text-gray-500 mt-2">
                  Veuillez sélectionner au moins une réponse correcte.
                </p>
              </div>

              <!-- Champ de fichier (pour les questions de type fichier) -->
              <div v-if="form.question_type === 'file'" class="mb-4">
                <InputLabel value="Configuration du téléchargement de fichier" />
                <div class="mt-2 p-4 bg-gray-50 rounded-md">
                  <p class="text-sm text-gray-600 mb-2">
                    Les apprenants devront télécharger un fichier comme réponse à cette question.
                  </p>
                  <div class="flex items-center">
                    <span class="text-sm text-gray-500">Types de fichiers acceptés:</span>
                    <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md">PDF</span>
                    <span class="ml-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md">DOC/DOCX</span>
                    <span class="ml-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md">JPG/PNG</span>
                  </div>
                </div>
              </div>

              <!-- Explication -->
              <div class="mb-6">
                <InputLabel for="explanation" value="Explication (optionnelle)" />
                <textarea
                  id="explanation"
                  v-model="form.explanation"
                  class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  rows="3"
                  placeholder="Explication qui sera montrée après la soumission de la réponse"
                ></textarea>
                <InputError class="mt-2" :message="form.errors.explanation" />
              </div>

              <!-- Boutons -->
              <div class="flex justify-end">
                <Link
                  :href="route('trainer.exam-questions.index', { exam_id: exam.id })"
                  class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150 mr-2"
                >
                  Annuler
                </Link>
                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                  Ajouter
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// Variable pour les erreurs de validation personnalisées
const optionsError = ref('');

// Props
const props = defineProps({
  exam: Object,
  questionTypes: Array,
});

// Formulaire
const form = useForm({
  exam_id: props.exam.id,
  question_text: '',
  question_type: '',
  points: 1,
  options: [],
  correct_options: [],
  explanation: '',
});

// Méthodes pour les options
const addOption = () => {
  form.options.push('');
};

const removeOption = (index) => {
  form.options.splice(index, 1);

  // Mettre à jour les options correctes
  if (form.question_type === 'multiple_choice') {
    form.correct_options = form.correct_options.filter(i => i !== index).map(i => i > index ? i - 1 : i);
  } else if (form.question_type === 'single_choice' && form.correct_options[0] === index) {
    form.correct_options = [];
  } else if (form.question_type === 'single_choice' && form.correct_options[0] > index) {
    form.correct_options = [form.correct_options[0] - 1];
  }
};

const isOptionCorrect = (index) => {
  if (form.question_type === 'multiple_choice') {
    return form.correct_options.includes(index);
  } else {
    return form.correct_options[0] === index;
  }
};

const toggleCorrectOption = (index) => {
  if (form.question_type === 'multiple_choice') {
    const position = form.correct_options.indexOf(index);
    if (position === -1) {
      form.correct_options.push(index);
    } else {
      form.correct_options.splice(position, 1);
    }
  } else {
    form.correct_options = [index];
  }
};

const handleTypeChange = () => {
  // Réinitialiser les options et les options correctes si le type change
  if (['multiple_choice', 'single_choice'].includes(form.question_type)) {
    if (form.options.length === 0) {
      // Ajouter quelques options par défaut
      form.options = ['', ''];
    }
    if (form.question_type === 'single_choice') {
      form.correct_options = form.correct_options.length > 0 ? [form.correct_options[0]] : [];
    }
  } else {
    form.options = [];
    form.correct_options = [];
  }
};

// Méthode de soumission
const submit = () => {
  // Réinitialiser les erreurs
  optionsError.value = '';

  // Validation personnalisée pour les questions à choix
  if (['multiple_choice', 'single_choice'].includes(form.question_type)) {
    // Vérifier qu'au moins une option est sélectionnée comme correcte
    if (form.correct_options.length === 0) {
      optionsError.value = 'Veuillez sélectionner au moins une réponse correcte.';
      return;
    }

    // Vérifier que toutes les options ont du texte
    const emptyOptions = form.options.some(option => !option.trim());
    if (emptyOptions) {
      optionsError.value = 'Toutes les options doivent avoir du texte.';
      return;
    }

    // Ajouter des logs pour le débogage
    console.log('Options correctes avant soumission:', form.correct_options);
  }

  // Soumettre le formulaire
  form.post(route('trainer.exam-questions.store'));
};
</script>
