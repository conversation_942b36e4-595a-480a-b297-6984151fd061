<template>
    <Head :title="`Profil de ${student.name}`" />

    <div class="min-h-screen bg-gray-50">
        <!-- Header -->
        <div class="bg-white shadow">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <img src="/images/logo.png" alt="Logo" class="h-8 w-auto" />
                        <span class="ml-2 text-xl font-semibold text-gray-900">Formation Platform</span>
                    </div>
                    <div class="text-sm text-gray-500">
                        Profil Public
                    </div>
                </div>
            </div>
        </div>

        <!-- Contenu principal -->
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Profil de l'apprenant -->
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="px-6 py-8">
                    <div class="flex items-center space-x-6">
                        <div class="flex-shrink-0">
                            <img
                                :src="getProfilePhotoUrl(student.profile_photo)"
                                :alt="student.name"
                                class="h-24 w-24 rounded-full object-cover border-4 border-blue-200"
                                @error="handleImageError"
                            />
                        </div>
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-gray-900">{{ student.name }}</h1>
                            <p class="text-lg text-gray-600 mt-1">Apprenant Certifié</p>
                            <div class="mt-4 flex items-center space-x-4">
                                <div class="flex items-center text-sm text-gray-500">
                                    <TrophyIcon class="h-5 w-5 mr-1 text-yellow-500" />
                                    {{ student.certificates.length }} Certificat(s)
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <AcademicCapIcon class="h-5 w-5 mr-1 text-blue-500" />
                                    {{ student.completed_trainings.length }} Formation(s) Terminée(s)
                                </div>
                            </div>
                        </div>

                        <!-- QR Code Section -->
                        <div v-if="student.qr_code_path" class="flex-shrink-0 text-center">
                            <div class="bg-white border-2 border-gray-200 rounded-lg p-4 shadow-sm">
                                <img
                                    :src="`/storage/${student.qr_code_path}`"
                                    :alt="`QR Code de ${student.name}`"
                                    class="w-24 h-24 mx-auto"
                                />
                                <p class="text-xs text-gray-500 mt-2">Scanner pour vérifier</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Certificats -->
            <div v-if="student.certificates.length > 0" class="mt-8">
                <div class="bg-white shadow rounded-lg overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                            <TrophyIcon class="h-6 w-6 mr-2 text-yellow-500" />
                            Certificats Obtenus
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div
                                v-for="certificate in student.certificates"
                                :key="certificate.id"
                                class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow bg-gradient-to-br from-yellow-50 to-orange-50"
                            >
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="bg-yellow-100 rounded-full p-2">
                                            <TrophyIcon class="h-6 w-6 text-yellow-600" />
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-lg font-semibold text-gray-900">
                                                {{ certificate.training_name }}
                                            </h3>
                                            <p class="text-sm text-gray-600">
                                                {{ certificate.domain_name }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xs text-gray-500 mb-1">Certificat N°</div>
                                        <div class="text-sm font-mono text-gray-700">
                                            {{ certificate.certificate_number }}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="border-t border-gray-200 pt-4">
                                    <div class="flex justify-between items-center text-sm">
                                        <div>
                                            <span class="text-gray-500">Délivré le :</span>
                                            <span class="font-medium text-gray-900 ml-1">
                                                {{ formatDate(certificate.issue_date) }}
                                            </span>
                                        </div>
                                        <div v-if="certificate.expiry_date" class="text-right">
                                            <span class="text-gray-500">Expire le :</span>
                                            <span class="font-medium text-gray-900 ml-1">
                                                {{ formatDate(certificate.expiry_date) }}
                                            </span>
                                        </div>
                                        <div v-else class="text-right">
                                            <span class="text-green-600 font-medium">Valide à vie</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions et badge de validité -->
                                <div class="mt-4 space-y-3">
                                    <!-- Bouton pour voir le certificat -->
                                    <div class="flex justify-center">
                                        <button
                                            @click="viewCertificate(certificate)"
                                            class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                                            </svg>
                                            Voir le Certificat
                                        </button>
                                    </div>

                                    <!-- Badge de validité -->
                                    <div class="flex justify-center">
                                        <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <CheckCircleIcon class="h-4 w-4 mr-1" />
                                            Certificat Vérifié
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Formations terminées -->
            <div v-if="student.completed_trainings.length > 0" class="mt-8">
                <div class="bg-white shadow rounded-lg overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                            <AcademicCapIcon class="h-6 w-6 mr-2 text-blue-500" />
                            Formations Terminées
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div
                                v-for="training in student.completed_trainings"
                                :key="training.training_name"
                                class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                                <div class="flex items-center">
                                    <div class="bg-blue-100 rounded-full p-2">
                                        <AcademicCapIcon class="h-5 w-5 text-blue-600" />
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-lg font-medium text-gray-900">
                                            {{ training.training_name }}
                                        </h3>
                                        <p class="text-sm text-gray-600">
                                            {{ training.domain_name }}
                                        </p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm text-gray-500">Terminé le</div>
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ formatDate(training.completion_date) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message si aucune donnée -->
            <div v-if="student.certificates.length === 0 && student.completed_trainings.length === 0" class="mt-8">
                <div class="bg-white shadow rounded-lg overflow-hidden">
                    <div class="px-6 py-12 text-center">
                        <AcademicCapIcon class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Aucune formation terminée</h3>
                        <p class="text-gray-600">
                            Cet apprenant n'a pas encore terminé de formation ou obtenu de certificat.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="bg-white border-t border-gray-200 mt-12">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="text-center text-sm text-gray-500">
                    <p>© 2024 Formation Platform. Tous droits réservés.</p>
                    <p class="mt-1">
                        Ce profil est vérifié et authentique.
                        <a href="#" class="text-blue-600 hover:text-blue-800">Vérifier l'authenticité</a>
                    </p>
                </div>
            </div>
        </footer>

        <!-- Modal de visualisation du certificat - Mobile Optimized -->
        <div v-if="showCertificateModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 overflow-y-auto">
            <!-- Mobile-first modal container -->
            <div class="min-h-screen px-4 py-4 flex items-center justify-center">
                <div class="bg-white rounded-lg shadow-xl w-full max-w-sm sm:max-w-lg md:max-w-4xl lg:max-w-6xl overflow-hidden flex flex-col" style="max-height: calc(100vh - 2rem);">
                    <!-- Header -->
                    <div class="flex justify-between items-center p-3 sm:p-4 md:p-6 border-b border-gray-200 flex-shrink-0">
                        <div class="min-w-0 flex-1">
                            <h3 class="text-base sm:text-lg md:text-xl font-semibold text-gray-900 truncate">Certificat</h3>
                            <p class="text-xs sm:text-sm text-gray-600 mt-1 truncate">{{ selectedCertificate.training_name }}</p>
                        </div>
                        <button @click="closeCertificateModal" class="ml-2 text-gray-400 hover:text-gray-600 transition-colors p-2 touch-button">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- Mobile-specific notice -->
                    <div class="bg-blue-50 border-b border-blue-200 p-3 sm:hidden">
                        <div class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <div class="text-xs text-blue-700">
                                <p class="font-medium mb-1">📱 Affichage mobile</p>
                                <p>Pour une meilleure visualisation, utilisez "Nouvel onglet".</p>
                            </div>
                        </div>
                    </div>

                    <!-- Certificate Content -->
                    <div class="flex-1 p-3 sm:p-4 md:p-6 overflow-hidden">
                        <div class="h-full border border-gray-300 rounded-lg overflow-hidden bg-gray-50 relative" style="min-height: 300px; max-height: 60vh;">
                            <!-- Loading state -->
                            <div v-if="certificateLoading" class="absolute inset-0 flex items-center justify-center bg-gray-50">
                                <div class="text-center">
                                    <div class="animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                                    <p class="text-gray-600 text-sm">Chargement du certificat...</p>
                                </div>
                            </div>

                            <!-- Certificate iframe with mobile optimization -->
                            <iframe
                                v-if="selectedCertificate.id && !certificateError"
                                :src="getCertificateViewUrl(selectedCertificate.id)"
                                class="w-full h-full certificate-iframe"
                                title="Certificat PDF"
                                frameborder="0"
                                scrolling="auto"
                                @load="handleCertificateLoad"
                                @error="handleCertificateError"
                                :style="{
                                    minHeight: isMobile ? '250px' : '400px',
                                    transform: isMobile ? 'scale(0.8)' : 'scale(1)',
                                    transformOrigin: 'top left',
                                    width: isMobile ? '125%' : '100%',
                                    height: isMobile ? '125%' : '100%'
                                }"
                            ></iframe>

                            <!-- Error state -->
                            <div v-if="certificateError" class="absolute inset-0 flex items-center justify-center bg-gray-50">
                                <div class="text-center p-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 sm:h-16 sm:w-16 text-red-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                                    </svg>
                                    <p class="text-red-600 font-medium mb-2 text-sm sm:text-base">Erreur de chargement</p>
                                    <p class="text-gray-600 text-xs sm:text-sm mb-4">Le certificat ne peut pas être affiché dans cette vue.</p>
                                    <div class="space-y-2">
                                        <button @click="reloadCertificate" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm touch-button">
                                            Réessayer
                                        </button>
                                        <p class="text-xs text-gray-500">Utilisez "Nouvel onglet" pour voir le certificat</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Mobile fallback message -->
                            <div v-if="!certificateLoading && !certificateError && isMobile" class="absolute bottom-2 left-2 right-2 bg-white bg-opacity-90 border border-gray-300 rounded p-2 text-xs text-gray-600">
                                💡 Pincer pour zoomer, faire défiler pour naviguer
                            </div>
                        </div>
                    </div>

                    <!-- Footer Actions -->
                    <div class="p-3 sm:p-4 md:p-6 border-t border-gray-200 flex-shrink-0">
                        <div class="flex flex-col gap-3">
                            <!-- Certificate number -->
                            <div class="text-xs sm:text-sm text-gray-600">
                                <span class="font-medium">Numéro:</span>
                                <span class="break-all">{{ selectedCertificate.certificate_number }}</span>
                            </div>

                            <!-- Action buttons -->
                            <div class="flex flex-col sm:flex-row gap-2">
                                <button @click="openCertificateInNewTab" class="flex-1 px-4 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center text-sm font-medium touch-button">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                    <span class="hidden sm:inline">Ouvrir dans un nouvel onglet</span>
                                    <span class="sm:hidden">Nouvel onglet</span>
                                </button>
                                <button @click="closeCertificateModal" class="px-4 py-3 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors text-sm font-medium touch-button">
                                    Fermer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import {
    TrophyIcon,
    AcademicCapIcon,
    CheckCircleIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
    student: Object
});

// État réactif
const showCertificateModal = ref(false);
const selectedCertificate = ref({});
const certificateLoading = ref(false);
const certificateError = ref(false);

// Mobile detection
const isMobile = ref(false);

// Check if device is mobile
const checkMobile = () => {
    isMobile.value = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// Méthodes
const formatDate = (date) => {
    if (!date) return 'Non défini';
    return new Date(date).toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
};

const viewCertificate = (certificate) => {
    selectedCertificate.value = certificate;
    certificateLoading.value = true;
    certificateError.value = false;
    showCertificateModal.value = true;

    // Check mobile and initialize
    checkMobile();

    // On mobile, show error state after timeout to encourage using new tab
    if (isMobile.value) {
        setTimeout(() => {
            if (certificateLoading.value) {
                certificateLoading.value = false;
                certificateError.value = true;
            }
        }, 3000);
    }
};

const closeCertificateModal = () => {
    showCertificateModal.value = false;
    selectedCertificate.value = {};
    certificateLoading.value = false;
    certificateError.value = false;
};

const getCertificateViewUrl = (certificateId) => {
    return `/public/certificates/${certificateId}/view`;
};

const openCertificateInNewTab = () => {
    if (selectedCertificate.value.id) {
        window.open(getCertificateViewUrl(selectedCertificate.value.id), '_blank');
    }
};

const handleCertificateLoad = () => {
    certificateLoading.value = false;
    certificateError.value = false;
};

const handleCertificateError = () => {
    certificateLoading.value = false;
    certificateError.value = true;
};

const reloadCertificate = () => {
    certificateError.value = false;
    certificateLoading.value = true;

    // Force reload the iframe by changing its src
    const iframe = document.querySelector('iframe[title="Certificat PDF"]');
    if (iframe && selectedCertificate.value.id) {
        const currentSrc = iframe.src;
        iframe.src = '';
        setTimeout(() => {
            iframe.src = currentSrc;
            // On mobile, set timeout again
            if (isMobile.value) {
                setTimeout(() => {
                    if (certificateLoading.value) {
                        certificateLoading.value = false;
                        certificateError.value = true;
                    }
                }, 3000);
            }
        }, 100);
    }
};

const getProfilePhotoUrl = (profilePhoto) => {
    if (!profilePhoto || profilePhoto === 'null' || profilePhoto === null) {
        return '/images/default-avatar.svg';
    }
    return `/storage/${profilePhoto}`;
};

const handleImageError = (event) => {
    // If the image fails to load, fallback to default avatar
    event.target.src = '/images/default-avatar.svg';
};
</script>

<style scoped>
/* Styles personnalisés pour la page publique */
.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
</style>
