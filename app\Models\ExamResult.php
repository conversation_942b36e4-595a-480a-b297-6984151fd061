<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExamResult extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'enrollment_id',
        'user_id',
        'exam_id',
        'score',
        'answers',
        'passed',
        'status',
        'attempt_number',
        'file_submission',
        'feedback',
        'completed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'answers' => 'array',
        'passed' => 'boolean',
        'completed_at' => 'datetime',
    ];

    /**
     * Relation avec l'inscription
     */
    public function enrollment()
    {
        return $this->belongsTo(Enrollment::class);
    }

    /**
     * Relation avec l'examen
     */
    public function exam()
    {
        return $this->belongsTo(Exam::class);
    }

    /**
     * Relation avec l'utilisateur
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relation avec l'étudiant (alias de user)
     */
    public function student()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Relation avec le certificat
     */
    public function certificate()
    {
        return $this->hasOne(Certificate::class);
    }

    /**
     * Détermine le statut du résultat d'examen
     *
     * @return string
     */
    public function getStatusAttribute($value)
    {
        // Si le statut est déjà défini, le retourner
        if (!empty($value)) {
            return $value;
        }

        // Déterminer le statut en fonction des autres attributs
        if ($this->passed) {
            return 'passed';
        }

        if (!is_null($this->feedback)) {
            return 'graded';
        }

        if (!is_null($this->completed_at)) {
            return 'completed';
        }

        return 'in_progress';
    }
}
