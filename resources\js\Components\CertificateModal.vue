<template>
  <!-- Mobile-Optimized Certificate Modal -->
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[95vh] overflow-hidden flex flex-col">
      <!-- Header -->
      <div class="flex justify-between items-center p-4 sm:p-6 border-b border-gray-200 flex-shrink-0">
        <div class="min-w-0 flex-1">
          <h3 class="text-lg sm:text-xl font-semibold text-gray-900 truncate">{{ title || 'Certificat' }}</h3>
          <p v-if="subtitle" class="text-sm text-gray-600 mt-1 truncate">{{ subtitle }}</p>
        </div>
        <button @click="$emit('close')" class="ml-4 text-gray-400 hover:text-gray-600 transition-colors p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Certificate Content -->
      <div class="flex-1 p-4 sm:p-6 overflow-hidden">
        <div class="h-full border border-gray-300 rounded-lg overflow-hidden bg-gray-50 relative">
          <!-- Loading state -->
          <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-gray-50">
            <div class="text-center">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p class="text-gray-600">Chargement du certificat...</p>
            </div>
          </div>

          <!-- Certificate iframe -->
          <iframe 
            v-if="certificateUrl"
            :src="certificateUrl" 
            class="w-full h-full"
            title="Certificat PDF"
            frameborder="0"
            @load="$emit('load')"
            @error="$emit('error')"
          ></iframe>

          <!-- Error state -->
          <div v-if="error" class="absolute inset-0 flex items-center justify-center bg-gray-50">
            <div class="text-center p-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-red-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <p class="text-red-600 font-medium mb-2">Erreur de chargement</p>
              <p class="text-gray-600 text-sm mb-4">Impossible de charger le certificat.</p>
              <button @click="$emit('retry')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                Réessayer
              </button>
            </div>
          </div>

          <!-- Mobile-specific message -->
          <div v-if="!loading && !error && certificateUrl" class="absolute bottom-4 left-4 right-4 bg-blue-50 border border-blue-200 rounded-lg p-3 md:hidden">
            <div class="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div class="text-xs text-blue-700">
                <p class="font-medium mb-1">Conseil mobile</p>
                <p>Pour une meilleure visualisation, utilisez le bouton "Nouvel onglet" ou téléchargez le certificat.</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer Actions -->
      <div class="p-4 sm:p-6 border-t border-gray-200 flex-shrink-0">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div v-if="certificateNumber" class="text-sm text-gray-600 min-w-0 flex-1">
            <span class="font-medium">Numéro:</span> 
            <span class="break-all">{{ certificateNumber }}</span>
          </div>
          <div class="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <button 
              v-if="newTabUrl"
              @click="openInNewTab" 
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
              <span class="hidden sm:inline">Ouvrir dans un nouvel onglet</span>
              <span class="sm:hidden">Nouvel onglet</span>
            </button>
            <a 
              v-if="downloadUrl"
              :href="downloadUrl" 
              class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center justify-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
              Télécharger
            </a>
            <button @click="$emit('close')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
              Fermer
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'Certificat'
  },
  subtitle: {
    type: String,
    default: null
  },
  certificateUrl: {
    type: String,
    required: true
  },
  newTabUrl: {
    type: String,
    default: null
  },
  downloadUrl: {
    type: String,
    default: null
  },
  certificateNumber: {
    type: String,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['close', 'load', 'error', 'retry']);

// Methods
const openInNewTab = () => {
  if (props.newTabUrl) {
    window.open(props.newTabUrl, '_blank');
  }
};
</script>

<style scoped>
/* Ensure proper iframe scaling on mobile */
iframe {
  min-height: 300px;
}

@media (max-width: 640px) {
  iframe {
    min-height: 250px;
  }
}
</style>
