<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\TrainingSession;
use App\Models\TrainingDomain;
use App\Models\User;

class TrainingSessionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer les domaines de formation
        $webDomain = TrainingDomain::where('name', 'Développement Web')->first();
        $securityDomain = TrainingDomain::where('name', 'Cybersecurité')->first();
        $aiDomain = TrainingDomain::where('name', 'Intelligence Artificielle')->first();
        $cloudDomain = TrainingDomain::where('name', 'Cloud Computing')->first();
        $projectDomain = TrainingDomain::where('name', 'Gestion de Projet IT')->first();

        // Récupérer les formateurs
        $trainer1 = User::where('email', '<EMAIL>')->first();
        $trainer2 = User::where('email', '<EMAIL>')->first();
        $trainer3 = User::where('email', '<EMAIL>')->first();

        // Créer des sessions de formation
        TrainingSession::create([
            'title' => 'Développement Web avec Laravel et Vue.js',
            'description' => 'Apprenez à développer des applications web modernes avec Laravel et Vue.js.',
            'training_domain_id' => $webDomain->id,
            'trainer_id' => $trainer1->id,
            'start_date' => now()->addDays(30),
            'end_date' => now()->addDays(60),
            'max_students' => 15,
            'price' => 1200.00,
            'active' => true,
        ]);

        TrainingSession::create([
            'title' => 'Cybersecurité : Protection des données',
            'description' => 'Apprenez les meilleures pratiques pour protéger vos données et systèmes contre les cyberattaques.',
            'training_domain_id' => $securityDomain->id,
            'trainer_id' => $trainer2->id,
            'start_date' => now()->addDays(15),
            'end_date' => now()->addDays(45),
            'max_students' => 12,
            'price' => 1500.00,
            'active' => true,
        ]);

        TrainingSession::create([
            'title' => 'Introduction à l\'Intelligence Artificielle',
            'description' => 'Découvrez les fondamentaux de l\'intelligence artificielle et du machine learning.',
            'training_domain_id' => $aiDomain->id,
            'trainer_id' => $trainer1->id,
            'start_date' => now()->addDays(45),
            'end_date' => now()->addDays(75),
            'max_students' => 10,
            'price' => 1800.00,
            'active' => true,
        ]);

        TrainingSession::create([
            'title' => 'AWS Cloud Practitioner',
            'description' => 'Préparation à la certification AWS Cloud Practitioner.',
            'training_domain_id' => $cloudDomain->id,
            'trainer_id' => $trainer2->id,
            'start_date' => now()->addDays(60),
            'end_date' => now()->addDays(90),
            'max_students' => 15,
            'price' => 1300.00,
            'active' => true,
        ]);

        TrainingSession::create([
            'title' => 'Gestion de Projet Agile avec Scrum',
            'description' => 'Apprenez à gérer des projets IT avec la méthodologie Agile et le framework Scrum.',
            'training_domain_id' => $projectDomain->id,
            'trainer_id' => $trainer3->id,
            'start_date' => now()->addDays(20),
            'end_date' => now()->addDays(40),
            'max_students' => 20,
            'price' => 1000.00,
            'active' => true,
        ]);
    }
}
