<template>
  <Head :title="announcement.title" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center space-x-4">
        <button
          @click="goBack"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <ArrowLeftIcon class="h-4 w-4 mr-2" />
          Retour
        </button>
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Annonce
        </h2>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-8">
            <!-- Header -->
            <div class="mb-8">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                  <h1 class="text-3xl font-bold text-gray-900">
                    {{ announcement.title }}
                  </h1>
                  <span
                    v-if="announcement.is_important"
                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800"
                  >
                    <ExclamationCircleIcon class="h-4 w-4 mr-1" />
                    Important
                  </span>
                </div>
              </div>

              <!-- Meta Information -->
              <div class="flex flex-wrap items-center gap-6 text-sm text-gray-600 border-b border-gray-200 pb-6">
                <div class="flex items-center">
                  <UserIcon class="h-5 w-5 mr-2 text-gray-400" />
                  <span class="font-medium">Publié par:</span>
                  <span class="ml-1">{{ announcement.author.name }}</span>
                </div>
                
                <div class="flex items-center">
                  <CalendarIcon class="h-5 w-5 mr-2 text-gray-400" />
                  <span class="font-medium">Date de publication:</span>
                  <span class="ml-1">{{ formatDate(announcement.publish_date) }}</span>
                </div>

                <div
                  v-if="announcement.training_session"
                  class="flex items-center"
                >
                  <AcademicCapIcon class="h-5 w-5 mr-2 text-gray-400" />
                  <span class="font-medium">Session:</span>
                  <span class="ml-1">{{ announcement.training_session.title }}</span>
                </div>

                <div class="flex items-center">
                  <EyeIcon class="h-5 w-5 mr-2 text-gray-400" />
                  <span class="font-medium">Visible pour:</span>
                  <span class="ml-1">{{ getVisibilityLabel(announcement.visible_to) }}</span>
                </div>
              </div>
            </div>

            <!-- Content -->
            <div class="prose prose-lg max-w-none">
              <div class="whitespace-pre-wrap text-gray-800 leading-relaxed">
                {{ announcement.content }}
              </div>
            </div>

            <!-- Training Session Details (if applicable) -->
            <div
              v-if="announcement.training_session"
              class="mt-8 p-6 bg-blue-50 rounded-lg border border-blue-200"
            >
              <h3 class="text-lg font-semibold text-blue-900 mb-3 flex items-center">
                <AcademicCapIcon class="h-5 w-5 mr-2" />
                Détails de la session de formation
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="font-medium text-blue-800">Session:</span>
                  <span class="ml-2 text-blue-700">{{ announcement.training_session.title }}</span>
                </div>
                <div v-if="announcement.training_session.training_domain">
                  <span class="font-medium text-blue-800">Domaine:</span>
                  <span class="ml-2 text-blue-700">{{ announcement.training_session.training_domain.name }}</span>
                </div>
                <div v-if="announcement.training_session.start_date">
                  <span class="font-medium text-blue-800">Date de début:</span>
                  <span class="ml-2 text-blue-700">{{ formatDate(announcement.training_session.start_date) }}</span>
                </div>
                <div v-if="announcement.training_session.end_date">
                  <span class="font-medium text-blue-800">Date de fin:</span>
                  <span class="ml-2 text-blue-700">{{ formatDate(announcement.training_session.end_date) }}</span>
                </div>
              </div>
            </div>

            <!-- Expiry Notice -->
            <div
              v-if="announcement.expiry_date"
              class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md"
            >
              <div class="flex items-center">
                <ClockIcon class="h-5 w-5 text-yellow-600 mr-2" />
                <span class="text-sm text-yellow-800">
                  <span class="font-medium">Cette annonce expire le:</span>
                  {{ formatDate(announcement.expiry_date) }}
                </span>
              </div>
            </div>

            <!-- Actions -->
            <div class="mt-8 pt-6 border-t border-gray-200">
              <div class="flex justify-between items-center">
                <button
                  @click="goBack"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <ArrowLeftIcon class="h-4 w-4 mr-2" />
                  Retour aux annonces
                </button>

                <div class="text-sm text-gray-500">
                  Annonce #{{ announcement.id }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head } from '@inertiajs/vue3'
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue'
import {
  ArrowLeftIcon,
  ExclamationCircleIcon,
  UserIcon,
  CalendarIcon,
  AcademicCapIcon,
  EyeIcon,
  ClockIcon,
} from '@heroicons/vue/24/outline'

// Props
const props = defineProps({
  announcement: Object,
})

// Methods
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const getVisibilityLabel = (visibility) => {
  const labels = {
    'all': 'Tous les utilisateurs',
    'admin': 'Administrateurs',
    'trainer': 'Formateurs',
    'student': 'Apprenants',
  }
  return labels[visibility] || visibility
}

const goBack = () => {
  window.history.back()
}
</script>

<style scoped>
.prose {
  max-width: none;
}

.prose p {
  margin-bottom: 1rem;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.prose ul, .prose ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.25rem;
}
</style>
