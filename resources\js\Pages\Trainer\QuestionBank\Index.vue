<template>
  <Head title="Banque de questions" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Banque de questions
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- En-tête avec bouton d'ajout -->
            <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-6">
              <h3 class="text-lg font-semibold mb-4 md:mb-0">Gérer votre banque de questions</h3>
              <div>
                <Link :href="route('trainer.question-bank.create')" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                  Ajouter une question
                </Link>
              </div>
            </div>

            <!-- Filtres -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <h4 class="font-medium mb-3">Filtres</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Recherche -->
                <div>
                  <InputLabel for="search" value="Recherche" />
                  <TextInput
                    id="search"
                    v-model="searchQuery"
                    type="text"
                    class="mt-1 block w-full"
                    placeholder="Rechercher..."
                  />
                </div>

                <!-- Type de question -->
                <div>
                  <InputLabel for="question_type" value="Type de question" />
                  <select
                    id="question_type"
                    v-model="questionTypeFilter"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  >
                    <option value="">Tous les types</option>
                    <option v-for="type in questionTypes" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </option>
                  </select>
                </div>

                <!-- Session de formation -->
                <div>
                  <InputLabel for="training_session_id" value="Session de formation" />
                  <select
                    id="training_session_id"
                    v-model="sessionFilter"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  >
                    <option value="">Toutes les sessions</option>
                    <option v-for="session in trainingSessions" :key="session.id" :value="session.id">
                      {{ session.title }}
                    </option>
                  </select>
                </div>

                <!-- Examen -->
                <div>
                  <InputLabel for="exam_id" value="Examen" />
                  <select
                    id="exam_id"
                    v-model="examFilter"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  >
                    <option value="">Tous les examens</option>
                    <option v-for="exam in exams" :key="exam.id" :value="exam.id">
                      {{ exam.title }}
                    </option>
                  </select>
                </div>
              </div>

              <!-- Boutons de filtrage -->
              <div class="flex justify-end mt-4">
                <SecondaryButton @click="resetFilters" class="mr-2">
                  Réinitialiser
                </SecondaryButton>
                <PrimaryButton @click="applyFilters">
                  Appliquer les filtres
                </PrimaryButton>
              </div>
            </div>

            <!-- Tableau des questions -->
            <div class="overflow-x-auto">
              <table class="min-w-full bg-white border border-gray-200">
                <thead>
                  <tr>
                    <th class="py-2 px-4 border-b text-left">Question</th>
                    <th class="py-2 px-4 border-b text-left">Type</th>
                    <th class="py-2 px-4 border-b text-left">Examen</th>
                    <th class="py-2 px-4 border-b text-left">Session</th>
                    <th class="py-2 px-4 border-b text-left">Points</th>
                    <th class="py-2 px-4 border-b text-center">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="question in questions.data" :key="question.id" class="hover:bg-gray-50">
                    <td class="py-2 px-4 border-b">
                      <div class="truncate max-w-xs">
                        {{ question.question_text }}
                      </div>
                    </td>
                    <td class="py-2 px-4 border-b">
                      <span v-if="question.question_type === 'multiple_choice'" class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">QCM</span>
                      <span v-else-if="question.question_type === 'text'" class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Texte</span>
                      <span v-else-if="question.question_type === 'file'" class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">Fichier</span>
                    </td>
                    <td class="py-2 px-4 border-b">{{ question.exam.title }}</td>
                    <td class="py-2 px-4 border-b">{{ question.exam.training_session.title }}</td>
                    <td class="py-2 px-4 border-b">{{ question.points }}</td>
                    <td class="py-2 px-4 border-b text-center">
                      <div class="flex justify-center space-x-2">
                        <Link :href="route('trainer.question-bank.edit', question.id)" class="text-indigo-600 hover:text-indigo-900">
                          <PencilIcon class="h-5 w-5" />
                        </Link>
                        <button @click="openDuplicateModal(question)" class="text-green-600 hover:text-green-900">
                          <DocumentDuplicateIcon class="h-5 w-5" />
                        </button>
                        <button @click="confirmDelete(question)" class="text-red-600 hover:text-red-900">
                          <TrashIcon class="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr v-if="questions.data.length === 0">
                    <td colspan="6" class="py-4 text-center text-gray-500">
                      Aucune question trouvée.
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            <div class="mt-6">
              <Pagination :links="questions.links" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de suppression -->
    <Modal :show="showDeleteModal" @close="closeDeleteModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">
          Confirmer la suppression
        </h2>
        <p class="mt-1 text-sm text-gray-600">
          Êtes-vous sûr de vouloir supprimer cette question ? Cette action est irréversible.
        </p>
        <div class="mt-6 flex justify-end">
          <SecondaryButton @click="closeDeleteModal" class="mr-2">
            Annuler
          </SecondaryButton>
          <DangerButton @click="deleteQuestion" :class="{ 'opacity-25': deleteForm.processing }" :disabled="deleteForm.processing">
            Supprimer
          </DangerButton>
        </div>
      </div>
    </Modal>

    <!-- Modal de duplication -->
    <Modal :show="showDuplicateModal" @close="closeDuplicateModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">
          Dupliquer la question
        </h2>
        <p class="mt-1 text-sm text-gray-600">
          Sélectionnez l'examen vers lequel vous souhaitez dupliquer cette question.
        </p>
        <div class="mt-4">
          <InputLabel for="target_exam_id" value="Examen cible" />
          <select
            id="target_exam_id"
            v-model="duplicateForm.target_exam_id"
            class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
            required
          >
            <option value="">Sélectionnez un examen</option>
            <option v-for="exam in exams" :key="exam.id" :value="exam.id" :disabled="exam.id === questionToDuplicate?.exam_id">
              {{ exam.title }}
            </option>
          </select>
          <InputError class="mt-2" :message="duplicateForm.errors.target_exam_id" />
        </div>
        <div class="mt-6 flex justify-end">
          <SecondaryButton @click="closeDuplicateModal" class="mr-2">
            Annuler
          </SecondaryButton>
          <PrimaryButton @click="duplicateQuestion" :class="{ 'opacity-25': duplicateForm.processing }" :disabled="duplicateForm.processing">
            Dupliquer
          </PrimaryButton>
        </div>
      </div>
    </Modal>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import Pagination from '@/Components/Pagination.vue';
import InputError from '@/Components/InputError.vue';
import { PencilIcon, TrashIcon, DocumentDuplicateIcon } from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  questions: Object,
  trainingSessions: Array,
  exams: Array,
  filters: Object,
  questionTypes: Array,
});

// État pour les filtres
const searchQuery = ref(props.filters?.search || '');
const questionTypeFilter = ref(props.filters?.question_type || '');
const sessionFilter = ref(props.filters?.training_session_id || '');
const examFilter = ref(props.filters?.exam_id || '');

// État pour la modal de suppression
const showDeleteModal = ref(false);
const questionToDelete = ref(null);

// État pour la modal de duplication
const showDuplicateModal = ref(false);
const questionToDuplicate = ref(null);

// Formulaire pour la suppression
const deleteForm = useForm({});

// Formulaire pour la duplication
const duplicateForm = useForm({
  target_exam_id: '',
});

// Méthodes pour les filtres
const applyFilters = () => {
  router.get(route('trainer.question-bank.index'), {
    search: searchQuery.value,
    question_type: questionTypeFilter.value,
    training_session_id: sessionFilter.value,
    exam_id: examFilter.value,
  }, {
    preserveState: true,
    replace: true,
  });
};

const resetFilters = () => {
  searchQuery.value = '';
  questionTypeFilter.value = '';
  sessionFilter.value = '';
  examFilter.value = '';
  applyFilters();
};

// Méthodes pour la suppression
const confirmDelete = (question) => {
  questionToDelete.value = question;
  showDeleteModal.value = true;
};

const closeDeleteModal = () => {
  showDeleteModal.value = false;
  questionToDelete.value = null;
};

const deleteQuestion = () => {
  if (questionToDelete.value) {
    deleteForm.delete(route('trainer.question-bank.destroy', questionToDelete.value.id), {
      onSuccess: () => {
        closeDeleteModal();
      },
    });
  }
};

// Méthodes pour la duplication
const openDuplicateModal = (question) => {
  questionToDuplicate.value = question;
  duplicateForm.target_exam_id = '';
  showDuplicateModal.value = true;
};

const closeDuplicateModal = () => {
  showDuplicateModal.value = false;
  questionToDuplicate.value = null;
  duplicateForm.reset();
  duplicateForm.clearErrors();
};

const duplicateQuestion = () => {
  if (questionToDuplicate.value) {
    duplicateForm.post(route('trainer.question-bank.duplicate', questionToDuplicate.value.id), {
      onSuccess: () => {
        closeDuplicateModal();
      },
    });
  }
};
</script>
