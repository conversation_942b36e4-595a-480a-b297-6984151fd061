<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur d'Images Placeholder</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        .image-placeholder {
            width: 100%;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #2A69E1, #1E4FBF);
            color: white;
            font-size: 14px;
            text-align: center;
            flex-direction: column;
        }
        .image-info {
            padding: 15px;
        }
        .image-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .image-desc {
            color: #666;
            font-size: 14px;
        }
        .download-btn {
            background: #2A69E1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .download-btn:hover {
            background: #1E4FBF;
        }
        h1 {
            color: #2A69E1;
            text-align: center;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            color: #2A69E1;
            border-bottom: 2px solid #2A69E1;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 Images Placeholder PCMET - Formation Premiers Secours</h1>
        
        <div class="section">
            <h2 class="section-title">Section Hero - Formation Premiers Secours</h2>
            <div class="image-grid">
                <div class="image-card">
                    <div class="image-placeholder" id="hero-main">
                        <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                        </svg>
                        <div style="margin-top: 10px;">Formation Premiers Secours</div>
                        <div style="font-size: 12px; opacity: 0.8;">800x600px</div>
                    </div>
                    <div class="image-info">
                        <div class="image-title">Formation Premiers Secours Principale</div>
                        <div class="image-desc">Image principale pour la section hero montrant une formation en cours</div>
                        <button class="download-btn" onclick="downloadSVG('hero-main', 'formation-premiers-secours.svg', 800, 600)">Télécharger SVG</button>
                    </div>
                </div>
                
                <div class="image-card">
                    <div class="image-placeholder" id="hero-instructor" style="background: linear-gradient(135deg, #10B981, #059669);">
                        <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                        <div style="margin-top: 10px;">Instructeur Certifié</div>
                        <div style="font-size: 12px; opacity: 0.8;">600x400px</div>
                    </div>
                    <div class="image-info">
                        <div class="image-title">Instructeur en Action</div>
                        <div class="image-desc">Photo d'un instructeur PCMET enseignant les premiers secours</div>
                        <button class="download-btn" onclick="downloadSVG('hero-instructor', 'instructeur-formation.svg', 600, 400)">Télécharger SVG</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">Section VR Training</h2>
            <div class="image-grid">
                <div class="image-card">
                    <div class="image-placeholder" id="vr-demo" style="background: linear-gradient(135deg, #8B5CF6, #7C3AED);">
                        <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                        </svg>
                        <div style="margin-top: 10px;">Formation VR</div>
                        <div style="font-size: 12px; opacity: 0.8;">1920x1080px</div>
                    </div>
                    <div class="image-info">
                        <div class="image-title">Démonstration VR</div>
                        <div class="image-desc">Étudiant utilisant un casque VR pour la formation premiers secours</div>
                        <button class="download-btn" onclick="downloadSVG('vr-demo', 'demo-vr-training.svg', 1920, 1080)">Télécharger SVG</button>
                    </div>
                </div>
                
                <div class="image-card">
                    <div class="image-placeholder" id="vr-equipment" style="background: linear-gradient(135deg, #F59E0B, #D97706);">
                        <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                        <div style="margin-top: 10px;">Équipement VR</div>
                        <div style="font-size: 12px; opacity: 0.8;">800x600px</div>
                    </div>
                    <div class="image-info">
                        <div class="image-title">Équipement VR Professionnel</div>
                        <div class="image-desc">Casques VR et équipement technologique utilisés pour la formation</div>
                        <button class="download-btn" onclick="downloadSVG('vr-equipment', 'equipement-vr.svg', 800, 600)">Télécharger SVG</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">Section À Propos - Installations</h2>
            <div class="image-grid">
                <div class="image-card">
                    <div class="image-placeholder" id="facility-1" style="background: linear-gradient(135deg, #EF4444, #DC2626);">
                        <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                            <path d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"/>
                        </svg>
                        <div style="margin-top: 10px;">Salle de Formation</div>
                        <div style="font-size: 12px; opacity: 0.8;">1200x800px</div>
                    </div>
                    <div class="image-info">
                        <div class="image-title">Salle de Formation Principale</div>
                        <div class="image-desc">Vue d'ensemble de notre salle de formation équipée</div>
                        <button class="download-btn" onclick="downloadSVG('facility-1', 'facility-1.svg', 1200, 800)">Télécharger SVG</button>
                    </div>
                </div>
                
                <div class="image-card">
                    <div class="image-placeholder" id="facility-2" style="background: linear-gradient(135deg, #06B6D4, #0891B2);">
                        <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                        <div style="margin-top: 10px;">Centre PCMET</div>
                        <div style="font-size: 12px; opacity: 0.8;">1200x800px</div>
                    </div>
                    <div class="image-info">
                        <div class="image-title">Façade du Centre PCMET</div>
                        <div class="image-desc">Extérieur du centre de formation PCMET</div>
                        <button class="download-btn" onclick="downloadSVG('facility-2', 'facility-2.svg', 1200, 800)">Télécharger SVG</button>
                    </div>
                </div>
                
                <div class="image-card">
                    <div class="image-placeholder" id="facility-3" style="background: linear-gradient(135deg, #84CC16, #65A30D);">
                        <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                        <div style="margin-top: 10px;">Groupe d'Étudiants</div>
                        <div style="font-size: 12px; opacity: 0.8;">1200x800px</div>
                    </div>
                    <div class="image-info">
                        <div class="image-title">Étudiants en Formation</div>
                        <div class="image-desc">Groupe d'étudiants participant à une session de formation</div>
                        <button class="download-btn" onclick="downloadSVG('facility-3', 'facility-3.svg', 1200, 800)">Télécharger SVG</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function downloadSVG(elementId, filename, width, height) {
            const element = document.getElementById(elementId);
            const style = window.getComputedStyle(element);
            const background = style.background;
            
            // Extract SVG content
            const svgElement = element.querySelector('svg');
            const svgContent = svgElement.outerHTML;
            
            // Create full SVG
            const fullSVG = `
                <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#2A69E1;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#1E4FBF;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#bg-gradient)"/>
                    <g transform="translate(${width/2 - 40}, ${height/2 - 40})">
                        ${svgContent.replace('<svg', '<svg fill="white"').replace('</svg>', '').replace(/^<svg[^>]*>/, '')}
                    </g>
                    <text x="${width/2}" y="${height/2 + 80}" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">
                        ${element.querySelector('div').textContent}
                    </text>
                    <text x="${width/2}" y="${height - 30}" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" opacity="0.8">
                        PCMET - Centre de Formation Premiers Secours
                    </text>
                </svg>
            `;
            
            // Download
            const blob = new Blob([fullSVG], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
