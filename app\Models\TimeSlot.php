<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TimeSlot extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'training_session_id',
        'name',
        'start_time',
        'end_time',
        'max_participants',
        'active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'active' => 'boolean',
    ];

    /**
     * Relation avec la session de formation
     */
    public function trainingSession()
    {
        return $this->belongsTo(TrainingSession::class);
    }

    /**
     * Relation avec les inscriptions
     */
    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    /**
     * Obtenir le nombre d'inscriptions pour ce créneau
     */
    public function getEnrollmentsCountAttribute()
    {
        return $this->enrollments()->count();
    }

    /**
     * Vérifier si le créneau est complet
     */
    public function isFull()
    {
        if (!$this->max_participants) {
            return false;
        }

        return $this->enrollments_count >= $this->max_participants;
    }

    /**
     * Obtenir les places disponibles
     */
    public function getAvailableSpotsAttribute()
    {
        if (!$this->max_participants) {
            return null; // Illimité
        }

        return max(0, $this->max_participants - $this->enrollments_count);
    }

    /**
     * Formater l'heure de début
     */
    public function getFormattedStartTimeAttribute()
    {
        return $this->start_time ? $this->start_time->format('H:i') : '';
    }

    /**
     * Formater l'heure de fin
     */
    public function getFormattedEndTimeAttribute()
    {
        return $this->end_time ? $this->end_time->format('H:i') : '';
    }

    /**
     * Obtenir la plage horaire formatée
     */
    public function getTimeRangeAttribute()
    {
        return $this->formatted_start_time . ' - ' . $this->formatted_end_time;
    }

    /**
     * Obtenir le nom complet du créneau
     */
    public function getFullNameAttribute()
    {
        $name = $this->name ?: 'Créneau';
        return $name . ' (' . $this->time_range . ')';
    }

    /**
     * Scope pour les créneaux actifs
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope pour les créneaux d'une session
     */
    public function scopeForSession($query, $sessionId)
    {
        return $query->where('training_session_id', $sessionId);
    }

    /**
     * Scope pour les créneaux disponibles (non complets)
     */
    public function scopeAvailable($query)
    {
        return $query->where('active', true)
            ->where(function ($q) {
                $q->whereNull('max_participants')
                  ->orWhereRaw('(SELECT COUNT(*) FROM enrollments WHERE time_slot_id = time_slots.id) < max_participants');
            });
    }
}
