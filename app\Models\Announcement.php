<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Announcement extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'content',
        'author_id',
        'training_session_id',
        'type',
        'visible_to',
        'is_important',
        'is_published',
        'publish_date',
        'expiry_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_important' => 'boolean',
        'publish_date' => 'datetime',
        'expiry_date' => 'datetime',
    ];

    /**
     * Relation avec l'utilisateur qui a créé l'annonce.
     */
    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * Relation avec la session de formation
     */
    public function trainingSession()
    {
        return $this->belongsTo(TrainingSession::class);
    }

    /**
     * Scope pour les annonces publiées.
     */
    public function scopePublished($query)
    {
        return $query->where('publish_date', '<=', now())
            ->where(function ($query) {
                $query->whereNull('expiry_date')
                    ->orWhere('expiry_date', '>=', now());
            });
    }

    /**
     * Scope pour les annonces visibles à un certain rôle.
     */
    public function scopeVisibleTo($query, $role)
    {
        return $query->where(function ($query) use ($role) {
            $query->where('visible_to', $role)
                ->orWhere('visible_to', 'all');
        });
    }

    /**
     * Scope pour les annonces importantes.
     */
    public function scopeImportant($query)
    {
        return $query->where('is_important', true);
    }

    /**
     * Scope pour les annonces spécifiques à une session.
     */
    public function scopeForSession($query, $sessionId)
    {
        return $query->where('training_session_id', $sessionId);
    }

    /**
     * Scope pour les annonces générales (sans session spécifique).
     */
    public function scopeGeneral($query)
    {
        return $query->whereNull('training_session_id');
    }
}
