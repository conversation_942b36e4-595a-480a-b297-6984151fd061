<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ExamQuestion;
use Illuminate\Http\Request;

class FixQuestionsController extends Controller
{
    /**
     * Répare les données des questions d'examen
     */
    public function fixQuestions()
    {
        // Récupérer toutes les questions à choix multiple
        $questions = ExamQuestion::where('question_type', 'multiple_choice')->get();
        $fixed = 0;

        foreach ($questions as $question) {
            $options = $question->options;
            $correctOptions = $question->correct_options;
            $needsUpdate = false;

            // Vérifier si les options sont vides ou mal formatées
            if (empty($options) || !is_array($options) || count($options) === 0) {
                // Créer des options par défaut
                $options = [
                    'A' => 'Option A',
                    'B' => 'Option B',
                    'C' => 'Option C',
                    'D' => 'Option D'
                ];
                $needsUpdate = true;
            }

            // Vérifier si les options correctes sont vides ou mal formatées
            if (empty($correctOptions) || !is_array($correctOptions) || count($correctOptions) === 0) {
                // Définir l'option A comme correcte par défaut
                $correctOptions = ['A'];
                $needsUpdate = true;
            }

            // Mettre à jour la question si nécessaire
            if ($needsUpdate) {
                $question->options = $options;
                $question->correct_options = $correctOptions;
                $question->save();
                $fixed++;
            }
        }

        return redirect()->back()->with('success', "Réparation terminée. $fixed questions ont été mises à jour.");
    }
}
