<template>
  <Head :title="`Matériels pédagogiques - ${module ? module.title : course.title}`" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Matériels pédagogiques
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Bouton de retour -->
            <div class="mb-6">
              <Link v-if="module" :href="route('admin.modules.index', { course_id: course.id })" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour aux modules
              </Link>
              <Link v-else :href="route('admin.courses.index')" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 focus:bg-gray-300 active:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Retour à la liste des cours
              </Link>
            </div>

            <!-- Informations sur le cours et le module -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <h3 class="text-lg font-semibold mb-2">Cours: {{ course.title }}</h3>
              <p><span class="font-medium">Session de formation:</span> {{ course.training_session.title }}</p>
              <p v-if="course.description"><span class="font-medium">Description du cours:</span> {{ course.description }}</p>

              <div v-if="module" class="mt-4 pt-4 border-t border-gray-200">
                <h4 class="text-md font-semibold mb-2">Module: {{ module.title }}</h4>
                <p v-if="module.description"><span class="font-medium">Description du module:</span> {{ module.description }}</p>
                <p><span class="font-medium">Ordre:</span> {{ module.order }}</p>
                <p>
                  <span class="font-medium">Statut:</span>
                  <span :class="module.is_published ? 'text-green-600' : 'text-red-600'">
                    {{ module.is_published ? 'Publié' : 'Non publié' }}
                  </span>
                </p>
              </div>
            </div>

            <!-- En-tête avec bouton d'ajout -->
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">
                {{ module ? 'Matériels du module' : 'Matériels hors modules' }}
              </h3>
              <Link
                :href="route('admin.course-materials.create', module ? { module_id: module.id } : { course_id: course.id })"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Ajouter un matériel
              </Link>
            </div>

            <!-- Messages de succès ou d'erreur -->
            <div v-if="$page.props.flash.success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              {{ $page.props.flash.success }}
            </div>
            <div v-if="$page.props.flash.error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {{ $page.props.flash.error }}
            </div>

            <!-- Tableau des matériels -->
            <div class="overflow-x-auto">
              <table class="min-w-full bg-white border border-gray-200">
                <thead>
                  <tr>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                      Titre
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                      Fichier
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="material in materials.data" :key="material.id">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">{{ material.title }}</div>
                      <div class="text-sm text-gray-500">Ordre: {{ material.order }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        :class="[
                          'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                          getTypeClass(material.type)
                        ]"
                      >
                        {{ formatMaterialType(material.type) }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div v-if="material.file_path" class="text-sm text-gray-900">
                        <div class="flex items-center">
                          <svg v-if="material.type === 'pdf'" class="h-5 w-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                          </svg>
                          <svg v-else-if="material.type === 'video'" class="h-5 w-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zm12.553 1.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                          </svg>
                          <svg v-else-if="material.type === 'audio'" class="h-5 w-5 text-purple-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828a1 1 0 010-1.415z" clip-rule="evenodd" />
                          </svg>
                          <svg v-else-if="material.type === 'image'" class="h-5 w-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                          </svg>
                          <svg v-else-if="material.type === 'archive'" class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V8a2 2 0 00-2-2h-5L9 4H4zm7 5a1 1 0 10-2 0v1H8a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd" />
                          </svg>
                          <span>{{ getFileName(material.file_path) }}</span>
                        </div>
                        <div class="text-xs text-gray-500 mt-1" v-if="material.file_size">
                          {{ formatFileSize(material.file_size) }}
                        </div>
                      </div>
                      <div v-else-if="material.type === 'text'" class="text-sm text-gray-500">
                        Contenu texte
                      </div>
                      <div v-else class="text-sm text-gray-500">
                        Aucun fichier
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex flex-col space-y-1">
                        <span
                          :class="[
                            'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                            material.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          ]"
                        >
                          {{ material.active ? 'Actif' : 'Inactif' }}
                        </span>
                        <span
                          v-if="material.file_path"
                          :class="[
                            'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                            material.allow_download ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                          ]"
                        >
                          {{ material.allow_download ? 'Téléchargeable' : 'Non téléchargeable' }}
                        </span>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div class="flex flex-col space-y-2">
                        <Link :href="route('admin.course-materials.show', material.id)" class="text-blue-600 hover:text-blue-900">
                          Voir
                        </Link>
                        <Link :href="route('admin.course-materials.edit', material.id)" class="text-indigo-600 hover:text-indigo-900">
                          Modifier
                        </Link>
                        <a v-if="material.file_path && material.allow_download" :href="`/admin/course-materials/${material.id}/download`" class="text-green-600 hover:text-green-900">
                          Télécharger
                        </a>
                        <button
                          @click="confirmDelete(material)"
                          class="text-red-600 hover:text-red-900 text-left"
                        >
                          Supprimer
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr v-if="materials.data.length === 0">
                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                      Aucun matériel pédagogique trouvé
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            <Pagination :links="materials.links" class="mt-6" />

            <!-- Modal de confirmation de suppression -->
            <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
              <div class="bg-white rounded-lg overflow-hidden shadow-xl max-w-md w-full">
                <div class="p-6">
                  <h3 class="text-lg font-medium text-gray-900 mb-4">Confirmer la suppression</h3>
                  <p class="text-gray-600 mb-6">
                    Êtes-vous sûr de vouloir supprimer le matériel "{{ materialToDelete?.title }}" ?
                    <span v-if="materialToDelete?.file_path" class="text-red-600 font-semibold block mt-2">
                      Le fichier associé sera également supprimé.
                    </span>
                  </p>
                  <div class="flex justify-end space-x-3">
                    <button
                      @click="showDeleteModal = false"
                      class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
                    >
                      Annuler
                    </button>
                    <button
                      @click="deleteMaterial"
                      class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                    >
                      Supprimer
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Pagination from '@/Components/Pagination.vue';

// Props
const props = defineProps({
  course: Object,
  module: Object,
  materials: Object,
});

// État
const showDeleteModal = ref(false);
const materialToDelete = ref(null);

// Méthodes
const formatMaterialType = (type) => {
  const types = {
    'text': 'Texte',
    'pdf': 'Document PDF',
    'video': 'Vidéo',
    'audio': 'Audio',
    'image': 'Image',
    'archive': 'Archive'
  };
  return types[type] || type;
};

const getTypeClass = (type) => {
  const classes = {
    'text': 'bg-gray-100 text-gray-800',
    'pdf': 'bg-red-100 text-red-800',
    'video': 'bg-blue-100 text-blue-800',
    'audio': 'bg-purple-100 text-purple-800',
    'image': 'bg-yellow-100 text-yellow-800',
    'archive': 'bg-green-100 text-green-800'
  };
  return classes[type] || 'bg-gray-100 text-gray-800';
};

const getFileName = (path) => {
  if (!path) return '';
  return path.split('/').pop();
};

const formatFileSize = (sizeInKB) => {
  if (sizeInKB < 1024) {
    return `${Math.round(sizeInKB)} Ko`;
  } else {
    return `${(sizeInKB / 1024).toFixed(2)} Mo`;
  }
};

const confirmDelete = (material) => {
  materialToDelete.value = material;
  showDeleteModal.value = true;
};

const deleteMaterial = () => {
  router.delete(route('admin.course-materials.destroy', materialToDelete.value.id), {
    onSuccess: () => {
      showDeleteModal.value = false;
      materialToDelete.value = null;
    },
  });
};
</script>
