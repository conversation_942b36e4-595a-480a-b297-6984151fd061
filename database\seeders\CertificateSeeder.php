<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Certificate;
use App\Models\Enrollment;
use App\Models\ExamResult;

class CertificateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer les inscriptions complétées
        $enrollments = Enrollment::where('status', 'completed')->get();

        foreach ($enrollments as $enrollment) {
            // Vérifier si tous les examens de certification ont été réussis
            $examResults = ExamResult::where('enrollment_id', $enrollment->id)
                ->with('exam')
                ->get();

            // Filtrer uniquement les examens de certification
            $certificationExamResults = $examResults->filter(function ($result) {
                return in_array($result->exam->exam_type, ['certification', 'certification_rattrapage']);
            });

            // Vérifier si tous les examens de certification ont été réussis
            $allCertificationExamsPassed = $certificationExamResults->count() > 0 &&
                $certificationExamResults->where('passed', true)->count() === $certificationExamResults->count();

            if ($allCertificationExamsPassed) {
                // Vérifier qu'un certificat n'existe pas déjà
                $existingCertificate = Certificate::where('enrollment_id', $enrollment->id)->first();

                if (!$existingCertificate) {
                    // Générer un numéro de certificat unique
                    $certificateNumber = 'CERT-' . $enrollment->id . '-' . time();

                    // Créer le certificat
                    $issuedAt = now()->subDays(rand(1, 5));
                    Certificate::create([
                        'enrollment_id' => $enrollment->id,
                        'user_id' => $enrollment->user_id,
                        'certificate_number' => $certificateNumber,
                        'issued_at' => $issuedAt,
                        'issue_date' => $issuedAt,
                        'status' => 'issued',
                    ]);
                }
            }
        }
    }
}
