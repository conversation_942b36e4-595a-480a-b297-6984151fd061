<?php

require_once 'vendor/autoload.php';

use BaconQrCode\Renderer\ImageRenderer;
use BaconQrCode\Renderer\Image\SvgImageBackEnd;
use BaconQrCode\Renderer\RendererStyle\RendererStyle;
use BaconQrCode\Writer;

try {
    echo "Test de génération de QR code avec BaconQrCode directement...\n";

    // Créer le renderer SVG
    $renderer = new ImageRenderer(
        new RendererStyle(200),
        new SvgImageBackEnd()
    );

    // Créer le writer
    $writer = new Writer($renderer);

    // Test 1: Génération basique
    $qrCodeData = $writer->writeString('https://example.com');
    echo "QR code généré avec succès (test basique)\n";
    echo "Taille des données: " . strlen($qrCodeData) . " bytes\n";

    // Test 2: Avec une URL de vérification
    $verificationUrl = 'http://localhost:8000/certificates/verify/TEST123';
    $qrCodeData2 = $writer->writeString($verificationUrl);
    echo "QR code généré avec succès (URL de vérification)\n";
    echo "Taille des données: " . strlen($qrCodeData2) . " bytes\n";

    // Test 3: Sauvegarde dans un fichier
    file_put_contents('test_qr_simple.svg', $qrCodeData2);
    echo "QR code sauvegardé dans test_qr_simple.svg\n";

    echo "Tous les tests ont réussi !\n";

} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
