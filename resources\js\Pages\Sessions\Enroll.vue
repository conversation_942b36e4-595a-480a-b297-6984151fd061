<template>
    <div class="min-h-screen bg-gray-100">
        <Head :title="'Inscription à ' + session.title + ' - Centre de Formation'" />

        <!-- Navigation -->
        <div class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <Link href="/" class="text-xl font-bold text-indigo-600">Centre de Formation</Link>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div v-if="$page.props.auth.user" class="ml-3 relative">
                            <Link :href="route('student.dashboard')" class="text-gray-700 hover:text-indigo-600">
                                Tableau de bord
                            </Link>
                        </div>
                        <div v-else class="ml-3 relative">
                            <Link :href="route('login')" class="text-gray-700 hover:text-indigo-600 mr-4">
                                Connexion
                            </Link>
                            <Link :href="route('register')" class="text-gray-700 hover:text-indigo-600">
                                Inscription
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contenu principal -->
        <main class="py-10">
            <div class="max-w-3xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <h1 class="text-2xl font-semibold text-gray-900 mb-6">
                            Inscription à la session : {{ session.title }}
                        </h1>

                        <!-- Détails de la session -->
                        <div class="mb-8 p-4 bg-gray-50 rounded-lg">
                            <h2 class="text-lg font-medium text-gray-900 mb-2">Détails de la session</h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p class="text-sm text-gray-600">Domaine :</p>
                                    <p class="font-medium">{{ session.training_domain?.name }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Formateur :</p>
                                    <p class="font-medium">{{ session.trainer?.name }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Dates :</p>
                                    <p class="font-medium">Du {{ formatDate(session.start_date) }} au {{ formatDate(session.end_date) }}</p>
                                </div>
                                <div v-if="session.price">
                                    <p class="text-sm text-gray-600">Prix :</p>
                                    <p class="font-medium">{{ session.price }} DT</p>
                                </div>
                            </div>
                        </div>

                        <!-- Formulaire pour utilisateur connecté -->
                        <div v-if="isLoggedIn" class="mb-8">
                            <h2 class="text-lg font-medium text-gray-900 mb-4">Confirmer votre inscription</h2>
                            <p class="mb-4">Vous êtes connecté en tant que <strong>{{ $page.props.auth.user.name }}</strong>.</p>
                            
                            <form @submit.prevent="submitExistingUserForm">
                                <div class="mb-4">
                                    <label for="notes" class="block text-sm font-medium text-gray-700">Notes ou commentaires (facultatif)</label>
                                    <textarea id="notes" v-model="existingUserForm.notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                                </div>
                                
                                <div class="flex items-center justify-end mt-6">
                                    <Link :href="route('sessions.show', session.id)" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-3">
                                        Annuler
                                    </Link>
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Confirmer l'inscription
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Formulaire pour nouvel utilisateur -->
                        <div v-else>
                            <!-- Section de connexion pour utilisateurs existants -->
                            <div class="mb-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="text-lg font-medium text-blue-900 mb-2">Vous avez déjà un compte ?</h3>
                                        <p class="text-blue-700">Connectez-vous pour simplifier votre inscription avec vos informations existantes.</p>
                                    </div>
                                    <div class="ml-6">
                                        <Link
                                            :href="route('login') + '?intended=' + encodeURIComponent(route('session.enrollment.create', session.id))"
                                            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
                                            aria-label="Se connecter pour simplifier votre inscription à cette session"
                                            role="button"
                                            tabindex="0"
                                        >
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                                            </svg>
                                            Se connecter
                                        </Link>
                                    </div>
                                </div>
                            </div>

                            <!-- Séparateur -->
                            <div class="relative mb-8">
                                <div class="absolute inset-0 flex items-center">
                                    <div class="w-full border-t border-gray-300"></div>
                                </div>
                                <div class="relative flex justify-center text-sm">
                                    <span class="px-2 bg-white text-gray-500">ou</span>
                                </div>
                            </div>

                            <h2 class="text-lg font-medium text-gray-900 mb-4">Créer un compte et s'inscrire</h2>
                            <p class="mb-4">Veuillez remplir le formulaire ci-dessous pour créer votre compte et vous inscrire à cette session.</p>
                            
                            <form @submit.prevent="submitNewUserForm">
                                <!-- Informations personnelles -->
                                <div class="mb-6">
                                    <h3 class="text-md font-medium text-gray-700 mb-3">Informations personnelles</h3>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="name" class="block text-sm font-medium text-gray-700">Nom complet *</label>
                                            <input type="text" id="name" v-model="newUserForm.name" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.name" class="text-red-500 text-sm mt-1">{{ errors.name }}</div>
                                        </div>
                                        
                                        <div>
                                            <label for="email" class="block text-sm font-medium text-gray-700">Email *</label>
                                            <input type="email" id="email" v-model="newUserForm.email" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.email" class="text-red-500 text-sm mt-1">{{ errors.email }}</div>
                                        </div>
                                        
                                        <div>
                                            <label for="phone" class="block text-sm font-medium text-gray-700">Téléphone *</label>
                                            <input type="tel" id="phone" v-model="newUserForm.phone" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.phone" class="text-red-500 text-sm mt-1">{{ errors.phone }}</div>
                                        </div>
                                        
                                        <div>
                                            <label for="id_card_number" class="block text-sm font-medium text-gray-700">Numéro de carte d'identité/passeport *</label>
                                            <input type="text" id="id_card_number" v-model="newUserForm.id_card_number" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.id_card_number" class="text-red-500 text-sm mt-1">{{ errors.id_card_number }}</div>
                                        </div>
                                        
                                        <div>
                                            <label for="birth_date" class="block text-sm font-medium text-gray-700">Date de naissance</label>
                                            <input type="date" id="birth_date" v-model="newUserForm.birth_date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.birth_date" class="text-red-500 text-sm mt-1">{{ errors.birth_date }}</div>
                                        </div>
                                        
                                        <div>
                                            <label for="profession" class="block text-sm font-medium text-gray-700">Profession</label>
                                            <input type="text" id="profession" v-model="newUserForm.profession" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.profession" class="text-red-500 text-sm mt-1">{{ errors.profession }}</div>
                                        </div>
                                        
                                        <div>
                                            <label for="company" class="block text-sm font-medium text-gray-700">Entreprise</label>
                                            <input type="text" id="company" v-model="newUserForm.company" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.company" class="text-red-500 text-sm mt-1">{{ errors.company }}</div>
                                        </div>
                                        
                                        <div class="md:col-span-2">
                                            <label for="address" class="block text-sm font-medium text-gray-700">Adresse postale</label>
                                            <textarea id="address" v-model="newUserForm.address" rows="2" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                                            <div v-if="errors.address" class="text-red-500 text-sm mt-1">{{ errors.address }}</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Mot de passe -->
                                <div class="mb-6">
                                    <h3 class="text-md font-medium text-gray-700 mb-3">Mot de passe</h3>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="password" class="block text-sm font-medium text-gray-700">Mot de passe *</label>
                                            <input type="password" id="password" v-model="newUserForm.password" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.password" class="text-red-500 text-sm mt-1">{{ errors.password }}</div>
                                        </div>
                                        
                                        <div>
                                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirmer le mot de passe *</label>
                                            <input type="password" id="password_confirmation" v-model="newUserForm.password_confirmation" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Informations supplémentaires -->
                                <div class="mb-6">
                                    <h3 class="text-md font-medium text-gray-700 mb-3">Informations supplémentaires</h3>
                                    
                                    <div>
                                        <label for="discovery_source" class="block text-sm font-medium text-gray-700">Comment avez-vous découvert notre formation ?</label>
                                        <select id="discovery_source" v-model="newUserForm.discovery_source" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <option value="">Sélectionnez une option</option>
                                            <option v-for="option in discoveryOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                                        </select>
                                        <div v-if="errors.discovery_source" class="text-red-500 text-sm mt-1">{{ errors.discovery_source }}</div>
                                    </div>
                                    
                                    <div v-if="newUserForm.discovery_source === 'other'" class="mt-3">
                                        <label for="discovery_source_other" class="block text-sm font-medium text-gray-700">Précisez</label>
                                        <input type="text" id="discovery_source_other" v-model="newUserForm.discovery_source_other" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <div v-if="errors.discovery_source_other" class="text-red-500 text-sm mt-1">{{ errors.discovery_source_other }}</div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes ou commentaires (facultatif)</label>
                                        <textarea id="notes" v-model="newUserForm.notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                                    </div>
                                </div>
                                
                                <div class="flex items-center justify-end mt-6">
                                    <Link :href="route('sessions.show', session.id)" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-3">
                                        Annuler
                                    </Link>
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Créer un compte et s'inscrire
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps({
    session: Object,
    isLoggedIn: Boolean,
    user: Object,
    discoveryOptions: Array,
    errors: Object,
});

// Formulaire pour utilisateur existant
const existingUserForm = useForm({
    notes: '',
});

// Formulaire pour nouvel utilisateur
const newUserForm = useForm({
    name: '',
    email: '',
    phone: '',
    id_card_number: '',
    password: '',
    password_confirmation: '',
    birth_date: '',
    profession: '',
    company: '',
    address: '',
    discovery_source: '',
    discovery_source_other: '',
    notes: '',
});

// Fonction pour formater les dates
const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
};

// Soumission du formulaire pour utilisateur existant
const submitExistingUserForm = () => {
    existingUserForm.post(route('session.enrollment.store-existing', props.session.id));
};

// Soumission du formulaire pour nouvel utilisateur
const submitNewUserForm = () => {
    newUserForm.post(route('session.enrollment.store-new', props.session.id));
};
</script>
