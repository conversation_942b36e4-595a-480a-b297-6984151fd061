<template>
    <div class="min-h-screen bg-gray-100">
        <Head :title="'Inscription à ' + session.title + ' - Centre de Formation'" />

        <!-- Navigation -->
        <div class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <Link href="/" class="text-xl font-bold text-indigo-600">Centre de Formation</Link>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div v-if="$page.props.auth.user" class="ml-3 relative">
                            <Link :href="route('student.dashboard')" class="text-gray-700 hover:text-indigo-600">
                                Tableau de bord
                            </Link>
                        </div>
                        <div v-else class="ml-3 relative">
                            <Link :href="route('login')" class="text-gray-700 hover:text-indigo-600 mr-4">
                                Connexion
                            </Link>
                            <Link :href="route('register')" class="text-gray-700 hover:text-indigo-600">
                                Inscription
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contenu principal -->
        <main class="py-10">
            <div class="max-w-3xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <h1 class="text-2xl font-semibold text-gray-900 mb-6">
                            Inscription à la session : {{ session.title }}
                        </h1>

                        <!-- Détails de la session -->
                        <div class="mb-8 p-4 bg-gray-50 rounded-lg">
                            <h2 class="text-lg font-medium text-gray-900 mb-2">Détails de la session</h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p class="text-sm text-gray-600">Domaine :</p>
                                    <p class="font-medium">{{ session.training_domain?.name }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Formateur :</p>
                                    <p class="font-medium">{{ session.trainer?.name }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Dates :</p>
                                    <p class="font-medium">Du {{ formatDate(session.start_date) }} au {{ formatDate(session.end_date) }}</p>
                                </div>
                                <div v-if="session.price">
                                    <p class="text-sm text-gray-600">Prix :</p>
                                    <p class="font-medium">{{ session.price }} DT</p>
                                </div>
                            </div>
                        </div>

                        <!-- Formulaire pour utilisateur connecté -->
                        <div v-if="isLoggedIn" class="mb-8">
                            <h2 class="text-lg font-medium text-gray-900 mb-4">Confirmer votre inscription</h2>
                            <p class="mb-4">Vous êtes connecté en tant que <strong>{{ $page.props.auth.user.name }}</strong>.</p>

                            <form @submit.prevent="submitExistingUserForm">
                                <!-- Sélection du créneau horaire -->
                                <div v-if="session.active_time_slots && session.active_time_slots.length > 0" class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-3">Choisissez votre créneau horaire</label>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div
                                            v-for="timeSlot in session.active_time_slots"
                                            :key="timeSlot.id"
                                            class="relative"
                                        >
                                            <input
                                                :id="`timeslot_${timeSlot.id}`"
                                                type="radio"
                                                :value="timeSlot.id"
                                                v-model="existingUserForm.time_slot_id"
                                                class="sr-only"
                                                :disabled="timeSlot.is_full"
                                            />
                                            <label
                                                :for="`timeslot_${timeSlot.id}`"
                                                :class="[
                                                    'block p-4 border-2 rounded-lg cursor-pointer transition-all duration-200',
                                                    timeSlot.is_full
                                                        ? 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed'
                                                        : existingUserForm.time_slot_id == timeSlot.id
                                                            ? 'border-indigo-500 bg-indigo-50 text-indigo-900'
                                                            : 'border-gray-300 bg-white text-gray-900 hover:border-indigo-300 hover:bg-indigo-50'
                                                ]"
                                            >
                                                <div class="flex items-center justify-between">
                                                    <div>
                                                        <div class="font-medium">
                                                            {{ timeSlot.name || 'Créneau' }}
                                                        </div>
                                                        <div class="text-sm text-gray-600">
                                                            {{ timeSlot.formatted_start_time }} - {{ timeSlot.formatted_end_time }}
                                                        </div>
                                                    </div>
                                                    <div class="text-sm">
                                                        <span v-if="timeSlot.is_full" class="text-red-600 font-medium">
                                                            Complet
                                                        </span>
                                                        <span v-else-if="timeSlot.max_participants" class="text-gray-500">
                                                            {{ timeSlot.available_spots }} places restantes
                                                        </span>
                                                        <span v-else class="text-green-600">
                                                            Places illimitées
                                                        </span>
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Informations supplémentaires pour compléter le profil -->
                                <div class="mb-6 border-t pt-6">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Compléter votre profil</h3>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <!-- Téléphone -->
                                        <div>
                                            <label for="existing_phone" class="block text-sm font-medium text-gray-700">Téléphone *</label>
                                            <input
                                                id="existing_phone"
                                                type="tel"
                                                v-model="existingUserForm.phone"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                required
                                            />
                                        </div>

                                        <!-- Numéro de carte d'identité -->
                                        <div>
                                            <label for="existing_id_card" class="block text-sm font-medium text-gray-700">Numéro de carte d'identité *</label>
                                            <input
                                                id="existing_id_card"
                                                type="text"
                                                v-model="existingUserForm.id_card_number"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                required
                                            />
                                        </div>

                                        <!-- Étudiant étranger -->
                                        <div class="md:col-span-2">
                                            <label class="flex items-center">
                                                <input
                                                    type="checkbox"
                                                    v-model="existingUserForm.is_foreign_student"
                                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                                />
                                                <span class="ml-2 text-sm text-gray-700">Je suis un étudiant étranger</span>
                                            </label>
                                        </div>

                                        <!-- Numéro de passeport (conditionnel) -->
                                        <div v-if="existingUserForm.is_foreign_student" class="md:col-span-2">
                                            <label for="existing_passport" class="block text-sm font-medium text-gray-700">Numéro de passeport *</label>
                                            <input
                                                id="existing_passport"
                                                type="text"
                                                v-model="existingUserForm.passport_number"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                :required="existingUserForm.is_foreign_student"
                                            />
                                        </div>

                                        <!-- Profession -->
                                        <div>
                                            <label for="existing_profession" class="block text-sm font-medium text-gray-700">Profession</label>
                                            <input
                                                id="existing_profession"
                                                type="text"
                                                v-model="existingUserForm.profession"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            />
                                        </div>

                                        <!-- Entreprise -->
                                        <div>
                                            <label for="existing_company" class="block text-sm font-medium text-gray-700">Entreprise</label>
                                            <input
                                                id="existing_company"
                                                type="text"
                                                v-model="existingUserForm.company"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            />
                                        </div>

                                        <!-- Adresse postale -->
                                        <div class="md:col-span-2">
                                            <label for="existing_address" class="block text-sm font-medium text-gray-700">Adresse postale</label>
                                            <textarea
                                                id="existing_address"
                                                v-model="existingUserForm.address"
                                                rows="2"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            ></textarea>
                                        </div>

                                        <!-- Comment avez-vous découvert notre formation -->
                                        <div class="md:col-span-2">
                                            <label for="existing_discovery" class="block text-sm font-medium text-gray-700">Comment avez-vous découvert notre formation ?</label>
                                            <select
                                                id="existing_discovery"
                                                v-model="existingUserForm.discovery_source"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            >
                                                <option value="">Sélectionnez une option</option>
                                                <option value="facebook">Facebook</option>
                                                <option value="instagram">Instagram</option>
                                                <option value="tiktok">TikTok</option>
                                                <option value="word_of_mouth">Bouche à oreille</option>
                                                <option value="other">Autre</option>
                                            </select>
                                        </div>

                                        <!-- Précisez (conditionnel) -->
                                        <div v-if="existingUserForm.discovery_source === 'other'" class="md:col-span-2">
                                            <label for="existing_discovery_other" class="block text-sm font-medium text-gray-700">Précisez</label>
                                            <input
                                                id="existing_discovery_other"
                                                type="text"
                                                v-model="existingUserForm.discovery_source_other"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-6">
                                    <label for="notes" class="block text-sm font-medium text-gray-700">Notes ou commentaires (facultatif)</label>
                                    <textarea id="notes" v-model="existingUserForm.notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                                </div>

                                <!-- Section de paiement -->
                                <div class="mb-6 border-t pt-6">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Informations de paiement</h3>

                                    <!-- Méthode de paiement -->
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-3">Méthode de paiement *</label>
                                        <div class="space-y-3">
                                            <label class="flex items-center">
                                                <input
                                                    type="radio"
                                                    value="cash"
                                                    v-model="existingUserForm.payment_method"
                                                    class="text-indigo-600 focus:ring-indigo-500"
                                                />
                                                <span class="ml-2 text-sm text-gray-700">Espèces</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input
                                                    type="radio"
                                                    value="d17"
                                                    v-model="existingUserForm.payment_method"
                                                    class="text-indigo-600 focus:ring-indigo-500"
                                                />
                                                <span class="ml-2 text-sm text-gray-700">D17</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input
                                                    type="radio"
                                                    value="bank_transfer"
                                                    v-model="existingUserForm.payment_method"
                                                    class="text-indigo-600 focus:ring-indigo-500"
                                                />
                                                <span class="ml-2 text-sm text-gray-700">Virement bancaire + 19% de frais</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input
                                                    type="radio"
                                                    value="postal_youth_card"
                                                    v-model="existingUserForm.payment_method"
                                                    class="text-indigo-600 focus:ring-indigo-500"
                                                />
                                                <span class="ml-2 text-sm text-gray-700">Virement postal avec carte jeune</span>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Informations conditionnelles selon la méthode de paiement -->
                                    <div v-if="existingUserForm.payment_method === 'd17'" class="mb-4 p-4 bg-blue-50 rounded-lg">
                                        <p class="text-sm text-blue-800 font-medium mb-2">Numéro D17 :</p>
                                        <p class="text-sm text-blue-700">+123 456 789</p>
                                    </div>

                                    <div v-if="existingUserForm.payment_method === 'bank_transfer'" class="mb-4 p-4 bg-green-50 rounded-lg">
                                        <p class="text-sm text-green-800 font-medium mb-2">Informations bancaires :</p>
                                        <p class="text-sm text-green-700">Compte : FR76 1234 5678 9012 3456 7890 123</p>
                                        <p class="text-sm text-green-700 mt-1">Frais supplémentaires de 19% appliqués</p>
                                    </div>

                                    <div v-if="existingUserForm.payment_method === 'postal_youth_card'" class="mb-4 p-4 bg-purple-50 rounded-lg">
                                        <p class="text-sm text-purple-800 font-medium mb-2">Informations carte jeune :</p>
                                        <p class="text-sm text-purple-700">Présentez votre carte jeune lors du virement postal</p>
                                    </div>

                                    <!-- Date de paiement -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <label for="existing_payment_date" class="block text-sm font-medium text-gray-700">Date de paiement</label>
                                            <input
                                                id="existing_payment_date"
                                                type="date"
                                                v-model="existingUserForm.payment_date"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            />
                                        </div>
                                        <div>
                                            <label for="existing_payment_due_date" class="block text-sm font-medium text-gray-700">Date limite de paiement</label>
                                            <input
                                                id="existing_payment_due_date"
                                                type="date"
                                                v-model="existingUserForm.payment_due_date"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            />
                                        </div>
                                    </div>

                                    <!-- Justificatif de paiement -->
                                    <div class="mb-4">
                                        <label for="existing_payment_proof" class="block text-sm font-medium text-gray-700">Justificatif de paiement</label>
                                        <input
                                            id="existing_payment_proof"
                                            type="file"
                                            @change="handleExistingPaymentProofUpload"
                                            accept="image/*,application/pdf"
                                            class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                                        />
                                    </div>
                                </div>
                                
                                <div class="flex items-center justify-end mt-6">
                                    <Link :href="route('sessions.show', session.id)" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-3">
                                        Annuler
                                    </Link>
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Confirmer l'inscription
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Formulaire pour nouvel utilisateur -->
                        <div v-else>
                            <!-- Section de connexion pour utilisateurs existants -->
                            <div class="mb-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="text-lg font-medium text-blue-900 mb-2">Vous avez déjà un compte ?</h3>
                                        <p class="text-blue-700">Connectez-vous pour simplifier votre inscription avec vos informations existantes.</p>
                                    </div>
                                    <div class="ml-6">
                                        <Link
                                            :href="route('login') + '?intended=' + encodeURIComponent(route('session.enrollment.create', session.id))"
                                            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
                                            aria-label="Se connecter pour simplifier votre inscription à cette session"
                                            role="button"
                                            tabindex="0"
                                        >
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                                            </svg>
                                            Se connecter
                                        </Link>
                                    </div>
                                </div>
                            </div>

                            <!-- Séparateur -->
                            <div class="relative mb-8">
                                <div class="absolute inset-0 flex items-center">
                                    <div class="w-full border-t border-gray-300"></div>
                                </div>
                                <div class="relative flex justify-center text-sm">
                                    <span class="px-2 bg-white text-gray-500">ou</span>
                                </div>
                            </div>

                            <h2 class="text-lg font-medium text-gray-900 mb-4">Créer un compte et s'inscrire</h2>
                            <p class="mb-4">Veuillez remplir le formulaire ci-dessous pour créer votre compte et vous inscrire à cette session.</p>

                            <form @submit.prevent="submitNewUserForm">
                                <!-- Sélection du créneau horaire -->
                                <div v-if="session.active_time_slots && session.active_time_slots.length > 0" class="mb-6">
                                    <h3 class="text-md font-medium text-gray-700 mb-3">Choisissez votre créneau horaire</h3>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div
                                            v-for="timeSlot in session.active_time_slots"
                                            :key="timeSlot.id"
                                            class="relative"
                                        >
                                            <input
                                                :id="`new_timeslot_${timeSlot.id}`"
                                                type="radio"
                                                :value="timeSlot.id"
                                                v-model="newUserForm.time_slot_id"
                                                class="sr-only"
                                                :disabled="timeSlot.is_full"
                                            />
                                            <label
                                                :for="`new_timeslot_${timeSlot.id}`"
                                                :class="[
                                                    'block p-4 border-2 rounded-lg cursor-pointer transition-all duration-200',
                                                    timeSlot.is_full
                                                        ? 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed'
                                                        : newUserForm.time_slot_id == timeSlot.id
                                                            ? 'border-indigo-500 bg-indigo-50 text-indigo-900'
                                                            : 'border-gray-300 bg-white text-gray-900 hover:border-indigo-300 hover:bg-indigo-50'
                                                ]"
                                            >
                                                <div class="flex items-center justify-between">
                                                    <div>
                                                        <div class="font-medium">
                                                            {{ timeSlot.name || 'Créneau' }}
                                                        </div>
                                                        <div class="text-sm text-gray-600">
                                                            {{ timeSlot.formatted_start_time }} - {{ timeSlot.formatted_end_time }}
                                                        </div>
                                                    </div>
                                                    <div class="text-sm">
                                                        <span v-if="timeSlot.is_full" class="text-red-600 font-medium">
                                                            Complet
                                                        </span>
                                                        <span v-else-if="timeSlot.max_participants" class="text-gray-500">
                                                            {{ timeSlot.available_spots }} places restantes
                                                        </span>
                                                        <span v-else class="text-green-600">
                                                            Places illimitées
                                                        </span>
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Informations personnelles -->
                                <div class="mb-6">
                                    <h3 class="text-md font-medium text-gray-700 mb-3">Informations personnelles</h3>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="name" class="block text-sm font-medium text-gray-700">Nom complet *</label>
                                            <input type="text" id="name" v-model="newUserForm.name" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.name" class="text-red-500 text-sm mt-1">{{ errors.name }}</div>
                                        </div>
                                        
                                        <div>
                                            <label for="email" class="block text-sm font-medium text-gray-700">Email *</label>
                                            <input type="email" id="email" v-model="newUserForm.email" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.email" class="text-red-500 text-sm mt-1">{{ errors.email }}</div>
                                        </div>
                                        
                                        <div>
                                            <label for="phone" class="block text-sm font-medium text-gray-700">Téléphone *</label>
                                            <input type="tel" id="phone" v-model="newUserForm.phone" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.phone" class="text-red-500 text-sm mt-1">{{ errors.phone }}</div>
                                        </div>
                                        
                                        <div>
                                            <label for="id_card_number" class="block text-sm font-medium text-gray-700">Numéro de carte d'identité/passeport *</label>
                                            <input type="text" id="id_card_number" v-model="newUserForm.id_card_number" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.id_card_number" class="text-red-500 text-sm mt-1">{{ errors.id_card_number }}</div>
                                        </div>
                                        
                                        <div>
                                            <label for="birth_date" class="block text-sm font-medium text-gray-700">Date de naissance</label>
                                            <input type="date" id="birth_date" v-model="newUserForm.birth_date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.birth_date" class="text-red-500 text-sm mt-1">{{ errors.birth_date }}</div>
                                        </div>
                                        
                                        <div>
                                            <label for="profession" class="block text-sm font-medium text-gray-700">Profession</label>
                                            <input type="text" id="profession" v-model="newUserForm.profession" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.profession" class="text-red-500 text-sm mt-1">{{ errors.profession }}</div>
                                        </div>
                                        
                                        <div>
                                            <label for="company" class="block text-sm font-medium text-gray-700">Entreprise</label>
                                            <input type="text" id="company" v-model="newUserForm.company" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.company" class="text-red-500 text-sm mt-1">{{ errors.company }}</div>
                                        </div>
                                        
                                        <div class="md:col-span-2">
                                            <label for="address" class="block text-sm font-medium text-gray-700">Adresse postale</label>
                                            <textarea id="address" v-model="newUserForm.address" rows="2" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                                            <div v-if="errors.address" class="text-red-500 text-sm mt-1">{{ errors.address }}</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Mot de passe -->
                                <div class="mb-6">
                                    <h3 class="text-md font-medium text-gray-700 mb-3">Mot de passe</h3>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="password" class="block text-sm font-medium text-gray-700">Mot de passe *</label>
                                            <input type="password" id="password" v-model="newUserForm.password" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div v-if="errors.password" class="text-red-500 text-sm mt-1">{{ errors.password }}</div>
                                        </div>
                                        
                                        <div>
                                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirmer le mot de passe *</label>
                                            <input type="password" id="password_confirmation" v-model="newUserForm.password_confirmation" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Informations supplémentaires -->
                                <div class="mb-6">
                                    <h3 class="text-md font-medium text-gray-700 mb-3">Informations supplémentaires</h3>
                                    
                                    <div>
                                        <label for="discovery_source" class="block text-sm font-medium text-gray-700">Comment avez-vous découvert notre formation ?</label>
                                        <select id="discovery_source" v-model="newUserForm.discovery_source" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <option value="">Sélectionnez une option</option>
                                            <option v-for="option in discoveryOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                                        </select>
                                        <div v-if="errors.discovery_source" class="text-red-500 text-sm mt-1">{{ errors.discovery_source }}</div>
                                    </div>
                                    
                                    <div v-if="newUserForm.discovery_source === 'other'" class="mt-3">
                                        <label for="discovery_source_other" class="block text-sm font-medium text-gray-700">Précisez</label>
                                        <input type="text" id="discovery_source_other" v-model="newUserForm.discovery_source_other" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <div v-if="errors.discovery_source_other" class="text-red-500 text-sm mt-1">{{ errors.discovery_source_other }}</div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes ou commentaires (facultatif)</label>
                                        <textarea id="notes" v-model="newUserForm.notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                                    </div>
                                </div>
                                
                                <div class="flex items-center justify-end mt-6">
                                    <Link :href="route('sessions.show', session.id)" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-3">
                                        Annuler
                                    </Link>
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Créer un compte et s'inscrire
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps({
    session: Object,
    isLoggedIn: Boolean,
    user: Object,
    discoveryOptions: Array,
    errors: Object,
});

// Formulaire pour utilisateur existant
const existingUserForm = useForm({
    time_slot_id: '',
    phone: '',
    id_card_number: '',
    passport_number: '',
    is_foreign_student: false,
    profession: '',
    company: '',
    address: '',
    discovery_source: '',
    discovery_source_other: '',
    payment_method: '',
    payment_date: '',
    payment_due_date: '',
    payment_proof: null,
    notes: '',
});

// Formulaire pour nouvel utilisateur
const newUserForm = useForm({
    name: '',
    email: '',
    phone: '',
    id_card_number: '',
    password: '',
    password_confirmation: '',
    birth_date: '',
    profession: '',
    company: '',
    address: '',
    discovery_source: '',
    discovery_source_other: '',
    time_slot_id: '',
    notes: '',
});

// Fonction pour formater les dates
const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
};

// Soumission du formulaire pour utilisateur existant
const submitExistingUserForm = () => {
    existingUserForm.post(route('session.enrollment.store-existing', props.session.id));
};

// Soumission du formulaire pour nouvel utilisateur
const submitNewUserForm = () => {
    newUserForm.post(route('session.enrollment.store-new', props.session.id));
};

// Gestion de l'upload du justificatif de paiement pour utilisateur existant
const handleExistingPaymentProofUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
        existingUserForm.payment_proof = file;
    }
};

// Gestion de l'upload du justificatif de paiement pour nouvel utilisateur
const handleNewPaymentProofUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
        newUserForm.payment_proof = file;
    }
};
</script>
