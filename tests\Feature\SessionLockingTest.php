<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\TrainingSession;
use App\Models\Enrollment;
use App\Models\TrainingDomain;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SessionLockingTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer un domaine de formation
        $this->domain = TrainingDomain::create([
            'name' => 'Test Domain',
            'description' => 'Test domain for testing',
            'active' => true
        ]);
    }

    /** @test */
    public function niveau_1_sessions_are_never_locked()
    {
        $user = User::factory()->create(['role' => 'student']);
        
        $session = TrainingSession::create([
            'title' => 'Test Session Niveau 1',
            'description' => 'Test description',
            'department' => 'Secourisme',
            'level' => 'Niveau 1',
            'training_domain_id' => $this->domain->id,
            'start_date' => now()->addDays(7),
            'end_date' => now()->addDays(8),
            'active' => true
        ]);

        $this->actingAs($user);
        
        $response = $this->get(route('student.sessions.index'));
        
        $response->assertStatus(200);
        
        // Vérifier que la session n'est pas verrouillée
        $sessionData = $response->viewData('sessions')->items()[0];
        $this->assertFalse($sessionData->is_locked);
    }

    /** @test */
    public function niveau_2_session_is_locked_without_niveau_1_completion()
    {
        $user = User::factory()->create(['role' => 'student']);
        
        $session = TrainingSession::create([
            'title' => 'Test Session Niveau 2',
            'description' => 'Test description',
            'department' => 'Secourisme',
            'level' => 'Niveau 2',
            'training_domain_id' => $this->domain->id,
            'start_date' => now()->addDays(7),
            'end_date' => now()->addDays(8),
            'active' => true
        ]);

        $this->actingAs($user);
        
        $response = $this->get(route('student.sessions.index'));
        
        $response->assertStatus(200);
        
        // Vérifier que la session est verrouillée
        $sessionData = $response->viewData('sessions')->items()[0];
        $this->assertTrue($sessionData->is_locked);
        $this->assertStringContainsString('Niveau 1', $sessionData->lock_reason);
    }

    /** @test */
    public function niveau_2_session_is_unlocked_after_niveau_1_completion()
    {
        $user = User::factory()->create(['role' => 'student']);
        
        // Créer une session Niveau 1
        $niveau1Session = TrainingSession::create([
            'title' => 'Test Session Niveau 1',
            'description' => 'Test description',
            'department' => 'Secourisme',
            'level' => 'Niveau 1',
            'training_domain_id' => $this->domain->id,
            'start_date' => now()->subDays(10),
            'end_date' => now()->subDays(9),
            'active' => true
        ]);

        // Créer une inscription complétée pour le Niveau 1
        Enrollment::create([
            'user_id' => $user->id,
            'training_session_id' => $niveau1Session->id,
            'status' => 'completed',
            'enrollment_date' => now()->subDays(10)
        ]);

        // Créer une session Niveau 2
        $niveau2Session = TrainingSession::create([
            'title' => 'Test Session Niveau 2',
            'description' => 'Test description',
            'department' => 'Secourisme',
            'level' => 'Niveau 2',
            'training_domain_id' => $this->domain->id,
            'start_date' => now()->addDays(7),
            'end_date' => now()->addDays(8),
            'active' => true
        ]);

        $this->actingAs($user);
        
        $response = $this->get(route('student.sessions.index'));
        
        $response->assertStatus(200);
        
        // Vérifier que la session Niveau 2 n'est pas verrouillée
        $sessions = $response->viewData('sessions')->items();
        $niveau2SessionData = collect($sessions)->firstWhere('level', 'Niveau 2');
        
        $this->assertNotNull($niveau2SessionData);
        $this->assertFalse($niveau2SessionData->is_locked);
    }

    /** @test */
    public function different_departments_have_independent_progression()
    {
        $user = User::factory()->create(['role' => 'student']);
        
        // Compléter Niveau 1 en Secourisme
        $secoursSession = TrainingSession::create([
            'title' => 'Secours Niveau 1',
            'description' => 'Test description',
            'department' => 'Secourisme',
            'level' => 'Niveau 1',
            'training_domain_id' => $this->domain->id,
            'start_date' => now()->subDays(10),
            'end_date' => now()->subDays(9),
            'active' => true
        ]);

        Enrollment::create([
            'user_id' => $user->id,
            'training_session_id' => $secoursSession->id,
            'status' => 'completed',
            'enrollment_date' => now()->subDays(10)
        ]);

        // Créer une session Niveau 2 en Langue (différent département)
        $langueSession = TrainingSession::create([
            'title' => 'Langue Niveau 2',
            'description' => 'Test description',
            'department' => 'Langue',
            'level' => 'Niveau 2',
            'training_domain_id' => $this->domain->id,
            'start_date' => now()->addDays(7),
            'end_date' => now()->addDays(8),
            'active' => true
        ]);

        $this->actingAs($user);
        
        $response = $this->get(route('student.sessions.index'));
        
        $response->assertStatus(200);
        
        // Vérifier que la session Langue Niveau 2 est verrouillée 
        // (car Niveau 1 Langue n'est pas complété)
        $sessions = $response->viewData('sessions')->items();
        $langueSessionData = collect($sessions)->firstWhere('department', 'Langue');
        
        $this->assertNotNull($langueSessionData);
        $this->assertTrue($langueSessionData->is_locked);
    }
}
