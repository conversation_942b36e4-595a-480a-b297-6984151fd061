<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TrainingSession;
use App\Models\Enrollment;

class CheckTrainingSessions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'training:check-sessions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check training session status and enrollments';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Checking training sessions...');
        
        $sessions = TrainingSession::with('enrollments')->get();
        
        $this->line("Total training sessions: {$sessions->count()}");
        
        foreach ($sessions as $session) {
            $enrollmentCount = $session->enrollments->count();
            $activeStatus = $session->active ? 'ACTIVE' : 'INACTIVE';
            $this->line("- {$session->title}: {$activeStatus} ({$enrollmentCount} enrollments)");
        }
        
        $this->info('');
        $this->info('🎓 Students with enrollments in inactive sessions:');
        
        $inactiveSessionIds = TrainingSession::where('active', false)->pluck('id');
        $enrollmentsInInactive = Enrollment::whereIn('training_session_id', $inactiveSessionIds)
            ->where('status', 'approved')
            ->with(['user', 'trainingSession'])
            ->get();
        
        if ($enrollmentsInInactive->count() > 0) {
            foreach ($enrollmentsInInactive as $enrollment) {
                $this->line("- {$enrollment->user->name}: {$enrollment->trainingSession->title}");
            }
        } else {
            $this->line("No students enrolled in inactive sessions");
        }
        
        return Command::SUCCESS;
    }
}
