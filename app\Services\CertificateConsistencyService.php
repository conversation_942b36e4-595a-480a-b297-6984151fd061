<?php

namespace App\Services;

use App\Models\Certificate;
use App\Models\Enrollment;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class CertificateConsistencyService
{
    /**
     * Validate certificate number consistency across all contexts
     */
    public static function validateCertificateConsistency(Certificate $certificate): array
    {
        $issues = [];
        
        // Check certificate number format
        $expectedPattern = '/^CERT-' . $certificate->enrollment_id . '-\d+$/';
        if (!preg_match($expectedPattern, $certificate->certificate_number)) {
            $issues[] = [
                'type' => 'format',
                'message' => "Certificate number format is incorrect",
                'expected' => "CERT-{$certificate->enrollment_id}-{timestamp}",
                'actual' => $certificate->certificate_number
            ];
        }
        
        // Check QR code consistency
        $expectedQrPath = 'certificates/qr/' . $certificate->certificate_number . '.svg';
        if ($certificate->qr_code !== $expectedQrPath) {
            $issues[] = [
                'type' => 'qr_code',
                'message' => "QR code path is inconsistent with certificate number",
                'expected' => $expectedQrPath,
                'actual' => $certificate->qr_code
            ];
        }
        
        // Check PDF path consistency
        if ($certificate->pdf_path) {
            $expectedPdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';
            if ($certificate->pdf_path !== $expectedPdfPath) {
                $issues[] = [
                    'type' => 'pdf_path',
                    'message' => "PDF path is inconsistent with certificate number",
                    'expected' => $expectedPdfPath,
                    'actual' => $certificate->pdf_path
                ];
            }
        }
        
        // Check status transition integrity
        if (in_array($certificate->status, ['active', 'issued']) && !$certificate->issued_at) {
            $issues[] = [
                'type' => 'status_integrity',
                'message' => "Certificate is {$certificate->status} but missing issued_at date",
                'expected' => 'issued_at should be set',
                'actual' => 'issued_at is null'
            ];
        }
        
        return $issues;
    }
    
    /**
     * Get certificate display data with consistency validation
     */
    public static function getCertificateDisplayData(Certificate $certificate): array
    {
        // Validate consistency
        $issues = self::validateCertificateConsistency($certificate);
        
        // Log any issues found
        if (!empty($issues)) {
            Log::warning("Certificate consistency issues found for certificate {$certificate->id}", [
                'certificate_number' => $certificate->certificate_number,
                'issues' => $issues
            ]);
        }
        
        return [
            'id' => $certificate->id,
            'certificate_number' => $certificate->certificate_number,
            'status' => $certificate->status,
            'status_label' => Certificate::getStatusOptions()[$certificate->status] ?? $certificate->status,
            'issued_at' => $certificate->issued_at,
            'issue_date' => $certificate->issue_date,
            'expiry_date' => $certificate->expiry_date,
            'qr_code' => $certificate->qr_code,
            'pdf_path' => $certificate->pdf_path,
            'enrollment_id' => $certificate->enrollment_id,
            'user_id' => $certificate->user_id,
            'training_session_id' => $certificate->training_session_id,
            'consistency_issues' => $issues,
            'is_consistent' => empty($issues)
        ];
    }
    
    /**
     * Track certificate number changes (for debugging)
     */
    public static function trackCertificateChange(Certificate $certificate, string $context, array $changes = []): void
    {
        $trackingData = [
            'certificate_id' => $certificate->id,
            'certificate_number' => $certificate->certificate_number,
            'enrollment_id' => $certificate->enrollment_id,
            'status' => $certificate->status,
            'context' => $context,
            'changes' => $changes,
            'timestamp' => now()->toISOString(),
            'user_agent' => request()->header('User-Agent'),
            'ip' => request()->ip()
        ];
        
        Log::info("Certificate tracking", $trackingData);
        
        // Store in cache for debugging (keep last 100 entries)
        $cacheKey = 'certificate_tracking';
        $tracking = Cache::get($cacheKey, []);
        $tracking[] = $trackingData;
        
        // Keep only last 100 entries
        if (count($tracking) > 100) {
            $tracking = array_slice($tracking, -100);
        }
        
        Cache::put($cacheKey, $tracking, now()->addHours(24));
    }
    
    /**
     * Ensure certificate number immutability
     */
    public static function ensureCertificateNumberImmutability(Certificate $certificate): bool
    {
        // If certificate already has a number, it should never change
        if ($certificate->exists && $certificate->isDirty('certificate_number')) {
            $original = $certificate->getOriginal('certificate_number');
            $new = $certificate->certificate_number;
            
            Log::error("Attempted to change certificate number", [
                'certificate_id' => $certificate->id,
                'original_number' => $original,
                'attempted_new_number' => $new,
                'stack_trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10)
            ]);
            
            // Revert the change
            $certificate->certificate_number = $original;
            
            return false;
        }
        
        return true;
    }
    
    /**
     * Get certificate tracking history
     */
    public static function getCertificateTrackingHistory(): array
    {
        return Cache::get('certificate_tracking', []);
    }
    
    /**
     * Validate certificate across all display contexts
     */
    public static function validateCertificateAcrossContexts(Certificate $certificate): array
    {
        $contexts = [
            'admin_list' => 'Admin certificate list',
            'admin_detail' => 'Admin certificate detail',
            'student_dashboard' => 'Student dashboard',
            'student_certificates' => 'Student certificates page',
            'pdf_generation' => 'PDF generation',
            'qr_verification' => 'QR code verification',
            'email_notification' => 'Email notification'
        ];
        
        $results = [];
        
        foreach ($contexts as $context => $description) {
            $displayData = self::getCertificateDisplayData($certificate);
            
            $results[$context] = [
                'description' => $description,
                'certificate_number' => $displayData['certificate_number'],
                'is_consistent' => $displayData['is_consistent'],
                'issues' => $displayData['consistency_issues']
            ];
        }
        
        return $results;
    }
    
    /**
     * Fix certificate inconsistencies
     */
    public static function fixCertificateInconsistencies(Certificate $certificate): array
    {
        $issues = self::validateCertificateConsistency($certificate);
        $fixed = [];
        
        foreach ($issues as $issue) {
            switch ($issue['type']) {
                case 'format':
                    // Don't auto-fix format issues as they might indicate deeper problems
                    Log::warning("Certificate format issue detected but not auto-fixed", [
                        'certificate_id' => $certificate->id,
                        'issue' => $issue
                    ]);
                    break;
                    
                case 'qr_code':
                    $certificate->generateQrCode();
                    $fixed[] = 'qr_code';
                    break;
                    
                case 'pdf_path':
                    $expectedPdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';
                    $certificate->update(['pdf_path' => $expectedPdfPath]);
                    $fixed[] = 'pdf_path';
                    break;
                    
                case 'status_integrity':
                    if (in_array($certificate->status, ['active', 'issued']) && !$certificate->issued_at) {
                        $certificate->update(['issued_at' => $certificate->updated_at]);
                        $fixed[] = 'status_integrity';
                    }
                    break;
            }
        }
        
        if (!empty($fixed)) {
            Log::info("Fixed certificate inconsistencies", [
                'certificate_id' => $certificate->id,
                'certificate_number' => $certificate->certificate_number,
                'fixed_issues' => $fixed
            ]);
        }
        
        return $fixed;
    }
}
