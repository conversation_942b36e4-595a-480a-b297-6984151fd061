<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ExamResult;
use App\Models\Certificate;
use App\Models\User;
use App\Models\Exam;
use App\Models\Enrollment;

class DiagnoseExamSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exam:diagnose {--fix : Fix issues found}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Diagnose exam results and certificate status issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Diagnosing exam system...');
        
        $this->checkExamResults();
        $this->checkCertificates();
        $this->checkCertificateStatusIssues();
        
        if ($this->option('fix')) {
            $this->fixCertificateStatusIssues();
        }
        
        return Command::SUCCESS;
    }
    
    /**
     * Check exam results
     */
    private function checkExamResults(): void
    {
        $this->info('📝 Checking exam results...');
        
        $examResults = ExamResult::with(['exam', 'user'])->get();
        $this->line("   Total exam results: {$examResults->count()}");
        
        if ($examResults->count() > 0) {
            $this->line("   Recent exam results:");
            $examResults->take(5)->each(function($result) {
                $this->line("   - User: {$result->user->name}, Exam: {$result->exam->title}, Score: {$result->score}%, Passed: " . ($result->passed ? 'Yes' : 'No') . ", Status: {$result->status}");
            });
        } else {
            $this->warn("   No exam results found!");
        }
    }
    
    /**
     * Check certificates
     */
    private function checkCertificates(): void
    {
        $this->info('🏆 Checking certificates...');
        
        $certificates = Certificate::with(['user', 'examResult'])->get();
        $this->line("   Total certificates: {$certificates->count()}");
        
        if ($certificates->count() > 0) {
            $this->line("   Certificate statuses:");
            $statusCounts = $certificates->groupBy('status')->map->count();
            foreach ($statusCounts as $status => $count) {
                $this->line("   - {$status}: {$count}");
            }
            
            $this->line("   Recent certificates:");
            $certificates->take(5)->each(function($cert) {
                $examInfo = $cert->examResult ? "Exam Result ID: {$cert->examResult->id}" : "No exam result";
                $this->line("   - User: {$cert->user->name}, Number: {$cert->certificate_number}, Status: {$cert->status}, {$examInfo}");
            });
        } else {
            $this->warn("   No certificates found!");
        }
    }
    
    /**
     * Check certificate status issues
     */
    private function checkCertificateStatusIssues(): void
    {
        $this->info('⚠️  Checking certificate status issues...');
        
        // Check for certificates with 'active' status that should be 'issued'
        $activeCertificates = Certificate::where('status', 'active')
            ->whereNotNull('issued_at')
            ->with(['user', 'examResult'])
            ->get();
        
        if ($activeCertificates->count() > 0) {
            $this->warn("   Found {$activeCertificates->count()} certificates with 'active' status that should be 'issued':");
            $activeCertificates->each(function($cert) {
                $this->line("   - Certificate {$cert->certificate_number} (User: {$cert->user->name}) - Status: {$cert->status}");
            });
        } else {
            $this->line("   ✅ No certificates with incorrect 'active' status found");
        }
        
        // Check for passed exam results without certificates
        $passedResultsWithoutCerts = ExamResult::where('passed', true)
            ->whereDoesntHave('certificate')
            ->with(['exam', 'user'])
            ->get();
        
        if ($passedResultsWithoutCerts->count() > 0) {
            $this->warn("   Found {$passedResultsWithoutCerts->count()} passed exam results without certificates:");
            $passedResultsWithoutCerts->each(function($result) {
                $this->line("   - User: {$result->user->name}, Exam: {$result->exam->title}, Score: {$result->score}%");
            });
        } else {
            $this->line("   ✅ All passed exam results have certificates");
        }
    }
    
    /**
     * Fix certificate status issues
     */
    private function fixCertificateStatusIssues(): void
    {
        $this->info('🔧 Fixing certificate status issues...');
        
        // Fix certificates with 'active' status to 'issued'
        $activeCertificates = Certificate::where('status', 'active')
            ->whereNotNull('issued_at')
            ->get();
        
        if ($activeCertificates->count() > 0) {
            $this->line("   Updating {$activeCertificates->count()} certificates from 'active' to 'issued' status...");
            
            foreach ($activeCertificates as $cert) {
                $cert->update(['status' => 'issued']);
                $this->line("   ✅ Updated certificate {$cert->certificate_number} to 'issued' status");
            }
        }
        
        // Create certificates for passed exam results that don't have them
        $passedResultsWithoutCerts = ExamResult::where('passed', true)
            ->whereDoesntHave('certificate')
            ->with(['exam', 'user', 'enrollment'])
            ->get();
        
        if ($passedResultsWithoutCerts->count() > 0) {
            $this->line("   Creating certificates for {$passedResultsWithoutCerts->count()} passed exam results...");
            
            foreach ($passedResultsWithoutCerts as $result) {
                if ($result->enrollment && ($result->exam->exam_type === 'certification' || $result->exam->exam_type === 'certification_rattrapage')) {
                    $certificateNumber = Certificate::generateCertificateNumber($result->enrollment->id);
                    
                    $certificate = Certificate::create([
                        'enrollment_id' => $result->enrollment->id,
                        'user_id' => $result->user_id,
                        'training_session_id' => $result->exam->training_session_id,
                        'exam_result_id' => $result->id,
                        'certificate_number' => $certificateNumber,
                        'status' => 'issued',
                        'issued_at' => now(),
                        'issue_date' => now(),
                    ]);
                    
                    $this->line("   ✅ Created certificate {$certificate->certificate_number} for user {$result->user->name}");
                }
            }
        }
    }
}
