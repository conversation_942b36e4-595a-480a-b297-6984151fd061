<?php

// Script pour réparer les données JSON dans la base de données

// Charger l'environnement Laravel
require __DIR__.'/vendor/autoload.php';
$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "Début de la réparation des données JSON...\n";

// Récupérer toutes les questions à choix multiple
$questions = DB::table('exam_questions')
    ->where('question_type', 'multiple_choice')
    ->get();

echo "Nombre de questions à choix multiple trouvées : " . count($questions) . "\n";

$updated = 0;

foreach ($questions as $question) {
    echo "Traitement de la question ID: " . $question->id . "\n";
    
    // Options par défaut
    $defaultOptions = [
        'A' => 'Option A',
        'B' => 'Option B',
        'C' => 'Option C',
        'D' => 'Option D'
    ];
    
    // Réponses correctes par défaut
    $defaultCorrectOptions = ['A'];
    
    // Vérifier les options actuelles
    $options = $question->options;
    $correctOptions = $question->correct_options;
    
    echo "Options actuelles: " . ($options ?: "NULL") . "\n";
    echo "Options correctes actuelles: " . ($correctOptions ?: "NULL") . "\n";
    
    // Déterminer si une mise à jour est nécessaire
    $needsUpdate = false;
    $newOptions = null;
    $newCorrectOptions = null;
    
    // Vérifier si les options sont vides ou invalides
    if (empty($options) || $options === "null" || $options === "[]" || $options === "{}" || $options === "[object Object]") {
        $newOptions = json_encode($defaultOptions);
        $needsUpdate = true;
        echo "Options mises à jour avec les valeurs par défaut\n";
    } else {
        // Essayer de parser les options pour vérifier si elles sont valides
        try {
            $decodedOptions = json_decode($options, true);
            if (json_last_error() !== JSON_ERROR_NONE || empty($decodedOptions) || !is_array($decodedOptions)) {
                $newOptions = json_encode($defaultOptions);
                $needsUpdate = true;
                echo "Options invalides, mises à jour avec les valeurs par défaut\n";
            } else {
                // Si les options sont un tableau indexé, les convertir en tableau associatif
                if (array_keys($decodedOptions) === range(0, count($decodedOptions) - 1)) {
                    $optionsObj = [];
                    foreach ($decodedOptions as $index => $option) {
                        $key = chr(65 + $index); // A, B, C, ...
                        $optionsObj[$key] = $option;
                    }
                    $newOptions = json_encode($optionsObj);
                    $needsUpdate = true;
                    echo "Options converties de tableau indexé à tableau associatif\n";
                }
            }
        } catch (Exception $e) {
            $newOptions = json_encode($defaultOptions);
            $needsUpdate = true;
            echo "Erreur lors du parsing des options, mises à jour avec les valeurs par défaut\n";
        }
    }
    
    // Vérifier si les options correctes sont vides ou invalides
    if (empty($correctOptions) || $correctOptions === "null" || $correctOptions === "[]" || $correctOptions === "{}" || $correctOptions === "[object Object]") {
        $newCorrectOptions = json_encode($defaultCorrectOptions);
        $needsUpdate = true;
        echo "Options correctes mises à jour avec les valeurs par défaut\n";
    } else {
        // Essayer de parser les options correctes pour vérifier si elles sont valides
        try {
            $decodedCorrectOptions = json_decode($correctOptions, true);
            if (json_last_error() !== JSON_ERROR_NONE || empty($decodedCorrectOptions)) {
                $newCorrectOptions = json_encode($defaultCorrectOptions);
                $needsUpdate = true;
                echo "Options correctes invalides, mises à jour avec les valeurs par défaut\n";
            }
        } catch (Exception $e) {
            $newCorrectOptions = json_encode($defaultCorrectOptions);
            $needsUpdate = true;
            echo "Erreur lors du parsing des options correctes, mises à jour avec les valeurs par défaut\n";
        }
    }
    
    // Mettre à jour la question si nécessaire
    if ($needsUpdate) {
        $updateData = [];
        
        if ($newOptions !== null) {
            $updateData['options'] = $newOptions;
        }
        
        if ($newCorrectOptions !== null) {
            $updateData['correct_options'] = $newCorrectOptions;
        }
        
        if (!empty($updateData)) {
            DB::table('exam_questions')
                ->where('id', $question->id)
                ->update($updateData);
            
            $updated++;
            echo "Question ID: " . $question->id . " mise à jour avec succès\n";
        }
    } else {
        echo "Question ID: " . $question->id . " n'a pas besoin de mise à jour\n";
    }
    
    echo "-----------------------------------\n";
}

echo "Réparation terminée. $updated questions ont été mises à jour.\n";
