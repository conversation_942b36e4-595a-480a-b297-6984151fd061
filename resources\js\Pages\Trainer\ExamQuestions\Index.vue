<template>
  <Head :title="'Questions - ' + exam.title" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Questions de l'examen: {{ exam.title }}
        </h2>
        <div class="flex space-x-2">
          <Link :href="route('trainer.exams.show', exam.id)" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
            Retour à l'examen
          </Link>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <div v-if="questions.length > 0">
              <!-- Résumé des questions -->
              <div class="mb-6 bg-gray-50 p-4 rounded-lg">
                <h3 class="text-lg font-semibold mb-2">Résumé</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div class="bg-blue-50 p-3 rounded-lg">
                    <p class="text-sm text-blue-600 mb-1">Total des questions</p>
                    <p class="text-2xl font-bold text-blue-800">{{ questions.length }}</p>
                  </div>
                  <div class="bg-green-50 p-3 rounded-lg">
                    <p class="text-sm text-green-600 mb-1">Points totaux</p>
                    <p class="text-2xl font-bold text-green-800">{{ totalPoints }}</p>
                  </div>
                  <div class="bg-purple-50 p-3 rounded-lg">
                    <p class="text-sm text-purple-600 mb-1">Choix multiple/unique</p>
                    <p class="text-2xl font-bold text-purple-800">{{ multipleChoiceCount }}</p>
                  </div>
                  <div class="bg-yellow-50 p-3 rounded-lg">
                    <p class="text-sm text-yellow-600 mb-1">Texte/Fichier</p>
                    <p class="text-2xl font-bold text-yellow-800">{{ textFileCount }}</p>
                  </div>
                </div>
              </div>

              <!-- Liste des questions -->
              <div class="space-y-4">
                <div v-for="(question, index) in questions" :key="question.id" class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                  <div class="flex justify-between items-start">
                    <div class="flex items-center">
                      <span class="bg-gray-200 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center mr-3">
                        {{ index + 1 }}
                      </span>
                      <div>
                        <h4 class="font-medium text-lg">{{ question.question_text }}</h4>
                        <div class="flex items-center mt-1 text-sm text-gray-600">
                          <span class="mr-3">
                            <span v-if="question.question_type === 'multiple_choice'" class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">Choix multiple</span>
                            <span v-else-if="question.question_type === 'single_choice'" class="px-2 py-1 bg-indigo-100 text-indigo-800 rounded-full text-xs">Choix unique</span>
                            <span v-else-if="question.question_type === 'text'" class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Texte libre</span>
                            <span v-else-if="question.question_type === 'file'" class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Fichier</span>
                          </span>
                          <span class="mr-3">{{ question.points }} point{{ question.points > 1 ? 's' : '' }}</span>
                        </div>
                      </div>
                    </div>
                    <!-- Aucune action disponible pour les formateurs -->
                    <div></div>
                  </div>

                  <!-- Détails de la question -->
                  <div class="mt-3 pl-11">
                    <!-- Options pour les questions à choix -->
                    <div v-if="['multiple_choice', 'single_choice'].includes(question.question_type) && question.options" class="mt-2">
                      <p class="text-sm text-gray-600 mb-1">Options:</p>
                      <ul class="list-disc pl-5">
                        <li v-for="(option, optIndex) in getOptionsArray(question.options)" :key="optIndex" class="text-sm">
                          <span :class="{ 'font-semibold text-green-600': isCorrectOption(question, optIndex) }">
                            {{ option }}
                            <span v-if="isCorrectOption(question, optIndex)" class="text-green-600 ml-1">(Correcte)</span>
                          </span>
                        </li>
                      </ul>
                    </div>

                    <!-- Explication -->
                    <div v-if="question.explanation" class="mt-2">
                      <p class="text-sm text-gray-600 mb-1">Explication:</p>
                      <p class="text-sm">{{ question.explanation }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <p class="text-gray-500">Aucune question trouvée pour cet examen.</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pas de modal de suppression pour les formateurs -->
  </AuthenticatedLayout>
</template>

<script setup>
import { computed } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  exam: Object,
  questions: Array,
  questionTypes: Array,
});

// Pas de fonctionnalités de suppression pour les formateurs

// Méthode pour convertir les options en tableau
const getOptionsArray = (options) => {
  if (!options) return [];

  if (Array.isArray(options)) {
    return options;
  } else if (typeof options === 'string') {
    try {
      // Essayer de parser la chaîne JSON
      return JSON.parse(options);
    } catch (e) {
      // Si ce n'est pas du JSON valide, retourner un tableau avec la chaîne
      return [options];
    }
  }

  return [];
};

// Méthode pour vérifier si une option est correcte
const isCorrectOption = (question, optionIndex) => {
  console.log('Checking if option', optionIndex, 'is correct among', question.correct_options);

  // Si aucune option correcte n'est fournie, retourner false
  if (!question.correct_options) return false;

  let correctOptions = question.correct_options;

  // Si les options correctes sont une chaîne, essayer de les parser en JSON
  if (typeof correctOptions === 'string') {
    try {
      correctOptions = JSON.parse(correctOptions);
      console.log('Parsed correctOptions from string:', correctOptions);
    } catch (e) {
      console.error('Erreur de parsing des options correctes:', e);
      // Ne pas définir d'option par défaut, retourner false
      return false;
    }
  }

  // Si les options correctes sont un tableau, vérifier si l'option est incluse
  if (Array.isArray(correctOptions)) {
    const result = correctOptions.includes(optionIndex) || correctOptions.includes(optionIndex.toString());
    console.log('Array check result:', result);
    return result;
  }

  // Si les options correctes sont un objet, vérifier si l'option est une clé avec valeur true
  if (typeof correctOptions === 'object' && !Array.isArray(correctOptions)) {
    const result = correctOptions[optionIndex] === true || correctOptions[optionIndex.toString()] === true;
    console.log('Object check result:', result);
    return result;
  }

  // Si les options correctes sont une valeur simple, vérifier l'égalité
  const result = correctOptions === optionIndex || correctOptions === optionIndex.toString();
  console.log('Simple value check result:', result);
  return result;
};

// Calculs
const totalPoints = computed(() => {
  return props.questions.reduce((sum, question) => sum + (question.points || 0), 0);
});

const multipleChoiceCount = computed(() => {
  return props.questions.filter(q =>
    q.question_type === 'multiple_choice' || q.question_type === 'single_choice'
  ).length;
});

const textFileCount = computed(() => {
  return props.questions.filter(q =>
    q.question_type === 'text' || q.question_type === 'file'
  ).length;
});
</script>
