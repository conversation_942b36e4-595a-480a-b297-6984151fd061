<template>
  <Head title="Mes paiements" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Mes paiements
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Filtres -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Filtrer mes paiements</h3>
            <div class="flex flex-wrap gap-4">
              <div>
                <label for="payment-status-filter" class="block text-sm font-medium text-gray-700 mb-1">Statut du paiement</label>
                <select
                  id="payment-status-filter"
                  v-model="paymentStatusFilter"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="">Tous les statuts</option>
                  <option value="unpaid">Non payé</option>
                  <option value="pending">En attente de validation</option>
                  <option value="paid">Payé</option>
                  <option value="refunded">Remboursé</option>
                </select>
              </div>
              <div>
                <label for="search-filter" class="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
                <input
                  id="search-filter"
                  v-model="searchFilter"
                  type="text"
                  placeholder="Rechercher une formation..."
                  class="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Liste des paiements -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Liste de mes paiements</h3>
            
            <div v-if="filteredEnrollments.length > 0" class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Formation</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date limite</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="enrollment in filteredEnrollments" :key="enrollment.id">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">{{ enrollment.training_session.title }}</div>
                      <div class="text-xs text-gray-500">{{ enrollment.training_session.training_domain.name }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">
                        {{ enrollment.payment_amount ? `${enrollment.payment_amount} DT` : (enrollment.training_session.price ? `${enrollment.training_session.price} DT` : 'Gratuit') }}
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="getPaymentStatusClass(enrollment.payment_status)" class="px-2 py-1 text-xs rounded-full">
                        {{ getPaymentStatusLabel(enrollment.payment_status) }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div v-if="enrollment.payment_due_date" class="text-sm text-gray-900">
                        {{ formatDate(enrollment.payment_due_date) }}
                      </div>
                      <div v-else class="text-sm text-gray-500">
                        Non définie
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                      <div class="flex space-x-2">
                        <Link 
                          :href="route('student.enrollments.show', enrollment.id)" 
                          class="text-indigo-600 hover:text-indigo-900"
                        >
                          Voir détails
                        </Link>
                        
                        <template v-if="['unpaid', 'pending'].includes(enrollment.payment_status)">
                          <span class="text-gray-300">|</span>
                          <button 
                            @click="showUploadModal(enrollment)" 
                            class="text-green-600 hover:text-green-900"
                          >
                            Téléverser justificatif
                          </button>
                        </template>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <div v-else class="text-center py-8 text-gray-500">
              Aucun paiement trouvé.
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Modal de téléversement de justificatif -->
    <div v-if="showUploadModalFlag" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Téléverser un justificatif de paiement</h3>
        <p class="text-gray-600 mb-4">
          Formation : {{ selectedEnrollment?.training_session.title }}
        </p>
        
        <form @submit.prevent="submitPaymentProof" class="mb-4">
          <div class="mb-4">
            <label for="payment-proof" class="block text-sm font-medium text-gray-700 mb-1">Justificatif</label>
            <input 
              type="file" 
              id="payment-proof"
              ref="paymentProofInput"
              class="block w-full text-sm text-gray-500
                file:mr-4 file:py-2 file:px-4
                file:rounded-md file:border-0
                file:text-sm file:font-semibold
                file:bg-indigo-50 file:text-indigo-700
                hover:file:bg-indigo-100"
              accept=".jpg,.jpeg,.png,.pdf"
            />
            <p class="mt-1 text-xs text-gray-500">Formats acceptés : JPG, PNG, PDF (max 2Mo)</p>
          </div>
          
          <div class="flex justify-end space-x-3">
            <button 
              type="button"
              @click="showUploadModalFlag = false" 
              class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            >
              Annuler
            </button>
            <button 
              type="submit" 
              class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            >
              Téléverser
            </button>
          </div>
        </form>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  enrollments: Array,
});

// État local
const paymentStatusFilter = ref('');
const searchFilter = ref('');
const showUploadModalFlag = ref(false);
const selectedEnrollment = ref(null);
const paymentProofInput = ref(null);

// Inscriptions filtrées
const filteredEnrollments = computed(() => {
  return props.enrollments.filter(enrollment => {
    // Filtre par statut de paiement
    if (paymentStatusFilter.value && enrollment.payment_status !== paymentStatusFilter.value) {
      return false;
    }
    
    // Filtre par recherche
    if (searchFilter.value) {
      const searchTerm = searchFilter.value.toLowerCase();
      return enrollment.training_session.title.toLowerCase().includes(searchTerm) ||
             enrollment.training_session.training_domain.name.toLowerCase().includes(searchTerm);
    }
    
    return true;
  });
});

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return '';
  const options = { day: 'numeric', month: 'long', year: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const getPaymentStatusLabel = (status) => {
  const labels = {
    'unpaid': 'Non payé',
    'pending': 'En attente de validation',
    'paid': 'Payé',
    'refunded': 'Remboursé'
  };
  return labels[status] || 'Non payé';
};

const getPaymentStatusClass = (status) => {
  const classes = {
    'unpaid': 'bg-red-100 text-red-800',
    'pending': 'bg-yellow-100 text-yellow-800',
    'paid': 'bg-green-100 text-green-800',
    'refunded': 'bg-blue-100 text-blue-800'
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

const showUploadModal = (enrollment) => {
  selectedEnrollment.value = enrollment;
  showUploadModalFlag.value = true;
};

const submitPaymentProof = () => {
  const fileInput = paymentProofInput.value;
  if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
    alert('Veuillez sélectionner un fichier');
    return;
  }

  const file = fileInput.files[0];

  // Validation côté client
  const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
  if (!allowedTypes.includes(file.type)) {
    alert('Le fichier doit être au format JPEG, PNG, JPG ou PDF.');
    return;
  }

  const maxSize = 2 * 1024 * 1024; // 2MB
  if (file.size > maxSize) {
    alert('Le fichier ne doit pas dépasser 2 Mo.');
    return;
  }

  const formData = new FormData();
  formData.append('payment_proof', file);

  // Utiliser fetch pour envoyer le fichier
  fetch(route('student.enrollments.upload-payment-proof', selectedEnrollment.value.id), {
    method: 'POST',
    body: formData,
    headers: {
      'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
      'Accept': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert(data.message || 'Justificatif de paiement téléversé avec succès.');
      showUploadModalFlag.value = false;
      window.location.reload();
    } else {
      // Afficher les erreurs spécifiques
      if (data.errors) {
        const errorMessages = Object.values(data.errors).flat().join('\n');
        alert(errorMessages);
      } else {
        alert(data.message || 'Une erreur est survenue lors du téléversement du fichier');
      }
    }
  })
  .catch(error => {
    console.error('Erreur:', error);
    alert('Une erreur de connexion est survenue. Veuillez réessayer.');
  });
};
</script>
