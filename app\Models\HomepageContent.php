<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HomepageContent extends Model
{
    use HasFactory;

    protected $fillable = [
        'section',
        'key',
        'value',
        'type',
        'metadata',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_active' => 'boolean'
    ];

    public static function getBySection($section)
    {
        return self::where('section', $section)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get()
            ->keyBy('key');
    }

    public static function getValue($section, $key, $default = null)
    {
        $content = self::where('section', $section)
            ->where('key', $key)
            ->where('is_active', true)
            ->first();

        return $content ? $content->value : $default;
    }

    public static function setValue($section, $key, $value, $type = 'text', $metadata = null)
    {
        return self::updateOrCreate(
            ['section' => $section, 'key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'metadata' => $metadata,
                'is_active' => true
            ]
        );
    }
}
