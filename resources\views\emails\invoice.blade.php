<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facture de Formation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #059669;
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8fafc;
            padding: 30px;
            border: 1px solid #e2e8f0;
        }
        .footer {
            background-color: #64748b;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 14px;
        }
        .invoice-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            background-color: white;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #d1d5db;
        }
        .invoice-details {
            background-color: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f3f4f6;
        }
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .label {
            font-weight: bold;
            color: #374151;
        }
        .value {
            color: #6b7280;
        }
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid #d1d5db;
        }
        .invoice-table th {
            background-color: #f3f4f6;
            padding: 15px;
            text-align: left;
            font-weight: bold;
            color: #374151;
        }
        .invoice-table td {
            padding: 15px;
            border-bottom: 1px solid #f3f4f6;
        }
        .invoice-table tr:last-child td {
            border-bottom: none;
        }
        .total-row {
            background-color: #f9fafb;
            font-weight: bold;
            font-size: 18px;
        }
        .amount {
            font-size: 24px;
            font-weight: bold;
            color: #059669;
        }
        .status-paid {
            background-color: #10b981;
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        .company-info {
            text-align: left;
        }
        .invoice-number {
            text-align: right;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧾 Facture de Formation</h1>
        <p>Centre de Formation PCMET</p>
    </div>

    <div class="content">
        <div class="invoice-header">
            <div class="company-info">
                <h3>Centre de Formation PCMET</h3>
                <p>
                    Adresse du centre<br>
                    Tunis, Tunisie<br>
                    Tél: +216 XX XXX XXX<br>
                    Email: <EMAIL>
                </p>
            </div>
            <div class="invoice-number">
                <h3>{{ $invoiceData['invoice_number'] }}</h3>
                <p>Date: {{ $invoiceData['invoice_date']->format('d/m/Y') }}</p>
                <p><span class="status-paid">PAYÉE</span></p>
            </div>
        </div>

        <div class="invoice-details">
            <h3>Facturé à :</h3>
            <div class="info-row">
                <span class="label">Nom :</span>
                <span class="value">{{ $invoiceData['student_name'] }}</span>
            </div>
            <div class="info-row">
                <span class="label">Email :</span>
                <span class="value">{{ $invoiceData['student_email'] }}</span>
            </div>
            <div class="info-row">
                <span class="label">Date d'inscription :</span>
                <span class="value">{{ $invoiceData['enrollment_date']->format('d/m/Y') }}</span>
            </div>
        </div>

        <table class="invoice-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Domaine</th>
                    <th>Quantité</th>
                    <th>Prix Unitaire</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ $invoiceData['training_title'] }}</td>
                    <td>{{ $invoiceData['training_domain'] }}</td>
                    <td>1</td>
                    <td>{{ number_format($invoiceData['amount'], 2) }} DT</td>
                    <td>{{ number_format($invoiceData['amount'], 2) }} DT</td>
                </tr>
                <tr class="total-row">
                    <td colspan="4" style="text-align: right;"><strong>Total TTC :</strong></td>
                    <td><span class="amount">{{ number_format($invoiceData['amount'], 2) }} DT</span></td>
                </tr>
            </tbody>
        </table>

        <div class="invoice-details">
            <h3>Détails du Paiement</h3>
            <div class="info-row">
                <span class="label">Méthode de paiement :</span>
                <span class="value">{{ $invoiceData['payment_method'] ?? 'Non spécifiée' }}</span>
            </div>
            <div class="info-row">
                <span class="label">Date de paiement :</span>
                <span class="value">{{ $invoiceData['payment_date'] ? $invoiceData['payment_date']->format('d/m/Y') : 'Non spécifiée' }}</span>
            </div>
            <div class="info-row">
                <span class="label">Statut :</span>
                <span class="value"><span class="status-paid">PAYÉE</span></span>
            </div>
        </div>

        <div style="background-color: #ecfdf5; border: 1px solid #10b981; border-radius: 6px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #059669; margin-top: 0;">🎉 Félicitations !</h3>
            <p>Votre inscription à la formation <strong>{{ $invoiceData['training_title'] }}</strong> a été approuvée et votre paiement confirmé.</p>
            <p>Vous pouvez maintenant accéder à vos cours et commencer votre formation.</p>
        </div>

        <p>
            <a href="{{ route('student.enrollments.index') }}" 
               style="background-color: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Accéder à mes formations
            </a>
        </p>

        <p style="margin-top: 30px; font-size: 14px; color: #6b7280;">
            <strong>Note :</strong> Cette facture confirme votre paiement et votre inscription. 
            Conservez-la pour vos dossiers. En cas de questions, contactez notre service administratif.
        </p>
    </div>

    <div class="footer">
        <p>Centre de Formation PCMET - Système de Gestion des Formations</p>
        <p>Merci de votre confiance !</p>
    </div>
</body>
</html>
