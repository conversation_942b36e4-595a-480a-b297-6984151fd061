<?php

// Connexion à la base de données
$host = 'localhost';
$db   = 'formation_pcmet';
$user = 'root';
$pass = '';
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;dbname=$db;charset=$charset";
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

try {
    $pdo = new PDO($dsn, $user, $pass, $options);
    echo "Connexion à la base de données réussie.\n";
} catch (\PDOException $e) {
    throw new \PDOException($e->getMessage(), (int)$e->getCode());
}

// Options par défaut
$defaultOptions = json_encode([
    'A' => 'Option A',
    'B' => 'Option B',
    'C' => 'Option C',
    'D' => 'Option D'
]);

// Réponses correctes par défaut
$defaultCorrectOptions = json_encode(['A']);

// Récupérer toutes les questions à choix multiple
$stmt = $pdo->query("SELECT * FROM exam_questions WHERE question_type = 'multiple_choice'");
$questions = $stmt->fetchAll();

echo "Nombre de questions à choix multiple trouvées : " . count($questions) . "\n";

$updated = 0;

foreach ($questions as $question) {
    echo "Traitement de la question ID: " . $question['id'] . "\n";

    // Mettre à jour la question
    $stmt = $pdo->prepare("UPDATE exam_questions SET options = ?, correct_options = ? WHERE id = ?");
    $stmt->execute([$defaultOptions, $defaultCorrectOptions, $question['id']]);

    if ($stmt->rowCount() > 0) {
        $updated++;
        echo "Question ID: " . $question['id'] . " mise à jour avec succès.\n";
    } else {
        echo "Question ID: " . $question['id'] . " n'a pas été mise à jour.\n";
    }
}

echo "Mise à jour terminée. $updated questions ont été mises à jour.\n";
