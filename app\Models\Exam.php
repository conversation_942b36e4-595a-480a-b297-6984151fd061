<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Exam extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'exam_type',
        'description',
        'training_session_id',
        'creator_id',
        'duration_minutes',
        'passing_score',
        'is_published',
        'available_from',
        'available_until',
        'status',
        'instructions',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_published' => 'boolean',
        'available_from' => 'datetime',
        'available_until' => 'datetime',
        'passing_score' => 'integer',
        'duration_minutes' => 'integer',
    ];

    /**
     * Relation avec la session de formation
     */
    public function trainingSession()
    {
        return $this->belongsTo(TrainingSession::class);
    }

    /**
     * Relation avec les questions d'examen
     */
    public function questions()
    {
        return $this->hasMany(ExamQuestion::class);
    }

    /**
     * Relation avec les résultats d'examen
     */
    public function examResults()
    {
        return $this->hasMany(ExamResult::class);
    }

    /**
     * Relation avec le créateur de l'examen
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }
}
