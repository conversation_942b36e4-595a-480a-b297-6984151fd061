<template>
  <div class="space-y-4">
    <!-- Affichage des niveaux -->
    <div v-for="level in availableLevels" :key="level" class="mb-6">
      <div class="flex items-center justify-between mb-3 p-3 bg-gray-50 rounded-lg border-l-4"
           :class="getLevelBorderClass(level)">
        <h4 class="font-semibold text-gray-900 flex items-center">
          {{ level }}
          <span v-if="isLevelCompleted(level)" class="ml-2">
            <CheckCircleIcon class="w-5 h-5 text-green-500" />
          </span>
          <span v-else-if="isLevelLocked(level)" class="ml-2">
            <LockClosedIcon class="w-5 h-5 text-gray-400" />
          </span>
        </h4>
        <div class="flex items-center space-x-2">
          <span v-if="isLevelCompleted(level)" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">
            ✓ Terminé
          </span>
          <span v-else-if="isLevelLocked(level)" class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
            🔒 Verrouillé
          </span>
          <span v-else class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
            📚 Disponible
          </span>
        </div>
      </div>

      <!-- Sessions pour ce niveau -->
      <div v-if="props.sessions && props.sessions[level] && props.sessions[level].length > 0" class="space-y-3 ml-4">
        <div
          v-for="sessionData in props.sessions[level]"
          :key="sessionData.session.id"
          class="bg-white rounded-lg p-4 border shadow-sm hover:shadow-md transition-shadow duration-200"
          :class="getSessionCardClass(sessionData.state)"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center mb-2">
                <h5 class="font-medium text-sm" :class="getSessionTitleClass(sessionData.state)">
                  {{ sessionData.session.title }}
                </h5>
                <SessionStateIcon :state="sessionData.state" class="ml-2" />
              </div>

              <div class="flex items-center text-xs text-gray-600 mb-2">
                <CalendarIcon class="w-3 h-3 mr-1" />
                {{ formatDate(sessionData.session.start_date) }}
              </div>

              <p class="text-xs mb-3 p-2 rounded" :class="getReasonClass(sessionData.state)"
                 :style="getReasonStyle(sessionData.state)">
                {{ sessionData.reason }}
              </p>

              <div class="flex items-center justify-between text-xs text-gray-500">
                <div class="flex items-center">
                  <UserIcon class="w-3 h-3 mr-1" />
                  {{ sessionData.session.enrollments_count || 0 }} /
                  {{ sessionData.session.max_participants || 'Illimité' }} participants
                </div>
                <div class="flex items-center">
                  <span class="text-gray-700 font-medium">{{ sessionData.session.price }}€</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Bouton d'action -->
          <div class="mt-4 flex justify-end">
            <Link
              v-if="sessionData.canEnroll"
              :href="route('student.sessions.show', sessionData.session.id)"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-sm"
            >
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              S'inscrire
            </Link>
            <Link
              v-else-if="sessionData.state === 'enrolled' || sessionData.state === 'pending' || sessionData.state === 'rejected'"
              :href="route('student.sessions.show', sessionData.session.id)"
              class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-xs font-medium rounded-lg hover:bg-gray-700 transition-colors duration-200 shadow-sm"
            >
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
              Voir détails
            </Link>
            <span
              v-else-if="sessionData.state === 'completed'"
              class="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 text-xs font-medium rounded-lg"
            >
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
              </svg>
              Terminé
            </span>
            <span
              v-else
              class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-600 text-xs font-medium rounded-lg cursor-not-allowed"
            >
              <svg v-if="sessionData.state === 'locked'" class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
              {{ getActionLabel(sessionData.state) }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- Aucune session disponible pour ce niveau -->
      <div v-else class="text-xs text-gray-500 italic">
        Aucune session disponible pour ce niveau
      </div>
    </div>

    <!-- Aucun niveau disponible -->
    <div v-if="availableLevels.length === 0" class="text-center py-6 text-gray-500">
      <AcademicCapIcon class="w-8 h-8 mx-auto mb-2 text-gray-400" />
      <p class="text-sm">Aucune formation disponible dans ce département</p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { Link } from '@inertiajs/vue3';
import {
  CheckCircleIcon,
  LockClosedIcon,
  UserIcon,
  AcademicCapIcon,
  CalendarIcon
} from '@heroicons/vue/24/outline';
import SessionStateIcon from '@/Components/SessionStateIcon.vue';

// Props
const props = defineProps({
  sessions: Object,
  completedLevels: Array,
  department: String,
});

// Computed
const availableLevels = computed(() => {
  if (!props.sessions || typeof props.sessions !== 'object') {
    return [];
  }
  const levels = ['Niveau 1', 'Niveau 2', 'Niveau 3', 'Niveau 4', 'Niveau 5', 'Niveau 6', 'Niveau 7'];
  return levels.filter(level => props.sessions[level] && props.sessions[level].length > 0);
});

// Méthodes
const isLevelCompleted = (level) => {
  return props.completedLevels.includes(level);
};

const isLevelLocked = (level) => {
  const levelNumber = parseInt(level.replace('Niveau ', ''));
  if (levelNumber <= 1) return false;
  
  for (let i = 1; i < levelNumber; i++) {
    if (!props.completedLevels.includes(`Niveau ${i}`)) {
      return true;
    }
  }
  return false;
};

const formatDate = (dateString) => {
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const getSessionCardClass = (state) => {
  const classes = {
    'available': 'border-blue-200 bg-blue-50',
    'enrolled': 'border-green-200 bg-green-50',
    'pending': 'border-yellow-200 bg-yellow-50',
    'completed': 'border-purple-200 bg-purple-50',
    'rejected': 'border-red-200 bg-red-50',
    'locked': 'border-gray-200 bg-gray-100 opacity-60',
    'full': 'border-orange-200 bg-orange-50',
    'started': 'border-gray-200 bg-gray-100'
  };
  return classes[state] || 'border-gray-200';
};

const getSessionTitleClass = (state) => {
  const classes = {
    'available': 'text-blue-900',
    'enrolled': 'text-green-900',
    'pending': 'text-yellow-900',
    'completed': 'text-purple-900',
    'rejected': 'text-red-900',
    'locked': 'text-gray-500',
    'full': 'text-orange-900',
    'started': 'text-gray-600'
  };
  return classes[state] || 'text-gray-900';
};

const getReasonClass = (state) => {
  const classes = {
    'available': 'text-blue-700',
    'enrolled': 'text-green-700',
    'pending': 'text-yellow-700',
    'completed': 'text-purple-700',
    'rejected': 'text-red-700',
    'locked': 'text-gray-500',
    'full': 'text-orange-700',
    'started': 'text-gray-600'
  };
  return classes[state] || 'text-gray-600';
};

const getActionLabel = (state) => {
  const labels = {
    'locked': 'Verrouillé',
    'full': 'Complet',
    'started': 'Commencé',
    'completed': 'Terminé',
    'rejected': 'Refusé'
  };
  return labels[state] || 'Non disponible';
};

const getLevelBorderClass = (level) => {
  if (isLevelCompleted(level)) {
    return 'border-green-400';
  } else if (isLevelLocked(level)) {
    return 'border-gray-300';
  } else {
    return 'border-blue-400';
  }
};

const getReasonStyle = (state) => {
  const styles = {
    'available': 'background-color: #eff6ff; color: #1e40af;',
    'enrolled': 'background-color: #f0fdf4; color: #166534;',
    'pending': 'background-color: #fefce8; color: #a16207;',
    'completed': 'background-color: #faf5ff; color: #7c2d12;',
    'rejected': 'background-color: #fef2f2; color: #dc2626;',
    'locked': 'background-color: #f9fafb; color: #6b7280;',
    'full': 'background-color: #fff7ed; color: #c2410c;',
    'started': 'background-color: #f9fafb; color: #6b7280;'
  };
  return styles[state] || 'background-color: #f9fafb; color: #6b7280;';
};
</script>
