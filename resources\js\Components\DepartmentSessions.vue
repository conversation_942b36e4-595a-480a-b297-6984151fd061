<template>
  <div class="space-y-4">
    <!-- Affichage des niveaux -->
    <div v-for="level in availableLevels" :key="level" class="border-l-4 border-gray-200 pl-4">
      <div class="flex items-center justify-between mb-2">
        <h4 class="font-semibold text-gray-900 flex items-center">
          {{ level }}
          <span v-if="isLevelCompleted(level)" class="ml-2">
            <CheckCircleIcon class="w-5 h-5 text-green-500" />
          </span>
          <span v-else-if="isLevelLocked(level)" class="ml-2">
            <LockClosedIcon class="w-5 h-5 text-gray-400" />
          </span>
        </h4>
        <span v-if="isLevelCompleted(level)" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
          Terminé
        </span>
        <span v-else-if="isLevelLocked(level)" class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
          Verrouillé
        </span>
      </div>

      <!-- Sessions pour ce niveau -->
      <div v-if="props.sessions && props.sessions[level] && props.sessions[level].length > 0" class="space-y-2">
        <div
          v-for="sessionData in props.sessions[level]"
          :key="sessionData.session.id"
          class="bg-gray-50 rounded-lg p-3 border"
          :class="getSessionCardClass(sessionData.state)"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h5 class="font-medium text-sm" :class="getSessionTitleClass(sessionData.state)">
                {{ sessionData.session.title }}
              </h5>
              <p class="text-xs text-gray-600 mt-1">
                {{ formatDate(sessionData.session.start_date) }}
              </p>
              <p class="text-xs mt-1" :class="getReasonClass(sessionData.state)">
                {{ sessionData.reason }}
              </p>
              <div class="flex items-center mt-2 text-xs text-gray-500">
                <UserIcon class="w-3 h-3 mr-1" />
                {{ sessionData.session.enrollments_count || 0 }} / 
                {{ sessionData.session.max_participants || 'Illimité' }}
              </div>
            </div>
            <div class="ml-3">
              <SessionStateIcon :state="sessionData.state" />
            </div>
          </div>
          
          <!-- Bouton d'action -->
          <div class="mt-3 flex justify-end">
            <Link 
              v-if="sessionData.canEnroll" 
              :href="route('student.sessions.show', sessionData.session.id)"
              class="text-xs bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 transition-colors"
            >
              S'inscrire
            </Link>
            <Link 
              v-else-if="sessionData.state === 'enrolled' || sessionData.state === 'pending'"
              :href="route('student.sessions.show', sessionData.session.id)"
              class="text-xs bg-gray-600 text-white px-3 py-1 rounded-md hover:bg-gray-700 transition-colors"
            >
              Voir détails
            </Link>
            <span 
              v-else
              class="text-xs bg-gray-300 text-gray-600 px-3 py-1 rounded-md cursor-not-allowed"
            >
              {{ getActionLabel(sessionData.state) }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- Aucune session disponible pour ce niveau -->
      <div v-else class="text-xs text-gray-500 italic">
        Aucune session disponible pour ce niveau
      </div>
    </div>

    <!-- Aucun niveau disponible -->
    <div v-if="availableLevels.length === 0" class="text-center py-6 text-gray-500">
      <AcademicCapIcon class="w-8 h-8 mx-auto mb-2 text-gray-400" />
      <p class="text-sm">Aucune formation disponible dans ce département</p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { Link } from '@inertiajs/vue3';
import { 
  CheckCircleIcon, 
  LockClosedIcon, 
  UserIcon,
  AcademicCapIcon 
} from '@heroicons/vue/24/outline';
import SessionStateIcon from '@/Components/SessionStateIcon.vue';

// Props
const props = defineProps({
  sessions: Object,
  completedLevels: Array,
  department: String,
});

// Computed
const availableLevels = computed(() => {
  if (!props.sessions || typeof props.sessions !== 'object') {
    return [];
  }
  const levels = ['Niveau 1', 'Niveau 2', 'Niveau 3', 'Niveau 4', 'Niveau 5', 'Niveau 6', 'Niveau 7'];
  return levels.filter(level => props.sessions[level] && props.sessions[level].length > 0);
});

// Méthodes
const isLevelCompleted = (level) => {
  return props.completedLevels.includes(level);
};

const isLevelLocked = (level) => {
  const levelNumber = parseInt(level.replace('Niveau ', ''));
  if (levelNumber <= 1) return false;
  
  for (let i = 1; i < levelNumber; i++) {
    if (!props.completedLevels.includes(`Niveau ${i}`)) {
      return true;
    }
  }
  return false;
};

const formatDate = (dateString) => {
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const getSessionCardClass = (state) => {
  const classes = {
    'available': 'border-blue-200 bg-blue-50',
    'enrolled': 'border-green-200 bg-green-50',
    'pending': 'border-yellow-200 bg-yellow-50',
    'completed': 'border-purple-200 bg-purple-50',
    'locked': 'border-gray-200 bg-gray-100 opacity-60',
    'full': 'border-orange-200 bg-orange-50',
    'started': 'border-gray-200 bg-gray-100'
  };
  return classes[state] || 'border-gray-200';
};

const getSessionTitleClass = (state) => {
  const classes = {
    'available': 'text-blue-900',
    'enrolled': 'text-green-900',
    'pending': 'text-yellow-900',
    'completed': 'text-purple-900',
    'locked': 'text-gray-500',
    'full': 'text-orange-900',
    'started': 'text-gray-600'
  };
  return classes[state] || 'text-gray-900';
};

const getReasonClass = (state) => {
  const classes = {
    'available': 'text-blue-700',
    'enrolled': 'text-green-700',
    'pending': 'text-yellow-700',
    'completed': 'text-purple-700',
    'locked': 'text-gray-500',
    'full': 'text-orange-700',
    'started': 'text-gray-600'
  };
  return classes[state] || 'text-gray-600';
};

const getActionLabel = (state) => {
  const labels = {
    'locked': 'Verrouillé',
    'full': 'Complet',
    'started': 'Commencé',
    'completed': 'Terminé'
  };
  return labels[state] || 'Non disponible';
};
</script>
