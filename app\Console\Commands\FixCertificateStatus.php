<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Certificate;
use App\Models\ExamResult;

class FixCertificateStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'certificates:fix-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix certificate status from active to issued';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Fixing certificate status issues...');
        
        // Fix certificates with 'active' status to 'issued'
        $activeCertificates = Certificate::where('status', 'active')
            ->whereNotNull('issued_at')
            ->with(['user', 'examResult'])
            ->get();
        
        if ($activeCertificates->count() > 0) {
            $this->line("Found {$activeCertificates->count()} certificates with 'active' status that should be 'issued':");
            
            foreach ($activeCertificates as $cert) {
                $this->line("- Certificate {$cert->certificate_number} (User: {$cert->user->name}) - Status: {$cert->status}");
                
                $cert->update(['status' => 'issued']);
                $this->info("  ✅ Updated to 'issued' status");
            }
        } else {
            $this->line("✅ No certificates with incorrect 'active' status found");
        }
        
        // Check for passed exam results without certificates
        $passedResultsWithoutCerts = ExamResult::where('passed', true)
            ->whereDoesntHave('certificate')
            ->with(['exam', 'user', 'enrollment'])
            ->get();
        
        if ($passedResultsWithoutCerts->count() > 0) {
            $this->line("Found {$passedResultsWithoutCerts->count()} passed exam results without certificates:");
            
            foreach ($passedResultsWithoutCerts as $result) {
                $this->line("- User: {$result->user->name}, Exam: {$result->exam->title}, Score: {$result->score}%");
                
                if ($result->enrollment && ($result->exam->exam_type === 'certification' || $result->exam->exam_type === 'certification_rattrapage')) {
                    $certificateNumber = Certificate::generateCertificateNumber($result->enrollment->id);
                    
                    $certificate = Certificate::create([
                        'enrollment_id' => $result->enrollment->id,
                        'user_id' => $result->user_id,
                        'training_session_id' => $result->exam->training_session_id,
                        'exam_result_id' => $result->id,
                        'certificate_number' => $certificateNumber,
                        'status' => 'issued',
                        'issued_at' => now(),
                        'issue_date' => now(),
                    ]);
                    
                    $this->info("  ✅ Created certificate {$certificate->certificate_number}");
                }
            }
        } else {
            $this->line("✅ All passed exam results have certificates");
        }
        
        $this->info('✅ Certificate status fix completed!');
        
        return Command::SUCCESS;
    }
}
