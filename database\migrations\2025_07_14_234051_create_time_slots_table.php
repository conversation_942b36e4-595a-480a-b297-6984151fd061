<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('time_slots', function (Blueprint $table) {
            $table->id();
            $table->foreignId('training_session_id')->constrained()->onDelete('cascade');
            $table->string('name')->nullable(); // Ex: "Matin", "Après-midi", "Soirée"
            $table->time('start_time'); // Heure de début
            $table->time('end_time'); // Heure de fin
            $table->integer('max_participants')->nullable(); // Limite par créneau (optionnel)
            $table->boolean('active')->default(true);
            $table->timestamps();

            // Index pour optimiser les requêtes
            $table->index(['training_session_id', 'active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('time_slots');
    }
};
