# Guide de Résolution du Problème de Base de Données

## Problème Identifié
L'erreur indique que la table `training_sessions` n'existe pas, mais les migrations ne peuvent pas s'exécuter à cause d'un conflit avec la table `announcements` qui existe déjà.

## Solutions Proposées

### Solution 1 : Exécution du Script SQL (Recommandée)

1. **Ouvrir phpMyAdmin ou votre client MySQL**
2. **Sélectionner la base de données** `formation_pcmet`
3. **Exécuter le script SQL** `database_fix.sql` qui :
   - Marque les migrations problématiques comme exécutées
   - Crée les tables manquantes
   - Insère des données de test
   - Crée un utilisateur de test

### Solution 2 : Command<PERSON> (Alternative)

Si vous préférez utiliser <PERSON>, essayez ces commandes dans l'ordre :

```bash
# 1. Marquer manuellement la migration announcements comme exécutée
php artisan tinker
DB::table('migrations')->insert(['migration' => '2023_06_15_000001_create_announcements_table', 'batch' => 1]);
exit

# 2. Essayer de relancer les migrations
php artisan migrate

# 3. Si ça ne marche pas, forcer les migrations spécifiques
php artisan migrate --path=database/migrations/2025_04_22_134958_create_training_domains_table.php
php artisan migrate --path=database/migrations/2025_04_22_135013_create_training_sessions_table.php
php artisan migrate --path=database/migrations/2025_04_22_135532_create_enrollments_table.php
```

### Solution 3 : Reset Complet (Attention : Perte de données)

⚠️ **ATTENTION : Cette solution supprime toutes les données existantes**

```bash
# 1. Reset complet de la base de données
php artisan migrate:fresh

# 2. Exécuter le seeder de test
php artisan db:seed --class=TestDataSeeder
```

## Vérification du Succès

Après avoir appliqué une des solutions, vérifiez que tout fonctionne :

### 1. Vérifier les tables
```sql
SHOW TABLES;
-- Doit inclure : training_domains, training_sessions, enrollments
```

### 2. Vérifier les données de test
```sql
SELECT COUNT(*) FROM training_sessions;
-- Doit retourner au moins 6 sessions

SELECT * FROM training_sessions WHERE department = 'Secourisme';
-- Doit afficher les sessions de secourisme
```

### 3. Tester l'application
- Aller sur http://localhost:8000/
- Vérifier que la page d'accueil affiche les départements avec sessions
- Se connecter avec `<EMAIL>` / `password`
- Aller sur http://localhost:8000/student/sessions
- Vérifier le système de verrouillage

## Données de Test Créées

### Utilisateur de test :
- **Email :** <EMAIL>
- **Mot de passe :** password
- **Rôle :** student

### Sessions créées :
1. **Secourisme Niveau 1** - Déverrouillé
2. **Secourisme Niveau 2** - Déverrouillé (si Niveau 1 complété)
3. **Secourisme Niveau 3** - Verrouillé
4. **Langue Niveau 1** - Déverrouillé
5. **Langue Niveau 2** - Verrouillé
6. **Formation à la carte Niveau 1** - Déverrouillé

### Progression de test :
- L'utilisateur test a complété "Secourisme Niveau 1"
- Cela déverrouille "Secourisme Niveau 2"
- Les autres départements restent au Niveau 1

## Dépannage

### Si les tables existent mais sont vides :
```bash
php artisan db:seed --class=TestDataSeeder
```

### Si les migrations sont toujours bloquées :
```sql
-- Supprimer les entrées problématiques
DELETE FROM migrations WHERE migration LIKE '%announcements%';
-- Puis relancer php artisan migrate
```

### Si l'erreur persiste :
1. Vérifier la configuration de la base de données dans `.env`
2. Vérifier que MySQL est démarré
3. Vérifier les permissions de la base de données
4. Consulter les logs Laravel : `storage/logs/laravel.log`

## Test Final

Une fois la base de données corrigée :

1. **Page d'accueil :** http://localhost:8000/
   - ✅ Section formations supprimée
   - ✅ Départements affichent les sessions publiques

2. **Sessions étudiants :** http://localhost:8000/student/sessions
   - ✅ Système de verrouillage actif
   - ✅ Messages d'état clairs
   - ✅ Progression affichée

3. **Fonctionnalités :**
   - ✅ Filtres fonctionnent
   - ✅ Recherche fonctionne
   - ✅ Design responsive

## Support

Si le problème persiste après avoir essayé ces solutions :
1. Vérifier les logs d'erreur
2. Tester avec une base de données vide
3. Vérifier la version de PHP et MySQL
4. Consulter la documentation Laravel sur les migrations
