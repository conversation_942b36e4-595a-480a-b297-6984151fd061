<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Enrollment;
use App\Models\User;
use App\Services\PaymentEmailService;
use Illuminate\Support\Facades\Mail;

class TestPaymentEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:payment-emails {enrollment_id?}';

    /**
     * The console description of the command.
     *
     * @var string
     */
    protected $description = 'Test payment email functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Payment Email System...');

        // Get enrollment ID from argument or use the first available
        $enrollmentId = $this->argument('enrollment_id');
        
        if ($enrollmentId) {
            $enrollment = Enrollment::find($enrollmentId);
            if (!$enrollment) {
                $this->error("Enrollment with ID {$enrollmentId} not found.");
                return 1;
            }
        } else {
            $enrollment = Enrollment::with(['user', 'trainingSession.trainingDomain'])->first();
            if (!$enrollment) {
                $this->error('No enrollments found in the database.');
                return 1;
            }
        }

        $this->info("Using enrollment ID: {$enrollment->id}");
        $this->info("Student: {$enrollment->user->name}");
        $this->info("Training: {$enrollment->trainingSession->title}");

        try {
            // Test 1: Payment Receipt Email
            $this->info("\n--- Testing Payment Receipt Email ---");
            Mail::fake();
            
            $emailService = new PaymentEmailService();
            $emailService->sendPaymentReceiptToAdmin($enrollment);
            
            $this->info('✅ Payment receipt email test completed (using Mail::fake())');

            // Test 2: Payment Proof Notification
            $this->info("\n--- Testing Payment Proof Notification ---");
            $emailService->notifyAdminsOfPaymentProof($enrollment);
            
            $this->info('✅ Payment proof notification test completed (using Mail::fake())');

            // Test 3: Invoice Email (only if conditions are met)
            $this->info("\n--- Testing Invoice Email ---");
            if ($enrollment->status === 'approved' && $enrollment->payment_status === 'paid') {
                $emailService->sendInvoiceToStudent($enrollment);
                $this->info('✅ Invoice email test completed (using Mail::fake())');
            } else {
                $this->warn("⚠️  Invoice not sent - conditions not met:");
                $this->warn("   Status: {$enrollment->status} (needs: approved)");
                $this->warn("   Payment: {$enrollment->payment_status} (needs: paid)");
            }

            // Check admin users
            $adminCount = User::where('role', 'admin')->count();
            $this->info("\n--- System Information ---");
            $this->info("Admin users found: {$adminCount}");
            
            if ($adminCount === 0) {
                $this->warn("⚠️  No admin users found. Payment receipts won't be sent.");
            }

            $this->info("\n✅ All email tests completed successfully!");
            $this->info("Note: Emails were faked for testing. In production, they will be sent to actual recipients.");

        } catch (\Exception $e) {
            $this->error("❌ Error during email testing: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }
}
