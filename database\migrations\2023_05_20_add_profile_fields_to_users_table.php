<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Champs obligatoires pour l'inscription aux sessions
            $table->date('birth_date')->nullable()->after('name');
            $table->string('id_card_number')->nullable()->after('phone');
            $table->string('profession')->nullable()->after('id_card_number');
            $table->string('company')->nullable()->after('profession');
            
            // Champ pour la découverte de la formation
            $table->enum('discovery_source', ['facebook', 'instagram', 'tiktok', 'word_of_mouth', 'other'])->nullable()->after('company');
            $table->string('discovery_source_other')->nullable()->after('discovery_source');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'birth_date',
                'id_card_number',
                'profession',
                'company',
                'discovery_source',
                'discovery_source_other'
            ]);
        });
    }
};
