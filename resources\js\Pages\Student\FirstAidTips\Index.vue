<template>
  <Head title="Conseils en premiers secours" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Conseils en premiers secours
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <div class="mb-6">
              <h3 class="text-lg font-semibold mb-2">Accès libre aux conseils en premiers secours</h3>
              <p class="text-gray-600">
                Consultez gratuitement nos conseils et ressources en premiers secours. 
                Ces contenus sont accessibles à tous les apprenants inscrits.
              </p>
            </div>

            <div v-if="firstAidTips.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div
                v-for="tip in firstAidTips"
                :key="tip.id"
                class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow"
              >
                <div class="p-6">
                  <div class="flex items-center mb-4">
                    <div class="flex-shrink-0">
                      <HeartIcon class="h-8 w-8 text-red-500" />
                    </div>
                    <div class="ml-3">
                      <h4 class="text-lg font-medium text-gray-900">{{ tip.title }}</h4>
                    </div>
                  </div>
                  
                  <p v-if="tip.description" class="text-gray-600 text-sm mb-4 line-clamp-3">
                    {{ tip.description }}
                  </p>
                  
                  <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                      {{ tip.materials ? tip.materials.length : 0 }} matériel(s)
                    </div>
                    <Link
                      :href="route('student.first-aid-tips.show', tip.id)"
                      class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      Consulter
                    </Link>
                  </div>
                  
                  <!-- Aperçu des types de matériels -->
                  <div v-if="tip.materials && tip.materials.length > 0" class="mt-4 flex flex-wrap gap-1">
                    <span
                      v-for="material in tip.materials.slice(0, 3)"
                      :key="material.id"
                      :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        getTypeColor(material.type)
                      ]"
                    >
                      {{ getTypeLabel(material.type) }}
                    </span>
                    <span
                      v-if="tip.materials.length > 3"
                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"
                    >
                      +{{ tip.materials.length - 3 }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="text-center py-12">
              <HeartIcon class="mx-auto h-12 w-12 text-gray-400" />
              <h3 class="mt-2 text-sm font-medium text-gray-900">Aucun conseil disponible</h3>
              <p class="mt-1 text-sm text-gray-500">
                Il n'y a actuellement aucun conseil en premiers secours disponible.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { HeartIcon } from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  firstAidTips: Array,
});

// Méthodes utilitaires
const getTypeLabel = (type) => {
  const labels = {
    text: 'Texte',
    pdf: 'PDF',
    video: 'Vidéo',
    audio: 'Audio',
    image: 'Image',
    archive: 'Archive',
    gallery: 'Galerie',
    embed_video: 'Vidéo externe'
  };
  return labels[type] || type;
};

const getTypeColor = (type) => {
  const colors = {
    text: 'bg-gray-100 text-gray-800',
    pdf: 'bg-red-100 text-red-800',
    video: 'bg-blue-100 text-blue-800',
    audio: 'bg-green-100 text-green-800',
    image: 'bg-yellow-100 text-yellow-800',
    archive: 'bg-purple-100 text-purple-800',
    gallery: 'bg-pink-100 text-pink-800',
    embed_video: 'bg-indigo-100 text-indigo-800'
  };
  return colors[type] || 'bg-gray-100 text-gray-800';
};
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
