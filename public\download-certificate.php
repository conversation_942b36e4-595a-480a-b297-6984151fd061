<?php
// Initialiser l'application Laravel sans passer par le middleware Inertia
require __DIR__.'/../vendor/autoload.php';
$app = require_once __DIR__.'/../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

// Vérifier si l'utilisateur est authentifié
if (!auth()->check()) {
    header('HTTP/1.1 401 Unauthorized');
    echo 'Vous devez être connecté pour télécharger un certificat.';
    exit;
}

// Récupérer l'ID du certificat
$id = $_GET['id'] ?? null;
if (!$id) {
    header('HTTP/1.1 400 Bad Request');
    echo 'ID du certificat manquant.';
    exit;
}

try {
    // Récupérer le certificat
    $certificate = \App\Models\Certificate::findOrFail($id);
    
    // Vérifier si l'utilisateur a le droit d'accéder à ce certificat
    if (!auth()->user()->hasRole('admin') && auth()->id() !== $certificate->user_id) {
        header('HTTP/1.1 403 Forbidden');
        echo 'Vous n\'êtes pas autorisé à télécharger ce certificat.';
        exit;
    }
    
    // Vérifier si le PDF existe
    $pdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';
    $filename = 'certificat_' . $certificate->certificate_number . '.pdf';
    
    if (!\Illuminate\Support\Facades\Storage::disk('public')->exists($pdfPath)) {
        // Générer le PDF s'il n'existe pas
        $certificateController = new \App\Http\Controllers\Admin\CertificateController();
        $certificateController->generatePdf($certificate);
        
        // Vérifier à nouveau si le PDF a été créé
        if (!\Illuminate\Support\Facades\Storage::disk('public')->exists($pdfPath)) {
            throw new \Exception("Impossible de générer le PDF");
        }
    }
    
    // Obtenir le chemin physique complet du fichier
    $fullPath = \Illuminate\Support\Facades\Storage::disk('public')->path($pdfPath);
    
    // Vérifier que le fichier existe physiquement
    if (!file_exists($fullPath)) {
        throw new \Exception("Le fichier PDF n'existe pas sur le disque: " . $fullPath);
    }
    
    // Lire le contenu du fichier
    $content = file_get_contents($fullPath);
    
    // Envoyer les en-têtes pour forcer le téléchargement
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . filesize($fullPath));
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Envoyer le contenu du fichier
    echo $content;
    exit;
    
} catch (\Exception $e) {
    // En cas d'erreur, enregistrer l'erreur dans les logs
    \Illuminate\Support\Facades\Log::error('Erreur lors du téléchargement direct du PDF: ' . $e->getMessage());
    \Illuminate\Support\Facades\Log::error('Trace: ' . $e->getTraceAsString());
    
    // Afficher un message d'erreur
    header('HTTP/1.1 500 Internal Server Error');
    echo 'Une erreur est survenue lors du téléchargement du certificat: ' . $e->getMessage();
    exit;
}
