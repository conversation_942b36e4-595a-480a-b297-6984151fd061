<?php

namespace App\Observers;

use App\Models\Certificate;
use App\Services\NotificationService;
use Illuminate\Support\Facades\Log;

class CertificateObserver
{
    /**
     * Handle the Certificate "created" event.
     */
    public function created(Certificate $certificate): void
    {
        // Send notification when certificate is created (draft status)
        if ($certificate->status === 'draft') {
            // Don't notify for draft certificates
            return;
        }

        $this->sendCertificateNotification($certificate);
    }

    /**
     * Handle the Certificate "updated" event.
     */
    public function updated(Certificate $certificate): void
    {
        // Send notification when certificate status changes to active or issued
        if ($certificate->isDirty('status') && in_array($certificate->status, ['active', 'issued'])) {
            $this->sendCertificateNotification($certificate);
        }
    }

    /**
     * Handle the Certificate "deleted" event.
     */
    public function deleted(Certificate $certificate): void
    {
        //
    }

    /**
     * Handle the Certificate "restored" event.
     */
    public function restored(Certificate $certificate): void
    {
        //
    }

    /**
     * Handle the Certificate "force deleted" event.
     */
    public function forceDeleted(Certificate $certificate): void
    {
        //
    }

    /**
     * Send certificate notification
     */
    private function sendCertificateNotification(Certificate $certificate): void
    {
        try {
            $notificationService = app(NotificationService::class);
            $notificationService->notifyCertificateGenerated($certificate);
        } catch (\Exception $e) {
            Log::error('Failed to send certificate notification: ' . $e->getMessage());
        }
    }
}
