<template>
  <div class="space-y-12">
    <!-- Département Secourisme -->
    <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
      <div class="bg-gradient-to-br from-red-500 to-red-700 p-8 relative overflow-hidden">
        <div class="absolute inset-0 bg-black/10"></div>
        <div class="relative z-10">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
              <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center">
                <HeartIcon class="w-10 h-10 text-white" />
              </div>
              <div>
                <h3 class="text-3xl font-bold text-white mb-2">Secourisme</h3>
                <p class="text-red-100">Formation aux premiers secours et techniques de sauvetage</p>
              </div>
            </div>
            <div class="text-right text-white">
              <div class="text-2xl font-bold">{{ getDepartmentSessionCount('Secourisme') }}</div>
              <div class="text-red-100 text-sm">Sessions disponibles</div>
            </div>
          </div>
        </div>
        <!-- Decorative elements -->
        <div class="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full"></div>
        <div class="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full"></div>
      </div>

      <div class="p-8">
        <div v-if="getDepartmentSessions('Secourisme').length > 0" class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <div v-for="session in getDepartmentSessions('Secourisme').slice(0, 3)" :key="session.id"
               class="group bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-all duration-200 hover:shadow-md">
            <!-- Image de la session -->
            <div class="relative h-32 mb-4 overflow-hidden rounded-lg">
              <img
                v-if="session.image"
                :src="getSessionImageUrl(session.image)"
                :alt="session.title"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                @error="handleImageError"
              />
              <div v-else class="w-full h-full bg-gradient-to-br from-red-200 to-red-300 flex items-center justify-center">
                <HeartIcon class="w-8 h-8 text-red-600" />
              </div>
              <!-- Badge du niveau -->
              <div v-if="session.level" class="absolute top-2 right-2 bg-white text-red-600 px-2 py-1 rounded-full text-xs font-semibold shadow">
                {{ session.level }}
              </div>
            </div>

            <h4 class="font-bold text-gray-900 mb-2 group-hover:text-red-600 transition-colors duration-200">{{ session.title }}</h4>

            <!-- Date -->
            <div class="flex items-center text-sm text-gray-600 mb-3">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              {{ formatDate(session.start_date) }}
            </div>

            <!-- Prix -->
            <div class="flex items-center justify-between">
              <span class="text-lg font-bold text-red-600">{{ session.price ? session.price + ' DT' : 'Gratuit' }}</span>
              <Link :href="route('sessions.show', session.id)"
                    class="text-sm text-red-600 hover:text-red-700 font-medium flex items-center gap-1">
                Voir détails
                <ArrowRightIcon class="w-3 h-3" />
              </Link>
            </div>
          </div>
        </div>

        <div v-else class="text-center py-8">
          <p class="text-gray-500">Aucune session disponible pour le moment</p>
        </div>

        <div v-if="getDepartmentSessions('Secourisme').length > 3" class="text-center mt-6">
          <Link v-if="canLogin" :href="route('student.departments.show', 'Secourisme')"
                class="inline-flex items-center gap-2 text-red-600 hover:text-red-700 font-medium">
            Voir toutes les sessions ({{ getDepartmentSessions('Secourisme').length }})
            <ArrowRightIcon class="w-4 h-4" />
          </Link>
          <button v-else @click="scrollToSection('contact')"
                  class="inline-flex items-center gap-2 text-red-600 hover:text-red-700 font-medium">
            Voir toutes les sessions ({{ getDepartmentSessions('Secourisme').length }})
            <ArrowRightIcon class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- Département Langue -->
    <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
      <div class="bg-gradient-to-br from-blue-500 to-blue-700 p-8 relative overflow-hidden">
        <div class="absolute inset-0 bg-black/10"></div>
        <div class="relative z-10">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
              <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center">
                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                </svg>
              </div>
              <div>
                <h3 class="text-3xl font-bold text-white mb-2">Langue</h3>
                <p class="text-blue-100">Formation linguistique et communication internationale</p>
              </div>
            </div>
            <div class="text-right text-white">
              <div class="text-2xl font-bold">{{ getDepartmentSessionCount('Langue') }}</div>
              <div class="text-blue-100 text-sm">Sessions disponibles</div>
            </div>
          </div>
        </div>
        <!-- Decorative elements -->
        <div class="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full"></div>
        <div class="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full"></div>
      </div>

      <div class="p-8">
        <div v-if="getDepartmentSessions('Langue').length > 0" class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <div v-for="session in getDepartmentSessions('Langue').slice(0, 3)" :key="session.id"
               class="group bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-all duration-200 hover:shadow-md">
            <!-- Image de la session -->
            <div class="relative h-32 mb-4 overflow-hidden rounded-lg">
              <img
                v-if="session.image"
                :src="getSessionImageUrl(session.image)"
                :alt="session.title"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                @error="handleImageError"
              />
              <div v-else class="w-full h-full bg-gradient-to-br from-blue-200 to-blue-300 flex items-center justify-center">
                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                </svg>
              </div>
              <!-- Badge du niveau -->
              <div v-if="session.level" class="absolute top-2 right-2 bg-white text-blue-600 px-2 py-1 rounded-full text-xs font-semibold shadow">
                {{ session.level }}
              </div>
            </div>

            <h4 class="font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-200">{{ session.title }}</h4>

            <!-- Date -->
            <div class="flex items-center text-sm text-gray-600 mb-3">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              {{ formatDate(session.start_date) }}
            </div>

            <!-- Prix -->
            <div class="flex items-center justify-between">
              <span class="text-lg font-bold text-blue-600">{{ session.price ? session.price + ' DT' : 'Gratuit' }}</span>
              <Link :href="route('sessions.show', session.id)"
                    class="text-sm text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1">
                Voir détails
                <ArrowRightIcon class="w-3 h-3" />
              </Link>
            </div>
          </div>
        </div>

        <div v-else class="text-center py-8">
          <p class="text-gray-500">Aucune session disponible pour le moment</p>
        </div>

        <div v-if="getDepartmentSessions('Langue').length > 3" class="text-center mt-6">
          <Link v-if="canLogin" :href="route('student.departments.show', 'Langue')"
                class="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium">
            Voir toutes les sessions ({{ getDepartmentSessions('Langue').length }})
            <ArrowRightIcon class="w-4 h-4" />
          </Link>
          <button v-else @click="scrollToSection('contact')"
                  class="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium">
            Voir toutes les sessions ({{ getDepartmentSessions('Langue').length }})
            <ArrowRightIcon class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- Département Formation à la carte -->
    <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
      <div class="bg-gradient-to-br from-green-500 to-green-700 p-8 relative overflow-hidden">
        <div class="absolute inset-0 bg-black/10"></div>
        <div class="relative z-10">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
              <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center">
                <AcademicCapIcon class="w-10 h-10 text-white" />
              </div>
              <div>
                <h3 class="text-3xl font-bold text-white mb-2">Formation à la carte</h3>
                <p class="text-green-100">Formations personnalisées selon vos besoins spécifiques</p>
              </div>
            </div>
            <div class="text-right text-white">
              <div class="text-2xl font-bold">{{ getDepartmentSessionCount('Formation à la carte') }}</div>
              <div class="text-green-100 text-sm">Sessions disponibles</div>
            </div>
          </div>
        </div>
        <!-- Decorative elements -->
        <div class="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full"></div>
        <div class="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full"></div>
      </div>

      <div class="p-8">
        <div v-if="getDepartmentSessions('Formation à la carte').length > 0" class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <div v-for="session in getDepartmentSessions('Formation à la carte').slice(0, 3)" :key="session.id"
               class="group bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-all duration-200 hover:shadow-md">
            <!-- Image de la session -->
            <div class="relative h-32 mb-4 overflow-hidden rounded-lg">
              <img
                v-if="session.image"
                :src="getSessionImageUrl(session.image)"
                :alt="session.title"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                @error="handleImageError"
              />
              <div v-else class="w-full h-full bg-gradient-to-br from-green-200 to-green-300 flex items-center justify-center">
                <AcademicCapIcon class="w-8 h-8 text-green-600" />
              </div>
              <!-- Badge du niveau -->
              <div v-if="session.level" class="absolute top-2 right-2 bg-white text-green-600 px-2 py-1 rounded-full text-xs font-semibold shadow">
                {{ session.level }}
              </div>
            </div>

            <h4 class="font-bold text-gray-900 mb-2 group-hover:text-green-600 transition-colors duration-200">{{ session.title }}</h4>

            <!-- Date -->
            <div class="flex items-center text-sm text-gray-600 mb-3">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              {{ formatDate(session.start_date) }}
            </div>

            <!-- Prix -->
            <div class="flex items-center justify-between">
              <span class="text-lg font-bold text-green-600">{{ session.price ? session.price + ' DT' : 'Gratuit' }}</span>
              <Link :href="route('sessions.show', session.id)"
                    class="text-sm text-green-600 hover:text-green-700 font-medium flex items-center gap-1">
                Voir détails
                <ArrowRightIcon class="w-3 h-3" />
              </Link>
            </div>
          </div>
        </div>

        <div v-else class="text-center py-8">
          <p class="text-gray-500">Aucune session disponible pour le moment</p>
        </div>

        <div v-if="getDepartmentSessions('Formation à la carte').length > 3" class="text-center mt-6">
          <Link v-if="canLogin" :href="route('student.departments.show', 'Formation à la carte')"
                class="inline-flex items-center gap-2 text-green-600 hover:text-green-700 font-medium">
            Voir toutes les sessions ({{ getDepartmentSessions('Formation à la carte').length }})
            <ArrowRightIcon class="w-4 h-4" />
          </Link>
          <button v-else @click="scrollToSection('contact')"
                  class="inline-flex items-center gap-2 text-green-600 hover:text-green-700 font-medium">
            Voir toutes les sessions ({{ getDepartmentSessions('Formation à la carte').length }})
            <ArrowRightIcon class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Link } from '@inertiajs/vue3';
import {
  HeartIcon,
  AcademicCapIcon,
  ArrowRightIcon,
  CheckIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  sessions: Array,
  canLogin: Boolean,
  scrollToSection: Function,
});

// Méthodes
const getDepartmentSessionCount = (department) => {
  if (!props.sessions) return 0;
  return props.sessions.filter(session => session.department === department).length;
};

const getDepartmentSessions = (department) => {
  if (!props.sessions) return [];
  return props.sessions.filter(session =>
    session.department === department &&
    new Date(session.start_date) > new Date()
  ).sort((a, b) => new Date(a.start_date) - new Date(b.start_date));
};

const getSessionImageUrl = (imagePath) => {
  if (!imagePath) return null;
  return imagePath.startsWith('http') ? imagePath : `/storage/${imagePath}`;
};

const handleImageError = (event) => {
  event.target.style.display = 'none';
  event.target.nextElementSibling?.style.display = 'flex';
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });
};
</script>
