<template>
  <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
    <!-- Département Secourisme -->
    <div class="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
      <div class="bg-gradient-to-br from-red-500 to-red-700 p-8 relative overflow-hidden">
        <div class="absolute inset-0 bg-black/10"></div>
        <div class="relative z-10">
          <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-4">
            <HeartIcon class="w-10 h-10 text-white" />
          </div>
          <h3 class="text-2xl font-bold text-white mb-2">Secourisme</h3>
          <p class="text-red-100">Formation aux premiers secours et techniques de sauvetage</p>
        </div>
        <!-- Decorative elements -->
        <div class="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full"></div>
        <div class="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full"></div>
      </div>
      
      <div class="p-8">
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">{{ getDepartmentSessionCount('Secourisme') }}</div>
            <div class="text-sm text-gray-600">Sessions</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ getAvailableSessionCount('Secourisme') }}</div>
            <div class="text-sm text-gray-600">Disponibles</div>
          </div>
        </div>
        
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-sm text-gray-600">
            <CheckIcon class="w-4 h-4 text-green-500 mr-2" />
            Gestes de premiers secours
          </div>
          <div class="flex items-center text-sm text-gray-600">
            <CheckIcon class="w-4 h-4 text-green-500 mr-2" />
            Techniques de réanimation
          </div>
          <div class="flex items-center text-sm text-gray-600">
            <CheckIcon class="w-4 h-4 text-green-500 mr-2" />
            Formation certifiée
          </div>
        </div>
        
        <Link 
          v-if="canLogin"
          :href="route('student.departments.show', 'Secourisme')"
          class="w-full inline-flex items-center justify-center gap-2 bg-red-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors duration-200 group"
        >
          Voir les formations
          <ArrowRightIcon class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
        </Link>
        <button 
          v-else
          @click="scrollToSection('formations')"
          class="w-full inline-flex items-center justify-center gap-2 bg-red-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors duration-200 group"
        >
          Voir les formations
          <ArrowRightIcon class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
        </button>
      </div>
    </div>

    <!-- Département Langue -->
    <div class="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
      <div class="bg-gradient-to-br from-blue-500 to-blue-700 p-8 relative overflow-hidden">
        <div class="absolute inset-0 bg-black/10"></div>
        <div class="relative z-10">
          <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-4">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-white mb-2">Langue</h3>
          <p class="text-blue-100">Formation linguistique et communication internationale</p>
        </div>
        <!-- Decorative elements -->
        <div class="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full"></div>
        <div class="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full"></div>
      </div>
      
      <div class="p-8">
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">{{ getDepartmentSessionCount('Langue') }}</div>
            <div class="text-sm text-gray-600">Sessions</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ getAvailableSessionCount('Langue') }}</div>
            <div class="text-sm text-gray-600">Disponibles</div>
          </div>
        </div>
        
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-sm text-gray-600">
            <CheckIcon class="w-4 h-4 text-green-500 mr-2" />
            Communication multilingue
          </div>
          <div class="flex items-center text-sm text-gray-600">
            <CheckIcon class="w-4 h-4 text-green-500 mr-2" />
            Certification internationale
          </div>
          <div class="flex items-center text-sm text-gray-600">
            <CheckIcon class="w-4 h-4 text-green-500 mr-2" />
            Méthodes interactives
          </div>
        </div>
        
        <Link 
          v-if="canLogin"
          :href="route('student.departments.show', 'Langue')"
          class="w-full inline-flex items-center justify-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 group"
        >
          Voir les formations
          <ArrowRightIcon class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
        </Link>
        <button 
          v-else
          @click="scrollToSection('formations')"
          class="w-full inline-flex items-center justify-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 group"
        >
          Voir les formations
          <ArrowRightIcon class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
        </button>
      </div>
    </div>

    <!-- Département Formation à la carte -->
    <div class="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
      <div class="bg-gradient-to-br from-green-500 to-green-700 p-8 relative overflow-hidden">
        <div class="absolute inset-0 bg-black/10"></div>
        <div class="relative z-10">
          <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-4">
            <AcademicCapIcon class="w-10 h-10 text-white" />
          </div>
          <h3 class="text-2xl font-bold text-white mb-2">Formation à la carte</h3>
          <p class="text-green-100">Formations personnalisées selon vos besoins spécifiques</p>
        </div>
        <!-- Decorative elements -->
        <div class="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full"></div>
        <div class="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full"></div>
      </div>
      
      <div class="p-8">
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">{{ getDepartmentSessionCount('Formation à la carte') }}</div>
            <div class="text-sm text-gray-600">Sessions</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ getAvailableSessionCount('Formation à la carte') }}</div>
            <div class="text-sm text-gray-600">Disponibles</div>
          </div>
        </div>
        
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-sm text-gray-600">
            <CheckIcon class="w-4 h-4 text-green-500 mr-2" />
            Formations sur mesure
          </div>
          <div class="flex items-center text-sm text-gray-600">
            <CheckIcon class="w-4 h-4 text-green-500 mr-2" />
            Flexibilité horaire
          </div>
          <div class="flex items-center text-sm text-gray-600">
            <CheckIcon class="w-4 h-4 text-green-500 mr-2" />
            Accompagnement personnalisé
          </div>
        </div>
        
        <Link 
          v-if="canLogin"
          :href="route('student.departments.show', 'Formation à la carte')"
          class="w-full inline-flex items-center justify-center gap-2 bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors duration-200 group"
        >
          Voir les formations
          <ArrowRightIcon class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
        </Link>
        <button 
          v-else
          @click="scrollToSection('formations')"
          class="w-full inline-flex items-center justify-center gap-2 bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors duration-200 group"
        >
          Voir les formations
          <ArrowRightIcon class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Link } from '@inertiajs/vue3';
import { 
  HeartIcon, 
  AcademicCapIcon,
  ArrowRightIcon,
  CheckIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  sessions: Array,
  canLogin: Boolean,
  scrollToSection: Function,
});

// Méthodes
const getDepartmentSessionCount = (department) => {
  if (!props.sessions) return 0;
  return props.sessions.filter(session => session.department === department).length;
};

const getAvailableSessionCount = (department) => {
  if (!props.sessions) return 0;
  return props.sessions.filter(session => 
    session.department === department && 
    new Date(session.start_date) > new Date()
  ).length;
};
</script>
