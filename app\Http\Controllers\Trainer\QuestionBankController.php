<?php

namespace App\Http\Controllers\Trainer;

use App\Http\Controllers\Controller;
use App\Models\ExamQuestion;
use App\Models\Exam;
use App\Models\TrainingSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class QuestionBankController extends Controller
{
    /**
     * Affiche la banque de questions du formateur
     */
    public function index(Request $request)
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();

        // Récupérer les sessions de formation du formateur
        $trainingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->with('trainingDomain')
            ->get();
        
        $sessionIds = $trainingSessions->pluck('id')->toArray();
        
        // Récupérer les examens associés à ces sessions
        $examIds = Exam::whereIn('training_session_id', $sessionIds)
            ->pluck('id')
            ->toArray();
            
        // Initialiser la requête
        $query = ExamQuestion::whereIn('exam_id', $examIds)
            ->with('exam', 'exam.trainingSession');
            
        // Appliquer la recherche si elle existe
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where('question_text', 'like', $searchTerm);
        }
        
        // Filtrer par type de question
        if ($request->has('question_type') && !empty($request->question_type)) {
            $query->where('question_type', $request->question_type);
        }
        
        // Filtrer par session de formation
        if ($request->has('training_session_id') && !empty($request->training_session_id)) {
            $query->whereHas('exam', function($q) use ($request) {
                $q->where('training_session_id', $request->training_session_id);
            });
        }
        
        // Filtrer par examen
        if ($request->has('exam_id') && !empty($request->exam_id)) {
            $query->where('exam_id', $request->exam_id);
        }
        
        // Récupérer les questions avec pagination
        $questions = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();
            
        // Récupérer tous les examens pour le filtre
        $exams = Exam::whereIn('id', $examIds)
            ->orderBy('title')
            ->get();
            
        // Retourner la vue avec les questions et les filtres
        return Inertia::render('Trainer/QuestionBank/Index', [
            'questions' => $questions,
            'trainingSessions' => $trainingSessions,
            'exams' => $exams,
            'filters' => [
                'search' => $request->search ?? '',
                'question_type' => $request->question_type ?? '',
                'training_session_id' => $request->training_session_id ?? '',
                'exam_id' => $request->exam_id ?? '',
            ],
            'questionTypes' => [
                ['value' => 'multiple_choice', 'label' => 'QCM'],
                ['value' => 'text', 'label' => 'Texte libre'],
                ['value' => 'file', 'label' => 'Fichier'],
            ],
        ]);
    }
    
    /**
     * Affiche le formulaire de création d'une question
     */
    public function create()
    {
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();
        
        // Récupérer les sessions de formation du formateur
        $trainingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->with('trainingDomain')
            ->get();
            
        $sessionIds = $trainingSessions->pluck('id')->toArray();
        
        // Récupérer les examens associés à ces sessions
        $exams = Exam::whereIn('training_session_id', $sessionIds)
            ->with('trainingSession')
            ->orderBy('title')
            ->get();
            
        return Inertia::render('Trainer/QuestionBank/Create', [
            'trainingSessions' => $trainingSessions,
            'exams' => $exams,
        ]);
    }
    
    /**
     * Enregistre une nouvelle question
     */
    public function store(Request $request)
    {
        // Valider les données
        $validated = $request->validate([
            'exam_id' => 'required|exists:exams,id',
            'question_text' => 'required|string',
            'question_type' => 'required|in:multiple_choice,text,file',
            'options' => 'nullable|array',
            'correct_answer' => 'nullable|string',
            'correct_answers' => 'nullable|array',
            'multiple_answers_allowed' => 'boolean',
            'points' => 'required|integer|min:1',
            'order' => 'required|integer|min:1',
        ]);
        
        // Vérifier que l'examen appartient bien au formateur
        $exam = Exam::findOrFail($validated['exam_id']);
        $trainingSession = TrainingSession::findOrFail($exam->training_session_id);
        
        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->back()->withErrors([
                'exam_id' => 'Vous n\'êtes pas autorisé à ajouter des questions à cet examen.'
            ]);
        }
        
        // Traiter les réponses correctes pour les QCM
        if ($validated['question_type'] === 'multiple_choice') {
            if ($validated['multiple_answers_allowed'] && isset($validated['correct_answers'])) {
                $validated['correct_answer'] = $validated['correct_answers'];
            }
            unset($validated['correct_answers']);
        } else if ($validated['question_type'] !== 'multiple_choice') {
            $validated['options'] = null;
            $validated['correct_answer'] = null;
            $validated['multiple_answers_allowed'] = false;
        }
        
        // Créer la question
        ExamQuestion::create($validated);
        
        return redirect()->route('trainer.question-bank.index')
            ->with('success', 'Question ajoutée avec succès.');
    }
    
    /**
     * Affiche le formulaire de modification d'une question
     */
    public function edit(string $id)
    {
        // Récupérer la question
        $question = ExamQuestion::with('exam')->findOrFail($id);
        
        // Vérifier que la question appartient bien au formateur
        $exam = Exam::findOrFail($question->exam_id);
        $trainingSession = TrainingSession::findOrFail($exam->training_session_id);
        
        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.question-bank.index')
                ->with('error', 'Vous n\'êtes pas autorisé à modifier cette question.');
        }
        
        // Récupérer l'utilisateur connecté (formateur)
        $trainer = Auth::user();
        
        // Récupérer les sessions de formation du formateur
        $trainingSessions = TrainingSession::where('trainer_id', $trainer->id)
            ->with('trainingDomain')
            ->get();
            
        $sessionIds = $trainingSessions->pluck('id')->toArray();
        
        // Récupérer les examens associés à ces sessions
        $exams = Exam::whereIn('training_session_id', $sessionIds)
            ->with('trainingSession')
            ->orderBy('title')
            ->get();
            
        return Inertia::render('Trainer/QuestionBank/Edit', [
            'question' => $question,
            'trainingSessions' => $trainingSessions,
            'exams' => $exams,
        ]);
    }
    
    /**
     * Met à jour une question
     */
    public function update(Request $request, string $id)
    {
        // Récupérer la question
        $question = ExamQuestion::findOrFail($id);
        
        // Vérifier que la question appartient bien au formateur
        $exam = Exam::findOrFail($question->exam_id);
        $trainingSession = TrainingSession::findOrFail($exam->training_session_id);
        
        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.question-bank.index')
                ->with('error', 'Vous n\'êtes pas autorisé à modifier cette question.');
        }
        
        // Valider les données
        $validated = $request->validate([
            'exam_id' => 'required|exists:exams,id',
            'question_text' => 'required|string',
            'question_type' => 'required|in:multiple_choice,text,file',
            'options' => 'nullable|array',
            'correct_answer' => 'nullable|string',
            'correct_answers' => 'nullable|array',
            'multiple_answers_allowed' => 'boolean',
            'points' => 'required|integer|min:1',
            'order' => 'required|integer|min:1',
        ]);
        
        // Vérifier que le nouvel examen appartient bien au formateur
        $newExam = Exam::findOrFail($validated['exam_id']);
        $newTrainingSession = TrainingSession::findOrFail($newExam->training_session_id);
        
        if ($newTrainingSession->trainer_id !== Auth::id()) {
            return redirect()->back()->withErrors([
                'exam_id' => 'Vous n\'êtes pas autorisé à ajouter des questions à cet examen.'
            ]);
        }
        
        // Traiter les réponses correctes pour les QCM
        if ($validated['question_type'] === 'multiple_choice') {
            if ($validated['multiple_answers_allowed'] && isset($validated['correct_answers'])) {
                $validated['correct_answer'] = $validated['correct_answers'];
            }
            unset($validated['correct_answers']);
        } else if ($validated['question_type'] !== 'multiple_choice') {
            $validated['options'] = null;
            $validated['correct_answer'] = null;
            $validated['multiple_answers_allowed'] = false;
        }
        
        // Mettre à jour la question
        $question->update($validated);
        
        return redirect()->route('trainer.question-bank.index')
            ->with('success', 'Question mise à jour avec succès.');
    }
    
    /**
     * Supprime une question
     */
    public function destroy(string $id)
    {
        // Récupérer la question
        $question = ExamQuestion::findOrFail($id);
        
        // Vérifier que la question appartient bien au formateur
        $exam = Exam::findOrFail($question->exam_id);
        $trainingSession = TrainingSession::findOrFail($exam->training_session_id);
        
        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.question-bank.index')
                ->with('error', 'Vous n\'êtes pas autorisé à supprimer cette question.');
        }
        
        // Supprimer la question
        $question->delete();
        
        return redirect()->route('trainer.question-bank.index')
            ->with('success', 'Question supprimée avec succès.');
    }
    
    /**
     * Duplique une question vers un autre examen
     */
    public function duplicate(Request $request, string $id)
    {
        // Récupérer la question
        $question = ExamQuestion::findOrFail($id);
        
        // Vérifier que la question appartient bien au formateur
        $exam = Exam::findOrFail($question->exam_id);
        $trainingSession = TrainingSession::findOrFail($exam->training_session_id);
        
        if ($trainingSession->trainer_id !== Auth::id()) {
            return redirect()->route('trainer.question-bank.index')
                ->with('error', 'Vous n\'êtes pas autorisé à dupliquer cette question.');
        }
        
        // Valider les données
        $validated = $request->validate([
            'target_exam_id' => 'required|exists:exams,id',
        ]);
        
        // Vérifier que l'examen cible appartient bien au formateur
        $targetExam = Exam::findOrFail($validated['target_exam_id']);
        $targetTrainingSession = TrainingSession::findOrFail($targetExam->training_session_id);
        
        if ($targetTrainingSession->trainer_id !== Auth::id()) {
            return redirect()->back()->withErrors([
                'target_exam_id' => 'Vous n\'êtes pas autorisé à ajouter des questions à cet examen.'
            ]);
        }
        
        // Déterminer l'ordre de la nouvelle question
        $nextOrder = ExamQuestion::where('exam_id', $validated['target_exam_id'])->max('order') + 1;
        
        // Dupliquer la question
        $newQuestion = $question->replicate();
        $newQuestion->exam_id = $validated['target_exam_id'];
        $newQuestion->order = $nextOrder;
        $newQuestion->save();
        
        return redirect()->route('trainer.question-bank.index')
            ->with('success', 'Question dupliquée avec succès.');
    }
}
