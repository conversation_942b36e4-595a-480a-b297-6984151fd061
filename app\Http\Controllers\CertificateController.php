<?php

namespace App\Http\Controllers;

use App\Models\Certificate;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use chillerlan\QRCode\QRCode;
use chillerlan\QRCode\QROptions;

class CertificateController extends Controller
{
    /**
     * Téléchargement direct d'un certificat (sans passer par Inertia)
     */
    public function directDownload($id)
    {
        // Récupérer le certificat
        $certificate = Certificate::findOrFail($id);

        // Vérifier que l'utilisateur a le droit de télécharger ce certificat
        if (auth()->user()->id !== $certificate->user_id && !auth()->user()->isAdmin() && !auth()->user()->isTrainer()) {
            abort(403, 'Vous n\'avez pas le droit de télécharger ce certificat.');
        }

        // Vérifier que le fichier existe
        if (!Storage::disk('public')->exists($certificate->file_path)) {
            abort(404, 'Le fichier du certificat n\'existe pas.');
        }

        // Télécharger le fichier
        return Storage::disk('public')->download(
            $certificate->file_path,
            'Certificat_' . $certificate->certificate_number . '.pdf'
        );
    }

    /**
     * Générer un QR code pour un certificat
     */
    public function generateQrCode($id)
    {
        try {
            // Récupérer le certificat
            $certificate = Certificate::findOrFail($id);

            // Vérifier que l'utilisateur a le droit de générer le QR code
            if (auth()->user()->id !== $certificate->user_id && !auth()->user()->isAdmin() && !auth()->user()->isTrainer()) {
                return response()->json(['error' => 'Vous n\'avez pas le droit de générer le QR code pour ce certificat.'], 403);
            }

            // URL de vérification du certificat
            $verificationUrl = url('/certificates/verify/' . $certificate->certificate_number);

            // Configuration pour le QR code
            $options = new QROptions([
                'version'    => 5,
                'outputType' => QRCode::OUTPUT_IMAGE_PNG,
                'eccLevel'   => QRCode::ECC_L,
                'scale'      => 10,
                'imageBase64' => false,
            ]);

            // Générer le QR code
            $qrcode = new QRCode($options);
            $qrCode = $qrcode->render($verificationUrl);

            // Encoder en base64 pour l'affichage
            $qrCodeBase64 = base64_encode($qrCode);

            return response()->json([
                'success' => true,
                'qr_code' => 'data:image/png;base64,' . $qrCodeBase64,
                'verification_url' => $verificationUrl
            ]);

        } catch (\Exception $e) {
            \Log::error('Erreur lors de la génération du QR code: ' . $e->getMessage());
            return response()->json(['error' => 'Erreur lors de la génération du QR code: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Vérifier un certificat via son numéro
     */
    public function verify($certificateNumber)
    {
        $certificate = Certificate::where('certificate_number', $certificateNumber)->first();

        if (!$certificate) {
            return response()->json(['error' => 'Certificat non trouvé'], 404);
        }

        return response()->json([
            'success' => true,
            'certificate' => [
                'certificate_number' => $certificate->certificate_number,
                'issue_date' => $certificate->issue_date,
                'expiry_date' => $certificate->expiry_date,
                'status' => $certificate->status,
                'user_name' => $certificate->user->name,
                'user_email' => $certificate->user->email,
                'formation' => $certificate->formation,
                'domain' => $certificate->domain,
                'trainer' => $certificate->trainer,
                'training_period' => $certificate->training_period
            ]
        ]);
    }
}
