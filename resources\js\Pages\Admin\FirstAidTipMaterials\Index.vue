<template>
  <Head :title="`Matériels - ${firstAidTip.title}`" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Matériels du conseil: {{ firstAidTip.title }}
        </h2>
        <div class="flex space-x-2">
          <Link :href="route('admin.first-aid-tips.index')" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
            Retour aux conseils
          </Link>
          <Link :href="route('admin.first-aid-tip-materials.create', { first_aid_tip_id: firstAidTip.id })" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            Ajouter un matériel
          </Link>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Informations du conseil -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div class="flex items-center">
                <HeartIcon class="h-5 w-5 text-blue-500 mr-2" />
                <div>
                  <h3 class="font-medium text-blue-900">{{ firstAidTip.title }}</h3>
                  <p class="text-sm text-blue-700" v-if="firstAidTip.description">{{ firstAidTip.description }}</p>
                </div>
              </div>
            </div>

            <!-- Liste des matériels -->
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">Matériels pédagogiques</h3>
              <Link
                :href="route('admin.first-aid-tip-materials.create', { first_aid_tip_id: firstAidTip.id })"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Ajouter un matériel
              </Link>
            </div>

            <div v-if="materials.data.length > 0" class="space-y-4">
              <div
                v-for="material in materials.data"
                :key="material.id"
                class="border border-gray-200 rounded-lg p-6"
              >
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <div class="flex items-center mb-2">
                      <component :is="getTypeIcon(material.type)" class="h-6 w-6 mr-2" :class="getTypeIconColor(material.type)" />
                      <h4 class="font-medium text-gray-900">{{ material.title }}</h4>
                    </div>
                    
                    <p class="text-sm text-gray-600 mb-3" v-if="material.description">
                      {{ material.description }}
                    </p>
                    
                    <div class="flex items-center space-x-4">
                      <span :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        getTypeColor(material.type)
                      ]">
                        {{ getTypeLabel(material.type) }}
                      </span>
                      
                      <span :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        material.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      ]">
                        {{ material.active ? 'Actif' : 'Inactif' }}
                      </span>

                      <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <span v-if="material.allow_online_viewing" class="flex items-center">
                          <EyeIcon class="h-4 w-4 mr-1" />
                          Visualisation
                        </span>
                        <span v-if="material.allow_download" class="flex items-center">
                          <ArrowDownTrayIcon class="h-4 w-4 mr-1" />
                          Téléchargement
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="flex space-x-2 ml-4">
                    <Link
                      :href="route('admin.first-aid-tip-materials.show', material.id)"
                      class="text-blue-600 hover:text-blue-900 text-sm"
                    >
                      Voir
                    </Link>
                    <Link
                      :href="route('admin.first-aid-tip-materials.edit', material.id)"
                      class="text-indigo-600 hover:text-indigo-900 text-sm"
                    >
                      Modifier
                    </Link>
                    <button
                      @click="deleteMaterial(material.id)"
                      class="text-red-600 hover:text-red-900 text-sm"
                    >
                      Supprimer
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="text-center py-12 text-gray-500">
              <DocumentIcon class="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun matériel</h3>
              <p class="text-gray-500 mb-4">Ce conseil n'a pas encore de matériels pédagogiques.</p>
              <Link
                :href="route('admin.first-aid-tip-materials.create', { first_aid_tip_id: firstAidTip.id })"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                Ajouter le premier matériel
              </Link>
            </div>

            <!-- Pagination -->
            <div class="mt-6" v-if="materials.links && materials.data.length > 0">
              <nav class="flex justify-center">
                <div class="flex space-x-1">
                  <Link
                    v-for="link in materials.links"
                    :key="link.label"
                    :href="link.url"
                    :class="[
                      'px-3 py-2 text-sm rounded-md',
                      link.active
                        ? 'bg-blue-600 text-white'
                        : link.url
                        ? 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    ]"
                    v-html="link.label"
                  />
                </div>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { 
  HeartIcon,
  EyeIcon, 
  ArrowDownTrayIcon, 
  DocumentIcon,
  DocumentTextIcon,
  VideoCameraIcon,
  PhotoIcon,
  SpeakerWaveIcon,
  ArchiveBoxIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  firstAidTip: Object,
  materials: Object,
});

// Méthodes
const getTypeLabel = (type) => {
  const labels = {
    text: 'Texte',
    pdf: 'PDF',
    video: 'Vidéo',
    audio: 'Audio',
    image: 'Image',
    archive: 'Archive',
    gallery: 'Galerie',
    embed_video: 'Vidéo externe'
  };
  return labels[type] || type;
};

const getTypeColor = (type) => {
  const colors = {
    text: 'bg-gray-100 text-gray-800',
    pdf: 'bg-red-100 text-red-800',
    video: 'bg-blue-100 text-blue-800',
    audio: 'bg-green-100 text-green-800',
    image: 'bg-yellow-100 text-yellow-800',
    archive: 'bg-purple-100 text-purple-800',
    gallery: 'bg-pink-100 text-pink-800',
    embed_video: 'bg-indigo-100 text-indigo-800'
  };
  return colors[type] || 'bg-gray-100 text-gray-800';
};

const getTypeIcon = (type) => {
  const icons = {
    text: DocumentTextIcon,
    pdf: DocumentIcon,
    video: VideoCameraIcon,
    audio: SpeakerWaveIcon,
    image: PhotoIcon,
    archive: ArchiveBoxIcon,
    gallery: PhotoIcon,
    embed_video: VideoCameraIcon
  };
  return icons[type] || DocumentIcon;
};

const getTypeIconColor = (type) => {
  const colors = {
    text: 'text-gray-500',
    pdf: 'text-red-500',
    video: 'text-blue-500',
    audio: 'text-green-500',
    image: 'text-yellow-500',
    archive: 'text-purple-500',
    gallery: 'text-pink-500',
    embed_video: 'text-indigo-500'
  };
  return colors[type] || 'text-gray-500';
};

const deleteMaterial = (id) => {
  if (confirm('Êtes-vous sûr de vouloir supprimer ce matériel ?')) {
    router.delete(route('admin.first-aid-tip-materials.destroy', id));
  }
};
</script>
