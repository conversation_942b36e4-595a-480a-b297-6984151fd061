<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    /**
     * Gère une requête entrante en vérifiant le rôle de l'utilisateur.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $role
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, string $role): Response
    {
        // Vérifier si l'utilisateur est authentifié
        if (!$request->user()) {
            return redirect()->route('login');
        }

        // Vérifier si l'utilisateur a le rôle requis
        if ($request->user()->role !== $role) {
            // Rediriger vers le tableau de bord approprié en fonction du rôle de l'utilisateur
            if ($request->user()->isAdmin()) {
                return redirect()->route('admin.dashboard');
            } elseif ($request->user()->isTrainer()) {
                return redirect()->route('trainer.dashboard');
            } else {
                return redirect()->route('student.dashboard');
            }
        }

        return $next($request);
    }
}
