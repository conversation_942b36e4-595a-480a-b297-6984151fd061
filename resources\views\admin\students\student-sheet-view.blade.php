<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fiche Apprenant - {{ $student->name }}</title>
    <style>
        @page {
            margin: 1cm;
            size: A4;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #1f2937;
            background: #f8fafc;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        /* Main container */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(79, 70, 229, 0.1);
            overflow: hidden;
            position: relative;
        }

        /* Formes géométriques décoratives */
        .geometric-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            overflow: hidden;
            pointer-events: none;
        }

        .geo-shape {
            position: absolute;
            background: #13BFDB;
            opacity: 0.08;
        }

        .geo-shape-1 {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            top: -100px;
            right: -100px;
        }

        .geo-shape-2 {
            width: 150px;
            height: 150px;
            transform: rotate(45deg);
            top: 200px;
            left: -75px;
        }

        .geo-shape-3 {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            top: 400px;
            right: 50px;
        }

        .geo-shape-4 {
            width: 80px;
            height: 80px;
            transform: rotate(30deg);
            top: 100px;
            left: 200px;
        }

        .geo-shape-5 {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            bottom: 200px;
            left: 100px;
        }

        .geo-shape-6 {
            width: 60px;
            height: 60px;
            transform: rotate(60deg);
            bottom: 100px;
            right: 200px;
        }

        .geo-shape-7 {
            width: 180px;
            height: 180px;
            border-radius: 50%;
            top: 300px;
            right: -90px;
        }

        .geo-shape-8 {
            width: 90px;
            height: 90px;
            transform: rotate(15deg);
            top: 500px;
            left: -90px;
            border-radius: 30px;
        }

        /* Boutons d'action */
        .action-buttons {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(107, 114, 128, 0.4);
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px 40px;
            position: relative;
            overflow: hidden;
            z-index: 10;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 2;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            padding: 5px;
        }

        .brand-text {
            display: flex;
            flex-direction: column;
        }

        .brand-name {
            font-size: 16px;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .brand-subtitle {
            font-size: 10px;
            opacity: 0.9;
            margin-top: 2px;
        }

        .document-title {
            font-size: 14px;
            font-weight: bold;
            margin-top: 20px;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .generation-date {
            margin-top: 10px;
            font-size: 11px;
            opacity: 0.8;
        }

        /* Content wrapper */
        .content-wrapper-main {
            padding: 30px 40px;
            position: relative;
            z-index: 10;
        }

        .student-profile {
            display: flex;
            margin-bottom: 30px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            padding: 25px;
            border-radius: 16px;
            border: 1px solid rgba(79, 70, 229, 0.1);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.1);
            position: relative;
            overflow: hidden;
        }

        .student-profile::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 100%);
        }

        .profile-photo {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            margin-right: 25px;
            border: 3px solid rgba(79, 70, 229, 0.2);
            object-fit: cover;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #94a3b8;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.15);
            flex-shrink: 0;
        }

        .profile-info {
            flex: 1;
            min-width: 0;
        }

        .student-name {
            font-size: 24px;
            font-weight: bold;
            color: #4f46e5;
            margin-bottom: 15px;
            line-height: 1.2;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 10px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            padding: 12px 0;
            border-bottom: 1px solid rgba(226, 232, 240, 0.3);
        }

        .info-label {
            font-weight: 600;
            color: #475569;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 6px;
        }

        .info-value {
            color: #1e293b;
            font-weight: 500;
            font-size: 14px;
            line-height: 1.4;
        }

        .section {
            margin-bottom: 30px;
            page-break-inside: avoid;
            position: relative;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 25px;
            border: 1px solid rgba(79, 70, 229, 0.1);
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.08);
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #4f46e5;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid #4f46e5;
            position: relative;
            display: flex;
            align-items: center;
        }

        .section-title::before {
            content: '';
            width: 5px;
            height: 28px;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            margin-right: 15px;
            border-radius: 3px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 25px;
        }

        .stat-card {
            text-align: center;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            padding: 20px 15px;
            border-radius: 12px;
            border: 1px solid rgba(79, 70, 229, 0.1);
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.1);
            position: relative;
            overflow: hidden;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 100%);
        }

        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #4f46e5;
            margin-bottom: 8px;
            line-height: 1;
        }

        .stat-label {
            font-size: 12px;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }

        /* Layout en colonnes pour optimiser l'espace */
        .content-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
            align-items: start;
        }

        .left-column, .right-column {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .full-width {
            grid-column: 1 / -1;
            margin-bottom: 30px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.1);
            border: 1px solid rgba(79, 70, 229, 0.1);
        }

        .table th,
        .table td {
            padding: 15px 18px;
            text-align: left;
            border-bottom: 1px solid rgba(226, 232, 240, 0.4);
            font-size: 14px;
            vertical-align: top;
        }

        .table th {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            font-weight: 600;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            white-space: nowrap;
        }

        .table td {
            color: #475569;
            line-height: 1.5;
            font-weight: 500;
        }

        .table tbody tr:hover {
            background: rgba(79, 70, 229, 0.05);
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .table tbody tr:nth-child(even) {
            background: rgba(248, 250, 252, 0.5);
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: 1px solid transparent;
            white-space: nowrap;
        }

        .badge-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .badge-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
        }

        .badge-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }

        .badge-info {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #64748b;
            padding: 20px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            border-top: 2px solid transparent;
            border-image: linear-gradient(90deg, #4f46e5 0%, #7c3aed 100%) 1;
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .no-data {
            text-align: center;
            color: #94a3b8;
            font-style: italic;
            padding: 25px;
            background: rgba(248, 250, 252, 0.7);
            border-radius: 8px;
            border: 2px dashed #cbd5e1;
            font-size: 12px;
            margin: 10px 0;
        }

        .certificate-number {
            font-family: 'Courier New', monospace;
            background: rgba(79, 70, 229, 0.1);
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: bold;
            color: #4f46e5;
        }

        .additional-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .additional-stat-item {
            background: rgba(255, 255, 255, 0.7);
            padding: 8px;
            border-radius: 6px;
            border-left: 3px solid #4f46e5;
            box-shadow: 0 1px 5px rgba(79, 70, 229, 0.1);
        }

        .section-icon {
            font-size: 16px;
            margin-right: 8px;
            opacity: 0.8;
        }

        .currency-value {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #059669;
        }

        /* Responsive design */
        @media (max-width: 1024px) {
            .content-wrapper {
                grid-template-columns: 1fr;
                gap: 25px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .main-container {
                border-radius: 12px;
            }

            .header {
                padding: 20px 25px;
            }

            .content-wrapper-main {
                padding: 20px 25px;
            }

            .student-profile {
                flex-direction: column;
                text-align: center;
                padding: 20px;
            }

            .profile-photo {
                margin-right: 0;
                margin-bottom: 15px;
                align-self: center;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .action-buttons {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 20px;
                justify-content: center;
                flex-wrap: wrap;
            }

            .section {
                padding: 20px;
            }

            .table th,
            .table td {
                padding: 10px 12px;
                font-size: 12px;
            }
        }

        /* Masquer les boutons lors de l'impression */
        @media print {
            .action-buttons {
                display: none !important;
            }

            .footer {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                margin-top: 0;
                margin-bottom: 0;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Boutons d'action -->
        <div class="action-buttons">
            <a href="{{ route('admin.students.sheet', $student->id) }}" class="btn btn-primary">
                📄 Exporter PDF
            </a>
            <a href="{{ route('admin.students.index') }}" class="btn btn-secondary">
                ← Retour
            </a>
        </div>

        <!-- Formes géométriques décoratives -->
        <div class="geometric-background">
            <div class="geo-shape geo-shape-1"></div>
            <div class="geo-shape geo-shape-2"></div>
            <div class="geo-shape geo-shape-3"></div>
            <div class="geo-shape geo-shape-4"></div>
            <div class="geo-shape geo-shape-5"></div>
            <div class="geo-shape geo-shape-6"></div>
            <div class="geo-shape geo-shape-7"></div>
            <div class="geo-shape geo-shape-8"></div>
        </div>

    <!-- En-tête -->
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <img src="{{ asset('images/2.png') }}" alt="PCMET Logo" class="logo-img">
                <div class="brand-text">
                    <div class="brand-name">PCMET Horizon Qualité</div>
                    <div class="brand-subtitle">Centre de Formation Professionnelle</div>
                </div>
            </div>
            <div class="document-title">FICHE APPRENANT COMPLÈTE</div>
            <div class="generation-date">
                Générée le {{ now()->format('d/m/Y à H:i') }}
            </div>
        </div>
    </div>

    <div class="content-wrapper-main">
        <!-- Profil de l'apprenant -->
    <div class="student-profile">
        <div class="profile-photo">
            @if($student->profile_photo)
                <img src="{{ asset('storage/' . $student->profile_photo) }}" alt="{{ $student->name }}" style="width: 100%; height: 100%; border-radius: 8px; object-fit: cover;">
            @else
                👤
            @endif
        </div>
        <div class="profile-info">
            <div class="student-name">{{ $student->name }}</div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">ID :</span>
                    <span class="info-value">{{ $student->student_id ?? 'N/A' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Email :</span>
                    <span class="info-value">{{ Str::limit($student->email, 20) }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Téléphone :</span>
                    <span class="info-value">{{ $student->phone ?? 'N/A' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Adresse :</span>
                    <span class="info-value">{{ Str::limit($student->address ?? 'N/A', 30) }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Naissance :</span>
                    <span class="info-value">{{ $student->birth_date ? \Carbon\Carbon::parse($student->birth_date)->format('d/m/Y') : 'N/A' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Profession :</span>
                    <span class="info-value">{{ Str::limit($student->profession ?? 'N/A', 20) }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Entreprise :</span>
                    <span class="info-value">{{ Str::limit($student->company ?? 'N/A', 20) }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Statut :</span>
                    <span class="info-value">
                        <span class="badge {{ $student->active ? 'badge-success' : 'badge-danger' }}">
                            {{ $student->active ? 'Actif' : 'Inactif' }}
                        </span>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="section full-width">
        <h2 class="section-title"><span class="section-icon">📊</span>Statistiques Générales</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{{ $stats['total_enrollments'] }}</div>
                <div class="stat-label">Inscriptions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ $stats['completed_courses'] }}</div>
                <div class="stat-label">Formations Terminées</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ $stats['certificates_earned'] }}</div>
                <div class="stat-label">Certificats Obtenus</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ number_format($stats['average_exam_score'], 1) }}%</div>
                <div class="stat-label">Moyenne Examens</div>
            </div>
        </div>

        <div class="additional-stats">
            <div class="additional-stat-item">
                <div class="info-item">
                    <span class="info-label">Taux de réussite :</span>
                    <span class="info-value">{{ number_format($stats['success_rate'], 1) }}%</span>
                </div>
            </div>
            <div class="additional-stat-item">
                <div class="info-item">
                    <span class="info-label">Total paiements :</span>
                    <span class="info-value currency-value">{{ number_format($stats['total_payments'], 2) }} DT</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Layout en colonnes pour optimiser l'espace -->
    <div class="content-wrapper">
        <!-- Colonne gauche -->
        <div class="left-column">
            <!-- Inscriptions aux formations -->
            <div class="section">
                <h2 class="section-title"><span class="section-icon">📚</span>Formations</h2>
                @if($student->enrollments->count() > 0)
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Formation</th>
                                <th>Date</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($student->enrollments as $enrollment)
                                <tr>
                                    <td>{{ Str::limit($enrollment->trainingSession->title ?? 'Non définie', 20) }}</td>
                                    <td>{{ $enrollment->created_at->format('d/m/Y') }}</td>
                                    <td>
                                        <span class="badge
                                            @if($enrollment->status === 'completed') badge-success
                                            @elseif($enrollment->status === 'approved') badge-info
                                            @elseif($enrollment->status === 'pending') badge-warning
                                            @else badge-danger
                                            @endif">
                                            {{ ucfirst($enrollment->status) }}
                                        </span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                @else
                    <div class="no-data">Aucune inscription</div>
                @endif
            </div>

            <!-- Certificats obtenus -->
            <div class="section">
                <h2 class="section-title"><span class="section-icon">🏆</span>Certificats</h2>
                @if($student->certificates->count() > 0)
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>Formation</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($student->certificates as $certificate)
                                <tr>
                                    <td><span class="certificate-number">{{ Str::limit($certificate->certificate_number, 15) }}</span></td>
                                    <td>{{ Str::limit($certificate->enrollment->trainingSession->title ?? 'Non définie', 20) }}</td>
                                    <td>{{ $certificate->issue_date ? \Carbon\Carbon::parse($certificate->issue_date)->format('d/m/Y') : 'N/A' }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                @else
                    <div class="no-data">Aucun certificat</div>
                @endif
            </div>
        </div>

        <!-- Colonne droite -->
        <div class="right-column">
            <!-- Résultats d'examens -->
            <div class="section">
                <h2 class="section-title"><span class="section-icon">📝</span>Examens</h2>
                @if($student->examResults->count() > 0)
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Examen</th>
                                <th>Score</th>
                                <th>Statut</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($student->examResults as $result)
                                <tr>
                                    <td>{{ Str::limit($result->exam->title ?? 'Non défini', 15) }}</td>
                                    <td><strong>{{ $result->score }}%</strong></td>
                                    <td>
                                        <span class="badge {{ $result->passed ? 'badge-success' : 'badge-danger' }}">
                                            {{ $result->passed ? 'Réussi' : 'Échoué' }}
                                        </span>
                                    </td>
                                    <td>{{ $result->completed_at ? \Carbon\Carbon::parse($result->completed_at)->format('d/m/Y') : 'N/A' }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                @else
                    <div class="no-data">Aucun examen</div>
                @endif
            </div>
        </div>
    </div>

        <!-- Pied de page -->
        <div class="footer">
            <p><strong>© {{ date('Y') }} PCMET Horizon Qualité</strong> - Document confidentiel généré le {{ now()->format('d/m/Y à H:i') }}</p>
            <p>Cette fiche contient des informations personnelles et ne doit pas être divulguée à des tiers non autorisés.</p>
        </div>
    </div> <!-- End content-wrapper-main -->
</div> <!-- End main-container -->
</body>
</html>
