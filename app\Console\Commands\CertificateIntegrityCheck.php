<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Certificate;
use App\Models\Enrollment;
use App\Models\User;
use Illuminate\Support\Facades\Storage;

class CertificateIntegrityCheck extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'certificates:integrity-check {--fix : Automatically fix issues found}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Comprehensive certificate system integrity check and validation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Starting comprehensive certificate integrity check...');
        
        $issues = 0;
        $fixes = 0;
        
        $issues += $this->checkCertificateNumberConsistency();
        $issues += $this->checkDuplicateCertificates();
        $issues += $this->checkOrphanedCertificates();
        $issues += $this->checkQrCodeConsistency();
        $issues += $this->checkPdfPathConsistency();
        $issues += $this->checkStatusTransitionIntegrity();
        $issues += $this->checkCrossReferenceIntegrity();
        
        if ($issues === 0) {
            $this->info('✅ Certificate system integrity check completed successfully - no issues found!');
        } else {
            $this->warn("⚠️  Found {$issues} integrity issues.");
            if (!$this->option('fix')) {
                $this->info('Run with --fix option to automatically fix issues.');
            }
        }
        
        return Command::SUCCESS;
    }

    /**
     * Check certificate number format consistency
     */
    private function checkCertificateNumberConsistency(): int
    {
        $this->info('📋 Checking certificate number consistency...');
        
        $certificates = Certificate::whereNotNull('certificate_number')
            ->whereNotNull('enrollment_id')
            ->get();
        
        $issues = 0;
        
        foreach ($certificates as $certificate) {
            $expectedPattern = '/^CERT-' . $certificate->enrollment_id . '-\d+$/';
            
            if (!preg_match($expectedPattern, $certificate->certificate_number)) {
                $this->error("❌ Certificate ID {$certificate->id} has inconsistent number format:");
                $this->line("   Current: {$certificate->certificate_number}");
                $this->line("   Expected pattern: CERT-{$certificate->enrollment_id}-{timestamp}");
                
                if ($this->option('fix')) {
                    $oldNumber = $certificate->certificate_number;
                    $newNumber = Certificate::generateCertificateNumber($certificate->enrollment_id);
                    
                    $certificate->update(['certificate_number' => $newNumber]);
                    $certificate->generateQrCode();
                    
                    $this->info("   ✅ Fixed: {$oldNumber} → {$newNumber}");
                }
                
                $issues++;
            }
        }
        
        if ($issues === 0) {
            $this->line('   ✅ All certificate numbers follow correct format');
        }
        
        return $issues;
    }

    /**
     * Check for duplicate certificates
     */
    private function checkDuplicateCertificates(): int
    {
        $this->info('🔄 Checking for duplicate certificates...');
        
        $duplicates = Certificate::selectRaw('enrollment_id, COUNT(*) as count')
            ->groupBy('enrollment_id')
            ->having('count', '>', 1)
            ->get();
        
        $issues = 0;
        
        foreach ($duplicates as $duplicate) {
            $certificates = Certificate::where('enrollment_id', $duplicate->enrollment_id)
                ->orderBy('created_at')
                ->get();
            
            $this->error("❌ Enrollment {$duplicate->enrollment_id} has {$duplicate->count} certificates:");
            
            foreach ($certificates as $cert) {
                $this->line("   - ID: {$cert->id}, Number: {$cert->certificate_number}, Status: {$cert->status}");
            }
            
            if ($this->option('fix')) {
                $toKeep = $certificates->first();
                $toDelete = $certificates->skip(1);
                
                foreach ($toDelete as $cert) {
                    $cert->delete();
                    $this->info("   ✅ Deleted duplicate certificate ID {$cert->id}");
                }
            }
            
            $issues++;
        }
        
        if ($issues === 0) {
            $this->line('   ✅ No duplicate certificates found');
        }
        
        return $issues;
    }

    /**
     * Check for orphaned certificates
     */
    private function checkOrphanedCertificates(): int
    {
        $this->info('🔗 Checking for orphaned certificates...');
        
        $orphaned = Certificate::whereNotExists(function ($query) {
            $query->select('id')
                ->from('enrollments')
                ->whereColumn('enrollments.id', 'certificates.enrollment_id');
        })->get();
        
        $issues = $orphaned->count();
        
        if ($issues > 0) {
            $this->error("❌ Found {$issues} orphaned certificates:");
            
            foreach ($orphaned as $cert) {
                $this->line("   - Certificate ID {$cert->id}, Number: {$cert->certificate_number}");
                
                if ($this->option('fix')) {
                    $cert->delete();
                    $this->info("   ✅ Deleted orphaned certificate ID {$cert->id}");
                }
            }
        } else {
            $this->line('   ✅ No orphaned certificates found');
        }
        
        return $issues;
    }

    /**
     * Check QR code consistency
     */
    private function checkQrCodeConsistency(): int
    {
        $this->info('📱 Checking QR code consistency...');
        
        $issues = 0;
        $certificates = Certificate::all();
        
        foreach ($certificates as $certificate) {
            $expectedQrPath = 'certificates/qr/' . $certificate->certificate_number . '.svg';
            
            if (empty($certificate->qr_code)) {
                $this->error("❌ Certificate {$certificate->certificate_number} missing QR code");
                
                if ($this->option('fix')) {
                    $certificate->generateQrCode();
                    $this->info("   ✅ Generated QR code for {$certificate->certificate_number}");
                }
                
                $issues++;
            } elseif ($certificate->qr_code !== $expectedQrPath) {
                $this->error("❌ Certificate {$certificate->certificate_number} has inconsistent QR path:");
                $this->line("   Current: {$certificate->qr_code}");
                $this->line("   Expected: {$expectedQrPath}");
                
                if ($this->option('fix')) {
                    $certificate->generateQrCode();
                    $this->info("   ✅ Fixed QR code path for {$certificate->certificate_number}");
                }
                
                $issues++;
            }
        }
        
        if ($issues === 0) {
            $this->line('   ✅ All QR codes are consistent');
        }
        
        return $issues;
    }

    /**
     * Check PDF path consistency
     */
    private function checkPdfPathConsistency(): int
    {
        $this->info('📄 Checking PDF path consistency...');
        
        $issues = 0;
        $certificates = Certificate::whereNotNull('pdf_path')->get();
        
        foreach ($certificates as $certificate) {
            $expectedPdfPath = 'certificates/' . $certificate->certificate_number . '.pdf';
            
            if ($certificate->pdf_path !== $expectedPdfPath) {
                $this->error("❌ Certificate {$certificate->certificate_number} has inconsistent PDF path:");
                $this->line("   Current: {$certificate->pdf_path}");
                $this->line("   Expected: {$expectedPdfPath}");
                
                if ($this->option('fix')) {
                    $certificate->update(['pdf_path' => $expectedPdfPath]);
                    $this->info("   ✅ Fixed PDF path for {$certificate->certificate_number}");
                }
                
                $issues++;
            }
        }
        
        if ($issues === 0) {
            $this->line('   ✅ All PDF paths are consistent');
        }
        
        return $issues;
    }

    /**
     * Check status transition integrity
     */
    private function checkStatusTransitionIntegrity(): int
    {
        $this->info('🔄 Checking status transition integrity...');
        
        $issues = 0;
        
        // Check for active/issued certificates without issued_at
        $invalidActive = Certificate::whereIn('status', ['active', 'issued'])
            ->whereNull('issued_at')
            ->get();
        
        if ($invalidActive->count() > 0) {
            $this->error("❌ Found {$invalidActive->count()} active/issued certificates without issued_at:");
            
            foreach ($invalidActive as $cert) {
                $this->line("   - Certificate {$cert->certificate_number} (Status: {$cert->status})");
                
                if ($this->option('fix')) {
                    $cert->update(['issued_at' => $cert->updated_at]);
                    $this->info("   ✅ Set issued_at for {$cert->certificate_number}");
                }
            }
            
            $issues += $invalidActive->count();
        } else {
            $this->line('   ✅ All active/issued certificates have valid issued_at');
        }
        
        return $issues;
    }

    /**
     * Check cross-reference integrity
     */
    private function checkCrossReferenceIntegrity(): int
    {
        $this->info('🔗 Checking cross-reference integrity...');
        
        $issues = 0;
        $certificates = Certificate::with(['enrollment', 'user'])->get();
        
        foreach ($certificates as $certificate) {
            // Check enrollment reference
            if (!$certificate->enrollment) {
                $this->error("❌ Certificate {$certificate->certificate_number} references non-existent enrollment {$certificate->enrollment_id}");
                $issues++;
            }
            
            // Check user reference
            if (!$certificate->user) {
                $this->error("❌ Certificate {$certificate->certificate_number} references non-existent user {$certificate->user_id}");
                $issues++;
            }
            
            // Check enrollment-user consistency
            if ($certificate->enrollment && $certificate->user) {
                if ($certificate->enrollment->user_id !== $certificate->user_id) {
                    $this->error("❌ Certificate {$certificate->certificate_number} has mismatched user references:");
                    $this->line("   Certificate user_id: {$certificate->user_id}");
                    $this->line("   Enrollment user_id: {$certificate->enrollment->user_id}");
                    
                    if ($this->option('fix')) {
                        $certificate->update(['user_id' => $certificate->enrollment->user_id]);
                        $this->info("   ✅ Fixed user reference for {$certificate->certificate_number}");
                    }
                    
                    $issues++;
                }
            }
        }
        
        if ($issues === 0) {
            $this->line('   ✅ All cross-references are consistent');
        }
        
        return $issues;
    }
}
