<template>
  <Head title="Mes inscriptions" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Mes inscriptions
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Filtres -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Filtrer mes inscriptions</h3>
            <div class="flex flex-wrap gap-4">
              <div>
                <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                <select
                  id="status-filter"
                  v-model="statusFilter"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="">Tous les statuts</option>
                  <option value="pending">En attente</option>
                  <option value="approved">Approuvée</option>
                  <option value="rejected">Rejetée</option>
                  <option value="completed">Terminée</option>
                  <option value="cancelled">Annulée</option>
                </select>
              </div>
              <div>
                <label for="search-filter" class="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
                <input
                  id="search-filter"
                  v-model="searchFilter"
                  type="text"
                  placeholder="Rechercher une formation..."
                  class="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Liste des inscriptions -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <h3 class="text-lg font-semibold mb-4">Liste de mes inscriptions</h3>
            
            <div v-if="filteredEnrollments.length > 0" class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Formation</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dates</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="enrollment in filteredEnrollments" :key="enrollment.id">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">{{ enrollment.training_session.title }}</div>
                      <div class="text-xs text-gray-500">{{ enrollment.training_session.training_domain.name }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ formatDate(enrollment.training_session.start_date) }} - {{ formatDate(enrollment.training_session.end_date) }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="getStatusClass(enrollment.status)" class="px-2 py-1 text-xs rounded-full">
                        {{ getStatusLabel(enrollment.status) }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                      <div class="flex space-x-2">
                        <Link 
                          :href="route('student.enrollments.show', enrollment.id)" 
                          class="text-indigo-600 hover:text-indigo-900"
                        >
                          Voir détails
                        </Link>
                        
                        <template v-if="enrollment.status === 'pending'">
                          <span class="text-gray-300">|</span>
                          <button 
                            @click="confirmCancel(enrollment)" 
                            class="text-red-600 hover:text-red-900"
                          >
                            Annuler
                          </button>
                        </template>
                        
                        <template v-if="enrollment.status === 'approved'">
                          <span class="text-gray-300">|</span>
                          <Link 
                            :href="route('student.courses.show', enrollment.training_session.id)" 
                            class="text-green-600 hover:text-green-900"
                          >
                            Accéder au contenu
                          </Link>
                        </template>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <div v-else class="text-center py-8 text-gray-500">
              Aucune inscription trouvée.
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Modal de confirmation d'annulation -->
    <div v-if="showCancelModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Confirmer l'annulation</h3>
        <p class="text-gray-600 mb-6">
          Êtes-vous sûr de vouloir annuler votre inscription à la formation "{{ enrollmentToCancel?.training_session.title }}" ?
        </p>
        <div class="flex justify-end space-x-3">
          <button 
            @click="showCancelModal = false" 
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
          >
            Annuler
          </button>
          <button 
            @click="cancelEnrollment" 
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Confirmer
          </button>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// Props
const props = defineProps({
  enrollments: Array,
});

// État local
const statusFilter = ref('');
const searchFilter = ref('');
const showCancelModal = ref(false);
const enrollmentToCancel = ref(null);

// Inscriptions filtrées
const filteredEnrollments = computed(() => {
  return props.enrollments.filter(enrollment => {
    // Filtre par statut
    if (statusFilter.value && enrollment.status !== statusFilter.value) {
      return false;
    }
    
    // Filtre par recherche
    if (searchFilter.value) {
      const searchTerm = searchFilter.value.toLowerCase();
      return enrollment.training_session.title.toLowerCase().includes(searchTerm) ||
             enrollment.training_session.training_domain.name.toLowerCase().includes(searchTerm);
    }
    
    return true;
  });
});

// Méthodes
const formatDate = (dateString) => {
  if (!dateString) return '';
  const options = { day: 'numeric', month: 'long', year: 'numeric' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
};

const getStatusLabel = (status) => {
  const labels = {
    'pending': 'En attente',
    'approved': 'Approuvée',
    'rejected': 'Rejetée',
    'completed': 'Terminée',
    'cancelled': 'Annulée'
  };
  return labels[status] || status;
};

const getStatusClass = (status) => {
  const classes = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'approved': 'bg-green-100 text-green-800',
    'rejected': 'bg-red-100 text-red-800',
    'completed': 'bg-blue-100 text-blue-800',
    'cancelled': 'bg-gray-100 text-gray-800'
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

const confirmCancel = (enrollment) => {
  enrollmentToCancel.value = enrollment;
  showCancelModal.value = true;
};

const cancelEnrollment = () => {
  if (enrollmentToCancel.value) {
    window.location.href = route('student.enrollments.cancel', enrollmentToCancel.value.id);
  }
  showCancelModal.value = false;
};
</script>
