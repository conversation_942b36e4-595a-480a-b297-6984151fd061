<script setup>
import { Link } from '@inertiajs/vue3';
</script>

<template>
  <div class="min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white shadow-sm">
      <nav class="container mx-auto px-4 py-4 flex justify-between items-center">
        <div class="flex items-center">
          <Link :href="route('welcome')">
            <img src="/logo-pcmet.png" alt="PCMET Logo" class="h-12">
            </Link>
        </div>
        <div class="hidden md:flex space-x-8">
          <Link :href="route('sessions.index')" class="text-gray-600 hover:text-blue-600">Sessions disponibles</Link>
          <a href="/#formations" class="text-gray-600 hover:text-blue-600">Nos formations</a>
          <a href="/#equipe" class="text-gray-600 hover:text-blue-600">Notre équipe</a>
          <a href="/#contact" class="text-gray-600 hover:text-blue-600">Contact</a>
        </div>
        <div class="flex items-center space-x-4">
          <Link :href="route('login')" class="text-gray-600 hover:text-blue-600">Connexion</Link>
          <Link :href="route('register')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">Inscription</Link>
        </div>
      </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-grow">
            <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 class="text-xl font-bold mb-4">PCMET</h3>
            <p class="text-gray-400">Votre partenaire de confiance pour des formations professionnelles de qualité.</p>
          </div>
          <div>
            <h3 class="text-xl font-bold mb-4">Contact</h3>
            <p class="text-gray-400">Email: <EMAIL></p>
            <p class="text-gray-400">Tél: +216 XX XXX XXX</p>
          </div>
          <div>
            <h3 class="text-xl font-bold mb-4">Liens rapides</h3>
            <ul class="space-y-2">
              <li><Link :href="route('sessions.index')" class="text-gray-400 hover:text-white">Sessions disponibles</Link></li>
              <li><a href="/#formations" class="text-gray-400 hover:text-white">Nos formations</a></li>
              <li><a href="/#contact" class="text-gray-400 hover:text-white">Contact</a></li>
            </ul>
          </div>
        </div>
        <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; {{ new Date().getFullYear() }} PCMET. Tous droits réservés.</p>
        </div>
      </div>
    </footer>
    </div>
</template>
