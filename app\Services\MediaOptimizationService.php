<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class MediaOptimizationService
{
    /**
     * Optimize and store an uploaded image
     */
    public function optimizeImage(UploadedFile $file, string $directory = 'homepage'): string
    {
        $filename = $this->generateFilename($file);
        $path = $directory . '/' . $filename;
        
        // Store the original file
        $storedPath = $file->storeAs($directory, $filename, 'public');
        
        // For now, we'll just store the file as-is
        // In a production environment, you would use a library like Intervention Image
        // to resize and optimize the image
        
        return $storedPath;
    }

    /**
     * Optimize and store an uploaded video
     */
    public function optimizeVideo(UploadedFile $file, string $directory = 'homepage'): string
    {
        $filename = $this->generateFilename($file);
        $path = $directory . '/' . $filename;
        
        // Store the video file
        $storedPath = $file->storeAs($directory, $filename, 'public');
        
        // For video optimization, you would typically use FFmpeg
        // For now, we'll just store the original file
        
        return $storedPath;
    }

    /**
     * Generate a unique filename for the uploaded file
     */
    private function generateFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $sanitizedName = Str::slug($name);
        
        return $sanitizedName . '_' . time() . '.' . $extension;
    }

    /**
     * Delete a media file
     */
    public function deleteMedia(string $path): bool
    {
        if (Storage::disk('public')->exists($path)) {
            return Storage::disk('public')->delete($path);
        }
        
        return false;
    }

    /**
     * Get file size in human readable format
     */
    public function getFileSize(string $path): string
    {
        if (!Storage::disk('public')->exists($path)) {
            return 'Unknown';
        }
        
        $bytes = Storage::disk('public')->size($path);
        
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Validate image file
     */
    public function validateImage(UploadedFile $file): array
    {
        $errors = [];
        
        // Check file size (max 5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            $errors[] = 'L\'image ne doit pas dépasser 5MB.';
        }
        
        // Check file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedTypes)) {
            $errors[] = 'Format d\'image non supporté. Utilisez JPEG, PNG, GIF ou WebP.';
        }
        
        return $errors;
    }

    /**
     * Validate video file
     */
    public function validateVideo(UploadedFile $file): array
    {
        $errors = [];
        
        // Check file size (max 50MB)
        if ($file->getSize() > 50 * 1024 * 1024) {
            $errors[] = 'La vidéo ne doit pas dépasser 50MB.';
        }
        
        // Check file type
        $allowedTypes = ['video/mp4', 'video/webm', 'video/ogg'];
        if (!in_array($file->getMimeType(), $allowedTypes)) {
            $errors[] = 'Format vidéo non supporté. Utilisez MP4, WebM ou OGG.';
        }
        
        return $errors;
    }

    /**
     * Generate thumbnail for video (placeholder for now)
     */
    public function generateVideoThumbnail(string $videoPath): ?string
    {
        // This would typically use FFmpeg to generate a thumbnail
        // For now, return null
        return null;
    }

    /**
     * Get media metadata
     */
    public function getMediaMetadata(string $path): array
    {
        if (!Storage::disk('public')->exists($path)) {
            return [];
        }
        
        $fullPath = Storage::disk('public')->path($path);
        
        $metadata = [
            'size' => $this->getFileSize($path),
            'mime_type' => mime_content_type($fullPath),
            'last_modified' => Storage::disk('public')->lastModified($path),
        ];
        
        // For images, get dimensions
        if (strpos($metadata['mime_type'], 'image/') === 0) {
            $imageInfo = getimagesize($fullPath);
            if ($imageInfo) {
                $metadata['width'] = $imageInfo[0];
                $metadata['height'] = $imageInfo[1];
                $metadata['dimensions'] = $imageInfo[0] . 'x' . $imageInfo[1];
            }
        }
        
        return $metadata;
    }
}
