<?php

namespace App\Observers;

use App\Models\Enrollment;
use App\Models\Certificate;
use App\Services\NotificationService;
use Illuminate\Support\Facades\Log;

class EnrollmentObserver
{
    /**
     * Handle the Enrollment "created" event.
     */
    public function created(Enrollment $enrollment): void
    {
        //
    }

    /**
     * Handle the Enrollment "updated" event.
     */
    public function updated(Enrollment $enrollment): void
    {
        // Check if status changed to 'approved'
        if ($enrollment->isDirty('status') && $enrollment->status === 'approved') {
            $this->generateDraftCertificate($enrollment);
        }

        // Send notification if status changed
        if ($enrollment->isDirty('status')) {
            try {
                $notificationService = app(NotificationService::class);
                $notificationService->notifyEnrollmentStatusChange($enrollment);
            } catch (\Exception $e) {
                Log::error('Failed to send enrollment notification: ' . $e->getMessage());
            }
        }

        // Check if both conditions are met for invoice generation
        $this->checkAndSendInvoice($enrollment);
    }

    /**
     * Handle the Enrollment "deleted" event.
     */
    public function deleted(Enrollment $enrollment): void
    {
        //
    }

    /**
     * Handle the Enrollment "restored" event.
     */
    public function restored(Enrollment $enrollment): void
    {
        //
    }

    /**
     * Handle the Enrollment "force deleted" event.
     */
    public function forceDeleted(Enrollment $enrollment): void
    {
        //
    }

    /**
     * Check if both conditions are met and send invoice to student
     */
    private function checkAndSendInvoice(Enrollment $enrollment): void
    {
        // Only proceed if both conditions are met: approved status AND paid payment
        if ($enrollment->status === 'approved' && $enrollment->payment_status === 'paid') {
            try {
                $emailService = app(\App\Services\PaymentEmailService::class);
                $emailService->sendInvoiceToStudent($enrollment);

                Log::info("Invoice sent to student for enrollment {$enrollment->id}");
            } catch (\Exception $e) {
                Log::error("Failed to send invoice for enrollment {$enrollment->id}: " . $e->getMessage());
            }
        }
    }

    /**
     * Generate a draft certificate when enrollment is approved
     */
    private function generateDraftCertificate(Enrollment $enrollment): void
    {
        try {
            // Check if a certificate already exists for this enrollment
            $existingCertificate = Certificate::where('enrollment_id', $enrollment->id)->first();

            if (!$existingCertificate) {
                // Generate final certificate number (no DRAFT suffix)
                $certificateNumber = Certificate::generateCertificateNumber($enrollment->id);

                // Create draft certificate with final certificate number and QR code
                Certificate::create([
                    'enrollment_id' => $enrollment->id,
                    'user_id' => $enrollment->user_id,
                    'training_session_id' => $enrollment->training_session_id,
                    'certificate_number' => $certificateNumber,
                    'status' => 'draft',
                    'issued_at' => null, // Will be set when activated
                    'issue_date' => null, // Will be set when activated
                ]);
                // Note: QR code will be automatically generated by the Certificate model's boot method

                Log::info("Draft certificate created for enrollment {$enrollment->id} with certificate number {$certificateNumber}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to create draft certificate for enrollment {$enrollment->id}: " . $e->getMessage());
        }
    }
}
