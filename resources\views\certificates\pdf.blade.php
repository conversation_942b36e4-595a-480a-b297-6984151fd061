<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Attestation de Formation</title>
    <style>
        @page {
            margin: 0;
            size: A4 portrait;
        }

        body {
            font-family: 'Deja<PERSON>u Sans', <PERSON><PERSON>, sans-serif;
            color: #000;
            margin: 0;
            padding: 0;
            background: white;
            font-size: 12px;
            line-height: 1.3;
        }

        .certificate-container {
            width: 100%;
            height:100vh;
            position: relative;
            background-color: white;
            padding: 20px;
            box-sizing: border-box;
        }

        .border-frame {
            position: absolute;
            width: 90%;
            height: 90vh;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            z-index: 1;
        }



        .content {
            position: relative;
            z-index: 3;
            padding: 40px;
            text-align: center;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            padding: 10px 30px;
            width: 100%;
        }

        .header-left {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            justify-content: flex-start;
            flex: 1;
        }

        .header-right {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            justify-content: flex-end;
            flex-direction: row-reverse;
            flex: 1;
        }

        .logo-flag {
            width: 30px;
            height: 20px;
            flex-shrink: 0;
            margin-bottom: 5px;
            margin-left: 25px !important;


        }

        .logo-ministry {
            width: 233px;
            height: 119px;
            flex-shrink: 0;
            margin-bottom: 5px;
        }

        .logo-pcmet {
            width: 50px;
            height: 50px;
            flex-shrink: 0;
            margin-bottom: 5px;
        }

        .header-text-left {
            font-size: 12px;
            line-height: 1.3;
            color: #333;
            font-weight: bold;
            font-family: "Times New Roman", Times, serif;
            text-align: left;
        }

        .header-text-right {
            font-size: 10px;
            line-height: 1.4;
            color: #333;
            text-align: right;
            direction: rtl;
            font-weight: bold;
            font-family: 'Arial Unicode MS', 'Tahoma', 'DejaVu Sans', sans-serif;
            unicode-bidi: bidi-override;
        }

        .arabic-text {
            font-size: 10px;
            line-height: 1.4;
            color: #333;
            text-align: right;
            direction: rtl;
            font-weight: bold;
            font-family: 'DejaVu Sans', 'Arial Unicode MS', 'Tahoma', 'Arial', sans-serif;
            unicode-bidi: bidi-override;
        }

        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
        @import url('https://fonts.cdnfonts.com/css/copperplate');

        .main-title {
            font-size: 48px;
            font-weight: bold;
            color: #000033;
            margin: 40px 0 5px 0;
            letter-spacing: 2px;
             font-family: Copperplate, "Copperplate Gothic Light", fantasy;
        }

        .subtitle {
            font-size: 18px;
            font-weight: bold;
            color: #000;
            margin-bottom: 30px;
        }

        .director-text {
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .student-name {
            font-size: 22px;
            font-weight: bold;
            color: #000033;
            margin: 20px 0;
        }

        .id-number {
            font-size: 14px;
            color: #000033;
            margin-bottom: 20px;
        }

        .formation-text {
            font-size: 14px;
            margin-bottom: 10px;
        }

        .formation-title {
            font-size: 20px;
            font-weight: bold;
            color: #000033;
            margin: 10px 0 20px 0;
        }

        .date-location {
            font-size: 14px;
            font-style: italic;
            margin: 20px 0;
            line-height: 1.5;
        }

        .objectives-text {
            text-align: justify;
            font-size: 16px;
            line-height: 1.5;
            margin: 20px 50px;
            text-align: center;
            font-style: italic;
            font-family: Georgia, serif;
        }

        .footer {
            display: table;
            width: 100%;
            margin-top: 40px;
            table-layout: fixed;
        }

        .signature-section {
            display: table-cell;
            width: 33.33%;
            text-align: center;
            vertical-align: top;
            padding: 0 10px;
        }

        .signature-title {
            font-size: 11px;
            font-weight: normal;
            color: #000;
            margin-top: 10px;
        }

        .qr-section {
            display: table-cell;
            width: 33.33%;
            text-align: center;
            vertical-align: top;
            padding: 0 10px;
        }

        .qr-code {
            width: 60px;
            height: 60px;
            margin: 0 auto 8px auto;
            border: 1px solid #000;
            display: block;
        }

        .validity-text {
            font-size: 10px;
            margin-top: 5px;
            line-height: 1.2;
            color: #000;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="certificate-container">
        @php
            $borderImage = '';
            try {
                if (file_exists(public_path('images/0.png'))) {
                    $borderImage = 'data:image/png;base64,' . base64_encode(file_get_contents(public_path('images/0.png')));
                }
            } catch (Exception $e) {
                // Ignorer les erreurs de lecture de fichier
            }
        @endphp

        @if($borderImage)
            <img class="border-frame" src="{{ $borderImage }}" alt="Contour du certificat">
        @endif

        <div class="content">
            <!-- Header avec informations officielles -->
            <div class="header">
                @php
                    $tunisianFlag = '';
                    $ministryLogo = '';
                    $pcmetLogo = '';
                    $arabicTextImage = '';

                    try {
                        if (file_exists(public_path('images/1.png'))) {
                            $tunisianFlag = 'data:image/png;base64,' . base64_encode(file_get_contents(public_path('images/1.png')));
                        }
                        if (file_exists(public_path('images/3.png'))) {
                            $ministryLogo = 'data:image/png;base64,' . base64_encode(file_get_contents(public_path('images/3.png')));
                        }
                        if (file_exists(public_path('images/2.png'))) {
                            $pcmetLogo = 'data:image/png;base64,' . base64_encode(file_get_contents(public_path('images/2.png')));
                        }

                        // Créer une image avec le texte arabe si GD est disponible
                        if (extension_loaded('gd')) {
                            $width = 300;
                            $height = 80;
                            $image = imagecreate($width, $height);
                            $white = imagecolorallocate($image, 255, 255, 255);
                            $black = imagecolorallocate($image, 51, 51, 51);

                            // Texte arabe (écrit de droite à gauche)
                            $arabicLines = [
                                'الجمهورية التونسية',
                                'وزارة التكوين المهني والتشغيل',
                                'مركز التكوين PCMET',
                                'عدد التسجيل : 11-2000-21'
                            ];

                            $y = 15;
                            foreach ($arabicLines as $line) {
                                imagestring($image, 3, 10, $y, $line, $black);
                                $y += 18;
                            }

                            ob_start();
                            imagepng($image);
                            $imageData = ob_get_contents();
                            ob_end_clean();
                            imagedestroy($image);

                            $arabicTextImage = 'data:image/png;base64,' . base64_encode($imageData);
                        }
                    } catch (Exception $e) {
                        // Ignorer les erreurs de lecture de fichier
                    }
                @endphp

                <div class="header-left">
                    <div style="display: flex; flex-direction: column; align-items: center; gap: 5px;text-align: left;margin-top: 40px;">
                        @if($tunisianFlag)
                            <img class="logo-flag" src="{{ $tunisianFlag }}" alt="Drapeau Tunisien">
                        @else
                            <div style="width: 50px; height: 30px; background: red; color: white; display: flex; align-items: center; justify-content: center; font-size: 8px;">FLAG</div>
                        @endif
                        <div class="header-text-left">
                            Republic of Tunisia<br>
                            Ministry of Vocational Training and Employment<br>
                            PCMET Training Center<br>
                            Registration N° : 11-2000-21
                        </div>
                    </div>
                </div>
                <div class="header-right">
                    <div style="text-align: right;align-items: flex-end;">
                        <div style="display: flex; flex-direction: column;  gap: 5px;text-align: right;align-items: flex-end;margin-top: -100px;margin-right:95px">
                            @if($ministryLogo)
                                <img class="logo-ministry" src="{{ $ministryLogo }}" alt="Logo Ministère">
                            @endif

                        </div>


                    </div>
                </div>
            </div>

            <!-- Contenu principal -->
            <div class="main-title">ATTESTATION</div>
            <div class="subtitle">DE FORMATION</div>

            <div class="director-text">
                Le Directeur du centre de formation PCMET<br>
                HORIZON QUALITÉ enregistré sous le numéro<br>
                11200021 atteste que l'apprenant(e)
            </div>

            <div class="student-name">{{ $user->name }}</div>

            <div class="id-number">
                @php
                    $idCardNumber = $user->id_card_number ?? '00000000';
                    // S'assurer que le numéro fait au moins 8 caractères
                    $idCardNumber = str_pad($idCardNumber, 8, '0', STR_PAD_LEFT);
                    // Masquer les 5 premiers chiffres avec des étoiles
                    $maskedIdCard = '*****' . substr($idCardNumber, -3);
                @endphp
                Titulaire de la carte d'identité nationale numéro : {{ $maskedIdCard }}
            </div>

            <div class="formation-text">A suivie avec succès la formation en:</div>

            <div class="formation-title">
                @if($trainingSession->certificate_name)
                    {{ $trainingSession->certificate_name }}
                @else
                    {{ $trainingSession->title }}
                @endif
            </div>

            <div class="date-location">
                @php
                    try {
                        $startDate = 'N/A';
                        $endDate = 'N/A';

                        if (isset($trainingSession->start_date) && $trainingSession->start_date) {
                            if (is_string($trainingSession->start_date)) {
                                $startDate = \Carbon\Carbon::parse($trainingSession->start_date)->format('d/m/Y');
                            } else {
                                $startDate = $trainingSession->start_date->format('d/m/Y');
                            }
                        }

                        if (isset($trainingSession->end_date) && $trainingSession->end_date) {
                            if (is_string($trainingSession->end_date)) {
                                $endDate = \Carbon\Carbon::parse($trainingSession->end_date)->format('d/m/Y');
                            } else {
                                $endDate = $trainingSession->end_date->format('d/m/Y');
                            }
                        }
                    } catch (Exception $e) {
                        $startDate = 'N/A';
                        $endDate = 'N/A';
                    }
                @endphp
                Qui s'est déroulée à Tunis du {{ $startDate }} au {{ $endDate }} en foi de quoi,<br>
                Lui délivre la présente attestation
            </div>

            @if($trainingSession->training_objectives)
            <div class="objectives-text">
                {{ $trainingSession->training_objectives }}
            </div>
            @else
            <div class="objectives-text">
                Cette formation a permis au participant de maîtriser les gestes essentiels de premiers secours, d'assurer la sécurité de la victime et de transmettre une alerte efficace. Les compétences ont été validées par des mises en situation pratiques.
            </div>
            @endif

            <!-- Pied de page avec signatures et QR code -->
            <div class="footer">
                <div class="signature-section">
                    <div class="signature-title">Signature du Formateur</div>
                </div>

                <div class="qr-section">
                    @if(isset($certificate->qr_code) && $certificate->qr_code)
                        @php
                            $qrCodeContent = '';
                            try {
                                if (\Illuminate\Support\Facades\Storage::disk('public')->exists($certificate->qr_code)) {
                                    $qrCodeContent = \Illuminate\Support\Facades\Storage::disk('public')->get($certificate->qr_code);
                                    // Si c'est un SVG, l'encoder en base64 pour l'affichage
                                    if (str_ends_with($certificate->qr_code, '.svg')) {
                                        $qrCodeContent = 'data:image/svg+xml;base64,' . base64_encode($qrCodeContent);
                                    } else {
                                        $qrCodeContent = 'data:image/png;base64,' . base64_encode($qrCodeContent);
                                    }
                                }
                            } catch (Exception $e) {
                                $qrCodeContent = '';
                            }
                        @endphp

                        @if($qrCodeContent)
                            <img class="qr-code" src="{{ $qrCodeContent }}" alt="QR Code">
                        @else
                            <div class="qr-code" style="background: #f0f0f0; display: flex; align-items: center; justify-content: center; font-size: 8px; border: 2px solid #000;">
                                QR CODE
                            </div>
                        @endif
                    @else
                        <div class="qr-code" style="background: #f0f0f0; display: flex; align-items: center; justify-content: center; font-size: 8px; border: 2px solid #000;">
                            QR CODE
                        </div>
                    @endif
                    <div class="validity-text">
                        Validité de l'attestation : 2 ans<br>
                        {{ $certificate->certificate_number }}
                    </div>
                </div>

                <div class="signature-section">
                    <div class="signature-title">Signature du Directeur</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
