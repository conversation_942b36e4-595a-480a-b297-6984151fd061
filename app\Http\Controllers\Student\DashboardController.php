<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Models\Enrollment;
use App\Models\CourseProgress;
use App\Models\ExamResult;
use App\Models\Certificate;
use App\Models\TrainingSession;
use App\Models\Announcement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DashboardController extends Controller
{
    /**
     * Affiche le tableau de bord de l'apprenant
     */
    public function index()
    {
        // Récupérer l'utilisateur connecté (apprenant)
        $student = Auth::user();

        // Récupérer les inscriptions de l'apprenant
        $enrollments = Enrollment::where('user_id', $student->id)
            ->with('trainingSession.trainingDomain')
            ->get();

        // Récupérer les IDs des sessions de formation auxquelles l'apprenant est inscrit
        $sessionIds = $enrollments->pluck('training_session_id');

        // Récupérer les niveaux complétés par département
        $completedLevels = $student->getCompletedLevelsByDepartment();

        // Récupérer un résumé des sessions par département pour les cartes
        $sessionsByDepartment = $this->getSessionsSummaryByDepartment($student, $completedLevels);

        // Récupérer les statistiques pour le tableau de bord
        $stats = [
            'enrollments_count' => $enrollments->count(),
            'active_enrollments_count' => $enrollments->where('status', 'approved')->count(),
            'pending_enrollments_count' => $enrollments->where('status', 'pending')->count(),
            'completed_enrollments_count' => $enrollments->where('status', 'completed')->count(),
            'completed_courses_count' => CourseProgress::where('user_id', $student->id)
                ->where('completed', true)
                ->count(),
            'passed_exams_count' => ExamResult::where('user_id', $student->id)
                ->where('passed', true)
                ->count(),
            'certificates_count' => Certificate::where('user_id', $student->id)
                ->visibleToStudents()
                ->count(),
        ];

        // Récupérer les sessions de formation actives de l'apprenant
        $activeSessions = TrainingSession::whereIn('id', $sessionIds)
            ->where('active', true)
            ->with(['trainingDomain', 'trainer'])
            ->get();

        // Récupérer les derniers résultats d'examen
        $recentExamResults = ExamResult::where('user_id', $student->id)
            ->with('exam.trainingSession')
            ->orderBy('completed_at', 'desc')
            ->take(5)
            ->get();

        // Récupérer les certificats de l'apprenant (seulement ceux visibles aux étudiants)
        $certificates = Certificate::where('user_id', $student->id)
            ->visibleToStudents()
            ->with('trainingSession.trainingDomain')
            ->orderBy('issued_at', 'desc')
            ->get();

        // Récupérer les annonces importantes et générales
        $announcements = Announcement::published()
            ->visibleTo('student')
            ->where(function($query) use ($sessionIds) {
                $query->whereNull('training_session_id')
                    ->orWhereIn('training_session_id', $sessionIds);
            })
            ->orderBy('is_important', 'desc')
            ->orderBy('publish_date', 'desc')
            ->take(5)
            ->get();

        // Récupérer la prochaine session à venir
        $nextSession = TrainingSession::whereIn('id', $sessionIds)
            ->where('active', true)
            ->where('start_date', '>', now())
            ->with(['trainingDomain', 'trainer'])
            ->orderBy('start_date', 'asc')
            ->first();

        // Retourner la vue avec les données
        return Inertia::render('Student/Dashboard', [
            'stats' => $stats,
            'activeSessions' => $activeSessions,
            'recentExamResults' => $recentExamResults,
            'certificates' => $certificates,
            'announcements' => $announcements,
            'nextSession' => $nextSession,
            'sessionsByDepartment' => $sessionsByDepartment,
            'completedLevels' => $completedLevels,
            'departments' => TrainingSession::DEPARTMENTS,
        ]);
    }

    /**
     * Récupère un résumé des sessions par département pour les cartes du dashboard
     */
    private function getSessionsSummaryByDepartment($student, $completedLevels)
    {
        $departments = TrainingSession::DEPARTMENTS;
        $sessionsSummary = [];

        foreach ($departments as $department) {
            $levels = ['Niveau 1', 'Niveau 2', 'Niveau 3', 'Niveau 4', 'Niveau 5', 'Niveau 6', 'Niveau 7'];
            $sessionsSummary[$department] = [];

            foreach ($levels as $level) {
                // Récupérer les sessions pour ce niveau et département
                $sessions = TrainingSession::where('department', $department)
                    ->where('level', $level)
                    ->where('active', true)
                    ->withCount('enrollments')
                    ->get();

                if ($sessions->isNotEmpty()) {
                    $sessionsSummary[$department][$level] = [];

                    foreach ($sessions as $session) {
                        // Pour le résumé, on stocke juste les sessions avec un état simple
                        $enrollment = $student->enrollments()
                            ->where('training_session_id', $session->id)
                            ->first();

                        $state = 'available';
                        if ($enrollment) {
                            $state = $enrollment->status;
                        } elseif (now() > $session->start_date) {
                            $state = 'started';
                        } elseif ($session->max_participants && $session->enrollments_count >= $session->max_participants) {
                            $state = 'full';
                        }

                        $sessionsSummary[$department][$level][] = [
                            'session' => $session,
                            'state' => $state,
                            'canEnroll' => $state === 'available'
                        ];
                    }
                }
            }
        }

        return $sessionsSummary;
    }

    /**
     * Récupère les sessions organisées par département et niveau avec les informations de progression
     */
    private function getSessionsByDepartment($student, $completedLevels)
    {
        $departments = TrainingSession::DEPARTMENTS;
        $sessionsByDepartment = [];

        foreach ($departments as $department) {
            $sessionsByDepartment[$department] = [];

            // Récupérer toutes les sessions de ce département
            $sessions = TrainingSession::where('department', $department)
                ->where('active', true)
                ->where('start_date', '>=', now()->startOfDay())
                ->with(['trainingDomain', 'trainer'])
                ->withCount('enrollments')
                ->orderBy('level')
                ->orderBy('start_date')
                ->get();

            // Organiser par niveau
            foreach ($sessions as $session) {
                $level = $session->level;

                if (!isset($sessionsByDepartment[$department][$level])) {
                    $sessionsByDepartment[$department][$level] = [];
                }

                // Déterminer l'état de la session pour cet apprenant
                $sessionState = $this->getSessionState($student, $session, $department, $level);

                $sessionsByDepartment[$department][$level][] = [
                    'session' => $session,
                    'state' => $sessionState['state'],
                    'reason' => $sessionState['reason'],
                    'canEnroll' => $sessionState['canEnroll'],
                ];
            }
        }

        return $sessionsByDepartment;
    }

    /**
     * Détermine l'état d'une session pour un apprenant donné
     */
    private function getSessionState($student, $session, $department, $level)
    {
        // Vérifier si l'apprenant est déjà inscrit
        $enrollment = $student->enrollments()
            ->where('training_session_id', $session->id)
            ->first();

        if ($enrollment) {
            switch ($enrollment->status) {
                case 'completed':
                    return [
                        'state' => 'completed',
                        'reason' => 'Formation terminée avec succès',
                        'canEnroll' => false
                    ];
                case 'approved':
                    return [
                        'state' => 'enrolled',
                        'reason' => 'Inscription approuvée',
                        'canEnroll' => false
                    ];
                case 'pending':
                    return [
                        'state' => 'pending',
                        'reason' => 'Inscription en attente d\'approbation',
                        'canEnroll' => false
                    ];
                case 'rejected':
                    return [
                        'state' => 'rejected',
                        'reason' => 'Inscription refusée',
                        'canEnroll' => false
                    ];
                default:
                    // Continuer l'évaluation pour les autres statuts
                    break;
            }
        }

        // Vérifier les prérequis de niveau
        if (!$student->canEnrollInLevel($department, $level)) {
            $levelNumber = (int) str_replace('Niveau ', '', $level);
            $previousLevel = "Niveau " . ($levelNumber - 1);

            return [
                'state' => 'locked',
                'reason' => "Vous devez d'abord terminer le $previousLevel en $department",
                'canEnroll' => false
            ];
        }

        // Vérifier les places disponibles
        if ($session->max_participants && $session->enrollments_count >= $session->max_participants) {
            return [
                'state' => 'full',
                'reason' => 'Session complète',
                'canEnroll' => false
            ];
        }

        // Vérifier si la session a déjà commencé
        if ($session->start_date <= now()->toDateString()) {
            return [
                'state' => 'started',
                'reason' => 'Session déjà commencée',
                'canEnroll' => false
            ];
        }

        // Session disponible
        return [
            'state' => 'available',
            'reason' => 'Inscription possible',
            'canEnroll' => true
        ];
    }
}
