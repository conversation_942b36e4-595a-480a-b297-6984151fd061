<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ExamResult;
use App\Models\Exam;
use App\Models\Enrollment;
use App\Models\ExamQuestion;

class ExamResultSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer les inscriptions approuvées ou complétées
        $enrollments = Enrollment::whereIn('status', ['approved', 'completed'])->get();

        foreach ($enrollments as $enrollment) {
            // Récupérer les examens pour la session de formation de cette inscription
            $exams = Exam::where('training_session_id', $enrollment->training_session_id)->get();

            foreach ($exams as $exam) {
                // Générer un score aléatoire (50-100)
                $score = rand(50, 100);

                // Déterminer si l'examen est réussi en fonction du score de passage
                $passed = $score >= $exam->passing_score;

                // Générer des réponses aléatoires pour les questions de l'examen
                $answers = $this->generateRandomAnswers($exam);

                // Créer le résultat d'examen
                ExamResult::create([
                    'exam_id' => $exam->id,
                    'enrollment_id' => $enrollment->id,
                    'user_id' => $enrollment->user_id,
                    'score' => $score,
                    'passed' => $passed,
                    'answers' => json_encode($answers),
                    'feedback' => $passed
                        ? 'Bon travail ! Vous avez réussi l\'examen avec un score de ' . $score . '%.'
                        : 'Vous n\'avez pas atteint le score minimum requis. Continuez à travailler sur les concepts clés.',
                    'created_at' => now()->subDays(rand(1, 10)),
                    'completed_at' => now()->subDays(rand(1, 10)),
                ]);
            }
        }
    }

    /**
     * Générer des réponses aléatoires pour les questions d'un examen
     */
    private function generateRandomAnswers(Exam $exam)
    {
        $answers = [];
        $questions = ExamQuestion::where('exam_id', $exam->id)->get();

        foreach ($questions as $question) {
            $answer = [
                'question_id' => $question->id,
            ];

            // Générer une réponse en fonction du type de question
            switch ($question->question_type) {
                case 'multiple_choice':
                    $options = json_decode($question->options, true);
                    $keys = array_keys($options);
                    $answer['answer'] = $keys[array_rand($keys)];
                    break;

                case 'text':
                    $answer['answer'] = 'Réponse textuelle pour la question ' . $question->id;
                    break;

                case 'file':
                    $answer['answer'] = 'fichier_reponse_' . $question->id . '.pdf';
                    break;
            }

            $answers[] = $answer;
        }

        return $answers;
    }
}
