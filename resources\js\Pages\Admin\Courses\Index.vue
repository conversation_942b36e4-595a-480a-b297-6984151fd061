<template>
  <Head title="Gestion des cours" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Gestion des cours
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- En-tête avec bouton d'ajout -->
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">Liste des cours</h3>
              <Link :href="route('admin.courses.create')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                Ajouter un cours
              </Link>
            </div>

            <!-- Messages de succès ou d'erreur -->
            <div v-if="$page.props.flash.success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              {{ $page.props.flash.success }}
            </div>
            <div v-if="$page.props.flash.error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {{ $page.props.flash.error }}
            </div>

            <!-- Formulaire de recherche et filtrage -->
            <div class="bg-gray-100 p-4 rounded-lg mb-6 border border-gray-200">
              <h3 class="text-lg font-medium mb-4">Recherche et filtrage</h3>
              <form @submit.prevent="applyFilters">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                  <!-- Barre de recherche -->
                  <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
                    <input
                      type="text"
                      id="search"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="Rechercher par titre..."
                      v-model="searchQuery"
                    />
                  </div>

                  <!-- Filtre par session de formation -->
                  <div>
                    <label for="training_session" class="block text-sm font-medium text-gray-700 mb-1">Session de formation</label>
                    <select
                      id="training_session"
                      v-model="sessionFilter"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Toutes les sessions</option>
                      <option v-for="session in trainingSessions" :key="session.id" :value="session.id">
                        {{ session.title }}
                      </option>
                    </select>
                  </div>

                  <!-- Filtre par domaine de formation -->
                  <div>
                    <label for="domain" class="block text-sm font-medium text-gray-700 mb-1">Domaine de formation</label>
                    <select
                      id="domain"
                      v-model="domainFilter"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Tous les domaines</option>
                      <option v-for="domain in domains" :key="domain.id" :value="domain.id">
                        {{ domain.name }}
                      </option>
                    </select>
                  </div>

                  <!-- Filtre par statut -->
                  <div>
                    <label for="active" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                    <select
                      id="active"
                      v-model="activeFilter"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Tous les statuts</option>
                      <option value="true">Actif</option>
                      <option value="false">Inactif</option>
                    </select>
                  </div>

                  <!-- Filtre par présence de matériels -->
                  <div>
                    <label for="has_materials" class="block text-sm font-medium text-gray-700 mb-1">Matériels pédagogiques</label>
                    <select
                      id="has_materials"
                      v-model="materialsFilter"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Tous les cours</option>
                      <option value="true">Avec matériels</option>
                      <option value="false">Sans matériels</option>
                    </select>
                  </div>
                </div>

                <!-- Boutons -->
                <div class="flex justify-end space-x-2">
                  <button
                    type="submit"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Filtrer
                  </button>
                  <button
                    type="button"
                    class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                    @click="resetFilters"
                  >
                    Réinitialiser
                  </button>
                </div>
              </form>
            </div>

            <!-- Tableau des cours -->
            <div class="overflow-x-auto">
              <table class="min-w-full bg-white border border-gray-200">
                <thead>
                  <tr>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                      Titre
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                      Session de formation
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                      Matériels
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                    <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="course in courses.data" :key="course.id">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">{{ course.title }}</div>
                      <div class="text-sm text-gray-500">Ordre: {{ course.order }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ course.training_session.title }}</div>
                      <div class="text-sm text-gray-500">{{ course.training_session.training_domain.name }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ course.materials ? course.materials.length : 0 }} matériels</div>
                      <div class="flex space-x-2">
                        <Link
                          :href="route('admin.modules.index', { course_id: course.id })"
                          class="text-sm text-purple-600 hover:text-purple-800"
                        >
                          Modules
                        </Link>
                        <Link
                          :href="route('admin.course-materials.index', { course_id: course.id })"
                          class="text-sm text-blue-600 hover:text-blue-800"
                        >
                          Matériels
                        </Link>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        :class="[
                          'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                          course.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        ]"
                      >
                        {{ course.active ? 'Actif' : 'Inactif' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div class="flex space-x-2">
                        <Link :href="route('admin.courses.show', course.id)" class="text-blue-600 hover:text-blue-900">
                          Voir
                        </Link>
                        <Link :href="route('admin.courses.edit', course.id)" class="text-indigo-600 hover:text-indigo-900">
                          Modifier
                        </Link>
                        <button
                          @click="confirmDelete(course)"
                          class="text-red-600 hover:text-red-900"
                        >
                          Supprimer
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr v-if="courses.data.length === 0">
                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                      Aucun cours trouvé
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            <Pagination :links="courses.links" class="mt-6" />

            <!-- Modal de confirmation de suppression -->
            <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
              <div class="bg-white rounded-lg overflow-hidden shadow-xl max-w-md w-full">
                <div class="p-6">
                  <h3 class="text-lg font-medium text-gray-900 mb-4">Confirmer la suppression</h3>
                  <p class="text-gray-600 mb-6">
                    Êtes-vous sûr de vouloir supprimer le cours "{{ courseToDelete?.title }}" ?
                    <span v-if="courseToDelete?.materials?.length > 0" class="text-red-600 font-semibold">
                      Ce cours contient {{ courseToDelete.materials.length }} matériels pédagogiques qui seront également supprimés.
                    </span>
                  </p>
                  <div class="flex justify-end space-x-3">
                    <button
                      @click="showDeleteModal = false"
                      class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
                    >
                      Annuler
                    </button>
                    <button
                      @click="deleteCourse"
                      class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                    >
                      Supprimer
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import Pagination from '@/Components/Pagination.vue';

// Props
const props = defineProps({
  courses: Object,
  trainingSessions: Array,
  domains: Array,
  filters: Object,
});

// État pour la modal de suppression
const showDeleteModal = ref(false);
const courseToDelete = ref(null);

// État pour les filtres
const searchQuery = ref(props.filters?.search || '');
const sessionFilter = ref(props.filters?.training_session_id || '');
const domainFilter = ref(props.filters?.domain_id || '');
const activeFilter = ref(props.filters?.active || '');
const materialsFilter = ref(props.filters?.has_materials || '');

// Méthodes pour les filtres
const applyFilters = () => {
  const params = {};

  // Ajouter la recherche si elle existe
  if (searchQuery.value) {
    params.search = searchQuery.value;
  }

  // Ajouter le filtre de session si il existe
  if (sessionFilter.value) {
    params.training_session_id = sessionFilter.value;
  }

  // Ajouter le filtre de domaine s'il existe
  if (domainFilter.value) {
    params.domain_id = domainFilter.value;
  }

  // Ajouter le filtre de statut s'il existe
  if (activeFilter.value !== '') {
    params.active = activeFilter.value;
  }

  // Ajouter le filtre de matériels s'il existe
  if (materialsFilter.value !== '') {
    params.has_materials = materialsFilter.value;
  }

  // Naviguer vers l'URL avec les paramètres
  router.get(route('admin.courses.index'), params, {
    preserveState: true,
    replace: true,
    preserveScroll: true
  });
};

const resetFilters = () => {
  searchQuery.value = '';
  sessionFilter.value = '';
  domainFilter.value = '';
  activeFilter.value = '';
  materialsFilter.value = '';

  // Naviguer vers l'URL sans paramètres
  router.get(route('admin.courses.index'), {}, {
    preserveState: true,
    replace: true,
    preserveScroll: true
  });
};

// Méthodes pour la suppression
const confirmDelete = (course) => {
  courseToDelete.value = course;
  showDeleteModal.value = true;
};

const deleteCourse = () => {
  router.delete(route('admin.courses.destroy', courseToDelete.value.id), {
    onSuccess: () => {
      showDeleteModal.value = false;
      courseToDelete.value = null;
    },
  });
};
</script>
