<template>
  <Head title="Ajouter une question" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        Ajouter une question à la banque
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 text-gray-900">
            <!-- Liens de navigation -->
            <div class="mb-6">
              <Link :href="route('trainer.question-bank.index')" class="text-indigo-600 hover:text-indigo-900">
                &larr; Retour à la banque de questions
              </Link>
            </div>

            <!-- Formulaire de création de question -->
            <form @submit.prevent="submit">
              <div class="grid grid-cols-1 gap-6">
                <!-- Examen -->
                <div>
                  <InputLabel for="exam_id" value="Examen" />
                  <select
                    id="exam_id"
                    v-model="form.exam_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    required
                  >
                    <option value="">Sélectionnez un examen</option>
                    <optgroup 
                      v-for="session in trainingSessions" 
                      :key="session.id" 
                      :label="session.title"
                    >
                      <option 
                        v-for="exam in examsForSession(session.id)" 
                        :key="exam.id" 
                        :value="exam.id"
                      >
                        {{ exam.title }}
                      </option>
                    </optgroup>
                  </select>
                  <InputError class="mt-2" :message="form.errors.exam_id" />
                </div>

                <!-- Type de question -->
                <div>
                  <InputLabel for="question_type" value="Type de question" />
                  <select
                    id="question_type"
                    v-model="form.question_type"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    required
                  >
                    <option value="multiple_choice">Choix multiple (QCM)</option>
                    <option value="text">Texte libre</option>
                    <option value="file">Soumission de fichier</option>
                  </select>
                  <InputError class="mt-2" :message="form.errors.question_type" />
                </div>

                <!-- Texte de la question -->
                <div>
                  <InputLabel for="question_text" value="Texte de la question" />
                  <textarea
                    id="question_text"
                    v-model="form.question_text"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    rows="3"
                    required
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.question_text" />
                </div>

                <!-- Options pour QCM -->
                <div v-if="form.question_type === 'multiple_choice'">
                  <InputLabel value="Options de réponse" />
                  <div class="mt-2 space-y-3">
                    <div v-for="(option, index) in options" :key="index" class="flex items-center">
                      <div class="flex-grow">
                        <div class="flex items-center">
                          <input
                            :id="`option_correct_${index}`"
                            type="checkbox"
                            v-model="correctAnswers"
                            :value="index"
                            class="mr-2 rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                            :disabled="!form.multiple_answers_allowed && correctAnswers.length > 0 && !correctAnswers.includes(index)"
                          />
                          <TextInput
                            :id="`option_${index}`"
                            v-model="options[index]"
                            type="text"
                            class="block w-full"
                            placeholder="Option de réponse"
                            required
                          />
                        </div>
                      </div>
                      <button
                        type="button"
                        @click="removeOption(index)"
                        class="ml-2 text-red-600 hover:text-red-900"
                        :disabled="options.length <= 2"
                      >
                        <XMarkIcon class="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                  <div class="mt-2">
                    <button
                      type="button"
                      @click="addOption"
                      class="text-sm text-indigo-600 hover:text-indigo-900"
                      :disabled="options.length >= 6"
                    >
                      + Ajouter une option
                    </button>
                  </div>
                  <div class="mt-4">
                    <div class="flex items-center">
                      <input
                        id="multiple_answers_allowed"
                        type="checkbox"
                        v-model="form.multiple_answers_allowed"
                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                      />
                      <label for="multiple_answers_allowed" class="ml-2 text-sm text-gray-600">
                        Autoriser plusieurs réponses correctes
                      </label>
                    </div>
                  </div>
                  <InputError class="mt-2" :message="form.errors.options" />
                </div>

                <!-- Réponse correcte pour texte libre -->
                <div v-if="form.question_type === 'text'">
                  <InputLabel for="correct_answer" value="Réponse correcte (facultatif, pour référence)" />
                  <textarea
                    id="correct_answer"
                    v-model="form.correct_answer"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                    rows="3"
                  ></textarea>
                  <InputError class="mt-2" :message="form.errors.correct_answer" />
                </div>

                <!-- Points -->
                <div>
                  <InputLabel for="points" value="Points" />
                  <TextInput
                    id="points"
                    v-model="form.points"
                    type="number"
                    class="mt-1 block w-full"
                    min="1"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.points" />
                </div>

                <!-- Ordre -->
                <div>
                  <InputLabel for="order" value="Ordre" />
                  <TextInput
                    id="order"
                    v-model="form.order"
                    type="number"
                    class="mt-1 block w-full"
                    min="1"
                    required
                  />
                  <InputError class="mt-2" :message="form.errors.order" />
                </div>

                <!-- Boutons de soumission -->
                <div class="flex items-center justify-end mt-4">
                  <Link
                    :href="route('trainer.question-bank.index')"
                    class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-4"
                  >
                    Annuler
                  </Link>
                  <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    Ajouter la question
                  </PrimaryButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref, computed, watch } from 'vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import { XMarkIcon } from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  trainingSessions: Array,
  exams: Array,
});

// Options pour les QCM
const options = ref(['', '']); // Au moins 2 options par défaut

// Tableau pour stocker les réponses correctes multiples
const correctAnswers = ref([]);

// Formulaire
const form = useForm({
  exam_id: '',
  question_text: '',
  question_type: 'multiple_choice',
  options: {},
  correct_answer: '',
  correct_answers: [],
  multiple_answers_allowed: false,
  points: 1,
  order: 1,
});

// Computed
const examsForSession = (sessionId) => {
  return props.exams.filter(exam => exam.training_session_id === sessionId);
};

// Méthodes
const addOption = () => {
  if (options.value.length < 6) { // Limiter à 6 options maximum
    options.value.push('');
  }
};

const removeOption = (index) => {
  if (options.value.length > 2) { // Garder au moins 2 options
    options.value.splice(index, 1);
    
    // Mettre à jour les réponses correctes
    const newCorrectAnswers = [];
    correctAnswers.value.forEach(answerIndex => {
      if (answerIndex < index) {
        newCorrectAnswers.push(answerIndex);
      } else if (answerIndex > index) {
        newCorrectAnswers.push(answerIndex - 1);
      }
    });
    correctAnswers.value = newCorrectAnswers;
  }
};

const submit = () => {
  // Préparer les options pour les QCM
  if (form.question_type === 'multiple_choice') {
    form.options = options.value;
    
    if (form.multiple_answers_allowed) {
      form.correct_answers = correctAnswers.value;
    } else {
      form.correct_answer = correctAnswers.value.length > 0 ? correctAnswers.value[0].toString() : '';
    }
  }
  
  form.post(route('trainer.question-bank.store'));
};

// Watchers
watch(() => form.multiple_answers_allowed, (newValue) => {
  if (!newValue && correctAnswers.value.length > 1) {
    // Si on désactive les réponses multiples, ne garder que la première réponse correcte
    correctAnswers.value = correctAnswers.value.slice(0, 1);
  }
});

watch(() => form.question_type, (newValue) => {
  if (newValue !== 'multiple_choice') {
    // Réinitialiser les options et les réponses correctes si ce n'est pas un QCM
    options.value = ['', ''];
    correctAnswers.value = [];
    form.multiple_answers_allowed = false;
  }
});
</script>
